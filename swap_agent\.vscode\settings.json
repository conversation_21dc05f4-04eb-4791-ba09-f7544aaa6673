{"files.associations": {"exception": "cpp", "mutex": "cpp", "array": "cpp", "string": "cpp", "unordered_map": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "vector": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "ratio": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "typeinfo": "cpp"}}