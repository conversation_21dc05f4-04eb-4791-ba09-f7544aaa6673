#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/feeder_interface.pb.h"
// #include "share/pb/idl/feeder_state.pb.h"
#include "share/pb/idl/dev_hmi.pb.h"

class feeder_interface
{
public:

    int init(zmq::context_t &ctx);

    int issue_feeder_scan(uint32_t feeder_id);

    int issue_belt_forward_rotation(uint32_t feeder_id, uint32_t dev_id);

    int issue_belt_inverse_rotation(uint32_t feeder_id, uint32_t dev_id);

    int issue_belt_speed(uint32_t feeder_id, uint32_t dev_id, int32_t speed);

    int get_feeder_state(feeder_dev_state_total &feeder_state);

    int get_button_state(key_event &button_info);
    int issue_work_status_set(uint32_t status);
    int issue_exception_handle_set(uint32_t value);

    int issue_control_feeder_rotate_belt_forward(uint32_t &feeder_id, uint32_t speed, uint32_t limit);
    int issue_control_feeder_rotate_belt_backward(uint32_t &feeder_id, uint32_t speed, uint32_t limit);
    int issue_control_feeder_rotate_belt_stop(uint32_t &feeder_id, uint32_t speed, uint32_t limit);


    static feeder_interface *get_instance(void)
    {
        static feeder_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *feeder_cmd_total;
    zmq::socket_t *feeder_state_button;
    zmq::socket_t *feeder_state_total;
};
