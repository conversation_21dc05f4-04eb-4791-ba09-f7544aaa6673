/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_DATA_MAP_PB_H_INCLUDED
#define PB_DATA_MAP_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _data_map_tunnel_type {
    data_map_tunnel_type_TUNNEL_TYPE_RESERVE = 0, /* 无定义 */
    data_map_tunnel_type_TUNNEL_TYPE_STRAIGHT = 1, /* 直线段 */
    data_map_tunnel_type_TUNNEL_TYPE_QRC = 2 /* 弧线段 */
} data_map_tunnel_type;

typedef enum _data_map_container_type {
    data_map_container_type_ORDINARY = 0,
    data_map_container_type_SPECIAL_FUNCTION = 1,
    data_map_container_type_HOSPICE = 2,
    data_map_container_type_FEEDER = 3,
    data_map_container_type_EXCESSIVE = 4
} data_map_container_type;

typedef enum _data_map_side {
    data_map_side_LEFT = 0,
    data_map_side_RIGHT = 1
} data_map_side;

typedef enum _data_map_calib_point_type {
    data_map_calib_point_type_CALIB_POINT_ORDINARY = 0,
    data_map_calib_point_type_CALIB_POINT_ORIGIN = 1
} data_map_calib_point_type;

/* Struct definitions */
typedef struct _data_map_tunnel {
    uint32_t tunnel_id;
    float tunnel_length;
    data_map_tunnel_type type;
} data_map_tunnel;

typedef struct _data_map_container_info {
    uint32_t id;
    data_map_container_type type;
    uint32_t tunnel;
    float position; /* 格口位置 */
    float height; /* 格口位置（高度） */
    data_map_side side;
    uint32_t can_addr;
    bool has_satration;
    bool has_rfid;
    bool mobile_rack;
} data_map_container_info;

typedef struct _data_map_containers_info {
    pb_size_t container_count;
    data_map_container_info container[1000];
} data_map_containers_info;

typedef struct _data_map_scanner_info {
    uint32_t scanner_id;
    float scanner_position;
    float scanner_center_point;
    float scanner_width;
} data_map_scanner_info;

typedef struct _data_map_feeders_infomation {
    uint32_t feeder_id;
    uint32_t tunnel_id;
    uint32_t scaner_cnt;
    float position;
    float feeder_width;
    float center_point;
    pb_size_t bind_scanner_if_count;
    data_map_scanner_info bind_scanner_if[4];
    uint32_t bind_hospice_cont;
    uint32_t bind_gray_camera;
    float height;
} data_map_feeders_infomation;

typedef struct _data_map_gray_camera_info {
    uint32_t id;
    float dev_pos;
    float dev_height;
} data_map_gray_camera_info;

typedef struct _data_map_feeders_info {
    pb_size_t feeder_info_count;
    data_map_feeders_infomation feeder_info[6];
} data_map_feeders_info;

typedef struct _data_map_calib_point_info {
    uint32_t id;
    data_map_calib_point_type type;
    uint32_t tunnel;
    float position;
    float global_position;
} data_map_calib_point_info;

typedef struct _data_map {
    /* 巷道宽度和高度 */
    float tunnel_straight;
    float tunnel_height;
    float arc_r; /* 换轨弧的长度 */
    pb_size_t tunnels_count;
    data_map_tunnel tunnels[32];
    bool has_containers;
    data_map_containers_info containers;
    bool has_feeders;
    data_map_feeders_info feeders;
    float total_length;
    uint32_t dev_calib_point_cnt;
    pb_size_t calib_points_count;
    data_map_calib_point_info calib_points[32];
    pb_size_t gray_camera_count;
    data_map_gray_camera_info gray_camera[8];
} data_map;

typedef struct _train_para {
    uint32_t train_cnt;
    float train_len;
    uint32_t carriage_cnt;
    uint32_t carriage_len;
    uint32_t carriage_space;
    uint32_t platform_cnt;
    uint32_t platform_space;
    uint32_t platform_type;
    uint32_t platform_heigth_offset;
    int32_t train_pos_base_offset;
} train_para;

typedef struct _coordinate_conv_para {
    int32_t x_axis_base_offset;
    int32_t y_axis_base_offset;
    int32_t z_axis_base_offset;
} coordinate_conv_para;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _data_map_tunnel_type_MIN data_map_tunnel_type_TUNNEL_TYPE_RESERVE
#define _data_map_tunnel_type_MAX data_map_tunnel_type_TUNNEL_TYPE_QRC
#define _data_map_tunnel_type_ARRAYSIZE ((data_map_tunnel_type)(data_map_tunnel_type_TUNNEL_TYPE_QRC+1))

#define _data_map_container_type_MIN data_map_container_type_ORDINARY
#define _data_map_container_type_MAX data_map_container_type_EXCESSIVE
#define _data_map_container_type_ARRAYSIZE ((data_map_container_type)(data_map_container_type_EXCESSIVE+1))

#define _data_map_side_MIN data_map_side_LEFT
#define _data_map_side_MAX data_map_side_RIGHT
#define _data_map_side_ARRAYSIZE ((data_map_side)(data_map_side_RIGHT+1))

#define _data_map_calib_point_type_MIN data_map_calib_point_type_CALIB_POINT_ORDINARY
#define _data_map_calib_point_type_MAX data_map_calib_point_type_CALIB_POINT_ORIGIN
#define _data_map_calib_point_type_ARRAYSIZE ((data_map_calib_point_type)(data_map_calib_point_type_CALIB_POINT_ORIGIN+1))


#define data_map_tunnel_type_ENUMTYPE data_map_tunnel_type

#define data_map_container_info_type_ENUMTYPE data_map_container_type
#define data_map_container_info_side_ENUMTYPE data_map_side






#define data_map_calib_point_info_type_ENUMTYPE data_map_calib_point_type




/* Initializer values for message structs */
#define data_map_init_default                    {0, 0, 0, 0, {data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default, data_map_tunnel_init_default}, false, data_map_containers_info_init_default, false, data_map_feeders_info_init_default, 0, 0, 0, {data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default, data_map_calib_point_info_init_default}, 0, {data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default, data_map_gray_camera_info_init_default}}
#define data_map_tunnel_init_default             {0, 0, _data_map_tunnel_type_MIN}
#define data_map_container_info_init_default     {0, _data_map_container_type_MIN, 0, 0, 0, _data_map_side_MIN, 0, 0, 0, 0}
#define data_map_containers_info_init_default    {0, {data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default, data_map_container_info_init_default}}
#define data_map_scanner_info_init_default       {0, 0, 0, 0}
#define data_map_feeders_infomation_init_default {0, 0, 0, 0, 0, 0, 0, {data_map_scanner_info_init_default, data_map_scanner_info_init_default, data_map_scanner_info_init_default, data_map_scanner_info_init_default}, 0, 0, 0}
#define data_map_gray_camera_info_init_default   {0, 0, 0}
#define data_map_feeders_info_init_default       {0, {data_map_feeders_infomation_init_default, data_map_feeders_infomation_init_default, data_map_feeders_infomation_init_default, data_map_feeders_infomation_init_default, data_map_feeders_infomation_init_default, data_map_feeders_infomation_init_default}}
#define data_map_calib_point_info_init_default   {0, _data_map_calib_point_type_MIN, 0, 0, 0}
#define train_para_init_default                  {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define coordinate_conv_para_init_default        {0, 0, 0}
#define data_map_init_zero                       {0, 0, 0, 0, {data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero, data_map_tunnel_init_zero}, false, data_map_containers_info_init_zero, false, data_map_feeders_info_init_zero, 0, 0, 0, {data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero, data_map_calib_point_info_init_zero}, 0, {data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero, data_map_gray_camera_info_init_zero}}
#define data_map_tunnel_init_zero                {0, 0, _data_map_tunnel_type_MIN}
#define data_map_container_info_init_zero        {0, _data_map_container_type_MIN, 0, 0, 0, _data_map_side_MIN, 0, 0, 0, 0}
#define data_map_containers_info_init_zero       {0, {data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero, data_map_container_info_init_zero}}
#define data_map_scanner_info_init_zero          {0, 0, 0, 0}
#define data_map_feeders_infomation_init_zero    {0, 0, 0, 0, 0, 0, 0, {data_map_scanner_info_init_zero, data_map_scanner_info_init_zero, data_map_scanner_info_init_zero, data_map_scanner_info_init_zero}, 0, 0, 0}
#define data_map_gray_camera_info_init_zero      {0, 0, 0}
#define data_map_feeders_info_init_zero          {0, {data_map_feeders_infomation_init_zero, data_map_feeders_infomation_init_zero, data_map_feeders_infomation_init_zero, data_map_feeders_infomation_init_zero, data_map_feeders_infomation_init_zero, data_map_feeders_infomation_init_zero}}
#define data_map_calib_point_info_init_zero      {0, _data_map_calib_point_type_MIN, 0, 0, 0}
#define train_para_init_zero                     {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define coordinate_conv_para_init_zero           {0, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define data_map_tunnel_tunnel_id_tag            1
#define data_map_tunnel_tunnel_length_tag        2
#define data_map_tunnel_type_tag                 3
#define data_map_container_info_id_tag           1
#define data_map_container_info_type_tag         2
#define data_map_container_info_tunnel_tag       3
#define data_map_container_info_position_tag     4
#define data_map_container_info_height_tag       5
#define data_map_container_info_side_tag         6
#define data_map_container_info_can_addr_tag     7
#define data_map_container_info_has_satration_tag 8
#define data_map_container_info_has_rfid_tag     9
#define data_map_container_info_mobile_rack_tag  10
#define data_map_containers_info_container_tag   1
#define data_map_scanner_info_scanner_id_tag     1
#define data_map_scanner_info_scanner_position_tag 2
#define data_map_scanner_info_scanner_center_point_tag 3
#define data_map_scanner_info_scanner_width_tag  4
#define data_map_feeders_infomation_feeder_id_tag 1
#define data_map_feeders_infomation_tunnel_id_tag 2
#define data_map_feeders_infomation_scaner_cnt_tag 3
#define data_map_feeders_infomation_position_tag 4
#define data_map_feeders_infomation_feeder_width_tag 5
#define data_map_feeders_infomation_center_point_tag 6
#define data_map_feeders_infomation_bind_scanner_if_tag 7
#define data_map_feeders_infomation_bind_hospice_cont_tag 8
#define data_map_feeders_infomation_bind_gray_camera_tag 9
#define data_map_feeders_infomation_height_tag   10
#define data_map_gray_camera_info_id_tag         1
#define data_map_gray_camera_info_dev_pos_tag    2
#define data_map_gray_camera_info_dev_height_tag 3
#define data_map_feeders_info_feeder_info_tag    1
#define data_map_calib_point_info_id_tag         1
#define data_map_calib_point_info_type_tag       2
#define data_map_calib_point_info_tunnel_tag     3
#define data_map_calib_point_info_position_tag   4
#define data_map_calib_point_info_global_position_tag 5
#define data_map_tunnel_straight_tag             1
#define data_map_tunnel_height_tag               2
#define data_map_arc_r_tag                       3
#define data_map_tunnels_tag                     4
#define data_map_containers_tag                  5
#define data_map_feeders_tag                     6
#define data_map_total_length_tag                7
#define data_map_dev_calib_point_cnt_tag         8
#define data_map_calib_points_tag                9
#define data_map_gray_camera_tag                 10
#define train_para_train_cnt_tag                 1
#define train_para_train_len_tag                 2
#define train_para_carriage_cnt_tag              3
#define train_para_carriage_len_tag              4
#define train_para_carriage_space_tag            5
#define train_para_platform_cnt_tag              6
#define train_para_platform_space_tag            7
#define train_para_platform_type_tag             8
#define train_para_platform_heigth_offset_tag    9
#define train_para_train_pos_base_offset_tag     10
#define coordinate_conv_para_x_axis_base_offset_tag 1
#define coordinate_conv_para_y_axis_base_offset_tag 2
#define coordinate_conv_para_z_axis_base_offset_tag 3

/* Struct field encoding specification for nanopb */
#define data_map_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    tunnel_straight,   1) \
X(a, STATIC,   SINGULAR, FLOAT,    tunnel_height,     2) \
X(a, STATIC,   SINGULAR, FLOAT,    arc_r,             3) \
X(a, STATIC,   REPEATED, MESSAGE,  tunnels,           4) \
X(a, STATIC,   OPTIONAL, MESSAGE,  containers,        5) \
X(a, STATIC,   OPTIONAL, MESSAGE,  feeders,           6) \
X(a, STATIC,   SINGULAR, FLOAT,    total_length,      7) \
X(a, STATIC,   SINGULAR, UINT32,   dev_calib_point_cnt,   8) \
X(a, STATIC,   REPEATED, MESSAGE,  calib_points,      9) \
X(a, STATIC,   REPEATED, MESSAGE,  gray_camera,      10)
#define data_map_CALLBACK NULL
#define data_map_DEFAULT NULL
#define data_map_tunnels_MSGTYPE data_map_tunnel
#define data_map_containers_MSGTYPE data_map_containers_info
#define data_map_feeders_MSGTYPE data_map_feeders_info
#define data_map_calib_points_MSGTYPE data_map_calib_point_info
#define data_map_gray_camera_MSGTYPE data_map_gray_camera_info

#define data_map_tunnel_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   tunnel_id,         1) \
X(a, STATIC,   SINGULAR, FLOAT,    tunnel_length,     2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3)
#define data_map_tunnel_CALLBACK NULL
#define data_map_tunnel_DEFAULT NULL

#define data_map_container_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UENUM,    type,              2) \
X(a, STATIC,   SINGULAR, UINT32,   tunnel,            3) \
X(a, STATIC,   SINGULAR, FLOAT,    position,          4) \
X(a, STATIC,   SINGULAR, FLOAT,    height,            5) \
X(a, STATIC,   SINGULAR, UENUM,    side,              6) \
X(a, STATIC,   SINGULAR, UINT32,   can_addr,          7) \
X(a, STATIC,   SINGULAR, BOOL,     has_satration,     8) \
X(a, STATIC,   SINGULAR, BOOL,     has_rfid,          9) \
X(a, STATIC,   SINGULAR, BOOL,     mobile_rack,      10)
#define data_map_container_info_CALLBACK NULL
#define data_map_container_info_DEFAULT NULL

#define data_map_containers_info_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  container,         1)
#define data_map_containers_info_CALLBACK NULL
#define data_map_containers_info_DEFAULT NULL
#define data_map_containers_info_container_MSGTYPE data_map_container_info

#define data_map_scanner_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   scanner_id,        1) \
X(a, STATIC,   SINGULAR, FLOAT,    scanner_position,   2) \
X(a, STATIC,   SINGULAR, FLOAT,    scanner_center_point,   3) \
X(a, STATIC,   SINGULAR, FLOAT,    scanner_width,     4)
#define data_map_scanner_info_CALLBACK NULL
#define data_map_scanner_info_DEFAULT NULL

#define data_map_feeders_infomation_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         1) \
X(a, STATIC,   SINGULAR, UINT32,   tunnel_id,         2) \
X(a, STATIC,   SINGULAR, UINT32,   scaner_cnt,        3) \
X(a, STATIC,   SINGULAR, FLOAT,    position,          4) \
X(a, STATIC,   SINGULAR, FLOAT,    feeder_width,      5) \
X(a, STATIC,   SINGULAR, FLOAT,    center_point,      6) \
X(a, STATIC,   REPEATED, MESSAGE,  bind_scanner_if,   7) \
X(a, STATIC,   SINGULAR, UINT32,   bind_hospice_cont,   8) \
X(a, STATIC,   SINGULAR, UINT32,   bind_gray_camera,   9) \
X(a, STATIC,   SINGULAR, FLOAT,    height,           10)
#define data_map_feeders_infomation_CALLBACK NULL
#define data_map_feeders_infomation_DEFAULT NULL
#define data_map_feeders_infomation_bind_scanner_if_MSGTYPE data_map_scanner_info

#define data_map_gray_camera_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, FLOAT,    dev_pos,           2) \
X(a, STATIC,   SINGULAR, FLOAT,    dev_height,        3)
#define data_map_gray_camera_info_CALLBACK NULL
#define data_map_gray_camera_info_DEFAULT NULL

#define data_map_feeders_info_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  feeder_info,       1)
#define data_map_feeders_info_CALLBACK NULL
#define data_map_feeders_info_DEFAULT NULL
#define data_map_feeders_info_feeder_info_MSGTYPE data_map_feeders_infomation

#define data_map_calib_point_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UENUM,    type,              2) \
X(a, STATIC,   SINGULAR, UINT32,   tunnel,            3) \
X(a, STATIC,   SINGULAR, FLOAT,    position,          4) \
X(a, STATIC,   SINGULAR, FLOAT,    global_position,   5)
#define data_map_calib_point_info_CALLBACK NULL
#define data_map_calib_point_info_DEFAULT NULL

#define train_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   train_cnt,         1) \
X(a, STATIC,   SINGULAR, FLOAT,    train_len,         2) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_cnt,      3) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_len,      4) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_space,    5) \
X(a, STATIC,   SINGULAR, UINT32,   platform_cnt,      6) \
X(a, STATIC,   SINGULAR, UINT32,   platform_space,    7) \
X(a, STATIC,   SINGULAR, UINT32,   platform_type,     8) \
X(a, STATIC,   SINGULAR, UINT32,   platform_heigth_offset,   9) \
X(a, STATIC,   SINGULAR, INT32,    train_pos_base_offset,  10)
#define train_para_CALLBACK NULL
#define train_para_DEFAULT NULL

#define coordinate_conv_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    x_axis_base_offset,   1) \
X(a, STATIC,   SINGULAR, INT32,    y_axis_base_offset,   2) \
X(a, STATIC,   SINGULAR, INT32,    z_axis_base_offset,   3)
#define coordinate_conv_para_CALLBACK NULL
#define coordinate_conv_para_DEFAULT NULL

extern const pb_msgdesc_t data_map_msg;
extern const pb_msgdesc_t data_map_tunnel_msg;
extern const pb_msgdesc_t data_map_container_info_msg;
extern const pb_msgdesc_t data_map_containers_info_msg;
extern const pb_msgdesc_t data_map_scanner_info_msg;
extern const pb_msgdesc_t data_map_feeders_infomation_msg;
extern const pb_msgdesc_t data_map_gray_camera_info_msg;
extern const pb_msgdesc_t data_map_feeders_info_msg;
extern const pb_msgdesc_t data_map_calib_point_info_msg;
extern const pb_msgdesc_t train_para_msg;
extern const pb_msgdesc_t coordinate_conv_para_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define data_map_fields &data_map_msg
#define data_map_tunnel_fields &data_map_tunnel_msg
#define data_map_container_info_fields &data_map_container_info_msg
#define data_map_containers_info_fields &data_map_containers_info_msg
#define data_map_scanner_info_fields &data_map_scanner_info_msg
#define data_map_feeders_infomation_fields &data_map_feeders_infomation_msg
#define data_map_gray_camera_info_fields &data_map_gray_camera_info_msg
#define data_map_feeders_info_fields &data_map_feeders_info_msg
#define data_map_calib_point_info_fields &data_map_calib_point_info_msg
#define train_para_fields &train_para_msg
#define coordinate_conv_para_fields &coordinate_conv_para_msg

/* Maximum encoded size of messages (where known) */
#define DATA_MAP_PB_H_MAX_SIZE                   data_map_size
#define coordinate_conv_para_size                33
#define data_map_calib_point_info_size           24
#define data_map_container_info_size             38
#define data_map_containers_info_size            40000
#define data_map_feeders_info_size               870
#define data_map_feeders_infomation_size         142
#define data_map_gray_camera_info_size           16
#define data_map_scanner_info_size               21
#define data_map_size                            42359
#define data_map_tunnel_size                     13
#define train_para_size                          64

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
