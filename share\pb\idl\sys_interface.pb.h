/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_SYS_INTERFACE_PB_H_INCLUDED
#define PB_SYS_INTERFACE_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
/* 系统状态相关 */
typedef enum _e_wkstate {
    e_wkstate_SYS_RESERVE = 0,
    e_wkstate_SYS_INIT = 1,
    e_wkstate_SYS_CHECK = 2,
    e_wkstate_SYS_CALIBRATE = 3,
    e_wkstate_SYS_STOP = 4,
    e_wkstate_SYS_STOPING = 5,
    e_wkstate_SYS_AUTO_RUNNING = 6,
    e_wkstate_SYS_PART_ERROR = 7,
    e_wkstate_SYS_ERROR_RECOVERY = 8,
    e_wkstate_SYS_MANUAL = 9
} e_wkstate;

typedef enum _e_errstate {
    e_errstate_NOERROR = 0,
    e_errstate_GLOBAL_ERROR = 1,
    e_errstate_LOCAL_ERROR = 2,
    e_errstate_GLOBAL_EMERG_ERROR = 3
} e_errstate;

typedef enum _e_sys_mode {
    e_sys_mode_AUTO = 0,
    e_sys_mode_MANNUAL = 1,
    e_sys_mode_DEMONSTRATE = 2
} e_sys_mode;

typedef enum _safty_door_state_offset {
    safty_door_state_offset_FRONT_DOOR = 0,
    safty_door_state_offset_TAIL_DOOR = 1,
    safty_door_state_offset_SYS_TRAIN_POWER = 2,
    safty_door_state_offset_SYS_RELAY_POWER = 3
} safty_door_state_offset;

typedef enum _emerg_button_state_offset {
    emerg_button_state_offset_FRONT_DOOR_EMERG = 0,
    emerg_button_state_offset_TAIL_DOOR_EMERG = 1,
    emerg_button_state_offset_CONTROL_CABINET = 2,
    emerg_button_state_offset_FEEDER_1 = 3,
    emerg_button_state_offset_FEEDER_2 = 4,
    emerg_button_state_offset_FEEDER_3 = 5,
    emerg_button_state_offset_FEEDER_4 = 6,
    emerg_button_state_offset_FEEDER_5 = 7,
    emerg_button_state_offset_FEEDER_6 = 8
} emerg_button_state_offset;

typedef enum _component_state {
    component_state_C_RESERVE = 0,
    component_state_C_IDLE = 1,
    component_state_C_RUNNING = 2,
    component_state_C_ERR = 3,
    component_state_C_INIT = 4,
    component_state_C_UNKNOW = 5
} component_state;

typedef enum _manual_cmd_type {
    manual_cmd_type_FEEDER_BELT_FORWARD = 0,
    manual_cmd_type_FEEDER_BELT_BACKWORD = 1,
    manual_cmd_type_FEEDER_BLET_STOP = 2,
    manual_cmd_type_TOWER_LIGHT_ON = 3,
    manual_cmd_type_TOWER_LIGHT_OFF = 4,
    manual_cmd_type_TOWER_BEEP_ON = 5,
    manual_cmd_type_TOWER_BEEP_OFF = 6,
    manual_cmd_type_TRAIN_MOVE_FORWARD_LIMIT = 7,
    manual_cmd_type_TRAIN_MOVE_FORWARD = 8,
    manual_cmd_type_TRAIN_MOVE_TO_FEEDER = 9,
    manual_cmd_type_TRAIN_MOVE_TO_HOS = 10,
    manual_cmd_type_TRAIN_MOVE_TO_REDUN = 11,
    manual_cmd_type_TRAIN_MOVE_TO_SLOT = 12,
    manual_cmd_type_TRAIN_MOVE_TO_CAMERA = 13,
    manual_cmd_type_TRAIN_MOVE_TO_TARGET = 14,
    manual_cmd_type_Y_AXIS_MOVE = 15,
    manual_cmd_type_Y_AXIS_CALIBRATE = 16,
    manual_cmd_type_PLATFORM_BELT_FORWARD = 17,
    manual_cmd_type_PLATFORM_BELT_BACKWORD = 18,
    manual_cmd_type_PLATFORM_BELT_ZEOR_CALIB = 19,
    manual_cmd_type_CONTAINER_LED_ON = 20,
    manual_cmd_type_CONTAINER_LED_OFF = 21,
    manual_cmd_type_CONTAINER_LED_TRAVERSE = 22,
    manual_cmd_type_DEVICE_SIMULATE_RUN = 23,
    manual_cmd_type_TRAIN_DISABLE = 24,
    manual_cmd_type_TRAIN_ENABLE = 25,
    manual_cmd_type_CARRIAGE_DISABLE = 26,
    manual_cmd_type_CARRIAGE_ENABLE = 27,
    manual_cmd_type_PLATFORM_DISABLE = 28,
    manual_cmd_type_PLATFORM_ENABLE = 29,
    manual_cmd_type_SHELVES_LOCK = 30,
    manual_cmd_type_SEELVER_UNLOCK = 31,
    manual_cmd_type_DEV_ENTER_MAINTENANCE_STATE = 32,
    manual_cmd_type_DEV_EXIT_MAINTENANCE_STATE = 33,
    manual_cmd_type_DEV_CTRL_PLATFORM_HEIGTH = 34
} manual_cmd_type;

typedef enum _dev_platform_heigth_ctrl {
    dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_RESERVE = 0,
    dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_TOP = 1,
    dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_FEEDER = 2,
    dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_DYNAMICS = 3
} dev_platform_heigth_ctrl;

typedef enum _task_cmd_task_cmd_type {
    task_cmd_task_cmd_type_SIMULATE_FINISH = 0,
    task_cmd_task_cmd_type_CANCEL = 1,
    task_cmd_task_cmd_type_HOSPICE_FINISH = 2
} task_cmd_task_cmd_type;

/* Struct definitions */
typedef struct _dev_state {
    bool safty_door_open;
    uint32_t safty_door_state;
    bool emerg_pressed;
    uint32_t emerg_button_state;
} dev_state;

typedef struct _sys_mode_state {
    e_sys_mode mode;
    e_wkstate state;
    e_errstate err;
    bool has_dev_st;
    dev_state dev_st;
    uint32_t error_code;
    bool maintenance_state;
} sys_mode_state;

typedef struct _train_move_para {
    uint32_t target_id;
    uint32_t move_length;
    uint32_t move_staight_speed;
    uint32_t move_arc_speed;
} train_move_para;

typedef struct _dev_move_para {
    uint32_t move_dir; /* 0-停止  1-正向 2-反向 */
    uint32_t move_length;
    uint32_t move_speed;
} dev_move_para;

typedef struct _manual_cmd {
    uint32_t dev_id;
    uint32_t sub_dev_id;
    manual_cmd_type cmd_type;
    pb_size_t which_cmd_para;
    union {
        train_move_para train_move;
        dev_move_para dev_move;
        bool move_continuout_flag;
        dev_platform_heigth_ctrl platform_height_para;
    } cmd_para;
} manual_cmd;

typedef struct _set_state {
    uint32_t state;
} set_state;

typedef struct _task_cmd {
    pb_byte_t task_id[36];
    uint32_t cmd;
    uint32_t param; /* 对于SIMULATE_FINISH，param是格口号 */
} task_cmd;

typedef struct _reset_exception {
    uint32_t exception_src;
    uint32_t dev;
    uint32_t error;
} reset_exception;

typedef struct _sys_cmd {
    pb_size_t which_cmd;
    union {
        set_state state;
        task_cmd task;
        reset_exception excep;
        manual_cmd misc;
    } cmd;
} sys_cmd;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _e_wkstate_MIN e_wkstate_SYS_RESERVE
#define _e_wkstate_MAX e_wkstate_SYS_MANUAL
#define _e_wkstate_ARRAYSIZE ((e_wkstate)(e_wkstate_SYS_MANUAL+1))

#define _e_errstate_MIN e_errstate_NOERROR
#define _e_errstate_MAX e_errstate_GLOBAL_EMERG_ERROR
#define _e_errstate_ARRAYSIZE ((e_errstate)(e_errstate_GLOBAL_EMERG_ERROR+1))

#define _e_sys_mode_MIN e_sys_mode_AUTO
#define _e_sys_mode_MAX e_sys_mode_DEMONSTRATE
#define _e_sys_mode_ARRAYSIZE ((e_sys_mode)(e_sys_mode_DEMONSTRATE+1))

#define _safty_door_state_offset_MIN safty_door_state_offset_FRONT_DOOR
#define _safty_door_state_offset_MAX safty_door_state_offset_SYS_RELAY_POWER
#define _safty_door_state_offset_ARRAYSIZE ((safty_door_state_offset)(safty_door_state_offset_SYS_RELAY_POWER+1))

#define _emerg_button_state_offset_MIN emerg_button_state_offset_FRONT_DOOR_EMERG
#define _emerg_button_state_offset_MAX emerg_button_state_offset_FEEDER_6
#define _emerg_button_state_offset_ARRAYSIZE ((emerg_button_state_offset)(emerg_button_state_offset_FEEDER_6+1))

#define _component_state_MIN component_state_C_RESERVE
#define _component_state_MAX component_state_C_UNKNOW
#define _component_state_ARRAYSIZE ((component_state)(component_state_C_UNKNOW+1))

#define _manual_cmd_type_MIN manual_cmd_type_FEEDER_BELT_FORWARD
#define _manual_cmd_type_MAX manual_cmd_type_DEV_CTRL_PLATFORM_HEIGTH
#define _manual_cmd_type_ARRAYSIZE ((manual_cmd_type)(manual_cmd_type_DEV_CTRL_PLATFORM_HEIGTH+1))

#define _dev_platform_heigth_ctrl_MIN dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_RESERVE
#define _dev_platform_heigth_ctrl_MAX dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_DYNAMICS
#define _dev_platform_heigth_ctrl_ARRAYSIZE ((dev_platform_heigth_ctrl)(dev_platform_heigth_ctrl_DEV_PLATFORM_HEIGHT_DYNAMICS+1))

#define _task_cmd_task_cmd_type_MIN task_cmd_task_cmd_type_SIMULATE_FINISH
#define _task_cmd_task_cmd_type_MAX task_cmd_task_cmd_type_HOSPICE_FINISH
#define _task_cmd_task_cmd_type_ARRAYSIZE ((task_cmd_task_cmd_type)(task_cmd_task_cmd_type_HOSPICE_FINISH+1))


#define sys_mode_state_mode_ENUMTYPE e_sys_mode
#define sys_mode_state_state_ENUMTYPE e_wkstate
#define sys_mode_state_err_ENUMTYPE e_errstate



#define manual_cmd_cmd_type_ENUMTYPE manual_cmd_type
#define manual_cmd_cmd_para_platform_height_para_ENUMTYPE dev_platform_heigth_ctrl






/* Initializer values for message structs */
#define dev_state_init_default                   {0, 0, 0, 0}
#define sys_mode_state_init_default              {_e_sys_mode_MIN, _e_wkstate_MIN, _e_errstate_MIN, false, dev_state_init_default, 0, 0}
#define train_move_para_init_default             {0, 0, 0, 0}
#define dev_move_para_init_default               {0, 0, 0}
#define manual_cmd_init_default                  {0, 0, _manual_cmd_type_MIN, 0, {train_move_para_init_default}}
#define set_state_init_default                   {0}
#define task_cmd_init_default                    {{0}, 0, 0}
#define reset_exception_init_default             {0, 0, 0}
#define sys_cmd_init_default                     {0, {set_state_init_default}}
#define dev_state_init_zero                      {0, 0, 0, 0}
#define sys_mode_state_init_zero                 {_e_sys_mode_MIN, _e_wkstate_MIN, _e_errstate_MIN, false, dev_state_init_zero, 0, 0}
#define train_move_para_init_zero                {0, 0, 0, 0}
#define dev_move_para_init_zero                  {0, 0, 0}
#define manual_cmd_init_zero                     {0, 0, _manual_cmd_type_MIN, 0, {train_move_para_init_zero}}
#define set_state_init_zero                      {0}
#define task_cmd_init_zero                       {{0}, 0, 0}
#define reset_exception_init_zero                {0, 0, 0}
#define sys_cmd_init_zero                        {0, {set_state_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define dev_state_safty_door_open_tag            1
#define dev_state_safty_door_state_tag           2
#define dev_state_emerg_pressed_tag              3
#define dev_state_emerg_button_state_tag         4
#define sys_mode_state_mode_tag                  1
#define sys_mode_state_state_tag                 2
#define sys_mode_state_err_tag                   3
#define sys_mode_state_dev_st_tag                4
#define sys_mode_state_error_code_tag            5
#define sys_mode_state_maintenance_state_tag     6
#define train_move_para_target_id_tag            1
#define train_move_para_move_length_tag          2
#define train_move_para_move_staight_speed_tag   3
#define train_move_para_move_arc_speed_tag       4
#define dev_move_para_move_dir_tag               1
#define dev_move_para_move_length_tag            2
#define dev_move_para_move_speed_tag             3
#define manual_cmd_dev_id_tag                    1
#define manual_cmd_sub_dev_id_tag                2
#define manual_cmd_cmd_type_tag                  3
#define manual_cmd_train_move_tag                4
#define manual_cmd_dev_move_tag                  5
#define manual_cmd_move_continuout_flag_tag      6
#define manual_cmd_platform_height_para_tag      7
#define set_state_state_tag                      1
#define task_cmd_task_id_tag                     1
#define task_cmd_cmd_tag                         2
#define task_cmd_param_tag                       3
#define reset_exception_exception_src_tag        1
#define reset_exception_dev_tag                  2
#define reset_exception_error_tag                3
#define sys_cmd_state_tag                        1
#define sys_cmd_task_tag                         2
#define sys_cmd_excep_tag                        3
#define sys_cmd_misc_tag                         4

/* Struct field encoding specification for nanopb */
#define dev_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     safty_door_open,   1) \
X(a, STATIC,   SINGULAR, UINT32,   safty_door_state,   2) \
X(a, STATIC,   SINGULAR, BOOL,     emerg_pressed,     3) \
X(a, STATIC,   SINGULAR, UINT32,   emerg_button_state,   4)
#define dev_state_CALLBACK NULL
#define dev_state_DEFAULT NULL

#define sys_mode_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    mode,              1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UENUM,    err,               3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  dev_st,            4) \
X(a, STATIC,   SINGULAR, UINT32,   error_code,        5) \
X(a, STATIC,   SINGULAR, BOOL,     maintenance_state,   6)
#define sys_mode_state_CALLBACK NULL
#define sys_mode_state_DEFAULT NULL
#define sys_mode_state_dev_st_MSGTYPE dev_state

#define train_move_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   target_id,         1) \
X(a, STATIC,   SINGULAR, UINT32,   move_length,       2) \
X(a, STATIC,   SINGULAR, UINT32,   move_staight_speed,   3) \
X(a, STATIC,   SINGULAR, UINT32,   move_arc_speed,    4)
#define train_move_para_CALLBACK NULL
#define train_move_para_DEFAULT NULL

#define dev_move_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   move_dir,          1) \
X(a, STATIC,   SINGULAR, UINT32,   move_length,       2) \
X(a, STATIC,   SINGULAR, UINT32,   move_speed,        3)
#define dev_move_para_CALLBACK NULL
#define dev_move_para_DEFAULT NULL

#define manual_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        2) \
X(a, STATIC,   SINGULAR, UENUM,    cmd_type,          3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd_para,train_move,cmd_para.train_move),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd_para,dev_move,cmd_para.dev_move),   5) \
X(a, STATIC,   ONEOF,    BOOL,     (cmd_para,move_continuout_flag,cmd_para.move_continuout_flag),   6) \
X(a, STATIC,   ONEOF,    UENUM,    (cmd_para,platform_height_para,cmd_para.platform_height_para),   7)
#define manual_cmd_CALLBACK NULL
#define manual_cmd_DEFAULT NULL
#define manual_cmd_cmd_para_train_move_MSGTYPE train_move_para
#define manual_cmd_cmd_para_dev_move_MSGTYPE dev_move_para

#define set_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   state,             1)
#define set_state_CALLBACK NULL
#define set_state_DEFAULT NULL

#define task_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           1) \
X(a, STATIC,   SINGULAR, UINT32,   cmd,               2) \
X(a, STATIC,   SINGULAR, UINT32,   param,             3)
#define task_cmd_CALLBACK NULL
#define task_cmd_DEFAULT NULL

#define reset_exception_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   exception_src,     1) \
X(a, STATIC,   SINGULAR, UINT32,   dev,               2) \
X(a, STATIC,   SINGULAR, UINT32,   error,             3)
#define reset_exception_CALLBACK NULL
#define reset_exception_DEFAULT NULL

#define sys_cmd_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd,state,cmd.state),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd,task,cmd.task),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd,excep,cmd.excep),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (cmd,misc,cmd.misc),   4)
#define sys_cmd_CALLBACK NULL
#define sys_cmd_DEFAULT NULL
#define sys_cmd_cmd_state_MSGTYPE set_state
#define sys_cmd_cmd_task_MSGTYPE task_cmd
#define sys_cmd_cmd_excep_MSGTYPE reset_exception
#define sys_cmd_cmd_misc_MSGTYPE manual_cmd

extern const pb_msgdesc_t dev_state_msg;
extern const pb_msgdesc_t sys_mode_state_msg;
extern const pb_msgdesc_t train_move_para_msg;
extern const pb_msgdesc_t dev_move_para_msg;
extern const pb_msgdesc_t manual_cmd_msg;
extern const pb_msgdesc_t set_state_msg;
extern const pb_msgdesc_t task_cmd_msg;
extern const pb_msgdesc_t reset_exception_msg;
extern const pb_msgdesc_t sys_cmd_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define dev_state_fields &dev_state_msg
#define sys_mode_state_fields &sys_mode_state_msg
#define train_move_para_fields &train_move_para_msg
#define dev_move_para_fields &dev_move_para_msg
#define manual_cmd_fields &manual_cmd_msg
#define set_state_fields &set_state_msg
#define task_cmd_fields &task_cmd_msg
#define reset_exception_fields &reset_exception_msg
#define sys_cmd_fields &sys_cmd_msg

/* Maximum encoded size of messages (where known) */
#define SYS_INTERFACE_PB_H_MAX_SIZE              sys_cmd_size
#define dev_move_para_size                       18
#define dev_state_size                           16
#define manual_cmd_size                          40
#define reset_exception_size                     18
#define set_state_size                           6
#define sys_cmd_size                             52
#define sys_mode_state_size                      32
#define task_cmd_size                            50
#define train_move_para_size                     24

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
