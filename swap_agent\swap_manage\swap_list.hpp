#ifndef __SWAP_MANAGE_TRAIN_LIST_HPP__
#define __SWAP_MANAGE_TRAIN_LIST_HPP__

#include "../threadpool/blocking_queue.hpp"

#include "share/pb/idl/auto_exchange.pb.h"
#include "share/pb/idl/exception.pb.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>

#include <iostream>

#include <sys/socket.h>
#include <arpa/inet.h>
#include "cfg.hpp"

#include <list>


using namespace std;


/**@enum _vheicle_map_opt_tab
* @brief 定义unordered_map的操作结果
*/
typedef enum
{//暂时没使用
	TRAIN_TASK_NONE                             = 0,    ///< 无任务
    TRAIN_TASK_CARRIAGR_MOTOR_ZERO_CALIBRATION  = 1,    ///<车厢电机零点校准
    TRAIN_TASK_CARRIAGR_MOTOR_MOVE              = 2,    ///<车厢电机移动
    TRAIN_TASK_CARRIAGR_BELT_MOTOR_ROTATE       = 3,    ///<车厢皮带转动
    TRAIN_TASK_CARRIAGR_BELT_MOTOR_ZERO         = 4,    ///<车厢皮带回零
    TRAIN_TASK_CARRIAGR_PACK                    = 5,    ///<车厢上包
    TRAIN_TASK_CARRIAGR_UNPACK                  = 6,    ///<车厢卸包
    // TRAIN_TASK_QUERY_PARA                       = 7,    ///<小车运行参数
    TRAIN_TASK_UNKNOWN                          = 8     ///< 无法解析
}_train_task_type;


//状态上报任务类型
#define SWAP_TASK_MOVE_X                    0x1000      //X轴移动命令
#define SWAP_TASK_MOVE_Y                    0x2000      //Y轴移动命令
#define SWAP_TASK_MOVE_Z                    0x4000      //Z轴移动命令
#define SWAP_TASK_LOAD                      0x6000      //取货命令
#define SWAP_TASK_UNLOAD                    0x7000      //卸货命令
#define SWAP_TASK_EMERG                     0x8001      //急停
#define SWAP_TASK_RESET                     0x8002      //复位
#define SWAP_TASK_RESTART                   0x8003      //重启
#define SWAP_TASK_FIND_ZERO                 0x8004      //寻零
#define SWAP_TASK_LOAD_MOVE_SOTP            0xd000      //取货行走任务,XY走到目标位置后等后续指令
#define SWAP_TASK_LOAD_MOVE_CONTINUE        0xd001      //取货行走任务,XY走到目标指令，立即执行取货任务
#define SWAP_TASK_UNLOAD_MOVE_SOTP          0xe000      //卸货行走任务,XY走到目标位置后等后续指令
#define SWAP_TASK_UNLOAD_MOVE_CONTINUE      0xe001      //卸货行走任务,XY走到目标指令，立即执行卸货任务

#define SWAP_TASK_INTE_MOVE_X               0xf001      //X轴移动命令
#define SWAP_TASK_INTE_MOVE_Y               0xf002      //Y轴移动命令
#define SWAP_TASK_INTE_MOVE_XY              0xf003      //XY轴移动命令
#define SWAP_TASK_INTE_MOVE_SWAP1           0xf004      //取货机构1移动任务
#define SWAP_TASK_INTE_MOVE_SWAP2           0xf005      //取货机构2移动任务
#define SWAP_TASK_INTE_MOVE_SWAP_DISTANCE   0xf007      //载货箱移动距离
#define SWAP_TASK_INTE_LOAD                 0xf008      //取货任务
#define SWAP_TASK_INTE_UBLOAD               0xf009      //卸货任务
#define SWAP_TASK_INTE_LOAD_UNLOAD          0xf00a      //取卸货任务
#define SWAP_TASK_INTE_FIND_ZERO            0xf00b      //寻零任务

//指令下发任务类型
#define SWAP_TASK_SEND_MOVE_X               0x01      //X轴移动命令
#define SWAP_TASK_SEND_MOVE_Y               0x02      //Y轴移动命令
#define SWAP_TASK_SEND_MOVE_XY              0x03      //XY轴移动命令
#define SWAP_TASK_SEND_MOVE_SWAP1           0x04      //取货机构1移动任务
#define SWAP_TASK_SEND_MOVE_SWAP2           0x05      //取货机构2移动任务
#define SWAP_TASK_SEND_MOVE_SWAP_DISTANCE   0x07      //载货箱移动距离
#define SWAP_TASK_SEND_LOAD                 0x08      //取货任务
#define SWAP_TASK_SEND_UBLOAD               0x09      //卸货任务
#define SWAP_TASK_SEND_LOAD_UNLOAD          0x0a      //取卸货任务
#define SWAP_TASK_SEND_FIND_ZERO            0x0b      //寻零任务

#define SWAP_TASK_SEND_EMERG                0x01      //急停
#define SWAP_TASK_SEND_RESET                0x02      //复位
#define SWAP_TASK_SEND_RESTART              0x03      //重启


typedef struct _task_state_
{
    uint8_t control_id;
    uint16_t task_type;
    uint8_t task_result;
}task_st;


typedef struct _net_msg
{
    uint32_t cin_addr;  //网络字节序
	uint16_t data_len;
	uint8_t msg_data[256];
}net_msg;


typedef struct _exception_info_t
{
    uint8_t sub_id;
    int exception_code;
}exception_info_t;



/**@struct train_info
* @brief 车辆信息数据结构体
*/
typedef struct _train_info
{
	/// 网络通信相关信息

    uint32_t cin_addr;
	struct sockaddr_in v_addr;
    char sw_version[16] = "";
    char hw_version[16] = "";

	/// 车辆最后一次有效通信时间，用于心跳检测
	struct timespec v_last_msg_upload_tick;

	/// 车辆最后一次数据下行时间, 用于进行发送就绪条件检测
	struct timespec v_last_msg_downlink_tick;

	dev_state_net v_last_state;   //网络消息状态

	exception_info_t v_exception[100];

    //上行sequence: 1 车辆重启; 0:注册
    volatile uint32_t v_comm_uplink_seque;

	_train_task_type v_task;
    task_st dev_task_state;

    //通信完成标志
	bool v_comm_finish_flag;
    
	uint32_t v_last_report_seque;

    //车头故障码, 用于调度系统上报
	uint32_t train_error_no = 0;

	//车辆最后一条消息
	net_msg v_last_msg;

    //上一次心跳中上报的异常码
    int except_code;

    //上一次心跳中上报的异常电机编号
    int except_motor_id;

    //设备重启标志
    bool dev_restart_flag = false;

}swap_info;


// typedef struct _swap_task_info
// {
//     //通信完成标志
// 	bool v_comm_finish_flag;

//     //上行sequence: 1 车辆重启; 0:注册
//     volatile uint32_t v_comm_uplink_seque;

//     //车辆最后一条消息
// 	net_msg v_last_msg;
// }swap_task_info;


typedef unordered_map<int, std::shared_ptr<spdlog::logger>> train_log_map;
typedef unordered_map<uint32_t, int> train_info_sock_map;    //<cin_addr -> dev_id>
typedef unordered_map<int, swap_info> train_info_map;  //<devid -> swap_info>
typedef unordered_map<int, auto_exchange_task_state> swap_task_info_map;  //<swap_id -> auto_exchange_task_state>
typedef unordered_map< int, blocking_queue<auto_exchange_task> > swap_task_map;
typedef unordered_map<int, uint32_t> swap_comm_sequence_map;
typedef unordered_map<int, int> resend_count;

typedef unordered_map< int, std::list<except_info> > train_except_map;


/**@enum _train_map_opt_tab
* @brief 定义unordered_map的操作结果
*/
typedef enum
{
	TRAIN_SESSION_SUCESS   = 0,  ///< 当前操作成功
	TRAIN_SESSION_NOT_FIND = 1,  ///< 指定对象不存在
	TRAIN_SESSION_EXCID    = 2,  ///< 指定对象已存在 
	TRAIN_SESSION_OPT_FIAL = 3   ///< 当前操作失败
}_train_map_opt_tab;






class train_list_map
{
public:

    // train_list_map class构造函数
	explicit train_list_map();

    ~train_list_map();

    int swap_list_get_task_id(void)
    {
        std::lock_guard<std::mutex> opt_lock(m_swap_dev_task_id_mtx);
        return m_task_id;
    }

    void swap_list_update_task_id(void)
    {
        std::lock_guard<std::mutex> opt_lock(m_swap_dev_task_id_mtx);
        m_task_id += 1;
    }

	/**@brief	  车辆列表指定对象列表当前存储数据量
	* @param[in]  NULl
	* @return	  当前列表大小
	*/
	int train_list_map_get_size(void)
	{
		return m_train_dev_list.size();
	}

    const train_info_map& get_train_dev_list(void) const
    {
        return m_train_dev_list;
    }

    static train_list_map* get_instance(void)
    {
        static train_list_map instance;
        return &instance;
    }


    /**@brief     更新车辆的 socket 列表, 确保每个车辆 ID 只对应一个最新的 IP
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @param[in]  int id      --- 指定的车辆ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_change(uint32_t cin_addr, int id);


    /**@brief	  车辆列表指定对象删除
	* @param[in]  int id --- 车辆ID
	* @return	  _train_map_opt_tab 操作结构
	* - TRAIN_SESSION_SUCESS	  ---  操作成功
	* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
	* - TRAIN_SESSION_EXCID	  ---  当前索引已存在
	* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
	*/
	_train_map_opt_tab train_list_map_delete(int id);


    /**@brief     车辆网络列表对象删除
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_delete(uint32_t cin_addr);


    /**@brief     车辆网络列表对象删除
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_delete(int dev_id);


    /**@brief     获取指定对象的网络通信IP
     * @param[in]  int id --- 指定的车辆ID
    * @return     网络通信套接字
    */
    uint32_t train_list_map_get_dev_addr(int id);


    /**@brief     获取指定对象的网络通信套接字
     * @param[in]  int id --- 指定的车辆ID
    * @return     网络通信套接字
    */
    int train_sock_list_get_socket_addr(int dev_id);


    /**@brief	  车辆列表指定对象数据更新(上行tick更新)
	* @param[in]  int id --- 车辆ID
	* @return	  NULL
	*/
	void train_list_map_update_upload_tick(int id);


    /**@brief	  车辆列表指定对象数据更新(车辆状态)
	* @param[in]  int id --- 车辆ID
	* @param[in]  dev_state_net state --- 通过网络上报的车辆状态数据
	* @return	  NULL
	*/
	void train_list_map_update_state(int id, dev_state_net state);

    /**@brief	  车辆列表指定对象数据更新(设备当前任务类型及状态)
	* @param[in]  int id --- 车辆ID
	* @return	  NULL
	*/
	void train_list_map_update_task_state(int id, task_st dev_task_state);


	/**@brief	  车辆列表指定对象数据更新(异常状态更新)
    * @param[in]  int devid --- 异常车辆ID, 包括车厢号
	* @param[in]  int exce_info --- 车辆异常状态数据
	* @return	  NULL
	*/
	void train_list_map_update_exce_info(uint8_t devid, int exce_info);

    /**@brief     车辆列表指定对象数据更新(下行tick更新)
    * @param[in]  int id --- 车辆ID
    * @param[in]  struct timespec tick --- 下行数据时间点
    * @return     NULL
    */
    void train_list_map_update_downlink_tick(int id);

    /**@brief     车辆列表指定对象数据更新(软硬件版本更新)
    * @param[in]  int id --- 车辆ID
    * @param[in] char *sw_version ---软件版本
    * @param[in] char *hw_version ---硬件版本
    * @return     NULL
    */
    void train_list_map_update_sw_hw_version(int id, char *sw_version, char *hw_version);

    /**@brief     车辆列表指定对象数据更新(车头故障码更新)
    * @param[in]  int id --- 车辆ID
    * @param[in]  uint32_t train_error_no ---车头故障码
    * @return     NULL
    */
    void train_list_map_update_train_error_no(int id, uint32_t train_error_no);

    //获取m_train_dev_list列表中的车辆信息数据
    int train_list_map_get_train_info(int id, swap_info &info);

    void train_list_map_update_exce_code(int dev_id, int exce_code, int exce_id);
    int train_list_map_get_exce_code(int dev_id, int *exce_code);

    //车辆列表指定对象数据更新(车辆重启标志)
    void train_list_map_update_train_restart_flag(int dev_id, bool flag);
    bool train_list_map_get_train_restart_flag(int dev_id);


    //获取m_train_dev_list列表中的车辆train_error_no
    uint32_t train_list_map_get_train_error_no(int id);


    /**@brief     车辆任务列表 任务数据存储
	* @param[in]  int dev_id --- 指定的车辆号
	* @param[in]  auto_exchange_task task --- 该车辆的任务
	* @return     NULL
	*/
	void train_task_list_push(int dev_id, auto_exchange_task task);


    /**@brief     车辆任务列表 任务数据存储
	* @param[in]  int dev_id --- 指定的车辆号
	* @param[in]  auto_exchange_task *task --- 该车辆的任务
	* @return     获取数据有效性
	* - true    ---  数据有效
	* - false   ---  数据无效
	*/
	bool swap_task_list_pop(int dev_id, auto_exchange_task *task);


    /**@brief     车辆任务列表非空查询
    * @param[in]  int dev_id --- 有任务的车列表
    * @param[in]  int dev_cnt --- 任务/车辆的数量
    * @return     当前任务队列深度
    */
    bool train_task_list_empty_state(int *dev_id, int *dev_cnt);


    /**@brief     创建指定车辆的logger对象，用来生成日志数据
    * @param[in]  int dev_id --- 指定的车辆ID
    * @return     NULL
    */
    void train_log_list_init_logger(int dev_id);


    /**@brief     获取当前车辆的spdlog执行的logger对象
    * @param[in]  int dev_id --- 指定的车辆ID
    * @return     对应的spdlog 的logger对象
    */
    std::shared_ptr<spdlog::logger> train_log_list_get_logger(int dev_id, bool *valid_flag);


	/**@brief	  获取指定车辆当前任务类型
	* @param[in]  int id --- 车辆ID
	* @return	  车辆任务信息
	*/
    _train_task_type train_list_map_get_task_info(int id);


    /**@brief	  车辆列表指定对象数据更新(当前任务更新)
	* @param[in]  int id --- 车辆ID
	* @param[in]  _train_task_type task_info --- 车辆任务状态
	* @return	  NULL
	*/
	void train_list_map_update_task_info(int id, _train_task_type task_info);


    /**@brief	  车辆列表指定对象数据重置(当前任务重置)
	* @param[in]  int id --- 车辆ID
	* @return	  NULL
	*/
	void train_list_map_reset_task_info(int id);


    /**@brief    检查软硬件版本号是否为空
    * @param[in]  int id --- 车辆ID
    * @return     true 不为空
    */
    bool train_list_map_check_version_empty(int id);


    /**@brief     车辆日志列表指定对象查找
    * @param[in]  int dev_id --- 设备ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_find(int dev_id);


    /**@brief	  车辆日志列表对象插入
        * @param[in]  int dev_id --- 设备ID
        * @return	  _train_map_opt_tab 操作结构
        * - TRAIN_SESSION_SUCESS	  ---  操作成功
        * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
        * - TRAIN_SESSION_EXCID	  ---  当前索引已存在
        * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_insert(int dev_id, std::shared_ptr<spdlog::logger> logger);


    /**@brief     车辆网络列表对象插入
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @param[in]  int id --- 该网络通信套接字对应的车辆ID信息
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_insert(uint32_t cin_addr, int id);

    /**@brief     车辆网络列表指定对象查找
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_find(uint32_t cin_addr);


    /**@brief     当前车辆列表所有对象显示(调试使用)
    * @param[in]  NULL
    * @return     NULL
    */
    void train_dev_list_display(void);


    /**@brief	  当前通信所有对象显示(调试使用)
	* @param[in]  NULL
	* @return	  NULL
	*/
	void train_sock_list_display(void);


    /**@brief     车辆列表数据插入
    * @param[in]  int id --- 车辆ID
    * @param[in]  swap_info dev_info --- 车辆信息数据结构体，具体内容见头文件
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_list_map_insert(int id, swap_info dev_info);


    /**@brief     车辆列表指定对象查找
    * @param[in]  int id --- 车辆ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_list_map_find(int id);



    _train_map_opt_tab train_list_platform_task_state_find(int platform_id);
    _train_map_opt_tab swap_list_task_state_find(int swap_id);

    _train_map_opt_tab swap_list_task_state_insert(int swap_id, auto_exchange_task_state m_swap_task_state);



    /**@brief     车辆任务列表对象初始化 - 清除设备ID对应的任务列表
    * @param[in]  int dev_id --- 指定的车辆号
    * @return     bool类型 操作结果
    * - true    ---  操作成功
    * - false  ---  操作失败
    */
    bool train_task_list_init(int dev_id);


    /**@brief     车辆列表指定对象数据获取(网络消息)
    * @param[in]  int id --- 车辆ID
    * @param[out]  dev_state_net *state --- 通过网络上报的车辆状态数据
    * @return     bool类型 操作结果
    * - true    ---  操作成功
    * - false  ---  操作失败    
    */
	bool train_list_map_get_train_state(int id, dev_state_net *state);

    bool swap_list_map_get_swap_task_state(int id, task_st *swap_task_state);

    // bool train_list_map_get_train_platform_task_state(int platform_id, train_task_state *platform_task_state);
    bool train_list_map_get_swap_task_state(int swap_id, auto_exchange_task_state *swap_task_state);

    /**@brief     车辆任务列表指定对象查找
	* @param[in]  int dev_id	 --- 指定的车辆号
	* @return     _train_map_opt_tab 操作结构
	* - TRAIN_SESSION_SUCESS    ---  操作成功
	* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
	* - TRAIN_SESSION_EXCID     ---  当前索引已存在
	* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
	*/
	_train_map_opt_tab train_task_list_find(int dev_id);


    /**@brief     车辆列表指定对象数据更新(车辆信息)
    * @param[in]  int id --- 车辆ID
    * @return     NULL
    */
    void train_list_map_update_comm_flag(int id, bool flag);

    /**@brief     车辆列表当前通信ACK确认完成标志查询
    * @param[out]  int *dev_id --- 通信未完成车辆列表
    * @param[out]  int *dev_cnt --- 通信未完成的车辆总数
    * @return     通信未完成车辆列表及车辆总数
    */
	bool train_list_map_get_dev_comm_state(int *dev_id, int *dev_cnt);


    /**@brief     获取通信ACK标志
    * @param[in]  int id --- 车辆ID
    * @return     NULL
    */
    bool train_list_map_get_comm_flag(int id);

    bool platform_list_map_get_comm_ack_flag(int platform_id);

    int get_swap_cmd_resend_count(int swap_id);
    void update_swap_cmd_resend_count(int swap_id, int count);

    /**@brief     下行通信序列号初始化 - m_swap_comm_sequ_list.insert
    * @param[in]  int swap_id --- 货箱ID
    * @return     NULL
    */
    void swap_list_map_insert_comm_sequeue(int swap_id);


    /**@brief     下行通信序列号更新
    * @param[in]  int platform_id --- 载货台ID
    * @return     NULL
    */
    void swap_list_map_update_comm_sequeue(int platform_id, uint32_t sequence);


    /**@brief     获取指定对象的最近一次通信的有效序列号, 上行sequence
    * @param[in]  int swap_id --- 货箱ID
    * @return     有效通信序列号
    */
    uint32_t swap_list_map_get_uplink_comm_sequeue(int swap_id);



    /**@brief	  获取指定对象的最近一次通信的有效序列号,下行sequence
	* @param[in]  int swap_id --- 货箱ID
	* @return	  有效通信序列号
	*/
	uint32_t swap_list_map_get_comm_sequeue(int swap_id);	

    /**@brief     货箱列表指定对象数据更新(上行sequence)
    * @param[in]  int id --- 货箱ID
    * @return     NULL
    */
    void swap_list_map_update_uplink_comm_squence(int swap_id, int seuq_cnt);

    /**@brief     车辆列表指定对象数据获取(网络数据)
    * @param[in]  int id --- 车辆ID
    * @param[in]  net_msg *msg --- 待发送的网络消息
    * @return     NULL
    */
    bool train_list_map_get_comm_msg(int id, net_msg *msg);


    /**@brief     车辆列表指定对象数据更新(网络数据)
    * @param[in]  int id --- 车辆ID
    * @param[in]  net_msg msg --- 通过网络上报的车辆状态数据
    * @return     NULL
    */
    void train_list_map_update_comm_msg(int id, net_msg msg);

    /**@brief	  获取指定对象的最新一次数据上行时刻时间信息
	* @param[in]  int id --- 车辆ID
	* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
	* @return	  函数执行结果，用来判断数据是否有效
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool train_list_map_get_upload_tick(int id, struct timespec *tic_out);



    /**@brief	  获取同当前指定的网络通信ip绑定的车辆ID
	* @param[in]  uint32_t cin_addr --- 指定的网络通信IP
	* @return	  车辆ID
	*/
	int train_sock_list_get_id(uint32_t cin_addr);

    
    /**@brief	  获取指定对象的最新一次数据下行时刻时间信息
	* @param[in]  int id --- 车辆ID
	* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
	* @return	  函数执行结果，用来判断数据是否有效
	* - false	  
	* - true	  
	*/
	bool train_list_map_get_download_tick(int id, struct timespec *tic_out);

    int devid_to_train_carriage(uint8_t id_temp, uint32_t *dev_id, uint32_t *carriage);

    void travese_all_pending_exception_for_swap(int id, int component_count, std::function<void (const except_info&)> f);


    /**@brief     车辆任务列表深度查询
	* @param[in]  int dev_id --- 指定的车辆号
	* @return     当前任务队列深度
	*/
	int train_task_list_size(int dev_id);


    /**@brief	  获取当前所有活跃车辆的ID信息
	* @param[out]  int *id_queue --- 车辆ID数据缓冲区
	* @return	  NULL
	*/
	void train_list_map_get_all_id(int *id_queue);



    /**@brief     车辆任务列表非空查询
	* @param[in]  int dev_id --- 指定的车辆号
	* @return     空满状态
	* - true    ---  空
	* - false   ---  非空
	*/
	bool train_task_list_empty(int dev_id);

    void train_task_list_clear(int dev_id);

    int exception_cnt_get(uint8_t devid, int component_num);
    int exception_occur(const except_info&);
    int exception_resume(int carriage_num, const except_info&);
    void exception_resume_all(int swap_id);


	/**@brief	  将pending_list中没有的异常插入到异常表中
    * @param[in]  uint8_t devid --- 异常车辆ID, 包括车厢号
	* @param[in]  const except_info& --- 车辆异常结构体指针
	* @return	  异常表中是否存在该异常
        * - 0    ---  存在
	    * - 1   ---  不存在
	*/
    int exception_occur(uint16_t devid, const except_info&);

    _train_map_opt_tab pending_excepts_find(int devid);

private:

    swap_comm_sequence_map m_swap_comm_sequ_list;     ///<下行sequence通信列表 <swap_id -> sequence>
    resend_count m_swap_cmd_resend_count;

    int m_task_id = 1;    //任务id号，下行通信，初始化为1，每次+1

    train_info_sock_map m_train_sock_list;              ///< 通信列表 <cin_addr -> dev_id>
    train_info_map m_train_dev_list;                    ///< 车辆列表 <dev_id -> swap_info>
    swap_task_info_map m_swap_task_state_list;          ///<任务列表 <dev_id -> auto_exchange_task_state>
    swap_task_map m_swap_task_list; 			        ///< 任务缓存列表 <dev_id -> blocking_queue<auto_exchange_task>>

    train_log_map m_train_log_list;                     ///< 车辆日志列表 <dev_id -> logger>

    std::mutex m_train_lot_mtx;	           		// 车辆日志互斥锁
    std::mutex m_train_dev_list_mtx;            //车辆列表互斥锁<dev_id -> swap_info>
    std::mutex m_train_dev_pos_mtx;             //车辆位置信息互斥锁<dev_id -> train_position_info>
    std::mutex m_train_dev_platform_task_state_mtx;      //车辆载货台任务状态互斥锁<dev_id -> m_train_task_state_list>
    std::mutex m_swap_dev_task_state_mtx;      //自动取换箱任务状态互斥锁<dev_id -> auto_exchange_task_state>
    std::mutex m_train_dev_comm_mtx;	        // 车辆列表互斥锁<id -> sequence>
    std::mutex swap_task_opt_mutex;
    std::mutex m_train_delete_mtx;	           	// 车辆列表删除互斥锁
    std::mutex m_train_dev_seq_mtx;	           // 下行通信序列号互斥锁
    std::mutex m_swap_dev_task_id_mtx;	           // 取换箱任务id互斥锁
    std::mutex cmd_resend_count_mutex;

    std::mutex lock_for_excepts;
    train_except_map pending_excepts;           ///< 异常信息列表 <dev_id -> except_info>

};


#endif