{"version": {"device_version": "gsbank", "mobile_shelf_version": 0, "connect_to_thing": 1}, "production_mode": {"mode": 0, "function_ip": "tcp://127.0.0.1:8110", "event_ip": "tcp://127.0.0.1:8111", "device_id": 1}, "container_reprort_mode": {"mode": 1, "con_seal_ip": "tcp://127.0.0.1:8112", "con_slot_ip": "tcp://127.0.0.1:8113"}, "scanner": {"id": 0}, "sdk_config": {"protocol": "mqtt_tls", "host_name": "127.0.0.1", "host_port": 1883, "device_id": "dtsk7nl3g8800", "ca_path": "/home/<USER>/auto_sort_high_efficient/cfg_file/data/cert/drl8cbt744400/ca.crt", "cert_path": "/home/<USER>/auto_sort_high_efficient/cfg_file/data/cert/drl8cbt744400/drl8cbt744400.crt", "key_path": "/home/<USER>/auto_sort_high_efficient/cfg_file/data/cert/drl8cbt744400/drl8cbt744400.key"}, "thing_model": {"id": "urn:user-spec-v1:thing-model:pc64e55f55821000:625a16fc4eccbfa6ad5e41fb99f9d447", "version": "V1.0.1"}, "time": {"report_sys_state": 20000, "report_vehicle_state_auto": 10000, "report_vehicle_state_manual": 2000, "issue_sys_state": 3000, "report_feeder_state": 10000, "report_switcher_state": 10000, "issue_slot_delay": 0, "report_feeder_belt_state": 1000}}