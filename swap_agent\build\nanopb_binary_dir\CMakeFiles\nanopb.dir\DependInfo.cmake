# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c" "/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o"
  "/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c" "/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o"
  "/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c" "/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/libs/x86/include"
  "/home/<USER>/myfile/project/auto_replace_box/swap_agent/.."
  "/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/pb/nanopb"
  "/home/<USER>/myfile/project/auto_replace_box/swap_agent/."
  "/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/libs/include"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
