#ifndef __JD_THINGTALK_TIME_H__
#define __JD_THINGTALK_TIME_H__
#include "jd_thingtalk_stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

typedef struct{
    uint32_t    year;         /* 年份，其值等于实际年份减去1900 */
    uint8_t     month;      /* 月份(从一月开始，0代一月)，取值区间为[0,11]*/
    uint8_t     week;         /*星期，取值区间为[0,6]，其中0代表星期天，1代表星期一 ，以此内推*/
    uint8_t     day;           /* 一个月中的日期，取值区间为[1,31]      */
    uint8_t     hour;        /* 时，取值区间为[0 ,23]        */
    uint8_t     minute;   /* 分，取值区间为 [0 ,59]        */
    uint8_t     second;   /* 秒，取值区间为[0,59]        */
    uint32_t    timestamp; /*秒，时间戳 */
} jd_thingtalk_time_t;

typedef struct {
    uint32_t second;
    uint32_t ms;
} jd_thingtalk_time_stamp_t;

/**
 * get time
 *
 * @out param: time
 * @return: UTC Second
 *
 */
uint32_t jd_thingtalk_pal_time(jd_thingtalk_time_t *jl_time);

/**
* @brief get timestamp(ms)
* @param none
* @return sucess or failed
*/
int32_t jd_thingtalk_pal_time_get_timestamp(jd_thingtalk_time_stamp_t *time_stamp);

/**
 * get os clock in milsecond
 *
 * @out param: none
 * @return: sys time ticks ms since sys start
*/
uint32_t jd_thingtalk_pal_time_clock_ms(void);

#ifdef __cplusplus
}
#endif

#endif
