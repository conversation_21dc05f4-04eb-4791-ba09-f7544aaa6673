#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
iostream
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
queue
-
climits
-
condition.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.cpp
../swap_agent_debug.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
condition.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
ctime
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
thp_mutex.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
memory
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.cpp
../swap_agent_debug.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
thp_mutex.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
string.h
-
stdint.h
-
unistd.h
-
errno.h
-
iostream
-
stdexcept
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
pthread.h
-
memory
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.cpp
../swap_agent_debug.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
blocking_queue.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
thread_pool.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
string.h
-
stdint.h
-
unistd.h
-
errno.h
-
iostream
-
stdexcept
-
algorithm
-
iostream
-
utility
-
vector
-
memory
-
functional
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
blocking_queue.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
vector
-
functional
-
memory
-

