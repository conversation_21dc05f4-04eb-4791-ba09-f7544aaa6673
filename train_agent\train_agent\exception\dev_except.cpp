#include "dev_except.hpp"

namespace dev_except
{
	enum except_attr
	{
		DEVEXCEPT_ATTR_RESUMABLE = 1,
		DEVEXCEPT_ATTR_SUBCODE_MEANS = 2,
		
		DEVEXCEPT_ATTR_SUBCODE_ATTR_BITS = (1<<16),
	};

	struct except_map
	{
		uint32_t errcode;		//设备上报异常码
		uint32_t subcode;

		except_info except;		//系统内定义的异常描述
		int attr;		
	};

	/*设备上报异常与系统内异常描述的对照表，用来实现异常相互转换*/
	static const except_map except_map_table[] =
	{	
		// 故障码	子码	异常源	异常等级	异常码	异常描述	异常子码	异常子码描述	异常状态
		// 严重故障
		{1, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_EMGE_FATAL, 0, "车头组急停触发", 0, 0, exception_state_STATE_OCCURED}, 0},
		{2, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_SOFT_EMGE_FATAL, 0, "车头组软急停触发", 0, 0, exception_state_STATE_OCCURED}, 0},
		{3, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_MOVE_SAFE_TRG_FATAL, 0, "车头组安全触边", 0, 0, exception_state_STATE_OCCURED}, 0},
		{4, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_MOVE_SERVO_FATAL, 0, "车头组行走伺服异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{5, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_MOVE_ZERO_FATAL, 0, "车头组寻零失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{6, EXCEPTION_ALL, {exception_src_CARRIAGE, exception_level_FATAL, TRAIN_LOAD_SERVO_FATAL, 0, "载货台伺服异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{7, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_PARA_NOT_CONFIG_FATAL, 0, "参数初始化异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{8, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_SERVO_INIT_X_FATAL, 0, "行走伺服初始化失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{9, EXCEPTION_ALL, {exception_src_CARRIAGE, exception_level_FATAL, TRAIN_SERVO_INIT_Y_FATAL, 0, "Y轴伺服初始化失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{10, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_FATAL, TRAIN_SERVO_INIT_Z_FATAL, 0, "z轴伺服初始化失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{11, EXCEPTION_ALL, {exception_src_CARRIAGE, exception_level_FATAL, TRAIN_ACT_TIMEOUT_Y_FATAL, 0, "Y轴执行动作超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{12, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_FATAL, TRAIN_ACT_TIMEOUT_Z_FATAL, 0, "z轴执行动作超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{13, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_FATAL, TRAIN_TIPPER_ZERO_FATAL, 0, "z轴电机回零异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{14, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_FATAL, TRAIN_TIPPER_ZERO_ERROR, 0, "翻斗寻零异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{15, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_FATAL, TRAIN_TIPPER_SERVO_ERROR, 0, "翻斗伺服异常", 0, 0, exception_state_STATE_OCCURED}, 0},


		{50, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_MOVE_MOTOR_IDLE_RUN_FATAL, 0, "行走电机空转", 0, 0, exception_state_STATE_OCCURED}, 0},
	

		{99, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_FATAL, TRAIN_SELF_RESET, 0, "车头组发生重启", 0, 0, exception_state_STATE_OCCURED}, 0},


	


		// 故障
		{100, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_ERROR, TRAIN_BELT_SERVO_ERROR, 0, "皮带伺服异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{101, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_ERROR, TRAIN_BELT_ZERO_ERROR, 0, "皮带寻零异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{102, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_ERROR, PARA_NOT_CONFIG_WARNING, 0, "使用默认参数故障", 0, 0, exception_state_STATE_OCCURED}, 0},
		{103, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_ERROR, BELT_DROP_TIMEOUT_FATAL, 0, "落包转动超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{104, EXCEPTION_ALL, {exception_src_PLATFORM, exception_level_ERROR, BELT_DROP_POS_WARNING_FATAL, 0, "落包位置异常", 0, 0, exception_state_STATE_OCCURED}, 0},


	



		//警告
		{200, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_WARNNING, TRAIN_MOV_SENSOR_CHECK_WARNING, 0, "行走传感器检测异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{201, EXCEPTION_ALL, {exception_src_TRAIN, exception_level_WARNNING, TRAIN_DOWN_HEARTBEAT_TIME_OUT_WARNING, 0, "下行心跳超时", 0, 0, exception_state_STATE_OCCURED}, 0},

		

	};

	//static bool comp(excpet_map a, )
	/*设备上报的异常码转为系统内定义的异常*/
	int errcode_to_exception(uint32_t err_code, uint32_t subcode, except_info &except)
	{
		for(auto const & e: except_map_table)
		{
			if ((e.errcode == err_code) && ((e.subcode == EXCEPTION_ALL) || (e.subcode == subcode)))
			{
				except = e.except;
				except.sub_code = subcode;

				// if(e.attr & DEVEXCEPT_ATTR_SUBCODE_MEANS)
				// {
				// 	strcat(except.description, std::to_string(subcode).c_str());
				// }
				return 0;
			}
		}
		
		return -1;
	}


	/*系统内定义的异常转为设备上报的异常码*/
	int exception_to_errcode(const except_info &except, uint32_t &err_code, uint32_t &subcode)
	{
		for(auto const & e: except_map_table)
		{
			if ((e.except.src == except.src) && (e.except.dev == except.dev) && (e.except.sub_dev == except.sub_dev) &&
				(e.except.code == except.code) && ((e.except.sub_code == except.sub_code) || (e.except.sub_code == EXCEPTION_ALL)))
			{
				err_code = e.errcode;
				subcode = except.sub_code;
				return 0;
			}
		}
		
		return -1;
	}
	
}