/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_TASK_PB_H_INCLUDED
#define PB_TASK_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _task_excep_inof {
    task_excep_inof_task_no_exception = 0,
    task_excep_inof_target_slot_full = 1,
    task_excep_inof_target_slot_seal = 2,
    task_excep_inof_task_vehicle_error = 3,
    task_excep_inof_task_munaul_reason = 4
} task_excep_inof;

typedef enum _sorting_task_state {
    sorting_task_state_INIT = 0,
    sorting_task_state_DISTRIBUTED = 1,
    sorting_task_state_TAKE_LOADING = 2,
    sorting_task_state_MOVING = 3,
    sorting_task_state_UNLOADING = 4,
    sorting_task_state_FINISHED = 5,
    sorting_task_state_FINISHED_TOHOSPICE = 6, /* 分到了收容口 */
    sorting_task_state_FINISHED_MANUALLY = 7, /* 手动完成 */
    sorting_task_state_SUSPEND = 8
} sorting_task_state;

/* Struct definitions */
typedef struct _volume {
    uint32_t length;
    uint32_t width;
    uint32_t height;
} volume;

typedef struct _sorting_task_msg {
    uint32_t sequence;
    uint32_t dev_id;
    /* 任务号， 举例：CQ20200217102058+随机数  字母（2位）+年月日十分秒（16位）+随机数 保证全局唯一即可 */
    pb_byte_t task_id[80];
    char gd_codes[513]; /* 商品码 */
    uint32_t container; /* 格口号 */
    bool task_valid; /* 任务有效信息字段 */
    bool has_vol;
    volume vol;
    uint32_t weight;
    uint32_t train_id;
    uint32_t platform_id;
    float platform_belt_speed;
    pb_size_t containers_count;
    uint32_t containers[4];
    uint32_t gd_codes_count;
    uint32_t scanner_id;
} sorting_task_msg;

typedef struct _sorting_task_state_msg {
    /* 任务号， 举例：CQ20200217102058+随机数  字母（2位）+年月日十分秒（16位）+随机数 保证全局唯一即可 */
    pb_byte_t task_id[80];
    char gd_codes[513]; /* 商品码 */
    uint32_t container; /* 格口号 */
    sorting_task_state state; /* 任务状态 */
    uint32_t train_id; /* 分播车车号 */
    uint32_t platform_id;
    uint32_t loop_cnt; /* 循环圈数 */
    uint32_t exp_info; /* 收容原因（任务异常信息) */
    task_excep_inof suspend_info;
} sorting_task_state_msg;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _task_excep_inof_MIN task_excep_inof_task_no_exception
#define _task_excep_inof_MAX task_excep_inof_task_munaul_reason
#define _task_excep_inof_ARRAYSIZE ((task_excep_inof)(task_excep_inof_task_munaul_reason+1))

#define _sorting_task_state_MIN sorting_task_state_INIT
#define _sorting_task_state_MAX sorting_task_state_SUSPEND
#define _sorting_task_state_ARRAYSIZE ((sorting_task_state)(sorting_task_state_SUSPEND+1))



#define sorting_task_state_msg_state_ENUMTYPE sorting_task_state
#define sorting_task_state_msg_suspend_info_ENUMTYPE task_excep_inof


/* Initializer values for message structs */
#define volume_init_default                      {0, 0, 0}
#define sorting_task_msg_init_default            {0, 0, {0}, "", 0, 0, false, volume_init_default, 0, 0, 0, 0, 0, {0, 0, 0, 0}, 0, 0}
#define sorting_task_state_msg_init_default      {{0}, "", 0, _sorting_task_state_MIN, 0, 0, 0, 0, _task_excep_inof_MIN}
#define volume_init_zero                         {0, 0, 0}
#define sorting_task_msg_init_zero               {0, 0, {0}, "", 0, 0, false, volume_init_zero, 0, 0, 0, 0, 0, {0, 0, 0, 0}, 0, 0}
#define sorting_task_state_msg_init_zero         {{0}, "", 0, _sorting_task_state_MIN, 0, 0, 0, 0, _task_excep_inof_MIN}

/* Field tags (for use in manual encoding/decoding) */
#define volume_length_tag                        1
#define volume_width_tag                         2
#define volume_height_tag                        3
#define sorting_task_msg_sequence_tag            1
#define sorting_task_msg_dev_id_tag              2
#define sorting_task_msg_task_id_tag             3
#define sorting_task_msg_gd_codes_tag            4
#define sorting_task_msg_container_tag           5
#define sorting_task_msg_task_valid_tag          6
#define sorting_task_msg_vol_tag                 7
#define sorting_task_msg_weight_tag              8
#define sorting_task_msg_train_id_tag            9
#define sorting_task_msg_platform_id_tag         10
#define sorting_task_msg_platform_belt_speed_tag 11
#define sorting_task_msg_containers_tag          12
#define sorting_task_msg_gd_codes_count_tag      13
#define sorting_task_msg_scanner_id_tag          14
#define sorting_task_state_msg_task_id_tag       1
#define sorting_task_state_msg_gd_codes_tag      2
#define sorting_task_state_msg_container_tag     3
#define sorting_task_state_msg_state_tag         4
#define sorting_task_state_msg_train_id_tag      5
#define sorting_task_state_msg_platform_id_tag   6
#define sorting_task_state_msg_loop_cnt_tag      7
#define sorting_task_state_msg_exp_info_tag      8
#define sorting_task_state_msg_suspend_info_tag  9

/* Struct field encoding specification for nanopb */
#define volume_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   length,            1) \
X(a, STATIC,   SINGULAR, UINT32,   width,             2) \
X(a, STATIC,   SINGULAR, UINT32,   height,            3)
#define volume_CALLBACK NULL
#define volume_DEFAULT NULL

#define sorting_task_msg_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            2) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           3) \
X(a, STATIC,   SINGULAR, STRING,   gd_codes,          4) \
X(a, STATIC,   SINGULAR, UINT32,   container,         5) \
X(a, STATIC,   SINGULAR, BOOL,     task_valid,        6) \
X(a, STATIC,   OPTIONAL, MESSAGE,  vol,               7) \
X(a, STATIC,   SINGULAR, UINT32,   weight,            8) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          9) \
X(a, STATIC,   SINGULAR, UINT32,   platform_id,      10) \
X(a, STATIC,   SINGULAR, FLOAT,    platform_belt_speed,  11) \
X(a, STATIC,   REPEATED, UINT32,   containers,       12) \
X(a, STATIC,   SINGULAR, UINT32,   gd_codes_count,   13) \
X(a, STATIC,   SINGULAR, UINT32,   scanner_id,       14)
#define sorting_task_msg_CALLBACK NULL
#define sorting_task_msg_DEFAULT NULL
#define sorting_task_msg_vol_MSGTYPE volume

#define sorting_task_state_msg_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           1) \
X(a, STATIC,   SINGULAR, STRING,   gd_codes,          2) \
X(a, STATIC,   SINGULAR, UINT32,   container,         3) \
X(a, STATIC,   SINGULAR, UENUM,    state,             4) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          5) \
X(a, STATIC,   SINGULAR, UINT32,   platform_id,       6) \
X(a, STATIC,   SINGULAR, UINT32,   loop_cnt,          7) \
X(a, STATIC,   SINGULAR, UINT32,   exp_info,          8) \
X(a, STATIC,   SINGULAR, UENUM,    suspend_info,      9)
#define sorting_task_state_msg_CALLBACK NULL
#define sorting_task_state_msg_DEFAULT NULL

extern const pb_msgdesc_t volume_msg;
extern const pb_msgdesc_t sorting_task_msg_msg;
extern const pb_msgdesc_t sorting_task_state_msg_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define volume_fields &volume_msg
#define sorting_task_msg_fields &sorting_task_msg_msg
#define sorting_task_state_msg_fields &sorting_task_state_msg_msg

/* Maximum encoded size of messages (where known) */
#define TASK_PB_H_MAX_SIZE                       sorting_task_msg_size
#define sorting_task_msg_size                    696
#define sorting_task_state_msg_size              631
#define volume_size                              18

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
