#include <fstream>      // std::ifstream
#include "setting.hpp"
#include <unistd.h>
#include <iostream>
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>
#include <iomanip>
#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/global_def.h"

int setting::load_setting(const char *file_name)
{
	int ret;

	settings.carriage_max_distance = 1000;

	SPDLOG_INFO("load setting from:{}", file_name);

	try
	{
		ret = load_thing_agent_setting(file_name);
	}
	catch (nlohmann::detail::exception &e)
	{
		SPDLOG_ERROR("json throw an error:{}", e.what());	
		ret = 0;
	}

	return ret;
}

int setting::load_thing_agent_setting(const char *file_name)
{
    json root;
	
	std::ifstream setting_file(file_name, std::ifstream::in);
	if (!setting_file.is_open())
	{
		SPDLOG_ERROR("Error opening file");
		return 0;
	}
	setting_file >> root;

	settings.sdk_config_host_name = root["sdk_config"]["host_name"];

	settings.sdk_config_host_port = root["sdk_config"]["host_port"];
	settings.sdk_config_device_id = root["sdk_config"]["device_id"];
	settings.sdk_config_ca_path = root["sdk_config"]["ca_path"];
	settings.sdk_config_cert_path = root["sdk_config"]["cert_path"];
	settings.sdk_config_key_path = root["sdk_config"]["key_path"];

	if (root.count("production_mode"))
	{
		settings.production_mode = root["production_mode"]["mode"];
		if (settings.production_mode == 1)
		{
			settings.wcs_event_ip = root["production_mode"]["event_ip"];
			settings.wcs_function_ip = root["production_mode"]["function_ip"];
			settings.device_id = root["production_mode"]["device_id"];
		}
	}
	else
	{
		settings.production_mode = 0;
	}
	if (root.count("container_reprort_mode"))
	{
		settings.container_reprort_mode = root["container_reprort_mode"]["mode"];
		if (settings.container_reprort_mode == 1)
		{
			settings.con_seal_ip = root["container_reprort_mode"]["con_seal_ip"];
			settings.con_slot_ip = root["container_reprort_mode"]["con_slot_ip"];
		}
	}
	else
	{
		settings.container_reprort_mode = 0;
	}
	settings.thing_model_id = root["thing_model"]["id"];
	settings.thing_model_version = root["thing_model"]["version"];

	settings.time_report_sys_state = root["time"]["report_sys_state"];
	settings.time_report_vehicle_state_auto = root["time"]["report_vehicle_state_auto"];
	settings.time_report_vehicle_state_manual = root["time"]["report_vehicle_state_manual"];
	settings.time_report_feeder_state = root["time"]["report_feeder_state"];
    settings.time_report_switcher_state = root["time"]["report_switcher_state"];
	settings.time_report_feeder_belt_state = root["time"]["report_feeder_belt_state"];
	settings.time_issue_sys_state = root["time"]["issue_sys_state"];
	if(root["time"].count("issue_slot_delay") != 0)
		settings.time_issue_slot_delay = root["time"]["issue_slot_delay"];
	else
		settings.time_issue_slot_delay = 0;

	if (root["version"].count("device_version") != 0)
		settings.device_version = root["version"]["device_version"];
	else
		settings.device_version = "";
	settings.mobile_shelf_version = root["version"]["mobile_shelf_version"];
	settings.connect_to_thing = root["version"]["connect_to_thing"];
	if (root["version"].count("need_report_query_result") != 0)
		settings.need_report_query_result = root["version"]["need_report_query_result"];
	else
		settings.need_report_query_result = true;

	if (root["version"].count("need_report_seal_state") != 0)
		settings.need_report_seal_state = root["version"]["need_report_seal_state"];
	else
		settings.need_report_seal_state = false;

	if (root["sdk_config"].count("protocol") != 0)
		settings.connect_protocol = root["sdk_config"]["protocol"];
	else
		settings.connect_protocol = "mqtt_tls";

	if (root["scanner"].count("id") != 0)
		settings.scanner_id = root["scanner"]["id"];
	else
		settings.scanner_id = 0;

    return 1;
}

int setting::load_thing_agent_connect_setting()
{
	int ret;

	std::string file_name;

	file_name = "/home/<USER>/auto_sort_high_efficient/cfg_file/device.json";
	SPDLOG_INFO("load thing connect setting from:{}", file_name);

	try
	{
		json root;

		std::ifstream setting_file(file_name.c_str(), std::ifstream::in);
		if (!setting_file.is_open())
		{
			SPDLOG_ERROR("Error opening file");
			ret = 0;
			return ret;	
		}
		setting_file >> root;

		settings.connect_to_thing = root["version"]["connect_to_thing"];
		SPDLOG_INFO("load thing connect setting value:{}", settings.connect_to_thing);
		ret = 1;
	}
	catch (nlohmann::detail::exception &e)
	{
		SPDLOG_ERROR("json throw an error:{}", e.what());	
		ret = 0;
	}

	return ret;	
}

int setting::save_thing_agent_connect_setting(int param_value)
{
	//int ret;
	std::string file_name;
	std::string pos_file_name;

	uint32_t value;
	if(param_value == 1)
		value = 0;
	else 
		value= 1;

	pos_file_name = "/home/<USER>/auto_sort_high_efficient/cfg_file";
	nlohmann::json j_pos;
	std::ifstream in(pos_file_name.c_str());
    nlohmann::json file = nlohmann::json::parse(in);
	j_pos = file;

	j_pos["version"]["connect_to_thing"] = value;
		SPDLOG_INFO("set device.jason connect to thing :{}",value);

	std::ofstream out(pos_file_name.c_str());
			SPDLOG_INFO("[11111111111111111111111" );
	out<<std::setw(4)<<j_pos;
	in.close();
			SPDLOG_INFO("[11111111111111111111111" );

	out.close();

	return 1;	
}

bool setting::thing_manager_get_data_map_info() 
{
	data_map map;

	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;

	zmq::message_t zmq_reply;
	pb_istream_t stream_in;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	if( !socket.connected() )
	{
		SPDLOG_ERROR("m_data_requester is unconnected");
		return false;
	}
	
	strncpy(request.key, DATA_KEY_MAP, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("map"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
		
	socket.recv(zmq_reply, zmq::recv_flags::none);
	
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get data from data_map.");
		return false;
	}

	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, data_map_fields, &map))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("map"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	settings.carriage_max_distance = (map.tunnel_height + 500);

	return true;
}
