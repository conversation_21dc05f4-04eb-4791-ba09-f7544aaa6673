
#include <string>
#include <functional>
#include <list>
#include <array>
#include <thread>
#include <mutex>

#include "share/pb/idl/exception.pb.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

class event_vector 
{
public:
	enum
	{
		/*通配符, 对于event_pattern的任意字段，取此值时，表示可以匹配任意值。
		* 具体可以参看match函数的实现
		*/
		EVENT_WILDCARD = 0xFFFFFFFF,
	};

	/*异常向量表通过该数据结构描述一条异常向量可以处理(匹配)什么异常*/
	struct event_pattern
	{
		uint32_t src;
		uint32_t dev;
		uint32_t sub_dev;
		uint32_t code;
		uint32_t sub_code;
	};

	static const event_pattern EVT_PATTERN_ALL;

	struct event_handler 
	{
		//lower value with higher priority
		int priority;

		event_pattern e;
		std::function<int (const event_exception &e)> func; 
	};

private:

	//按照优先级逆序排列，高优先级在前
	std::list<event_handler> 	evt_handler_list;

	std::mutex 					list_lock;		//锁用来保护evt_handler_list的访问

public:

	/*事件/异常发生时，调用此接口将异常与异常向量表中的条目逐项匹配。
	* 返回true, 则对应项处理函数会被调用。
	*/
	static bool match(const event_exception &e, const event_pattern &pattern);

	event_vector(void) { };

	event_vector(const event_handler *handlers, size_t n);

	~event_vector();

	int add_handler(const event_handler &handler);

	/*匹配对应handler并调用处理函数, 一个异常可能匹配多个异常处理向量,
	* 那么对应的处理函数都会被调用。
	*/
	int process_event(const event_exception &evt_except);
};


