# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 nanopb_binary_dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 nanopb_binary_dir/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 nanopb_binary_dir/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 nanopb_binary_dir/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
nanopb_binary_dir/CMakeFiles/nanopb.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 nanopb_binary_dir/CMakeFiles/nanopb.dir/rule
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/rule

# Convenience name for target.
nanopb: nanopb_binary_dir/CMakeFiles/nanopb.dir/rule

.PHONY : nanopb

# fast build rule for target.
nanopb/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/build
.PHONY : nanopb/fast

pb_common.o: pb_common.c.o

.PHONY : pb_common.o

# target to build an object file
pb_common.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o
.PHONY : pb_common.c.o

pb_common.i: pb_common.c.i

.PHONY : pb_common.i

# target to preprocess a source file
pb_common.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.i
.PHONY : pb_common.c.i

pb_common.s: pb_common.c.s

.PHONY : pb_common.s

# target to generate assembly for a file
pb_common.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.s
.PHONY : pb_common.c.s

pb_decode.o: pb_decode.c.o

.PHONY : pb_decode.o

# target to build an object file
pb_decode.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o
.PHONY : pb_decode.c.o

pb_decode.i: pb_decode.c.i

.PHONY : pb_decode.i

# target to preprocess a source file
pb_decode.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.i
.PHONY : pb_decode.c.i

pb_decode.s: pb_decode.c.s

.PHONY : pb_decode.s

# target to generate assembly for a file
pb_decode.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.s
.PHONY : pb_decode.c.s

pb_encode.o: pb_encode.c.o

.PHONY : pb_encode.o

# target to build an object file
pb_encode.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o
.PHONY : pb_encode.c.o

pb_encode.i: pb_encode.c.i

.PHONY : pb_encode.i

# target to preprocess a source file
pb_encode.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.i
.PHONY : pb_encode.c.i

pb_encode.s: pb_encode.c.s

.PHONY : pb_encode.s

# target to generate assembly for a file
pb_encode.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.s
.PHONY : pb_encode.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... nanopb"
	@echo "... pb_common.o"
	@echo "... pb_common.i"
	@echo "... pb_common.s"
	@echo "... pb_decode.o"
	@echo "... pb_decode.i"
	@echo "... pb_decode.s"
	@echo "... pb_encode.o"
	@echo "... pb_encode.i"
	@echo "... pb_encode.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

