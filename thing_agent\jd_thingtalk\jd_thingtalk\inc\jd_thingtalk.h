/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 头文件，提供一些通用的定义
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */
#ifndef __JD_THINGTALK_H__
#define __JD_THINGTALK_H__

#ifdef __cplusplus
extern "C"{
#endif /* __cplusplus */

/**
 * @brief  返回值定义
 */
typedef enum {
    JD_THINGTALK_RET_SUCCESS   = 0,
    JD_THINGTALK_RET_FAILED    = -1,
} JDThingTalkRetCode_E;
typedef int JDThingTalkRetCode_t;


#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __JD_THINGTALK_H__ */
