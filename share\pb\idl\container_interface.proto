syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";
import "sys_interface.proto";

enum box_state
{
    BIND = 0;
	UNBIND = 1;
	ERROR = 2;
}

message box_info_single
{
    uint32 box_id = 1;
    string RFID = 2 [(nanopb).max_length = 32];
    box_state box_st = 3;
}

message box_info_multiple
{
	repeated box_info_single boxes = 1 [(nanopb).max_count = 1000];
}

enum container_seal_state
{
    IDLE = 0;
	SEAL = 1;
	CONTAIN = 2;
	UNKNOWN = 3;
}

message container_seal_state_single
{
	uint32 container_id = 1;
	container_seal_state seal_state = 2;
}

message led_info
{
    uint32 id = 1;
    uint32 color = 2;
    uint32 flash_freq = 3;
}

message container_seal_cmd
{
	uint32 container_id = 1;
}


message container_agent_state
{
	component_state state = 1;
}

message led_info_multiple
{
	repeated led_info led_state = 1 [(nanopb).max_count = 1000];
}


enum state
{
	ABSENT = 0;
	FULL = 1;
	NORMAL = 2;
	RASTER_TRIGGERED = 3;
}

message slot_state
{
	uint32 id = 1;
	state st = 2;
}


enum shelves_cmd_tab
{
    RESERVE = 0;
	LOCK = 1;
	UNLOCK = 2;
}

message shelves_cmd
{
    uint32 shelves_grout = 1;
    shelves_cmd_tab cmd = 2;
}

message shelves_state
{
    uint32 shelves_group = 1;
    shelves_cmd_tab state = 2;
}