#include "jd_thingtalk_stdio.h"
#include "jd_thingtalk_mqtt.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"
#include "jd_thingtalk_thread.h"

// MQTT LOG
#define log_mqtt(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_DEBUG){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[1;32m[DEBUG][%s] " format "\r\n", time_format, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)


#define __MQTT_MOSQUITTO_PAL__

/**
 * @brief  mqtt 客户端用户数据定义
 *
 */
typedef struct {
    int (*on_connect)(jd_thingtalk_mqtt_t, void *);
    int (*on_disconnect)(jd_thingtalk_mqtt_t, void *);
    int (*on_message)(jd_thingtalk_mqtt_t, jd_thingtalk_mqtt_msg_t *, void *);
    int (*on_subscribe)(jd_thingtalk_mqtt_t, int32_t, void *);
    int (*on_publish)(jd_thingtalk_mqtt_t, int32_t, void *);
    void *user_data;
#ifdef __MQTT_MOSQUITTO_PAL__
    bool is_started;
    int run;
#endif
} pal_mqtt_data_t;

#ifdef __MQTT_MOSQUITTO_PAL__
#include <stdio.h>
#include <sys/time.h>
#include <time.h>

#include "mosquitto.h"
#include "mosquitto_internal.h"
#include "mqtt_protocol.h"

// static variable for init mosquitto once
static int mosq_lib_init = 0;

// call back on mosquitto connect 
static void mosq_connect_callback(struct mosquitto *mosq, void *obj, int result, int flags, const mosquitto_property *properties)
{
    log_debug("Enter mosq_connect_callback");
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        if (mqtt_data->on_connect != NULL) {
            mqtt_data->on_connect(mosq, mqtt_data->user_data);
        }
    }
    log_debug("Leave mosq_connect_callback");
}

static void mosq_disconnect_callback(struct mosquitto *mosq, void *obj, int flags, const mosquitto_property *properties)
{
    log_mqtt("Client %s Callback DISCONNECT (Reason Code = %d)", mosq->id, flags);
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        if (mqtt_data->on_disconnect != NULL) {
            mqtt_data->on_disconnect(mosq, mqtt_data->user_data);
        }
    }
    log_debug("Leave mosq_disconnect_callback");
}

// call back on mosquitto message recieved
static void mosq_message_callback(struct mosquitto *mosq, void *obj, const struct mosquitto_message *message, const mosquitto_property *properties)
{
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    jd_thingtalk_mqtt_msg_t mqtt_msg;
    memset(&mqtt_msg, 0, sizeof(jd_thingtalk_mqtt_msg_t));
    if (mqtt_data != NULL) {
        if (mqtt_data->on_message != NULL) {
            mqtt_msg.topic = message->topic;
            mqtt_msg.payload = message->payload;
            mqtt_msg.qos = message->qos;
            mqtt_msg.mid = message->mid;
            mqtt_data->on_message(mosq, &mqtt_msg, mqtt_data->user_data);
        }
    }
}

// call back on mosquitto log print
static void mosq_log_callback(struct mosquitto *mosq, void *obj, int level, const char *str)
{
	UNUSED(mosq);
	UNUSED(obj);
	UNUSED(level);

	log_mqtt("%s", str);
}

// call back on subscribed
static void mosq_subscribe_callback(struct mosquitto *mosq, void *obj, int mid, int qos_count, const int *granted_qos)
{
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        if (mqtt_data->on_subscribe != NULL) {
            mqtt_data->on_subscribe(mosq, mid, mqtt_data->user_data);
        }
    }
}

// call back on publish
void mosq_publish_callback(struct mosquitto *mosq, void *obj, int mid, int reason_code, const mosquitto_property *properties)
{
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        if (mqtt_data->on_publish != NULL) {
            mqtt_data->on_publish(mosq, mid, mqtt_data->user_data);
        }
    }
}

/**
 * @brief  mosquitto 客户端主循环
 *
 */
static int mosq_client_task_loop_yield(struct mosquitto *mosq, int timeout, int max_packets, int *run)
{
	int rc;
    struct timeval delay;
    unsigned long reconnect_delay = 0;
    unsigned int reconnects = 0;

    // while(run){
        do{
            rc = mosquitto_loop(mosq, timeout, max_packets);
            if (reconnects !=0 && rc == MOSQ_ERR_SUCCESS){
                reconnects = 0;
            }

            //FIXME return for one step
            if (rc == MOSQ_ERR_SUCCESS) {
                return rc;
            }

        }while((*run) && rc == MOSQ_ERR_SUCCESS);
        /* Quit after fatal errors. */
        switch(rc){
            case MOSQ_ERR_NOMEM:
            case MOSQ_ERR_PROTOCOL:
            case MOSQ_ERR_INVAL:
            case MOSQ_ERR_NOT_FOUND:
            case MOSQ_ERR_TLS:
            case MOSQ_ERR_PAYLOAD_SIZE:
            case MOSQ_ERR_NOT_SUPPORTED:
            case MOSQ_ERR_AUTH:
            case MOSQ_ERR_ACL_DENIED:
            case MOSQ_ERR_UNKNOWN:
            case MOSQ_ERR_EAI:
            case MOSQ_ERR_PROXY:
                printf("Mosquitto Encounter Fatal Error:%d\n", rc);
                //return rc;
            case MOSQ_ERR_ERRNO:
                printf("Mosquitto Encounter MOSQ_ERR_ERRNO:%d, Error Number:%d\n", rc, errno);
                //break;
        }
        if(errno == EPROTO){
            printf("Mosquitto Quit Cause of Error Number: EPROTO\n");
            //return rc;
        }
        do{
            rc = MOSQ_ERR_SUCCESS;
            pthread_mutex_lock(&mosq->state_mutex);
            if(mosq->state == mosq_cs_disconnecting){
                *run = 0;
                pthread_mutex_unlock(&mosq->state_mutex);
            }else{
                pthread_mutex_unlock(&mosq->state_mutex);

                if(mosq->reconnect_delay_max > mosq->reconnect_delay){
                    if(mosq->reconnect_exponential_backoff){
                        reconnect_delay = mosq->reconnect_delay*(reconnects+1)*(reconnects+1);
                    }else{
                        reconnect_delay = mosq->reconnect_delay*(reconnects+1);
                    }
                }else{
                    reconnect_delay = mosq->reconnect_delay;
                }

                if(reconnect_delay > mosq->reconnect_delay_max){
                    reconnect_delay = mosq->reconnect_delay_max;
                }else{
                    reconnects++;
                }
 
                // reconnect
                printf("MQTT Reconnect 5 Seconds Later, Time Now %d", (unsigned int)time(NULL));
                jd_thingtalk_pal_msleep(1000);
/*
                req.tv_sec = reconnect_delay;
                req.tv_nsec = 0;
                while(nanosleep(&req, &rem) == -1 && errno == EINTR){
                req = rem;
                }
*/
                delay.tv_sec = 2;//reconnect_delay;
                delay.tv_usec = 0;
                //select(0, NULL, NULL, NULL, &delay);

                pthread_mutex_lock(&mosq->state_mutex);
                if(mosq->state == mosq_cs_disconnecting){
                    *run = 0;
                    pthread_mutex_unlock(&mosq->state_mutex);
                }else{
                    pthread_mutex_unlock(&mosq->state_mutex);
                    rc = mosquitto_reconnect(mosq);
                }
            }
        }while((*run) && rc != MOSQ_ERR_SUCCESS);
    //}
    return rc;
}

#endif // __MQTT_MOSQUITTO_PAL__

/**
 * @brief   创建一个MQTT客户端
 *
 * @param[in] config: MQTT的设置参指针
 * @return 
 	MQTT客户端句柄
 * @see None.
 * @note None.
 */
jd_thingtalk_mqtt_t jd_thingtalk_pal_mqtt_create(jd_thingtalk_mqtt_config_t *config)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *my_mosq = NULL;

    // TODO for mosquitto multi client should be init once
    if (mosq_lib_init == 0) {
        mosquitto_lib_init();
        mosq_lib_init = 1;
    }

    // create mosquitto
    my_mosq = mosquitto_new(config->clientId, true, NULL);
    if(!my_mosq){
        return NULL;
    }
    
    // set options mqtt versions
    mosquitto_int_option(my_mosq, MOSQ_OPT_PROTOCOL_VERSION, MQTT_PROTOCOL_V311);

    // set options username and password
    if ((config->username != NULL) || (config->password != NULL)) {
        mosquitto_username_pw_set(my_mosq, config->username, config->password);
    }

    // set options tls
    if (!strcmp(config->protocol, "mqtt_tls")) {
        mosquitto_tls_set(my_mosq, config->cafile, NULL, config->cert, config->key, NULL);
    }

    // set options insecure
    if (config->insecure) {
        mosquitto_tls_insecure_set(my_mosq, true);
    }

    // set options max message inflight
    mosquitto_max_inflight_messages_set(my_mosq, 20);

    mosquitto_log_callback_set(my_mosq, mosq_log_callback);
    mosquitto_connect_v5_callback_set(my_mosq, mosq_connect_callback);
    mosquitto_disconnect_v5_callback_set(my_mosq, mosq_disconnect_callback);
    mosquitto_message_v5_callback_set(my_mosq, mosq_message_callback);

    mosquitto_subscribe_callback_set(my_mosq, mosq_subscribe_callback);
    mosquitto_publish_v5_callback_set(my_mosq, mosq_publish_callback);

    pal_mqtt_data_t *mqtt_data;
    mqtt_data = (pal_mqtt_data_t *) malloc(sizeof(pal_mqtt_data_t));
    memset(mqtt_data, 0, sizeof(pal_mqtt_data_t));
    mosquitto_user_data_set(my_mosq, (void *) mqtt_data);

    return (jd_thingtalk_mqtt_t) my_mosq;

#else
    return NULL;
#endif
}

/**
 * @brief   销毁一个MQTT客户端
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_destory(jd_thingtalk_mqtt_t mqtt_handler)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *my_mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data;
    
    // TODO for mosquitto if multi instance should be cleanup once
    mosquitto_lib_cleanup();

    if (my_mosq) {
        mqtt_data = (pal_mqtt_data_t *) mosquitto_userdata(my_mosq);
        if (mqtt_data != NULL) {
            free(mqtt_data);
            mosquitto_user_data_set(my_mosq, (void *)NULL);
        }
        mosquitto_destroy(my_mosq);
        mqtt_handler = NULL;
    }
    return 0;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端连接到broker
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cfg: MQTT 配置参数指针
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_connect(jd_thingtalk_mqtt_t mqtt_handler, jd_thingtalk_mqtt_config_t *cfg)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;

    int rc;
    rc = mosquitto_connect_bind_v5(mosq, cfg->hostname, cfg->port, 60, NULL, NULL);
    if(rc>0){
        if(rc == MOSQ_ERR_ERRNO){
            printf("Error: mosquitto_connect_bind_v5:%d, %d\n", MOSQ_ERR_ERRNO, errno);
        } else {
            printf("Unable to connect (%s).\n", mosquitto_strerror(rc));
        }
        mosquitto_lib_cleanup();
        return rc;
    }
    return 0;
#else
    return -1;
#endif
}

/**
 * @brief   MQTT客户端断开连接
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_disconnect(jd_thingtalk_mqtt_t mqtt_handler)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    return mosquitto_disconnect(mosq);
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端运单步行执行函数，会循环调用
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] timeout_ms: 运行超时时间，单位：毫秒
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_yield(jd_thingtalk_mqtt_t mqtt_handler, int32_t timeout_ms)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data == NULL) {
        return -1;
    }
    if (!mqtt_data->is_started) {
        mqtt_data->is_started = true;
        mqtt_data->run = 1;
        if(mosq->state == mosq_cs_connect_async){
            mosquitto_reconnect(mosq);
        }
    }
    return mosq_client_task_loop_yield(mosq, timeout_ms, 1, &(mqtt_data->run));
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 订阅消息主题函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] topic: 订阅主题字符串的指针
 * @param[in] qos: 对应主题的消息服务等级
 * @param[in] mid: 对应MQTT的消息id
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_subscribe(jd_thingtalk_mqtt_t mqtt_handler, char *topic, int32_t qos, int32_t *mid)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    return mosquitto_subscribe(mosq, mid, topic, qos);
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 消息发布函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_publish(jd_thingtalk_mqtt_t mqtt_handler, jd_thingtalk_mqtt_msg_t *message)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    mosquitto_publish(mosq, &message->mid,
                        message->topic, 
                        strlen(message->payload), 
                        message->payload,
                        message->qos,
                        true);
    return 0;
#else
    return -1;
#endif
}

/**
 * @brief   MQTT客户端 设置 连接成功回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_connect: MQTT 连接成功后需要调用的函数句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_connect(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_connect)(jd_thingtalk_mqtt_t ,void *), void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        mqtt_data->on_connect = cb_connect;
        if (mqtt_data->user_data == NULL) mqtt_data->user_data = user_data;
    }
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 设置 连接断开回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_disconnect: MQTT 连接断开后需要调用的函数句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_disconnect(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_disconnect)(jd_thingtalk_mqtt_t, void *), void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        mqtt_data->on_disconnect = cb_disconnect;
        if (mqtt_data->user_data == NULL) mqtt_data->user_data = user_data;
    }
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 设置 收到消息后的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_message: MQTT 收到消息后需要调用的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_message(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_message)(jd_thingtalk_mqtt_t, jd_thingtalk_mqtt_msg_t *, void *), void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        mqtt_data->on_message = cb_message;
        if (mqtt_data->user_data == NULL) mqtt_data->user_data = user_data;
    }
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 设置 订阅ACK的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_subscribe: MQTT 收到订阅ACK后的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_subscribe(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_subscribe)(jd_thingtalk_mqtt_t, int32_t, void *), void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        mqtt_data->on_subscribe = cb_subscribe;
        if (mqtt_data->user_data == NULL) mqtt_data->user_data = user_data;
    }
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 设置 消息发布ACk的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_publish: MQTT 收到消息发送ACK后的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_publish(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_publish)(jd_thingtalk_mqtt_t, int32_t, void *), void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) {
        mqtt_data->on_publish = cb_publish;
        if (mqtt_data->user_data == NULL) mqtt_data->user_data = user_data;
    }
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 设置用户数据
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_user_data(jd_thingtalk_mqtt_t mqtt_handler, void *user_data)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) mqtt_data->user_data = user_data;
    else return -1;
#else
    return 0;
#endif
}

/**
 * @brief   MQTT客户端 获取用户数据
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 用户数据指针
 * @see None.
 * @note None.
 */
void *jd_thingtalk_pal_mqtt_get_user_data(jd_thingtalk_mqtt_t mqtt_handler)
{
#ifdef __MQTT_MOSQUITTO_PAL__
    struct mosquitto *mosq = (struct mosquitto *)mqtt_handler;
    pal_mqtt_data_t *mqtt_data = mosquitto_userdata(mosq);
    if (mqtt_data != NULL) return mqtt_data->user_data;
    else return NULL;
#else
    return NULL;
#endif
}

// end of file
