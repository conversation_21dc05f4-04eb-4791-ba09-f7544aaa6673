# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include swap_manage/CMakeFiles/lib_swap_manage.dir/depend.make

# Include the progress variables for this target.
include swap_manage/CMakeFiles/lib_swap_manage.dir/progress.make

# Include the compile flags for this target's objects.
include swap_manage/CMakeFiles/lib_swap_manage.dir/flags.make

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o: swap_manage/CMakeFiles/lib_swap_manage.dir/flags.make
swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o: ../swap_manage/swap_list.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_swap_manage.dir/swap_list.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp > CMakeFiles/lib_swap_manage.dir/swap_list.cpp.i

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_swap_manage.dir/swap_list.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp -o CMakeFiles/lib_swap_manage.dir/swap_list.cpp.s

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.requires:

.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.requires

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.provides: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.requires
	$(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.provides.build
.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.provides

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.provides.build: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o


swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o: swap_manage/CMakeFiles/lib_swap_manage.dir/flags.make
swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o: ../swap_manage/swap_manage.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp > CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.i

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp -o CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.s

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.requires:

.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.requires

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.provides: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.requires
	$(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.provides.build
.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.provides

swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.provides.build: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o


# Object files for target lib_swap_manage
lib_swap_manage_OBJECTS = \
"CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o" \
"CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o"

# External object files for target lib_swap_manage
lib_swap_manage_EXTERNAL_OBJECTS =

swap_manage/liblib_swap_manage.a: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o
swap_manage/liblib_swap_manage.a: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o
swap_manage/liblib_swap_manage.a: swap_manage/CMakeFiles/lib_swap_manage.dir/build.make
swap_manage/liblib_swap_manage.a: swap_manage/CMakeFiles/lib_swap_manage.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library liblib_swap_manage.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && $(CMAKE_COMMAND) -P CMakeFiles/lib_swap_manage.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lib_swap_manage.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
swap_manage/CMakeFiles/lib_swap_manage.dir/build: swap_manage/liblib_swap_manage.a

.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/build

swap_manage/CMakeFiles/lib_swap_manage.dir/requires: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o.requires
swap_manage/CMakeFiles/lib_swap_manage.dir/requires: swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o.requires

.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/requires

swap_manage/CMakeFiles/lib_swap_manage.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage && $(CMAKE_COMMAND) -P CMakeFiles/lib_swap_manage.dir/cmake_clean.cmake
.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/clean

swap_manage/CMakeFiles/lib_swap_manage.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage/CMakeFiles/lib_swap_manage.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/depend

