#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/task.pb.h"
// #include "share/pb/idl/feeder_goods.pb.h"
#include "share/global_def.h"
#include "setting/setting.hpp"

// #include "share/pb/idl/sys_cmd.pb.h"

#include "ipc_interface/task_interface.hpp"
#include "ipc_interface/sys_state_interface.hpp"


#define DEFAULT_SCANER_NO           ("0")

#define HOSPICE_REASON_OVERLENGTH   6
#define HOSPICE_REASON_OVERHEIGHT   7
#define HOSPICE_REASON_OVERWEIGHT   12
#define HOSPICE_REASON_NOREAD       10

class task_manager
{
public:

    class timer
    {
    private:
        std::chrono::high_resolution_clock::time_point start_time;   //定时查询用
        std::chrono::high_resolution_clock::time_point end_time;

    public:
        void start()
        {
            start_time = std::chrono::high_resolution_clock::now();
        }

        uint32_t execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

    enum device_task_state
    {
        IDLE = 0,           //初始值
        INIT = 1,           //从供包台获取到，还未上报物控
        REPORTED = 2,       //上报给物控，物控还未下发正确格口
        OBTAINED = 3,       //物控下发了正确格口的任务，还未下发给供包台，目前未使用
        ISSUED = 4,         //已经下发给供包台的
    };

    struct device_task
    {
        uint32_t feeder_id;
        std::string task_id;
        device_task_state state;
        timer task_timer;

        device_task(uint32_t id)
        {
            feeder_id = id;
            state = IDLE;
        }
    };

    enum task_type
    {
        NORMAL_SORTING = 0,
        DISCLOSED_SORTING = 1,
        SIMULATIVE_SORTING = 2
    };

    struct sorting_task         //下发的分播任务
    {
        std::string grid_no;
        std::string task_no;
        task_type type;
    };

    struct barcode_task_info         //上报扫码信息
    {
        std::string feeder_no;
        std::string scaner_no;
        std::string task_no;
        std::vector<std::string> code_group;
        volume vol;
        uint32_t weight;
        uint32_t barcode_count;
    };

    enum task_status
    {
        START = 0,
        FINISH = 1
    };

    enum report_server_state
    {
        NOT_REPORT = 0,     //不需要上报
        REPORT_HOSPICE_FINISH = 1,
        NORMAL_REPORT = 2,
        REPORT_SUSPEND = 3
    };

    enum task_exp_reason
    {
        task_no_exception = 0,
        target_slot_full = 1,
        target_slot_seal = 2,
        task_vehicle_error = 3,
        task_munaul_reason = 4,
    };

    struct task_state           //任务状态上报
    {
        std::string task_no;
        task_type type;
        report_server_state report_state;
        task_status status;
        std::string grid_no;
        std::string sort_no;
        std::string exp_info;          //收容原因
        task_exp_reason exp_reason;
    };

    int init(zmq::context_t &ctx);

    int issue_task(std::string grid_no, std::string &task_no, int type, float speed);          //从物控节点获取要下发的任务

    int get_task_state_to_report(task_state &task_state);              //在物控节点获取需要上报的任务状态

    int get_barcode_to_request(barcode_task_info &barcode);              //在物控节点获取码值，向上请求的任务使用

    static task_manager *get_instance(void)
    {
        static task_manager instance;
        return &instance;
    }

    void update_task(const uint32_t &feeder_id, const std::string &task_id, const device_task_state &st)
    {
        auto task = get_task(feeder_id);
        if (task)
        {
            task->task_id = task_id;
            task->state = st;
        }
        else
            SPDLOG_ERROR("task {} is not exist", task_id);
    }

    int set_task_state(const std::string &task_id, const device_task_state &st)
    {
        auto task = get_task(task_id);
        if (task)
            task->state = st;
        else
            SPDLOG_ERROR("task {} is not exist", task_id);

        return 0;
    }

    int set_task_state(const uint32_t &feeder_id, const device_task_state &st)
    {
        auto task = get_task(feeder_id);
        if (task)
            task->state = st;
        else
            SPDLOG_ERROR("feeder id {} task is not exist", feeder_id);

        return 0;
    }

    const device_task_state get_task_state(uint32_t &feeder_id)
    {
        auto task = get_task(feeder_id);
        if (task)
            return task->state;
        else
            SPDLOG_ERROR("feeder id {} task is not exist", feeder_id);

        return device_task_state::IDLE;
    }

    const device_task_state get_task_state(std::string &task_id)
    {
        auto task = get_task(task_id);
        if (task)
            return task->state;
        else
            SPDLOG_ERROR("task {} is not exist", task_id);

        return device_task_state::IDLE;
    }

private:

    std::queue<task_state> task_state_queue;                    //待向物控上报的任务状态队列

    void split_codes_group(std::vector<std::string> &group, const char *code);

    int make_barcode(const sorting_task_msg &task_msg, barcode_task_info &barcode);

    int make_task(const std::string &grid_no, const std::string &task_no, sorting_task_msg &task, float speed);

    int make_simulate_task(std::string grid_no, std::string task_no, int type);

    std::string make_task_state_task_no(const unsigned char *task_id);
    int make_task_state_task_type(const sorting_task_state &sorting_state, task_type &type);
    int make_task_state_task_status(const sorting_task_state &sorting_state, task_status &status);
    int make_task_state_task_report_type(const sorting_task_state &sorting_state, report_server_state &state);
    std::string make_task_state_hospice_reason(const sorting_task_state_msg &sorting_task_state);
    task_exp_reason make_task_state_exp_reason(const sorting_task_state_msg &sorting_task_state);
    std::string make_task_state_grid_no(uint32_t container_id);
    std::string make_task_state_sort_no(uint32_t train_id, uint32_t platform_id);
    int make_task_state(const sorting_task_state_msg &sorting_task_state, task_state &task_state_to_report);
    int monitor_task_state_from_scheduler_thread();

    device_task *get_task(const uint32_t &dev_id)
    {
        std::lock_guard<std::mutex> lk(device_task_mutex);
        for (auto &t: tasks)
            if (t.feeder_id == dev_id)
                return &t;
        return nullptr;
    }

    device_task *get_task(const std::string &task_id)
    {
        std::lock_guard<std::mutex> lk(device_task_mutex);
        for (auto &t: tasks)
        {
            if ((t.task_id).compare(task_id) == 0)
                return &t;
        }
        return nullptr;
    }

    std::mutex device_task_mutex;
    std::vector<device_task> tasks;

    task_cmd simulated_task;         //模拟任务暂不设置任务状态，可直接下发

    std::mutex task_state_queue_lock;
    std::mutex task_issuer_mutex;

    int scanner_id;

};

