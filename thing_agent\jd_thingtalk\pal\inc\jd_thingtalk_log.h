#ifndef __JD_THINGTALK_LOG_H__
#define __JD_THINGTALK_LOG_H__

#include <stdio.h>

#include <errno.h>
#include <stdarg.h>
#include <locale.h>
#include <string.h>
#include "jd_thingtalk_stdio.h"
#include "jd_thingtalk_time.h"
#include "jd_thingtalk_stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

#define JD_THINGTALK_LOG_LEVEL_FATAL      (0)
#define JD_THINGTALK_LOG_LEVEL_NOTICE     (1)
#define JD_THINGTALK_LOG_LEVEL_INFO       (2)
#define JD_THINGTALK_LOG_LEVEL_ERROR      (3)
#define JD_THINGTALK_LOG_LEVEL_WARN       (4)
#define JD_THINGTALK_LOG_LEVEL_DEBUG      (5)

#define Black   0;30
#define <PERSON>     0;31
#define <PERSON>   0;32
#define <PERSON>   0;33
#define <PERSON>    0;34
#define <PERSON>  0;35
#define Cyan    0;36

//#define JD_THINGTALK_LOG_LEVEL  JD_THINGTALK_LOG_LEVEL_ERROR
#define JD_THINGTALK_LOG_LEVEL  JD_THINGTALK_LOG_LEVEL_DEBUG

// 获取时间格式
int jd_thingtalk_log_time_format(char *time_str);

#ifndef __FILENAME__
#define __FILENAME__ (strrchr("/" __FILE__, '/') + 1)
#endif

#ifdef __GNUC__
#define SHORT_FILE    __FILENAME__
#else
#define SHORT_FILE   strrchr(__FILE__, '\\')?strrchr(__FILE__, '\\') + 1 : __FILE__
#endif

#define log_fatal(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_FATAL){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[0;31m[FATAL][%s][%s][%s][%d]\r\n" format "\r\n", time_format,  SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_notice(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_NOTICE){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[0;36m[NOTICE][%s][%s][%s][%d]\r\n" format "\r\n", time_format, SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_info(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_INFO){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[1;36m[INFO][%s][%s][%s][%d]\r\n" format "\r\n", time_format,  SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_error(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_ERROR){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[0;31m[ERROR][%s][%s][%s][%d]\r\n" format "\r\n", time_format, SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_warn(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_WARN){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[1;33m[WARN][%s][%s][%s][%d]\r\n" format "\r\n", time_formate,  SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_debug(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_DEBUG){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[1;32m[DEBUG][%s][%s][%s][%d]\r\n" format "\r\n", time_format,  SHORT_FILE, __FUNCTION__, __LINE__, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)

#define log_debug_plain(format, ...) \
    do{\
        if(JD_THINGTALK_LOG_LEVEL >= JD_THINGTALK_LOG_LEVEL_DEBUG){\
            char time_format[32] = {};\
            jd_thingtalk_log_time_format(time_format);\
            jd_thingtalk_pal_printf_lock();\
            jd_thingtalk_pal_printf("\033[1;34m[DEBUG][%s] " format "\r\n", time_format, ##__VA_ARGS__);\
            jd_thingtalk_pal_fflush(stdout);\
            jd_thingtalk_pal_printf("\033[0m"); \
            jd_thingtalk_pal_printf_unlock();\
        }\
    }while(0)


#ifdef __cplusplus
}
#endif
#endif
