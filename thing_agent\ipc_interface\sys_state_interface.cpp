#include "sys_state_interface.hpp"

int sys_state_interface::init(zmq::context_t &ctx)
{
    sys_cmd_sender = new zmq::socket_t {ctx, zmq::socket_type::pub};
	sys_cmd_sender -> bind(TOPIC_SYS_CMD);

	sys_state_recv = new zmq::socket_t {ctx, zmq::socket_type::sub};
	sys_state_recv -> connect(TOPIC_SYS_STATE);
    sys_state_recv -> set(zmq::sockopt::subscribe, "");

	// sub_keyevt = new zmq::socket_t {ctx, zmq::socket_type::sub};
	// sub_keyevt -> connect(TOPIC_PLANE_HMI_KEYEVT);
	// sub_keyevt -> set(zmq::sockopt::subscribe, "");

    return 0;
}

int sys_state_interface::issue_sys_state(sys_mode_state &st)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (sys_state_recv->recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, sys_mode_state_fields, &st))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
		else
		{
			SPDLOG_INFO("************dev_st:{}-{}-{}-{}-{}-{}", st.dev_st.safty_door_open, st.dev_st.safty_door_state, st.dev_st.emerg_pressed, st.dev_st.emerg_button_state, st.mode, st.state);
			return 1;
		}
	}

    return 0;
}

int sys_state_interface::issue_simulate_sorting(task_cmd &task)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;
    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_task_tag;
    cmd.cmd.task = task;

    SPDLOG_DEBUG("task_interface issue simulate task---------------");

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
    if (!pb_encode(&stream_out, sys_cmd_fields, &cmd))
    {
        SPDLOG_WARN("task msg encode failed");
    }
    else
    {
        sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    }

    SPDLOG_DEBUG("cmd:{}, task_id:{}, container:{}", task.cmd, task.task_id, task.param);
    
    return 0;
}

int sys_state_interface::issue_vehicle_start_moving()
{
    uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
    cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_FORWARD;
    cmd.cmd.misc.which_cmd_para = manual_cmd_move_continuout_flag_tag;
    cmd.cmd.misc.cmd_para.move_continuout_flag = true;

    SPDLOG_DEBUG("sys_state_interface issue start vehicle moving test---------------");

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
    if (!pb_encode(&stream_out, sys_cmd_fields, &cmd))
    {
        SPDLOG_WARN("task msg encode failed");
    }
    else
    {
        sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    }

    return 0;
}

int sys_state_interface::issue_vehicle_stop_moving()
{
    uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
    cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_FORWARD;
    cmd.cmd.misc.which_cmd_para = manual_cmd_move_continuout_flag_tag;
    cmd.cmd.misc.cmd_para.move_continuout_flag = false;

    SPDLOG_DEBUG("sys_state_interface issue stop vehicle moving test---------------");

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
    if (!pb_encode(&stream_out, sys_cmd_fields, &cmd))
    {
        SPDLOG_WARN("task msg encode failed");
    }
    else
    {
        sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    }

    return 0;
}

int sys_state_interface::issue_mcu_reboot()
{
    SPDLOG_WARN("sys_state_interface issue mcu rebooot");

    system("echo jd@ugv | sudo -S reboot");

    return 0;
}

int sys_state_interface::issue_mcu_reset()
{
    SPDLOG_WARN("sys_state_interface issue mcu reset");

#ifdef SURESORT_HORIZONTAL_VERSION
    system("/home/<USER>/auto_sort/startup.sh");
#endif

#ifdef SURESORT_VERTICAL_VERSION
    system("/home/<USER>/control_center/startup_test.sh");
#endif

    return 0;
}


int sys_state_interface::issue_vehicle_to_feeder(uint32_t &vehicle_id, uint32_t level_speed, uint32_t turn_speed, int feeder_id)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_TO_FEEDER;
	cmd.cmd.misc.which_cmd_para = manual_cmd_train_move_tag;
	cmd.cmd.misc.cmd_para.train_move.target_id = feeder_id;
	cmd.cmd.misc.cmd_para.train_move.move_staight_speed = level_speed;
    cmd.cmd.misc.cmd_para.train_move.move_arc_speed = turn_speed;

    SPDLOG_DEBUG("sys_state_interface issue vehicle {} to feeder {}", vehicle_id, feeder_id);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);


	return 0;
}

int sys_state_interface::issue_vehicle_to_camera(uint32_t &vehicle_id, uint32_t camera_id, uint32_t level_speed, uint32_t turn_speed)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_TO_CAMERA;
	cmd.cmd.misc.which_cmd_para = manual_cmd_train_move_tag;
	cmd.cmd.misc.cmd_para.train_move.target_id = camera_id;
	cmd.cmd.misc.cmd_para.train_move.move_staight_speed = level_speed;
    cmd.cmd.misc.cmd_para.train_move.move_arc_speed = turn_speed;
    
    SPDLOG_DEBUG("sys_state_interface issue vehicle {} to camera {}", vehicle_id, camera_id);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}

int sys_state_interface::issue_vehicle_to_slot(uint32_t &vehicle_id, uint32_t &slot_id, uint32_t level_speed, uint32_t turn_speed)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_TO_SLOT;
	cmd.cmd.misc.which_cmd_para = manual_cmd_train_move_tag;
	cmd.cmd.misc.cmd_para.train_move.target_id = slot_id;
	cmd.cmd.misc.cmd_para.train_move.move_staight_speed = level_speed;
    cmd.cmd.misc.cmd_para.train_move.move_arc_speed = turn_speed;
    SPDLOG_DEBUG("sys_state_interface issue vehicle {} to slot {}---------------", vehicle_id, slot_id);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}

int sys_state_interface::issue_vehicle_move_forward(uint32_t &vehicle_id, uint32_t &length, uint32_t level_speed, uint32_t turn_speed)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_MOVE_FORWARD_LIMIT;
	cmd.cmd.misc.which_cmd_para = manual_cmd_train_move_tag;
	cmd.cmd.misc.cmd_para.train_move.target_id = 0;
	cmd.cmd.misc.cmd_para.train_move.move_length = length;
	cmd.cmd.misc.cmd_para.train_move.move_staight_speed = level_speed;
    cmd.cmd.misc.cmd_para.train_move.move_arc_speed = turn_speed;

    SPDLOG_DEBUG("sys_state_interface issue vehicle {} move_forward_length {}---------------", vehicle_id, length);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}

int sys_state_interface::issue_vehicle_register_cmd(uint32_t &vehicle_id)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_ENABLE;
	
    SPDLOG_DEBUG("sys_state_interface issue vehicle {} register---------------", vehicle_id);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
	
}

int sys_state_interface::issue_vehicle_unregister_cmd(uint32_t &vehicle_id)
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_misc_tag;
	cmd.cmd.misc.dev_id = vehicle_id;
	cmd.cmd.misc.sub_dev_id = 0;
	cmd.cmd.misc.cmd_type = manual_cmd_type_TRAIN_DISABLE;
	
    SPDLOG_DEBUG("sys_state_interface issue vehicle {} unregister---------------", vehicle_id);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}


int sys_state_interface::issue_set_auto_mode()
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_state_tag;
	cmd.cmd.state.state = e_sys_mode_AUTO;
	
    SPDLOG_DEBUG("sys_state_interface issue set auto mode");

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}

int sys_state_interface::issue_set_manual_mode()
{
	uint8_t pub_msg[sys_cmd_size];
	pb_ostream_t stream_out;

    sys_cmd cmd;
    cmd.which_cmd = sys_cmd_state_tag;
	cmd.cmd.state.state = e_sys_mode_MANNUAL;
	
    SPDLOG_DEBUG("sys_state_interface issue set manual mode");

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	sys_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}