#include "multi_swap_manager.hpp"
#include <spdlog/spdlog.h>
#include "swap_agent_debug.h"
#include <arpa/inet.h>
#include "protocol/train_protocol.hpp"
#include "swap_manage/swap_manage.hpp"
#include "share/pb/idl/exception.pb.h"
#include "exception/dev_except.hpp"
#include "share/event_code.hpp"
#include "scheduler_msg/scheduler_msg.hpp"
#include "swap_manage/swap_list.hpp"
#include "share/global_def.h"


/**@brief     multi_vehicle_manager class析构函数
* @param[in]  NULL
* @return     NULL
*/
multi_swap_manager::~multi_swap_manager()
{
	for (auto& x: m_epoll_dev_list)
	{
		close(x.first);
	}
}


/**@brief     查找指定对象是否在本地列表中
* @param[in]  int dev_id --- 指定设备ID
* @return     NULL 
*/
bool multi_swap_manager::multi_swap_manager_train_list_find(int dev_id)
{
	int i;
	bool result = false;

	for(i = 0; i < m_database_swap_cnt; i++)
	{
		if(m_database_swap_list[i] == ((uint32_t )dev_id))
		{
			result = true;
			return result;
		}
	}

	result = false;
	return result;
}


/**@brief     车辆消息处理函数，主要实现车辆端消息的接收、解码及解析执行
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_swap_manager::multi_swap_dev_ctrl_func(int fd)
{
	struct sockaddr_in clnt_addr;
    uint8_t buffer[20480] = {0};
	uint8_t data_buf[20480] = {0};
    int recv_len = 0;
	train_protocol_err_tab head_match, tail_match;
	TRAIN_MSG_TYPE msg_type;
	net_msg msg_temp;
	uint32_t cli_addr_temp;
	uint32_t cli_addr_check = 0x00;
	uint32_t seque_temp;
	uint32_t seque_up_temp;
	uint32_t seque_down_temp;

	uint8_t id_temp;
	uint16_t except_dev_id;
	uint8_t excep_level, excep_motor_id;
	uint32_t excep_code, excep_subcode;
	int data_cnt;

	swap_info temp;
	msg_queue state_data;

	_train_map_opt_tab train_opt_temp;
	_train_map_opt_tab swap_id_opt_temp;
	swap_info train_info_temp;
	auto_exchange_dev_state swap_state_temp;
	dev_state_net dev_state_inline;
	task_st swap_task_state, swap_task_state_temp;

	auto_exchange_task_state m_swap_task_state;

	event_exception exce_info;
	dev_run_para para;
	std::shared_ptr<spdlog::logger> logger; 
	char soft_version[16];
    char hard_version[16];

	struct timespec tic_dev;
	struct timespec tic_curr;
	struct timespec tick;
	struct timespec tick_down_temp;
	long timedif;
	
	bool logger_valid = false;
	char data[4];
	string net_msg_temp = {""};
	string hb_net_msg;
	
	std::lock_guard<std::mutex> dev_lock(m_train_mtx);

	m_locol_server.udp_server_socket_recv_msg(&recv_len, buffer, &clnt_addr);
	
	if((recv_len > 14) && (htons(clnt_addr.sin_port) == m_client_port))
	{
		cli_addr_temp = clnt_addr.sin_addr.s_addr;
		SPDLOG_INFO("recv msg from fd:{}, cli_addr:{}, cli_port:{}, msg_len:{}", fd, inet_ntoa(clnt_addr.sin_addr), htons(clnt_addr.sin_port), recv_len);

		// 将客户端地址信息插入到设备管理中
    	epoll_poller_dev_insert(cli_addr_temp, clnt_addr);
		display();
	}
	else
	{
		SPDLOG_INFO("recvfrom error, fd:{}, client_info:{}-{}", fd, inet_ntoa(clnt_addr.sin_addr), htons(clnt_addr.sin_port));
		return;
	}

	head_match = train_protocol_head_match(buffer, recv_len);
	tail_match = train_protocol_tail_match(buffer, recv_len);
	if((TRAIN_PROTOCOL_SUCESS == head_match) && (TRAIN_PROTOCOL_SUCESS == tail_match))
	{
		msg_type = swap_manage_msg_type(buffer, recv_len, data_buf, &data_cnt, &id_temp, &seque_temp);
		if((TRAIN_MSG_UNDEFINED == msg_type)||(0x00 == id_temp))
		{
			return ;
		}

		train_opt_temp = m_train_list.train_sock_list_find(cli_addr_temp);		//m_train_sock_list <cin_addr -> dev_id>
		if(TRAIN_SESSION_NOT_FIND == train_opt_temp)
		{
			std::unique_lock<std::mutex> insert_lock(m_insert_lock);

			swap_id_opt_temp = m_train_list.train_list_map_find(id_temp);	//m_train_dev_list <dev_id -> train_info>
			if(TRAIN_SESSION_NOT_FIND == swap_id_opt_temp)
			{
				m_train_list.train_sock_list_insert(cli_addr_temp, id_temp);	//<cin_addr -> dev_id>
				train_info_temp.cin_addr = cli_addr_temp;
				train_info_temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &train_info_temp.v_last_msg_upload_tick);
				train_info_temp.v_comm_finish_flag = true;
				train_info_temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, train_info_temp);
				m_train_list.train_log_list_init_logger(id_temp);

				try
				{
					m_task_mtx.try_lock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("m_task_mtx, dev_id:{} error! :{}", id_temp, e.what() );
				}

				m_train_list.train_task_list_init(id_temp);
				try
				{
					m_task_mtx.unlock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("m_task_mtx, dev_id:{} error! :{}", id_temp, e.what() );
				}
		
				m_train_list.swap_list_map_insert_comm_sequeue(id_temp);	//下行sequence,insert
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.swap_list_map_update_uplink_comm_squence(id_temp, 1);	//上行sequence

				/*复位心跳超时*/
				exce_info.which_evt_except = event_exception_except_tag;
				except_info e = dev_except::swap_offline(id_temp);
				e.state = exception_state_STATE_RESET;
				exce_info.evt_except.except = e;

				state_data.type = SWAP_EXCEP_PUB;
				state_data.swap_id = id_temp;
				memcpy(state_data.msg_data, (uint8_t *)(&exce_info), sizeof(exce_info));
				scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
			}
			else
			{
				m_train_list.train_sock_list_change(cli_addr_temp, id_temp);
				temp.cin_addr = cli_addr_temp;
				temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &temp.v_last_msg_upload_tick);
				temp.v_comm_finish_flag = true;
				temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, temp);

				SPDLOG_INFO("id :{} already exit", id_temp);
			}

			insert_lock.unlock();

#ifdef 	MULTI_DEV_DEBUG
			m_train_list.train_sock_list_display();
			m_train_list.train_dev_list_display();
#endif	
		}
		else
		{
			//校验ip地址和dev_id的匹配性
			cli_addr_check = m_train_list.train_list_map_get_dev_addr(id_temp);

			if(cli_addr_temp != cli_addr_check)
			{
				SPDLOG_INFO("id :{} need refresh ip addr", id_temp);
				m_train_list.train_sock_list_change(cli_addr_temp, id_temp);
				temp.cin_addr = cli_addr_temp;
				temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &temp.v_last_msg_upload_tick);
				temp.v_comm_finish_flag = true;
				temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, temp);
			}
		}

		logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);

		for(int j = 0; j < recv_len; j++)
		{
			sprintf(data, "%x ", buffer[j]);
			data[3] = 0x00;
			net_msg_temp += data;
		}

		if( logger_valid )
		{
			string filename = basename((char *)(__FILE__));
			logger->info( "[{}: {}: {}] [NET] [IN] msglen:{} msg_type:{} / dev_id:{} / sequence:{}", filename, __FUNCTION__, __LINE__, \
																	 recv_len, msg_type, id_temp, seque_temp);
			if(msg_type == SWAP_MSG_STATE)
			{
				hb_log_decode(buffer, data_buf, &hb_net_msg);
				logger->info( "[{}: {}: {}] [NET] [IN] msg:{} ", filename, __FUNCTION__, __LINE__, hb_net_msg); 
			}				
			else
				logger->info( "[{}: {}: {}] [NET] [IN] msg:{} ", filename, __FUNCTION__, __LINE__, net_msg_temp); 	
		}

		if(multi_swap_manager_train_list_find(id_temp))
		{
			if(SWAP_MSG_REG == msg_type)
			{
				SPDLOG_INFO("swap register info: devid:{}, software_version:{}, hardware_version:{}", id_temp, *(uint32_t *)&data_buf[0], *(uint32_t *)&data_buf[4]);
				
				m_train_list.train_list_map_update_train_restart_flag(id_temp, true);

				snprintf(soft_version, sizeof(temp.sw_version), "%u", *(uint32_t *)&data_buf[0]);
				snprintf(hard_version, sizeof(temp.hw_version), "%u", *(uint32_t *)&data_buf[4]);
				m_train_list.train_list_map_update_sw_hw_version(id_temp, soft_version, hard_version);
		
				m_train_list.train_list_map_update_upload_tick(id_temp);
				m_train_list.train_list_map_reset_task_info(id_temp);

				m_train_list.swap_list_map_update_uplink_comm_squence(id_temp, 0x00);	//上行sequence
				
				logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);
				if( logger_valid )
				{
					logger->info( "[{}: {}: {}] [NET] [IN] SWAP_MSG_REG ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);
				}
				
				seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(id_temp);	//下行sequence
				swap_manage_train_register_ack(id_temp, seque_down_temp + 1, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg);
			
				msg_temp.cin_addr = cli_addr_temp;

				try
				{
					m_task_mtx.try_lock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}

				m_net_msg_queue.push(msg_temp);

				seque_down_temp++;
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.swap_list_map_update_comm_sequeue(id_temp, seque_down_temp);	//下行sequence
				m_train_list.train_list_map_update_comm_flag(id_temp, true);

				//处理原有任务
				if( !m_train_list.train_task_list_empty(id_temp) )
				{
					m_train_list.train_task_list_clear(id_temp);
				}

				try
				{
					m_task_mtx.unlock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}

				//生成设备复位消息
				exce_info.which_evt_except = event_exception_evt_tag;
				exce_info.evt_except.evt.src = exception_src_AUTO_EXCHANGE_AGENT;
				exce_info.evt_except.evt.code = DEV_RESET;
				exce_info.evt_except.evt.sub_code = 0x00;
				exce_info.evt_except.evt.dev = id_temp;
				exce_info.evt_except.evt.sub_dev = 0x00;
				state_data.type = SWAP_RESET_PUB;
				state_data.swap_id = id_temp;
				state_data.sub_id = 0x00;
				memcpy(state_data.msg_data, (uint8_t *)(&exce_info), sizeof(exce_info));
				scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);


				//如果车辆异常列表为空，且20秒内重启了，说明车辆异常重启了，需要上报一个异常报文
				clock_gettime(CLOCK_MONOTONIC, &tic_curr);
				if( 0x00 == m_train_list.exception_cnt_get(id_temp, m_dev_cfg.component_count))
				{
					if( m_train_list.train_list_map_get_upload_tick(id_temp, &tic_dev) )
					{
						//计算时间差 (微秒)
						timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/10;

						SPDLOG_INFO("log timedif :{} ", timedif);

						if( timedif < (m_dev_cfg.heartbeat_timeout*1000*1000) && (timedif > (3)) )
						{
							if( multi_train_reset_dev_find(id_temp) )	//m_train_reset_dev_map < devid -> 待重启标志true >
							{
								multi_train_reset_dev_clear(id_temp);
							}
							else
							{
								exce_info.which_evt_except = event_exception_except_tag;
								dev_except::dev_except except(id_temp, 0, 99, 0);
								exce_info.evt_except.except = except.get_except_info();
								if (m_train_list.exception_occur(id_temp * 100, exce_info.evt_except.except) > 0)
								{
									swap_state_temp.work_state = auto_exchange_work_state_WORK_STATE_FATAL;
									swap_state_temp.dev_error_no = AUTO_EXCHANGE_SELF_RESET;
									swap_state_temp.curr_state = auto_exchange_device_state_DEV_UNKNOWN;
									swap_state_temp.motion_positon_valid_flag = false;
									swap_state_temp.dev_id = id_temp;
								
									state_data.type = SWAP_STATE_PUB;
									state_data.swap_id = id_temp;
									memcpy(&state_data.msg_data[0], &swap_state_temp, sizeof(swap_state_temp));
									scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
								}
							}
						}
					}
				}					
			}

			else if( SWAP_MSG_STATE == msg_type )	//心跳数据
			{
				if( logger_valid )
				{
					logger->info( "[{}: {}: {}] [NET] [IN] SWAP_MSG_STATE ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
				}

				seque_up_temp = m_train_list.swap_list_map_get_uplink_comm_sequeue(id_temp);	//上行sequence
				if( seque_temp < seque_up_temp )
					return;
				

				//如果是上报状态消息
				swap_manage_get_dev_state(&swap_state_temp, &dev_state_inline, data_buf, id_temp, &excep_motor_id, &excep_level, &excep_code);
				m_train_list.train_list_map_update_state(id_temp, dev_state_inline);
				m_train_list.train_list_map_update_upload_tick(id_temp);
				m_train_list.swap_list_map_get_swap_task_state(id_temp, &swap_task_state_temp);

				SPDLOG_INFO("curr task id:{}, seq:{}", dev_state_inline.curr_task_id, seque_temp);

				//获取任务状态
				swap_task_state.control_id = dev_state_inline.task_control_object;
				swap_task_state.task_type = dev_state_inline.task_type;
				swap_task_state.task_result = dev_state_inline.task_state;
				m_train_list.train_list_map_update_task_state(id_temp, swap_task_state);

				if(m_train_list.train_list_map_get_swap_task_state(id_temp, &m_swap_task_state))
				{
					m_swap_task_state.sub_dev_id = swap_task_state.control_id;

					switch(swap_task_state.task_type)
					{
						case SWAP_TASK_INTE_MOVE_X:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_X_AXIS;
							break;
						case SWAP_TASK_INTE_MOVE_Y:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_Y_AXIS;
							break;
						case SWAP_TASK_INTE_MOVE_XY:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_XY_AXIS;
							break;
						case SWAP_TASK_INTE_MOVE_SWAP1:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_Z1_AXIS;
							break;
						case SWAP_TASK_INTE_MOVE_SWAP2:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_Z2_AXIS;
							break;
						case SWAP_TASK_INTE_MOVE_SWAP_DISTANCE:
							m_swap_task_state.type = auto_exchange_task_type_MOVE_Z3_AXIS;
							break;
						case SWAP_TASK_INTE_LOAD:
							m_swap_task_state.type = auto_exchange_task_type_HOOK_GRAB;
							break;
						case SWAP_TASK_INTE_UBLOAD:
							m_swap_task_state.type = auto_exchange_task_type_HOOK_UNLOAD;
							break;
						case SWAP_TASK_LOAD_MOVE_SOTP:
						case SWAP_TASK_LOAD_MOVE_CONTINUE:
							m_swap_task_state.type = auto_exchange_task_type_TASK_INTE_GRAB_MOVE;
							break;
						case SWAP_TASK_UNLOAD_MOVE_SOTP:
						case SWAP_TASK_UNLOAD_MOVE_CONTINUE:
							m_swap_task_state.type = auto_exchange_task_type_TASK_INTE_UNLOAD_MOVE;
							break;
						case SWAP_TASK_INTE_LOAD_UNLOAD:
							m_swap_task_state.type = auto_exchange_task_type_TASK_INTE_GRAB_UNLOAD;
							break;
						default:
							SPDLOG_ERROR("error task type:{}", swap_task_state.task_type);
							break;
					}

					if(m_train_list.train_list_map_get_comm_flag(id_temp))
					{
						if(0x01 == swap_task_state.task_result)
							m_swap_task_state.state = auto_exchange_dev_task_state_RUNNING;
					
						if((0x01 == swap_task_state_temp.task_result) && (0x00 == swap_task_state.task_result))
							m_swap_task_state.state = auto_exchange_dev_task_state_SUCCEED_OVER;
						
						if((0x02 == swap_task_state_temp.task_result) && (0x00 == swap_task_state.task_result))
							m_swap_task_state.state = auto_exchange_dev_task_state_SUCCEED_OVER;
							
						if(0x00 == swap_task_state.task_result)										
						{
						
						}
						if(0x02 == swap_task_state.task_result)
						{
							m_swap_task_state.state = auto_exchange_dev_task_state_ERROR;
							SPDLOG_ERROR("m_swap_task_state error, id:{}, task_result:{}", id_temp, swap_task_state.task_result);
						}
					}
					else
					{
						// m_swap_task_state.state = task_state_INIT;
					}

					m_train_list.swap_list_task_state_insert(id_temp, m_swap_task_state);
				}

				SPDLOG_INFO("task state, swap_id:{}, state:{}, type:{}, flag:{}", id_temp, m_swap_task_state.state, m_swap_task_state.type, m_train_list.train_list_map_get_comm_flag(id_temp));
				

				//处理异常消息
				SPDLOG_INFO("except_info, swap_id:{}, exce_motor_id:{}, excep_level:{}, excep_code:{}", id_temp, excep_motor_id, excep_level, excep_code);

				m_train_list.train_list_map_update_exce_code(id_temp, excep_code, excep_motor_id);
				if(0x00 != excep_code)
				{
					if((1 == excep_code) || (2 == excep_code))
						swap_emerg_state = true;
					else
						swap_emerg_state = false;

					if((5 == excep_code) || (6 == excep_code))	//伺服异常
					{
						switch(excep_motor_id)
						{
							case 1:
								excep_subcode = dev_state_inline.motor_error_code_x1;
								break;
							case 2:
								excep_subcode = dev_state_inline.motor_error_code_x2;
								break;
							case 3:
								excep_subcode = dev_state_inline.motor_error_code_y1;
								break;
							case 4:
								excep_subcode = dev_state_inline.motor_error_code_y2;
								break;
							case 5:
								excep_subcode = dev_state_inline.motor_error_code_z1;
								break;
							case 6:
								excep_subcode = dev_state_inline.motor_error_code_z2;
								break;
							case 7:
								excep_subcode = dev_state_inline.motor_error_code_z3;
								break;
							default:
								SPDLOG_ERROR("error motor excep_subcode:{}", excep_motor_id);
								break;
						}
					}
					else
						excep_subcode = 0;

					exce_info.which_evt_except = event_exception_except_tag;
					dev_except::dev_except except(id_temp, excep_motor_id, excep_code, excep_subcode);
					exce_info.evt_except.except = except.get_except_info();

					swap_state_temp.dev_error_no = exce_info.evt_except.except.code;
					m_train_list.train_list_map_update_train_error_no(id_temp, exce_info.evt_except.except.code);
					SPDLOG_INFO("excet info, src:{}, excep_code:{}, dev_error_no:{}", exce_info.evt_except.except.src, excep_code, swap_state_temp.dev_error_no);

					except_dev_id = id_temp * 100 + excep_motor_id;
					m_train_list.train_list_map_update_exce_info(except_dev_id, excep_code);

					if (m_train_list.exception_occur(except_dev_id, exce_info.evt_except.except) > 0)
					{
						state_data.type = SWAP_EXCEP_PUB;
						state_data.swap_id = id_temp;
						memcpy(&state_data.msg_data[0], &exce_info, sizeof(exce_info));
						scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
					}
				}
				else
				{
					swap_emerg_state = false;
					
					if(0x00 == dev_state_inline.dev_run_st)
					{
						/*对该车处于pending状态的异常，逐个异常恢复消息*/
						state_data.type = SWAP_EXCEP_PUB;
						state_data.swap_id = id_temp;
						event_exception *excep_resume = (event_exception *)(&state_data.msg_data[0]);
						excep_resume->which_evt_except = event_exception_except_tag;
						auto f = [&](const except_info &excp)
						{
							excep_resume->evt_except.except = excp;
							excep_resume->evt_except.except.state = exception_state_STATE_RESET;
							scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
						};
						m_train_list.travese_all_pending_exception_for_swap(id_temp, m_dev_cfg.component_count, f);
						m_train_list.exception_resume_all(id_temp * 100);
						
					}
				}

				if( seque_temp > seque_up_temp )
				{
					m_train_list.swap_list_map_update_uplink_comm_squence(id_temp, seque_temp);

					state_data.type = SWAP_STATE_PUB;
					state_data.swap_id = id_temp;
					memcpy(state_data.msg_data, (uint8_t *)(&swap_state_temp), sizeof(swap_state_temp));
					scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
				}
				else
				{
					if( logger_valid )
					{
						logger->info( "[{}: {}: {}] [NET] [IN] up load sequence error :{} :{}", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, seque_up_temp, seque_temp);		
					}
				}
			}

			else if(SWAP_MSG_EXCEPTIONAL == msg_type)
			{			
				if( logger_valid )
				{
					try
					{
						logger->info( "[{}: {}: {}] [NET] [IN] TRAIN_MSG_EMERGENCY ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
					}
					catch(const std::exception& e)
					{
						SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
					}
				}
					
				//如果是紧急上报消息
				m_train_list.train_list_map_update_upload_tick(id_temp);
				
				seque_up_temp = m_train_list.swap_list_map_get_uplink_comm_sequeue(id_temp);	//上行sequence
				if( seque_temp > seque_up_temp )
					m_train_list.swap_list_map_update_uplink_comm_squence(id_temp, seque_temp);

				exce_info.which_evt_except = event_exception_except_tag;
				dev_except::dev_except except(id_temp, data_buf[0], *(uint32_t *)&data_buf[2], 0);
				exce_info.evt_except.except = except.get_except_info();

				except_dev_id = id_temp * 100 + data_buf[0];
				m_train_list.train_list_map_update_exce_info(except_dev_id, *(uint32_t *)&data_buf[2]);
				if (m_train_list.exception_occur(except_dev_id, exce_info.evt_except.except) > 0)
				{
					state_data.type = SWAP_EXCEP_PUB;
					state_data.swap_id = id_temp;
					memcpy(&state_data.msg_data[0], &exce_info, sizeof(exce_info));
					scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
				}
			}
				
			else if(SWAP_MSG_CMD_ACK == msg_type)	
			{
				if( logger_valid )
				{
					try
					{
						logger->info( "[{}: {}: {}] [NET] [IN] SWAP_MSG_CMD_ACK ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
					}
					catch(const std::exception& e)
					{
						SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
					}
				}
				
				m_train_list.train_list_map_update_upload_tick(id_temp);
				seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(id_temp);

				if(data_buf[0] == TRAIN_PROTOCOL_CMD_QUERY_PARA_ACK)
				{
					dev_query_para_recv(&para, &data_buf[1]);
					SPDLOG_INFO("dev query para ack dev_id:{}", id_temp);
				}
				
				else if(data_buf[0] == TRAIN_PROTOCOL_CMD_VERSION_INFO_ACK)
				{
					snprintf(soft_version, sizeof(temp.sw_version), "%u", *(uint32_t *)&data_buf[1]);
					snprintf(hard_version, sizeof(temp.hw_version), "%u", *(uint32_t *)&data_buf[5]);
					m_train_list.train_list_map_update_sw_hw_version(id_temp, soft_version, hard_version);
					SPDLOG_INFO("dev version info ack devid, dev_id:{}, sw:{}, hw:{}", id_temp, *(uint32_t *)&data_buf[1], *(uint32_t *)&data_buf[5]);
				}

				else
				{
					SPDLOG_INFO("dev task sequence ack, id:{}, cmd:{}, sub_id:{}, seq:{}", id_temp, data_buf[0], data_buf[1], *(uint32_t *)&data_buf[2]);
					if( !m_train_list.train_list_map_get_comm_flag(id_temp) )
					{
						SPDLOG_INFO("seque_down_temp:{}-{}", seque_down_temp, *(uint32_t *)&data_buf[2]);
						if( seque_down_temp <= (*(uint32_t *)&data_buf[2] + 1) )
						{
							clock_gettime(CLOCK_MONOTONIC, &tick);
							m_train_list.train_list_map_get_download_tick(id_temp, &tick_down_temp);
							timedif = MILLION*(tick.tv_sec-tick_down_temp.tv_sec)+(tick.tv_nsec-tick_down_temp.tv_nsec)/1000/1000;
							if( logger_valid )
							{
								if(timedif > 1000)
								{
									try
									{
										logger->info( "[{}: {}: {}] [NET] [IN] curr dev msg confirm delay too long :{} ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, timedif);		
									}
									catch(const std::exception& e)
									{
										SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
									}
								}
							}

							m_train_list.train_list_map_update_comm_flag(id_temp, true);
							m_train_list.update_swap_cmd_resend_count(id_temp, 0);
						}
					}
				}
			}

			if(m_train_list.train_list_map_check_version_empty(id_temp))
			{
				seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(id_temp);	//下行sequence
				swap_manage_train_register_ack(id_temp, seque_down_temp + 1, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg);
				msg_temp.cin_addr = cli_addr_temp;
				m_net_msg_queue.push(msg_temp);
				
				seque_down_temp++;
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.swap_list_map_update_comm_sequeue(id_temp, seque_down_temp);	//下行sequence
			}
		}
	}
	else
		SPDLOG_INFO("recvfrom HEAD/TAIL error, H:{}, T:{}", head_match, tail_match);
	
	return;
}


/**@brief     epoll监听过程中，已有接入节点消息处理---从线程池中获取空闲线程执行操作
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_swap_manager::epoll_existConnection(int fd) 
{
	m_recv_worker.add_task(std::bind(&multi_swap_manager::multi_swap_dev_ctrl_func, this, fd));
}


/**@brief     epoll监听过程中，新接入节点处理
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_swap_manager::epoll_newConnection(int fd) 
{

}

/**@brief     epoll监听主循环
* @param[in]  int timeout      ---   epoll时间监听的超时等待时间
* @return     NULL 
*/
int multi_swap_manager::epoll_main_loop(int timeout)
{
	int event_cnt = -1;
	msg_queue data_temp;
	auto_exchange_task task_pop_temp;

	while(1)
	{
		event_cnt = epoll_wait_fd(timeout);
		
		if((event_cnt != -1) && (event_cnt != 0))
		{
			for(int i = 0; i != event_cnt; i++)
			{
				if(m_events[i].data.fd == m_server_fd)
				{
					epoll_existConnection(m_events[i].data.fd);
				}
			}
		}

		// 车辆需要下发的任务消息处理
		if(!scheduler_manager::get_instance()->scheduler_manager_task_msg_queue_empty())
		{
			int task_cnt = scheduler_manager::get_instance()->scheduler_manager_task_msg_queue_size();

			//获取系统内指定的下发任务消息
			scheduler_manager::get_instance()->scheduler_manager_task_msg_queue_pop(&data_temp);
			
			memcpy(&task_pop_temp, data_temp.msg_data, sizeof(task_pop_temp));				
			SPDLOG_INFO("task_pop_temp, swap_id:{}, sub_id, seq:{}, which_task:{}, task_cnt:{}", task_pop_temp.dev_id, task_pop_temp.sub_dev_id, task_pop_temp.sequence, task_pop_temp.which_task, task_cnt);

			if(auto_exchange_task_hb_time_sync_tag == task_pop_temp.which_task)
				m_dev_downlink_hb_msg.push(task_pop_temp);
			else
			{
				if(auto_exchange_task_cmd_tag == task_pop_temp.which_task)
					m_dev_reset_msg.push(task_pop_temp);
				else
					m_train_list.train_task_list_push(data_temp.swap_id, task_pop_temp);
			}
		}

		// m_dev_curr_state = fsm_manager::get_instance()->fsm_manager_get_sys_state(); 

		std::this_thread::sleep_for(std::chrono::milliseconds(1));

	}
}


//处理多车辆管理中的设备重置任务
void multi_swap_manager::multi_swap_manager_task_dev_reset_gen(void)
{
	net_msg msg_temp;
	
	uint32_t cin_addr_temp = 0;
	int addr_swap_id = 0;
	int task_id;
	int want_rst_dev = 0x00;
	int rst_cnt = 0;
	bool rst_dev_flag = false;
	volatile int seque_down_temp;
	auto_exchange_task task_pop_temp;

	while(1)
	{
		if( !m_dev_reset_msg.empty() )
		{
			m_dev_reset_msg.pop(task_pop_temp);

			seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id);
			seque_down_temp++;
			task_pop_temp.sequence = seque_down_temp;
			
			if(auto_exchange_task_cmd_tag == task_pop_temp.which_task)
			{
				if(cmd_type_CMD_CALIB == task_pop_temp.task.cmd.cmd)
				{
					task_id = m_train_list.swap_list_get_task_id();
					m_train_list.swap_list_update_task_id();
				}
				else
					task_id = 0;
			}

			swap_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, task_id);

			msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);
						
			addr_swap_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

			//自检socket addr
			if( (int)task_pop_temp.dev_id != addr_swap_id )
			{
				cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(task_pop_temp.dev_id);
				SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} " ,task_pop_temp.dev_id, msg_temp.cin_addr, cin_addr_temp);
				msg_temp.cin_addr = cin_addr_temp;
			}

			if( 0x00 != addr_swap_id )
			{
				m_net_msg_queue.push(msg_temp);
			}
					
			if(task_pop_temp.which_task == auto_exchange_task_cmd_tag)
			{
				if(task_pop_temp.task.cmd.cmd == cmd_type_CMD_REBOOT)
				{
					m_train_list.swap_list_map_update_uplink_comm_squence(task_pop_temp.dev_id, 1);
					m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);

					msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);								
					want_rst_dev = task_pop_temp.dev_id;
					rst_dev_flag = true;
					rst_cnt = 0x01;

					multi_train_reset_dev_add(task_pop_temp.dev_id);
				}
				else
				{
					m_train_list.train_list_map_update_comm_msg(task_pop_temp.dev_id, msg_temp);
					m_train_list.swap_list_map_update_comm_sequeue(task_pop_temp.dev_id, seque_down_temp);
					m_train_list.train_list_map_update_downlink_tick(task_pop_temp.dev_id);
					m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, false);
				}
			}
			
			SPDLOG_INFO("reset msg send to net downlink queue devid:{}, client_addr:{}, which_task:{}, seque_down_temp:{} - {}", task_pop_temp.dev_id, msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id));

		}

		if(rst_dev_flag)
		{
			rst_cnt++;
			if((rst_cnt%10) == 0)
			{
				uint32_t addr = m_train_list.train_list_map_get_dev_addr(want_rst_dev);
				SPDLOG_INFO("train:{} restart, ip:{}", want_rst_dev, addr);

				m_train_list.train_task_list_clear(want_rst_dev);

				rst_dev_flag = false;
			
			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(10));		
	}
}




bool multi_swap_manager::multi_train_reset_dev_find(int dev)
{

	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		return false;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		return false;
	}
	else
	{
		return m_train_reset_dev_map.at(dev);
	}
}


void multi_swap_manager::multi_train_reset_dev_clear(int dev)
{
	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, false));
		return;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, false));
		return ;
	}
	else
	{
		m_train_reset_dev_map.at(dev) = false;
		return ;
	}
}


//网络数据重发线程
void multi_swap_manager::multi_swap_manager_task_resend_thread_exe()
{
	struct timespec tic_dev;
	struct timespec tic_curr;
	long timedif;
	
	int i;	
	int dev_comm_unfinish_list_temp[32];
	int dev_comm_unfinish_cnt = 0x00;
	bool msg_valid_flag;
	net_msg msg_temp;

	event_exception exce_info;
	msg_queue state_data;

	while(1)
	{
		if( swap_emerg_state )
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(10));
			continue;
		}

		try
		{
			if( m_task_mtx.try_lock() )
			{
				clock_gettime(CLOCK_MONOTONIC, &tic_curr);	

				if( m_train_list.train_list_map_get_dev_comm_state(dev_comm_unfinish_list_temp, &dev_comm_unfinish_cnt) )
				{
					if( 0 != dev_comm_unfinish_cnt )
					{
						for(i = 0; i < dev_comm_unfinish_cnt; i++)
						{
							SPDLOG_INFO("swap_task_list_comm_unfinish swap_id:{}", dev_comm_unfinish_list_temp[i]);
					
							m_train_list.train_list_map_get_download_tick(dev_comm_unfinish_list_temp[i], &tic_dev);
							timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/1000;

							SPDLOG_INFO("task resend timedif :{}", timedif);

							if( timedif > (m_dev_cfg.resend_timeout *1000))//resend_timeout -> 单位us
							{					
								SPDLOG_INFO("task log unfinish_list_temp[{}]={}, cnt_total:{}", i, dev_comm_unfinish_list_temp[i], dev_comm_unfinish_cnt);

								msg_valid_flag = m_train_list.train_list_map_get_comm_msg(dev_comm_unfinish_list_temp[i], &msg_temp);
						
								if( msg_valid_flag )
								{
									msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(dev_comm_unfinish_list_temp[i]);
						
									int addr_swap_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

									if( (dev_comm_unfinish_list_temp[i]) != addr_swap_id )
									{
										uint32_t cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(dev_comm_unfinish_list_temp[i]);
										SPDLOG_INFO("msg send to net downlink queue addr error, swap_id:{}, cin_addr:{}-{} ", dev_comm_unfinish_list_temp[i], msg_temp.cin_addr, cin_addr_temp);
										msg_temp.cin_addr = cin_addr_temp;
									}

									m_train_list.train_list_map_update_downlink_tick(dev_comm_unfinish_list_temp[i]);
									m_net_msg_queue.push(msg_temp);
								}

								//统计重发次数
								int resend_count = m_train_list.get_swap_cmd_resend_count(dev_comm_unfinish_list_temp[i]);
								m_train_list.update_swap_cmd_resend_count(dev_comm_unfinish_list_temp[i], resend_count+1);

								if(m_train_list.get_swap_cmd_resend_count(dev_comm_unfinish_list_temp[i]) >= 5)
                            	{
									int sequence = ((msg_temp.msg_data[5] << 24) | (msg_temp.msg_data[6] << 16) | (msg_temp.msg_data[7] << 8) | (msg_temp.msg_data[8]));
									SPDLOG_ERROR("swap_id:{}, sequence:{} resend 5 times without success.", dev_comm_unfinish_list_temp[i], sequence);

									exce_info.which_evt_except = event_exception_except_tag;
									dev_except::dev_except except(dev_comm_unfinish_list_temp[i], 0, 300, 0);
									exce_info.evt_except.except = except.get_except_info();

									if (m_train_list.exception_occur(dev_comm_unfinish_list_temp[i] * 100, exce_info.evt_except.except) > 0)
									{
										state_data.type = SWAP_EXCEP_PUB;
										state_data.swap_id = dev_comm_unfinish_list_temp[i];
										memcpy(&state_data.msg_data[0], &exce_info, sizeof(exce_info));
										scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
									}
                             
                                	m_train_list.update_swap_cmd_resend_count(dev_comm_unfinish_list_temp[i], 0);
                            	}
							}
						}
			
						try
						{
							m_task_mtx.unlock();
						}
						catch(const std::exception& e)
						{
							SPDLOG_INFO("log :{} error! :{}", dev_comm_unfinish_list_temp[i], e.what() );
						}
					}
				}
			}
			else
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(1));	
				continue;
			}
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_comm_unfinish_list_temp[i], e.what() );
		}
		
		std::this_thread::sleep_for(std::chrono::milliseconds(5));	
	}
}


////车辆任务管理线程，根据调度及心跳行走任务，生成网络数据下发
void multi_swap_manager::multi_swap_manager_task_new_gen_thread_exe()
{
	net_msg msg_temp;

	int dev_id_list_temp[32];
	int dev_task_valid_cnt = 0x00;
	int i;
	int task_type_temp;
	uint32_t cin_addr_temp = 0;
	int addr_swap_id = 0;
	int want_rst_dev = 0x00;
	int rst_cnt = 0;
	bool rst_dev_flag = false;

	auto_exchange_task_state swap_task_state;

	while(1)
	{
		if( swap_emerg_state )
		{
			SPDLOG_INFO("swap_emerg_state: {}", swap_emerg_state);
			std::this_thread::sleep_for(std::chrono::milliseconds(100));
			continue;
		}
	
		//存在非空的任务缓存，执行发送机制
		try
		{
			m_task_mtx.try_lock();
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_id_list_temp[0], e.what() );
		}

		if( m_train_list.train_task_list_empty_state(dev_id_list_temp, &dev_task_valid_cnt) )
		{
			if( 0 != dev_task_valid_cnt )
			{
				for(i = 0; i < dev_task_valid_cnt; i++)
				{
					SPDLOG_INFO("train_task_list_empty_state devid:{}, dev_task_valid_cnt:{}", dev_id_list_temp[i], dev_task_valid_cnt);

					if( m_train_list.train_list_map_get_comm_flag(dev_id_list_temp[i]) )
					{
						SPDLOG_INFO("dev_id_list_temp[{}]={}, size:{}", i, dev_id_list_temp[i], m_train_list.train_task_list_size(dev_id_list_temp[i]));

						volatile int seque_down_temp;
						auto_exchange_task task_pop_temp;

						//获取系统内指定的下发任务消息
						m_train_list.swap_task_list_pop( dev_id_list_temp[i], &task_pop_temp );
						SPDLOG_INFO("task_pop_temp, swap_id:{}-{}, sub_id:{}, which_task:{}", dev_id_list_temp[i], task_pop_temp.dev_id, task_pop_temp.sub_dev_id, task_pop_temp.which_task);

						//组帧并控制发送
						seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id);	//下行sequence
						seque_down_temp++;
						task_pop_temp.sequence = seque_down_temp;

						int task_id = m_train_list.swap_list_get_task_id();
						m_train_list.swap_list_update_task_id();

						task_type_temp = swap_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, task_id);
						if(-1 == task_type_temp)
							continue;

						//任务开始执行	
						if( task_pop_temp.which_task == auto_exchange_task_move_tag)
						{
							swap_task_state.state = auto_exchange_dev_task_state_START;
							swap_task_state.type = task_pop_temp.task.move.type;
							swap_task_state.sub_dev_id = task_pop_temp.sub_dev_id;
							m_train_list.swap_list_task_state_insert(task_pop_temp.dev_id, swap_task_state);
						}

						if( task_pop_temp.which_task == auto_exchange_task_grab_tag)
						{
							swap_task_state.state = auto_exchange_dev_task_state_START;
							swap_task_state.type = task_pop_temp.task.grab.type;
							swap_task_state.sub_dev_id = task_pop_temp.sub_dev_id;
							m_train_list.swap_list_task_state_insert(task_pop_temp.dev_id, swap_task_state);
						}

						if( task_pop_temp.which_task == auto_exchange_task_grab_move_tag)
						{
							swap_task_state.state = auto_exchange_dev_task_state_START;
							swap_task_state.type = task_pop_temp.task.grab_move.type;
							swap_task_state.sub_dev_id = task_pop_temp.sub_dev_id;
							m_train_list.swap_list_task_state_insert(task_pop_temp.dev_id, swap_task_state);
						}

						if( task_pop_temp.which_task == auto_exchange_task_grab_inte_tag)
						{
							swap_task_state.state = auto_exchange_dev_task_state_START;
							swap_task_state.type = task_pop_temp.task.grab_inte.type;
							swap_task_state.sub_dev_id = task_pop_temp.sub_dev_id;
							m_train_list.swap_list_task_state_insert(task_pop_temp.dev_id, swap_task_state);
						}
						
						//网络下发数据队列push，由线程处理下发
						msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(dev_id_list_temp[i]);
						
						addr_swap_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);
						if( dev_id_list_temp[i] != addr_swap_id )
						{
							cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(dev_id_list_temp[i]);
							SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} ", dev_id_list_temp[i], msg_temp.cin_addr, cin_addr_temp);
							msg_temp.cin_addr = cin_addr_temp;
						}

						if( 0x00 != addr_swap_id )
						{
							m_net_msg_queue.push(msg_temp);
						}
						
						m_train_list.train_list_map_update_comm_msg(task_pop_temp.dev_id, msg_temp);
						m_train_list.swap_list_map_update_comm_sequeue(task_pop_temp.dev_id, seque_down_temp);
						m_train_list.train_list_map_update_downlink_tick(dev_id_list_temp[i]);

						if( task_pop_temp.which_task == auto_exchange_task_cmd_tag)
						{
							if(task_pop_temp.task.cmd.cmd == cmd_type_CMD_REBOOT)
							{
								m_train_list.swap_list_map_update_uplink_comm_squence(task_pop_temp.dev_id, 1);
								m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);

								want_rst_dev = dev_id_list_temp[i];
								rst_dev_flag = true;
								rst_cnt = 0x01;

								multi_train_reset_dev_add(dev_id_list_temp[i]);
							}
						}
						else if(task_pop_temp.which_task == auto_exchange_task_hb_time_sync_tag)
						{
							m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);
						}
						else
						{
							m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, false); 
						}

						SPDLOG_INFO("msg send to net downlink queue swap_id:{}, cin_addr:{}, which_task:{}, sequence:{} - {}", dev_id_list_temp[i], msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id));
					}
					else
					{
						SPDLOG_INFO("task_new_gen flag: flase");
						continue;
					}
				}
			}			
		}

		try
		{
			m_task_mtx.unlock();
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_id_list_temp[i], e.what() );
		}
		
		if(rst_dev_flag)
		{
			rst_cnt++;
			if((rst_cnt % 10) == 0)
			{
				uint32_t addr = m_train_list.train_list_map_get_dev_addr(want_rst_dev);
				SPDLOG_INFO("train:{} restart, ip:{}", want_rst_dev, addr);

				m_train_list.train_task_list_clear(want_rst_dev);

				rst_dev_flag = false;
			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(10));	
	}
}


void multi_swap_manager::multi_train_reset_dev_add(int dev)
{
	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, true));
		return;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, true));
	}
	else
	{
		m_train_reset_dev_map.at(dev) = true;
	}

	return;
}



//网络数据下发线程
void multi_swap_manager::multi_swap_manager_net_msg_thread_exe()
{
	net_msg msg_temp;
	_train_map_opt_tab train_opt_temp;
	std::shared_ptr<spdlog::logger> logger;
	string temp;
	swap_info dev_info;
	char data[4];
	int i;
	int id_temp;
	bool logger_valid = false;

	while(1)
	{
		m_net_msg_queue.pop(msg_temp);
		dev_info.v_addr = epoll_poller_dev_get_sock_addr(msg_temp.cin_addr);

#if 1
		SPDLOG_INFO("msg downlink fd:{}, client_addr:{}, data_len:{}", m_server_fd, inet_ntoa(dev_info.v_addr.sin_addr), msg_temp.data_len);
		printf("net msg send data: ");
		for(int i = 0; i < msg_temp.data_len; i++)
			printf("%02x ", msg_temp.msg_data[i]);
		printf("\n\n");
#endif

		if(0 > sendto(m_server_fd, msg_temp.msg_data, msg_temp.data_len, MSG_NOSIGNAL, (struct sockaddr *)&dev_info.v_addr, sizeof(dev_info.v_addr)))
			SPDLOG_INFO("net msg send error, errno:{}", errno);
			
		SPDLOG_INFO("msg downlink addr:{}, data_len:{}", msg_temp.cin_addr, msg_temp.data_len);

		train_opt_temp = m_train_list.train_sock_list_find(msg_temp.cin_addr);	
			
		if( TRAIN_SESSION_EXCID == train_opt_temp )
		{
			id_temp = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

			temp = {""};
			for(i = 0; i < msg_temp.data_len; i++)
			{
				sprintf(data, "%x ", msg_temp.msg_data[i]);
				data[3] = 0x00;
				temp += data;
			}

			logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);	

			if( logger_valid )
			{
				logger->info( "[{}: {}: {}] [NET] [OUT] msglen:{} msg:{}", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, msg_temp.data_len, temp);		
			}
		}
		
		// std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
}
    

////定时下发心跳报文
void multi_swap_manager::multi_swap_manager_hb_time_sync_exe()
{
	auto_exchange_task task_pop_temp;
	msg_queue data_temp;
	int i;

	std::this_thread::sleep_for(std::chrono::seconds(10));

	while(1)
	{
		// std::this_thread::sleep_for(std::chrono::seconds(m_dev_cfg.heartbeat_cycle));
		std::this_thread::sleep_for(std::chrono::milliseconds(m_dev_cfg.heartbeat_cycle));


		//遍历目前系统里的车辆
		for(i = 0; i < m_database_swap_cnt; i++)
		{
			task_pop_temp.dev_id = m_database_swap_list[i];
			SPDLOG_INFO("hb create m_database_swap_list[{}]={}, dev_id:{}", i, m_database_swap_list[i], task_pop_temp.dev_id);

			task_pop_temp.sequence = 40;
			task_pop_temp.which_task = auto_exchange_task_hb_time_sync_tag;
			task_pop_temp.task.hb_time_sync = 0x01;

			memcpy(data_temp.msg_data, (uint8_t *)(&task_pop_temp), sizeof(task_pop_temp));
			data_temp.swap_id = task_pop_temp.dev_id;
			data_temp.sub_id = 0;
			data_temp.type = SWAP_TASK_OUT;
			scheduler_manager::get_instance()->scheduler_manager_task_msg_queue_push(data_temp);

			// std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}
	}
}


void multi_swap_manager::multi_swap_manager_downlink_heartbeat_msg(void)
{
	net_msg msg_temp;

	int cli_addr_temp = 0;
	int addr_swap_id = 0;

	volatile int seque_down_temp;
	auto_exchange_task task_pop_temp;

	while(1)
	{
		if( !m_dev_downlink_hb_msg.empty() )
		{
			m_dev_downlink_hb_msg.pop(task_pop_temp);

			seque_down_temp = m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id);
			seque_down_temp++;
			task_pop_temp.sequence = seque_down_temp;
			
			int task_id = 0;
			swap_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, task_id);

			msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);
		
			addr_swap_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);
	
			if( (int)task_pop_temp.dev_id != addr_swap_id )
			{
				cli_addr_temp = m_train_list.train_sock_list_get_socket_addr(task_pop_temp.dev_id);
				SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} ", task_pop_temp.dev_id, msg_temp.cin_addr, cli_addr_temp);
				msg_temp.cin_addr = cli_addr_temp;
			}

			if( 0x00 != addr_swap_id )
			{
				m_net_msg_queue.push(msg_temp);
			}

			if(task_pop_temp.which_task == auto_exchange_task_hb_time_sync_tag)
			{
				m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true); 
				m_train_list.swap_list_map_update_comm_sequeue(task_pop_temp.dev_id, seque_down_temp);
			}
			
			SPDLOG_INFO("heartbeat msg send to net downlink queue swap_id:{}, cin_addr:{}, which_task:{}, sequence:{}-{}", task_pop_temp.dev_id, msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.swap_list_map_get_comm_sequeue(task_pop_temp.dev_id));
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(2));
	}
}

void multi_swap_manager::multi_swap_manager_train_list_update(auto_exchange_basic_info_mutilp list)
{
	int i;
	int cnt = 0;

	m_database_swap_cnt = list.dev_info_count;

	SPDLOG_INFO("m_database_swap_cnt :{} :{}", m_database_swap_cnt, list.dev_info_count);

	for(i = 0; i < list.dev_info_count; i++)
	{
		if(auto_exchange_enable_state_DEV_ENABLE_STATE_ENABLE == list.dev_info[i].state)
		{
			m_database_swap_list[cnt] = list.dev_info[i].dev_id;
			SPDLOG_INFO("m_database_swap_list :{} :{} :{} :{}", i, cnt, m_database_swap_list[cnt], list.dev_info[i].dev_id);
			cnt++;
		}
	}
}


//从list中删除已下线的车辆
static void delete_outof_system(auto_exchange_basic_info_mutilp &list)
{
	int i;
	int to_delete_idx = -1;

	for(i = 0; i < list.dev_info_count; i++)
	{
		if(list.dev_info[i].state == auto_exchange_enable_state_DEV_ENABLE_STATE_ENABLE)
		{
			if(to_delete_idx >= 0)
			{
				list.dev_info[to_delete_idx] = list.dev_info[i];
				to_delete_idx++;
			}
		}
		else if (to_delete_idx < 0)
			to_delete_idx = i;
	}

	if ((to_delete_idx >= 0) && (list.dev_info_count > to_delete_idx))
	{
		list.dev_info_count = to_delete_idx;
	}

	SPDLOG_INFO("{} swap in system.", list.dev_info_count);
	for(i = 0; i < list.dev_info_count; i++)
		SPDLOG_INFO("cnt:{}, devid:{} - state:{}", list.dev_info_count, list.dev_info[i].dev_id, list.dev_info[i].state);
}


void multi_swap_manager::multi_swap_manager_heart_beat_thread_exe()
{
	auto_exchange_dev_state dev_state_temp;
	auto_exchange_agent_state dev_agent_state;
	msg_queue state_data;
	int list_id_temp[20];
	event_exception exce_temp;
	int dev_cnt = 0;
	long start_timedif;
	long timedif;
	_train_map_opt_tab dev_find_result;
	struct timespec tic_dev;
	struct timespec tic_curr;
	uint32_t cin_addr;
	uint16_t except_dev_id;
	dev_state_net dev_net_info;
	bool state_valid_flag;
	int i;

	
	while(1)
	{

		delete_outof_system(m_swap_info);

		if( 0 == m_swap_info.dev_info_count )
		{
			m_swap_info.dev_info_count = 0x00;
		}
		else
			multi_swap_manager_train_list_update(m_swap_info);

	
		//获取当前的单调时钟时间
		clock_gettime(CLOCK_MONOTONIC, &tic_curr);	

		dev_cnt = m_train_list.train_list_map_get_size();

		//计算两个时间点之间的时间差，微秒
		start_timedif = MILLION*(tic_curr.tv_sec-m_start_tick.tv_sec)+(tic_curr.tv_nsec-m_start_tick.tv_nsec)/1000;

		// if( start_timedif > (m_dev_cfg.heartbeat_timeout * 1000 * 1000) ) 
		if( start_timedif > START_UP_HEART_BEAT_TIME_OUT )
		{
			//首先查找是否有database存在但是list列表中没有的车，若存在，需要上报异常
			for(i = 0; i < m_swap_info.dev_info_count; i++)
			{
				dev_find_result = m_train_list.train_list_map_find(m_swap_info.dev_info[i].dev_id);

				if( TRAIN_SESSION_NOT_FIND == dev_find_result )
				{
					dev_state_temp.dev_id = m_swap_info.dev_info[i].dev_id;
					dev_state_temp.pos_state = dev_pos_calib_state_STATE_ERROR;
					dev_state_temp.curr_state = auto_exchange_device_state_DEV_UNKNOWN;
					dev_state_temp.motion_positon_valid_flag = false;
				
					state_data.type = SWAP_STATE_PUB;
					state_data.swap_id = m_swap_info.dev_info[i].dev_id;
					memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
					scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);

					exce_temp.which_evt_except = event_exception_except_tag;
					except_info except = dev_except::swap_offline(m_swap_info.dev_info[i].dev_id);
					except.state = exception_state_STATE_OCCURED;
					exce_temp.evt_except.except = except;

					except_dev_id = m_swap_info.dev_info[i].dev_id * 100;
					if (m_train_list.exception_occur(except_dev_id, exce_temp.evt_except.except) > 0)
					{
						if(multi_swap_manager_train_list_find(except.dev))
						{
							state_data.type = SWAP_EXCEP_PUB;
							state_data.swap_id = except.dev;
							state_data.sub_id = except.sub_dev;
							memcpy(&state_data.msg_data[0], &exce_temp, sizeof(exce_temp));
							scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
						}
					}
					
					SPDLOG_INFO("train :{} not in the locol list", m_swap_info.dev_info[i].dev_id);
				}
			}
		}

	
		// clock_gettime(CLOCK_MONOTONIC, &tic_curr);

		// 检查已有的车辆中，是否有超时时间发生
		if( 0 != dev_cnt )
		{
			m_train_list.train_list_map_get_all_id(list_id_temp);

			for( i = 0; i < dev_cnt; i++ )
			{
				
				if( !multi_swap_manager_train_list_find(list_id_temp[i]) )
				{
					SPDLOG_INFO("vh[{}] not in the curr list.", list_id_temp[i]);
					
					dev_state_temp.dev_id = list_id_temp[i];
					dev_state_temp.curr_state = auto_exchange_device_state_DEV_UNKNOWN;
					dev_state_temp.motion_positon_valid_flag = false;
					
					state_data.type = SWAP_STATE_PUB;
					state_data.swap_id = list_id_temp[i];
					memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
					scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);

					continue;
				}

				if( m_train_list.train_list_map_get_upload_tick(list_id_temp[i], &tic_dev) )
				{
					//计算时间差
					timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/1000;
					
					if( timedif > (m_dev_cfg.heartbeat_timeout * 1000 * 1000) ) 
					{
						exce_temp.which_evt_except = event_exception_except_tag;
						except_info except = dev_except::swap_offline(list_id_temp[i]);
						except.state = exception_state_STATE_OCCURED;
						exce_temp.evt_except.except = except;
						except_dev_id = list_id_temp[i] * 100;
						if (m_train_list.exception_occur(except_dev_id, exce_temp.evt_except.except) > 0)
						{
							if(multi_swap_manager_train_list_find(except.dev))
							{
								state_data.type = SWAP_EXCEP_PUB;
								state_data.swap_id = except.dev;
								state_data.sub_id = except.sub_dev;
								memcpy(&state_data.msg_data[0], &exce_temp, sizeof(exce_temp));
								scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);
							}				
						}

						SPDLOG_INFO("train :{}  heart beat time out ", exce_temp.evt_except.except.dev);

						
						//车辆掉线状态上报
						dev_state_temp.dev_id = list_id_temp[i];
						dev_state_temp.curr_state = auto_exchange_device_state_DEV_UNKNOWN;
						dev_state_temp.motion_positon_valid_flag = false;
						
						state_data.type = SWAP_STATE_PUB;
						state_data.swap_id = list_id_temp[i];
						memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
						scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);


						//添加心跳超时处理, 删除车辆
						std::unique_lock<std::mutex> delete_lock(m_delete_lock);
						cin_addr = m_train_list.train_list_map_get_dev_addr(list_id_temp[i]);
						m_train_list.train_list_map_delete(list_id_temp[i]);
						m_train_list.train_sock_list_delete(cin_addr);
						m_train_list.train_log_list_delete(list_id_temp[i]);
	
			
						// 从设备map中删除
						epoll_poller_dev_delete(cin_addr);
					
#ifdef MULTI_DEV_DEBUG
						m_train_list.train_dev_list_display();
#endif		
						delete_lock.unlock();
					
					}
				}
			}
		}


		dev_agent_state.agent_work_state = component_state_C_INIT;

		if(0 == m_swap_info.dev_info_count)
		{
			dev_agent_state.agent_work_state = component_state_C_IDLE;
		}

		for(i = 0; i < m_swap_info.dev_info_count; i++)
		{
			state_valid_flag = m_train_list.train_list_map_get_train_state(m_swap_info.dev_info[i].dev_id, &dev_net_info);
			if(state_valid_flag)
			{
				if(0x00 == dev_net_info.dev_run_st)
				{
					dev_agent_state.agent_work_state = component_state_C_RUNNING;
					break;
				}
			}
			else
				dev_agent_state.agent_work_state = component_state_C_UNKNOW;
		}

		state_data.type = SWAP_AGENT_STATE_PUB;
		state_data.swap_id = m_swap_info.dev_info[i].dev_id;
		memcpy(&state_data.msg_data[0], &dev_agent_state, sizeof(dev_agent_state));
		scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);

		std::this_thread::sleep_for(std::chrono::seconds(2));
	}
}


void multi_swap_manager::multi_swap_manager_task_state_report_thread_exe()
{
	auto_exchange_task_state swap_task_state;
	msg_queue state_data;
	int i;
	bool state_valid_flag = false;

	std::unordered_map<int, timer> swap_timers;
    std::unordered_map<int, auto_exchange_task_state> swap_old_state;

	for(i = 0; i < (int)m_swap_info.dev_info_count; i++)
	{
		swap_timers[m_swap_info.dev_info[i].dev_id].start();
        swap_old_state[m_swap_info.dev_info[i].dev_id] = {};
	}

	while(1)
	{
		for(i = 0; i < (int)m_swap_info.dev_info_count; i++)
		{
			int swap_id = m_swap_info.dev_info[i].dev_id;
			state_valid_flag = m_train_list.train_list_map_get_swap_task_state(swap_id, &swap_task_state);
			if(state_valid_flag)
			{
				if((swap_old_state[swap_id].type != swap_task_state.type)
				|| (swap_old_state[swap_id].state != swap_task_state.state)
				|| swap_timers[swap_id].execute_time() > 2000)
				{
					swap_task_state.dev_id = swap_id;

					state_data.type = SWAP_LOAD_TASK_STATE_PUB;
					state_data.swap_id = swap_id;
					memcpy(&state_data.msg_data[0], &swap_task_state, sizeof(swap_task_state));
					scheduler_manager::get_instance()->scheduler_manager_queue_push(state_data);

					swap_old_state[swap_id] = swap_task_state;
					swap_timers[swap_id].start();
				}
			}	
		}
		
		std::this_thread::sleep_for(std::chrono::milliseconds(2));
	}
}


void multi_swap_manager::multi_swap_manager_swap_basic_info_summary()
{
	swap_info info;
	auto_exchange_basic_info_mutilp swap_basic_info;

	while(1)
	{
		if(scheduler_manager::get_instance()->scheduler_manager_get_swap_info(swap_basic_info))
		{
			for(int i = 0; i < swap_basic_info.dev_info_count; i++)
			{
				if(m_train_list.train_list_map_get_train_info(swap_basic_info.dev_info[i].dev_id, info))
				{
					strcpy(swap_basic_info.dev_info[i].ip, inet_ntoa(info.v_addr.sin_addr));
					strcpy(swap_basic_info.dev_info[i].sw_ver, info.sw_version);
					strcpy(swap_basic_info.dev_info[i].hw_ver, info.hw_version);
					
					// SPDLOG_INFO("333IP: {}-{}", inet_ntoa(info.v_addr.sin_addr), swap_basic_info.train_info[i].ip);
				}
			}
		}
		else
		{
			SPDLOG_INFO("get swap list from coreserver failed");
			std::this_thread::sleep_for(std::chrono::milliseconds(10));

			continue;
		}

		m_swap_info = swap_basic_info;


		std::this_thread::sleep_for(std::chrono::seconds(2));
	}

}

void multi_swap_manager::multi_swap_manager_reply_train_info_thread()
{
	while(1)
	{
		scheduler_manager_reply_swap_info();
		std::this_thread::sleep_for(std::chrono::milliseconds(1));		
	}
}


/**@brief	  通过REQ-REP模式向coreserver填充数据
* @param[out] train_basic_info_mutilp *dev_list  --- 获取到的coreserver发布的设备列表
* @return	  当前函数执行结果，用于判断 dev_list的有效性
* - true	  成功
* - false	  失败
*/

int multi_swap_manager::scheduler_manager_reply_swap_info(void) 
{
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t req_msg;
	uint8_t rep_msg_swap_basic_info[auto_exchange_basic_info_mutilp_size];
	
	// 首先检查当前socket的连接状态
	if( !m_swap_info_replayer->connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
	}

	m_swap_info_replayer->recv(req_msg, zmq::recv_flags::none);	

	if (req_msg.size() == 0)
    	return 0;
	
	stream_in = pb_istream_from_buffer((const uint8_t*)req_msg.data(), req_msg.size());
	if (!pb_decode(&stream_in, data_request_fields, &request))
	{
		spdlog::error("pb decode error: {}", stream_in.errmsg);
		return 0;
	}

	if(strncmp(request.key, DATA_KEY_AUTO_EXCHANGE_BASE_INFO, sizeof(request.key)) == 0)
	{
		if(request.type == data_request_cmd_READ)
		{
			stream_out = pb_ostream_from_buffer(rep_msg_swap_basic_info, auto_exchange_basic_info_mutilp_size);
			if (!pb_encode(&stream_out, auto_exchange_basic_info_mutilp_fields, &m_swap_info))
			{
				SPDLOG_LOGGER_DEBUG(spdlog::get("autoexchange_base_info"), "pb encode error: {}", stream_out.errmsg);
				return 0;
			}
			else
				m_swap_info_replayer->send(zmq::buffer(rep_msg_swap_basic_info, stream_out.bytes_written), zmq::send_flags::none);
		}
		else if(request.type == data_request_cmd_WRITE)	
			SPDLOG_INFO("not surpport write command to swap_basic_info");
	}
	else
		SPDLOG_ERROR("error key command: {}", std::string(request.key));

	return 1;
}

//将里程信息写入配置文件
void multi_swap_manager::mileage_info_write_to_cfg_file()
{
	bool state_valid_flag = false;
	dev_state_net dev_net_info;
	std::string mileage_file_name = "/home/<USER>/auto_sort_high_efficient/cfg_file/autoswap_mileage_info.json";
	nlohmann::json j;
    std::ifstream jfile(mileage_file_name.c_str());

	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("read file occurs error: {} -{}", fe.what(), mileage_file_name);
	}

	jfile.close();

	
	for(int i = 0; i < m_swap_info.dev_info_count; i++)
	{
		state_valid_flag = m_train_list.train_list_map_get_train_state(m_swap_info.dev_info[i].dev_id, &dev_net_info);
		if(state_valid_flag)
		{

			for (auto& item : j["mileage_info"])
			{
				if (item["swap_id"] == m_swap_info.dev_info[i].dev_id)
				{
					std::ofstream o(mileage_file_name);
					
					if(dev_net_info.mileage < 0 || dev_net_info.mileage_reference < 0)
					{
						dev_net_info.mileage = 0;
						dev_net_info.mileage_reference = 0;
					}
					
					if(m_train_list.train_list_map_get_train_restart_flag(m_swap_info.dev_info[i].dev_id))
					{
						SPDLOG_INFO("swap:{} restart", m_swap_info.dev_info[i].dev_id);
						item["encoder_mileage"] = 0;
						item["current_mileage"] = 0;
						o << std::setw(4) << j << std::endl;

						m_train_list.train_list_map_update_train_restart_flag(m_swap_info.dev_info[i].dev_id, false);
					}


					std::string encoder_total_mileage_string = item["encoder_total_mileage"].get<std::string>();
					double encoder_total_mileage = std::stod(encoder_total_mileage_string);

					std::string current_total_mileage_string = item["current_total_mileage"].get<std::string>();
					double current_total_mileage = std::stod(current_total_mileage_string);

					int32_t encoder_mileage = item["encoder_mileage"].get<int32_t>();
					int32_t current_mileage = item["current_mileage"].get<int32_t>();
					
					encoder_total_mileage = encoder_total_mileage + (dev_net_info.mileage - encoder_mileage) / 1000.0;
					current_total_mileage = current_total_mileage + (dev_net_info.mileage_reference - current_mileage) / 1000.0;
					
					std::string encoder_total_mileage_temp = std::to_string(encoder_total_mileage);
					std::string current_total_mileage_temp = std::to_string(current_total_mileage);
					
                    item["encoder_total_mileage"] = encoder_total_mileage_temp;
                    item["current_total_mileage"] = current_total_mileage_temp;

					item["encoder_mileage"] = dev_net_info.mileage;
					item["current_mileage"] = dev_net_info.mileage_reference;
										
					o << std::setw(4) << j << std::endl;

				}
			}
		}
	}
}


void multi_swap_manager::multi_swap_manager_mileage_info_statistic_thread()
{
	std::this_thread::sleep_for(std::chrono::seconds(10));

	while(1)
	{
		mileage_info_write_to_cfg_file();

		std::this_thread::sleep_for(std::chrono::seconds(60));
	}	
}



void multi_swap_manager::udp_net_init()
{
	m_locol_server.udp_server_socket_init();

	m_locol_server.udp_server_socket_server_cfg(m_server_addr, m_server_port);
	SPDLOG_INFO("udp_server_socket_server_cfg({}/{}) init ok", m_server_addr, m_server_port);

	m_locol_server.udp_server_socket_bind();

	m_locol_server.udp_server_socket_set_reuseaddr(true);

	m_locol_server.udp_server_socket_set_nonblocking();

    epoll_init(m_locol_server.udp_server_socket_get_fd());

	epoll_add_fd(m_locol_server.udp_server_socket_get_fd(), EPOLLIN, false);

    SPDLOG_INFO("epoll init ok, add:{} to epoll event", m_locol_server.udp_server_socket_get_fd());

	m_recv_worker.start(); 
	SPDLOG_INFO("thread pool init ok");

	clock_gettime(CLOCK_MONOTONIC, &m_start_tick);	
}

void multi_swap_manager::multi_swap_manager_config(dev_agent_cfg *cfg)
{
	m_server_addr = cfg->server_info.server_addr;
	m_server_port = cfg->server_info.server_port;
	m_client_port = cfg->server_info.client_port;

	m_dev_cfg = *cfg;
}



void multi_swap_manager::swap_agent_get_config_from_sch(dev_agent_cfg *cfg)
{
	bool result = false;

	result = scheduler_manager::get_instance()->scheduler_manager_get_swap_map_info(cfg->swap_map);
	SPDLOG_INFO("[DEV] scheduler manager get swap map info result :{}", result);
	
	result = false;
	result = scheduler_manager::get_instance()->scheduler_manager_get_swap_info(cfg->m_swap_basic_info);
	SPDLOG_INFO("[DEV] scheduler manager get swap cnt:{}, result:{}", cfg->m_swap_basic_info.dev_info_count, result);

	multi_swap_manager_config(cfg);
}


void multi_swap_manager::multi_swap_manager_init(zmq::context_t &ctx, dev_agent_cfg *cfg)
{
	swap_agent_get_config_from_sch(cfg);
	std::this_thread::sleep_for(std::chrono::milliseconds(10));
	
	m_swap_info_replayer = new zmq::socket_t {ctx, zmq::socket_type::rep};
	m_swap_info_replayer->bind(SERVICE_AUTO_EXCHANGE_BASE_INFO);

    udp_net_init();
}

void multi_swap_manager::multi_swap_manager_run()
{
	task_msg_new_thread = new std::thread(&multi_swap_manager::multi_swap_manager_task_new_gen_thread_exe, this);
	SPDLOG_INFO("multi_swap_manager_task_new_gen_thread_exe thread init ok");

	heart_beat_thread = new std::thread(&multi_swap_manager::multi_swap_manager_heart_beat_thread_exe, this);
	SPDLOG_INFO("multi_swap_manager_heart_beat_thread_exe thread init ok");

	net_msg_send_thread = new std::thread(&multi_swap_manager::multi_swap_manager_net_msg_thread_exe, this);
	SPDLOG_INFO("multi_swap_manager_net_msg_thread_exe thread init ok");

	task_msg_resend_thread = new std::thread(&multi_swap_manager::multi_swap_manager_task_resend_thread_exe, this);
	SPDLOG_INFO("multi_swap_manager_task_resend_thread_exe thread init ok");

	hb_time_sync_thread = new std::thread(&multi_swap_manager::multi_swap_manager_hb_time_sync_exe, this);
	task_msg_hb_thread = new std::thread(&multi_swap_manager::multi_swap_manager_downlink_heartbeat_msg, this);
	SPDLOG_INFO("multi_swap_manager_hb_time_sync_exe thread init ok");

	dev_rst_msg_thread = new std::thread(&multi_swap_manager::multi_swap_manager_task_dev_reset_gen, this);
	SPDLOG_INFO("multi_swap_manager_task_dev_reset_gen thread init ok");
	
	train_info_summary_thread = new std::thread(&multi_swap_manager::multi_swap_manager_swap_basic_info_summary, this);
	SPDLOG_INFO("multi_swap_manager_swap_basic_info_summary thread init ok");

	train_info_reply_thread = new std::thread(&multi_swap_manager::multi_swap_manager_reply_train_info_thread, this);
	SPDLOG_INFO("multi_swap_manager_reply_train_info_thread thread init ok");

	task_report_thread = new std::thread(&multi_swap_manager::multi_swap_manager_task_state_report_thread_exe, this);
	SPDLOG_INFO("multi_swap_manager_task_state_report_thread_exe thread init ok");

	mileage_info_thread = new std::thread(&multi_swap_manager::multi_swap_manager_mileage_info_statistic_thread, this);
	SPDLOG_INFO("multi_swap_manager_mileage_info_statistic_thread thread init ok");

    epoll_main_loop(3);
}