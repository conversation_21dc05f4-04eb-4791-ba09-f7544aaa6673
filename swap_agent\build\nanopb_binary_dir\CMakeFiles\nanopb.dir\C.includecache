#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h
stdint.h
-
stddef.h
-
stdbool.h
-
string.h
-
limits.h
-
stdlib.h
-
avr/pgmspace.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c
pb_common.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.h
pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c
pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h
pb_decode.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.h
pb_common.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.h
pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c
pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h
pb_encode.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.h
pb_common.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.h
pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb.h

