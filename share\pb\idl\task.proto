syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";


enum task_excep_inof
{
    task_no_exception = 0;
    target_slot_full = 1;
    target_slot_seal = 2;
	task_vehicle_error = 3;
	task_munaul_reason = 4;
}

message volume
{
	uint32 length = 1;
	uint32 width = 2;
	uint32 height = 3;
}

message sorting_task_msg
{
	uint32 sequence = 1;
	uint32 dev_id = 2;
	uint32 scanner_id = 14;
	//任务号， 举例：CQ20200217102058+随机数  字母（2位）+年月日十分秒（16位）+随机数 保证全局唯一即可
	bytes task_id = 3 [(nanopb).max_size = 80, (nanopb).fixed_length = true];
	string gd_codes = 4 [(nanopb).max_length = 512];	//商品码
	uint32 container = 5;								//格口号
	bool task_valid = 6;    //任务有效信息字段
	volume vol = 7;
	uint32 weight = 8;
	uint32 train_id = 9;
	uint32 platform_id = 10;
	float platform_belt_speed = 11;
	repeated uint32 containers = 12 [(nanopb).max_count = 4];
	uint32 gd_codes_count = 13;
}

enum sorting_task_state
{
	INIT = 0;
	DISTRIBUTED = 1;
	TAKE_LOADING =2;
	MOVING = 3;
	UNLOADING = 4;
	FINISHED = 5;
	FINISHED_TOHOSPICE = 6;	//分到了收容口
	FINISHED_MANUALLY = 7;	//手动完成
	SUSPEND = 8;
}

message sorting_task_state_msg
{
	//任务号， 举例：CQ20200217102058+随机数  字母（2位）+年月日十分秒（16位）+随机数 保证全局唯一即可
	bytes task_id = 1 [(nanopb).max_size = 80, (nanopb).fixed_length = true];
	string gd_codes = 2 [(nanopb).max_length = 512];	//商品码
	uint32 container = 3;								//格口号
	sorting_task_state state = 4;						//任务状态
	uint32 train_id = 5;								//分播车车号
	uint32 platform_id = 6;
	uint32 loop_cnt = 7;								//循环圈数
	uint32 exp_info = 8;								//收容原因（任务异常信息)
	task_excep_inof suspend_info = 9;
}
