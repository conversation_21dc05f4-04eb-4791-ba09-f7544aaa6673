#include "thing_agent.hpp"

int thing_agent::init(jd_thingtalk_sdk_t **my_sdk, zmq::context_t &context)
{
    SPDLOG_DEBUG("thing agent init");

    init_sdk(my_sdk);
    SPDLOG_DEBUG("sdk init done");

    thing_interface::get_instance()->init(*my_sdk);
    SPDLOG_DEBUG("thing_interface init done");

    thing_manager::get_instance()->init(context);
    SPDLOG_DEBUG("thing_manager init done");

    jd_thingtalk_sdk_connect(*my_sdk);
    SPDLOG_DEBUG("sdk connect mqtt done");

    return 0;
}

int thing_agent::run()
{
    SPDLOG_DEBUG("thing agent run");

    thing_interface::get_instance()->run();
    SPDLOG_DEBUG("thing interface run");

    thing_manager::get_instance()->run();
    SPDLOG_DEBUG("thing manager run");

    return 0;
}

int thing_agent::init_sdk(jd_thingtalk_sdk_t **my_sdk)
{
    jd_thingtalk_sdk_test();

    /***********************************************************************
     * sdk 配置参数
     ***********************************************************************/
    jd_thingtalk_sdk_config_t *config = jd_thingtalk_sdk_config_create();
    if (config == NULL) {
        SPDLOG_ERROR("sdk config create failed");
        return -1;
    }
    jd_thingtalk_pal_memset(config, 0, sizeof(jd_thingtalk_sdk_config_t));
    jd_thingtalk_pal_strcpy(config->protocol, const_cast<char *>(setting::get_instance()->get_setting().connect_protocol.c_str()));

    // 主机地址和端口号
    config->hostname = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_host_name.c_str());
    config->port = setting::get_instance()->get_setting().sdk_config_host_port;

    // 设备id，用户名和密码
    config->deviceId = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_device_id.c_str());
    config->username = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_device_id.c_str());

    // TLS 证书
    config->cafile = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_ca_path.c_str());
    config->cert = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_cert_path.c_str());
    config->key = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_key_path.c_str());

    // 验证主机名
    config->insecure = true;

    /***********************************************************************
     * sdk 创建
     ***********************************************************************/
    *my_sdk = jd_thingtalk_sdk_create(config);

    /***********************************************************************
     * sdk 初始化
     ***********************************************************************/
    jd_thingtalk_sdk_initialise(*my_sdk);

    /***********************************************************************
     * sdk 设置回调函数
     ***********************************************************************/
    // 设置 连接成功回调函数
    jd_thingtalk_sdk_set_dev_cb_connect(*my_sdk, thing_interface::suresort_device_callback_connect);

    // 设置 连接断开回调函数
    jd_thingtalk_sdk_set_dev_cb_disconnect(*my_sdk, thing_interface::suresort_device_callback_disconnect);

    // 设置 物模型上报响应回调函数
    jd_thingtalk_sdk_set_dev_cb_thmd_post_res(*my_sdk, thing_interface::suresort_device_callback_thingmodel_post_response);

    // 设置 属性设置回调函数
    jd_thingtalk_sdk_set_dev_cb_prop_set(*my_sdk, thing_interface::suresort_device_callback_property_set);

    // 设置 属性获取回调函数
    jd_thingtalk_sdk_set_dev_cb_prop_get(*my_sdk, thing_interface::suresort_device_callback_property_get);

    // 设置 方法调用回调函数
    jd_thingtalk_sdk_set_dev_cb_func_call(*my_sdk, thing_interface::suresort_device_callback_function_call);

    // 设置 自动注册响应回调函数
    jd_thingtalk_sdk_set_dev_cb_reg_res(*my_sdk, thing_interface::suresort_device_callback_register_response);
    
    // 设置 ntp请求回调函数
    //jd_thingtalk_sdk_set_dev_ntp_req_res(*my_sdk, thing_agent::suresort_device_callback_ntp_response);

    //todo::ota

    return 0;
}
