#include "../swap_agent_debug.h"

#include "swap_list.hpp"

#include "share/pb/idl/exception.pb.h"
#include "share/exception_code.hpp"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>
#include <spdlog/async.h>


/**@brief  train_list_map class构造函数
* @param[in]  NULL
* @return     NULL
*/
train_list_map::train_list_map() 
{
	return ;
}

/**@brief  train_list_map class析构函数
* @param[in]  NULL
* @return     NULL
*/
train_list_map::~train_list_map() 
{
	m_train_dev_list.clear();
	m_train_sock_list.clear();
}


/**@brief     下行通信序列号初始化 - m_swap_comm_sequ_list.insert
* @param[in]  int swap_id --- 货箱ID
* @return     NULL
*/
void train_list_map::swap_list_map_insert_comm_sequeue(int swap_id)
{
	int sequence;

	struct tm *tp; 
  	time_t t = time(NULL); 
  	// tp = localtime(&t);
	tp = gmtime(&t);

	m_train_dev_seq_mtx.lock();

	// sequence = (tp->tm_hour*10000+tp->tm_min*100+tp->tm_sec)*1000;
	sequence = (((tp->tm_year-100)&0x3F)<<26)|(((tp->tm_mon+1)&0x0F)<<22)|((tp->tm_mday&0x1F)<<17)|((tp->tm_hour&0x1F)<<12)|((tp->tm_min&0x3F)<<6)|(tp->tm_sec&0x3F);

	m_swap_comm_sequ_list[swap_id] = sequence;
	m_train_dev_seq_mtx.unlock();

	SPDLOG_INFO("swap_list_map_insert_comm_sequeue swap_id:{}, seq:{}", swap_id, sequence);
}


/**@brief    下行通信序列号更新
* @param[in]  int swap_id --- 货箱ID
* @return     NULL
*/
void train_list_map::swap_list_map_update_comm_sequeue(int swap_id, uint32_t sequence)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	swap_comm_sequence_map::iterator  iter = m_swap_comm_sequ_list.find(swap_id);

	// 首先查找当前索引是否存在，若存在才能更新
	if( iter == m_swap_comm_sequ_list.end() )
	{
		return;
	}

	m_swap_comm_sequ_list[swap_id] = sequence;

	SPDLOG_INFO("swap_list_map_update_comm_sequeue, swap_id:{}, seq:{}", swap_id, sequence);
}



/**@brief     车辆任务列表非空查询
* @param[in]  int dev_id --- 指定的车辆号
* @return     空满状态
* - true    ---  空
* - false   ---  非空
*/
bool train_list_map::train_task_list_empty(int dev_id)
{
	std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);

	return m_swap_task_list[dev_id].empty();
}


void train_list_map::train_task_list_clear(int dev_id)
{
	swap_task_map::const_iterator result;

	std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);

	result = m_swap_task_list.find(dev_id);
	
	if( result != m_swap_task_list.end() )
		m_swap_task_list[dev_id].clear();

}


/**@brief     货箱列表指定对象数据更新(上行sequence)
* @param[in]  int swap_id --- 货箱ID
* @return     NULL
*/
void train_list_map::swap_list_map_update_uplink_comm_squence(int swap_id, int seuq_cnt)
{
	swap_info swap_info_temp;

	train_info_map::iterator iter = m_train_dev_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));

	swap_info_temp.v_comm_uplink_seque = seuq_cnt;
	
	m_train_dev_list.at(swap_id) = swap_info_temp;
}


/**@brief     车辆列表指定对象数据更新(下行tick更新)
* @param[in]  int id --- 车辆ID
* @param[in]  struct timespec tick --- 下行数据时间点
* @return     NULL
*/
void train_list_map::train_list_map_update_downlink_tick(int id)
{
	swap_info swap_info_temp;
	struct timespec tick;
	
	//自动获取当前系统时间，并进行更新
	clock_gettime(CLOCK_MONOTONIC, &tick);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_last_msg_downlink_tick = tick;
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     车辆列表指定对象数据更新(软硬件版本更新)
* @param[in]  int id --- 车辆ID
* @param[in] char *sw_version ---软件版本
* @param[in] char *hw_version ---硬件版本
* @return     NULL
*/
void train_list_map::train_list_map_update_sw_hw_version(int id, char *sw_ver, char *hw_ver)
{
	swap_info swap_info_temp;
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	memcpy(swap_info_temp.sw_version, sw_ver, sizeof(swap_info_temp.sw_version));
	memcpy(swap_info_temp.hw_version, hw_ver, sizeof(swap_info_temp.hw_version));

	m_train_dev_list.at(id) = swap_info_temp;
}

/**@brief     车辆列表指定对象数据更新(车头故障码更新)
* @param[in]  int id --- 车辆ID
* @param[in]  uint32_t train_error_no ---车头故障码
* @return     NULL
*/
void train_list_map::train_list_map_update_train_error_no(int id, uint32_t train_error_no)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	swap_info train_info_temp;
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&train_info_temp, &iter->second, sizeof(train_info_temp));
	train_info_temp.train_error_no = train_error_no;
	
	m_train_dev_list.at(id) = train_info_temp;
}

uint32_t train_list_map::train_list_map_get_train_error_no(int id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}
	
	return iter->second.train_error_no;
}

int train_list_map::train_list_map_get_train_info(int id, swap_info &info)
{
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}

	memcpy(&info, &iter->second, sizeof(info));
	
	return 1;

}


/**@brief     车辆列表指定对象数据更新(车辆信息)
* @param[in]  int id --- 车辆ID
* @return     NULL
*/
void train_list_map::train_list_map_update_comm_flag(int id, bool flag)
{
	swap_info swap_info_temp;
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_comm_finish_flag = flag;
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     车辆列表指定对象数据更新(上行tick更新)
* @param[in]  int id --- 车辆ID
* @return     NULL
*/
void train_list_map::train_list_map_update_upload_tick(int id)
{
	swap_info swap_info_temp;
	struct timespec tick;

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	//自动获取当前系统时间，并进行更新
	clock_gettime(CLOCK_MONOTONIC, &tick);
	
	train_info_map::iterator iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}
	
	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_last_msg_upload_tick = tick;
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     车辆列表指定对象数据更新(车辆状态)
* @param[in]  int id --- 车辆ID
* @param[in]  dev_state_net state --- 通过网络上报的车辆状态数据
* @return     NULL
*/
void train_list_map::train_list_map_update_state(int id, dev_state_net state)
{
	swap_info swap_info_temp;
	
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_last_state = state;
	m_train_dev_list.at(id) = swap_info_temp;
}

void train_list_map::train_list_map_update_task_state(int id, task_st dev_task_st)
{
	swap_info swap_info_temp;
	
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.dev_task_state = dev_task_st;
	
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     获取指定对象的最近一次通信的有效序列号, 上行sequence
* @param[in]  int swap_id --- 货箱ID
* @return     有效通信序列号
*/
uint32_t train_list_map::swap_list_map_get_uplink_comm_sequeue(int swap_id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	train_info_map::iterator  iter = m_train_dev_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}
	
	return iter->second.v_comm_uplink_seque;
}


/**@brief     车辆列表指定对象数据重置(当前任务重置)
* @param[in]  int id --- 车辆ID
* @return     NULL
*/
void train_list_map::train_list_map_reset_task_info(int id)
{
	swap_info swap_info_temp;

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if(iter == m_train_dev_list.end())
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_task = TRAIN_TASK_NONE;
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     更新车辆的 socket 列表, 确保每个车辆 ID 只对应一个最新的 IP
 * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
 * @param[in]  int id      --- 指定的车辆ID
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_sock_list_change(uint32_t cin_addr, int id)
{
    std::unique_lock<std::mutex> del_lock(m_train_delete_mtx);

    uint32_t addr_temp = 0x00;

    for (auto &x : m_train_sock_list) // 查找与给定 id 匹配的旧 ip
    {
        if (x.second == id)
        {
            addr_temp = x.first;
        }
    }
    SPDLOG_INFO("id :{}, addr_temp :{} ", id, addr_temp);
    if (0x00 != addr_temp) // 如果找到了匹配的旧 ip，则从列表中删除该 ip
    {
        m_train_sock_list.erase(addr_temp);
    }

    m_train_sock_list[cin_addr] = id; // 将新的 ip 和 id 添加到列表中
    SPDLOG_INFO("id :{} oldip:{}, newip:{}", id, addr_temp, cin_addr);
    del_lock.unlock();

    return TRAIN_SESSION_SUCESS;
}

/**@brief     车辆任务列表指定对象查找
 * @param[in]  int dev_id	 --- 指定的车辆号
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_task_list_find(int dev_id)
{
    std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);

    swap_task_map::const_iterator result;

    result = m_swap_task_list.find(dev_id);

    // SPDLOG_INFO( "[LIST] [Task Queue] result :{} ", result );

    if (result == m_swap_task_list.end())
    {
        OUTPUT_LOG;
        return TRAIN_SESSION_NOT_FIND;
    }
    else
    {
        OUTPUT_LOG;
        return TRAIN_SESSION_EXCID;
    }
}

/**@brief     车辆任务列表对象初始化 - 清除设备ID对应的任务列表
 * @param[in]  int dev_id --- 指定的车辆号
 * @return     bool类型 操作结果
 * - true    ---  操作成功
 * - false  ---  操作失败
 */
bool train_list_map::train_task_list_init(int dev_id)
{
    _train_map_opt_tab result;

    // 首先查找元素是否存在
    result = train_task_list_find(dev_id);
    SPDLOG_INFO("[LIST] [Task Queue] result :{} ", result);

    m_swap_task_list[dev_id].clear();

    SPDLOG_INFO("[LIST] [Task Queue] result :{} ", result);

    return true;
}

/**@brief     车辆列表指定对象查找
 * @param[in]  int id --- 车辆ID
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_list_map_find(int id)
{
    train_info_map::const_iterator result;

    std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

    result = m_train_dev_list.find(id);

    if (result == m_train_dev_list.end())
    {
        return TRAIN_SESSION_NOT_FIND;
    }
    else
    {
        return TRAIN_SESSION_EXCID;
    }
}


_train_map_opt_tab train_list_map::swap_list_task_state_find(int swap_id)
{
    swap_task_info_map::const_iterator result;

    result = m_swap_task_state_list.find(swap_id);

    if (result == m_swap_task_state_list.end())
    {
        return TRAIN_SESSION_NOT_FIND;
    }
    else
    {
        return TRAIN_SESSION_EXCID;
    }
}

/**@brief     车辆列表数据插入
 * @param[in]  int id --- 车辆ID
 * @param[in]  swap_info dev_info --- 车辆信息数据结构体，具体内容见头文件
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_list_map_insert(int id, swap_info dev_info)
{
    _train_map_opt_tab result;

    // 首先查找元素是否存在
    result = train_list_map_find(id);

    std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

    if (TRAIN_SESSION_NOT_FIND == result)
    {
        m_train_dev_list.insert(std::make_pair(id, dev_info));
        return TRAIN_SESSION_SUCESS;
    }
    else
    {
        m_train_dev_list.at(id) = dev_info;
        return TRAIN_SESSION_SUCESS;
    }
}


_train_map_opt_tab train_list_map::swap_list_task_state_insert(int swap_id, auto_exchange_task_state m_swap_task_state)
{
	_train_map_opt_tab result;

	std::lock_guard<std::mutex> opt_lock(m_swap_dev_task_state_mtx);

    // 首先查找元素是否存在
    result = swap_list_task_state_find(swap_id);

    if (TRAIN_SESSION_NOT_FIND == result)
    {
        m_swap_task_state_list.insert(std::make_pair(swap_id, m_swap_task_state));
        return TRAIN_SESSION_SUCESS;
    }
    else
    {
        m_swap_task_state_list.at(swap_id) = m_swap_task_state;
        return TRAIN_SESSION_SUCESS;
    }
}


/**@brief     车辆列表指定对象数据获取(网络消息)
* @param[in]  int id --- 车辆ID
* @param[out]  dev_state_net *state --- 通过网络上报的车辆状态数据
* @return     bool类型 操作结果
* - true    ---  操作成功
* - false  ---  操作失败    
*/
bool train_list_map::train_list_map_get_train_state(int id, dev_state_net *state)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	memcpy(state, &iter->second.v_last_state, sizeof(iter->second.v_last_state));

	SPDLOG_INFO("get info, swap_locate_st:{}-{}", id, iter->second.v_last_state.dev_locate_st);
	
	return true;
}


bool train_list_map::swap_list_map_get_swap_task_state(int id, task_st *swap_task_state)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	memcpy(swap_task_state, &iter->second.dev_task_state, sizeof(iter->second.dev_task_state));

	
	return true;
}



bool train_list_map::train_list_map_get_swap_task_state(int swap_id, auto_exchange_task_state *swap_task_state)
{
	std::lock_guard<std::mutex> opt_lock(m_swap_dev_task_state_mtx);

	swap_task_info_map::iterator  iter = m_swap_task_state_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_swap_task_state_list.end() )
	{
		return false;
	}

	memcpy(swap_task_state, &iter->second, sizeof(iter->second));
	
	return true;
}


/**@brief     获取当前所有活跃车辆的ID信息
* @param[out]  int *id_queue --- 车辆ID数据缓冲区
* @return     NULL
*/
void train_list_map::train_list_map_get_all_id(int *id_queue)
{
	int i = 0;
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	for (auto& x: m_train_dev_list)
	{
		id_queue[i] = x.first;
		i++;
	}
}


/**@brief     当前通信所有对象显示(调试使用)
* @param[in]  NULL
* @return     NULL
*/
void train_list_map::train_sock_list_display(void)
{
	cout << "m_train_sock_list :" << endl;
    
	for (auto& x: m_train_sock_list)
        cout << x.first << ": " << x.second << endl;
    cout << endl;
}


/**@brief     当前车辆列表所有对象显示(调试使用)
 * @param[in]  NULL
 * @return     NULL
 */
void train_list_map::train_dev_list_display(void)
{
    cout << "train_dev_list :" << endl;

    std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

    for (auto &x : m_train_dev_list)
    {
        cout << x.first << ": " << x.second.cin_addr << " - " << htons(x.second.v_addr.sin_port) << endl;
        SPDLOG_INFO("id :{}  cin_addr :{} ", x.first, x.second.cin_addr);
    }
    cout << endl;
}

/**@brief     创建指定车辆的logger对象，用来生成日志数据
 * @param[in]  int dev_id --- 指定的车辆ID
 * @return     NULL
 */
void train_list_map::train_log_list_init_logger(int dev_id)
{
    std::unique_lock<std::mutex> init_lock(m_train_lot_mtx);

    std::shared_ptr<spdlog::logger> logger;

	std::string home_path = getenv("HOME");

    std::string str_log_path = home_path + "/auto_sort_high_efficient/logs/swap_agent/swap_" + to_string(dev_id) + "/";

    std::cout << "log>>: str_log_path " << str_log_path << std::endl;

    std::string log_file_name = str_log_path + "swap_" + to_string(dev_id);

    try
    {
        logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>(to_string(dev_id), log_file_name, 1024 * 1024 * 16, 60);
    }
    catch (spdlog::spdlog_ex &fe)
    {
        SPDLOG_ERROR("open file occurs error: {}", fe.what());
        init_lock.unlock();
        return;
    }

    logger->set_level(spdlog::level::trace);

    spdlog::flush_every(std::chrono::seconds(3));

    // logger->set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%s: %!: %#] [%l] %v");
    logger->set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%l] %v");

    logger->info("initialize for dev:{} log ok", dev_id);

    train_log_list_insert(dev_id, logger);

    init_lock.unlock();
}


/**@brief     获取指定对象的网络通信IP
 * @param[in]  int id --- 指定的车辆ID
* @return     网络通信套接字
*/
uint32_t train_list_map::train_list_map_get_dev_addr(int id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}

	return iter->second.cin_addr;
}


/**@brief     获取指定对象的网络通信套接字
 * @param[in]  int id --- 指定的车辆ID
* @return     网络通信套接字
*/
int train_list_map::train_sock_list_get_socket_addr(int dev_id)
{
	std::unique_lock<std::mutex> del_lock(m_train_delete_mtx);
	uint32_t cin_addr = 0x00;

	for (auto& x: m_train_sock_list)
	{
		if( x.second == dev_id )
		{
			cin_addr = x.first;
		}
	}

	del_lock.unlock();

	return cin_addr;
}


/**@brief     车辆网络列表指定对象查找
 * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_sock_list_find(uint32_t cin_addr)
{
    train_info_sock_map::const_iterator result;

    result = m_train_sock_list.find(cin_addr);

    if (result == m_train_sock_list.end())
    {
        OUTPUT_LOG;
        return TRAIN_SESSION_NOT_FIND;
    }
    else
    {
        OUTPUT_LOG;
        return TRAIN_SESSION_EXCID;
    }
}

/**@brief     车辆网络列表对象插入
 * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
 * @param[in]  int id --- 该网络通信IP对应的车辆ID信息
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_sock_list_insert(uint32_t cin_addr, int id)
{
    _train_map_opt_tab result;

    // 首先查找元素是否存在
    result = train_sock_list_find(cin_addr);

    if (TRAIN_SESSION_NOT_FIND == result)
    {
        m_train_sock_list[cin_addr] = id;
        return TRAIN_SESSION_SUCESS;
    }
    else
    {
        return TRAIN_SESSION_OPT_FIAL;
    }
}

/**@brief     车辆日志列表指定对象查找
 * @param[in]  int dev_id --- 设备ID
 * @return     _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS    ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID     ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_log_list_find(int dev_id)
{
    train_log_map::const_iterator result;

    result = m_train_log_list.find(dev_id);

    if (result == m_train_log_list.end())
    {
        return TRAIN_SESSION_NOT_FIND;
    }
    else
    {
        return TRAIN_SESSION_EXCID;
    }
}

/**@brief	  车辆日志列表对象插入
 * @param[in]  int dev_id --- 设备ID
 * @return	  _train_map_opt_tab 操作结构
 * - TRAIN_SESSION_SUCESS	  ---  操作成功
 * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
 * - TRAIN_SESSION_EXCID	  ---  当前索引已存在
 * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
 */
_train_map_opt_tab train_list_map::train_log_list_insert(int dev_id, std::shared_ptr<spdlog::logger> logger)
{
    _train_map_opt_tab result;

    // 首先查找元素是否存在
    result = train_log_list_find(dev_id);

    if (TRAIN_SESSION_NOT_FIND == result)
    {
        m_train_log_list[dev_id] = logger;
        return TRAIN_SESSION_SUCESS;
    }
    else
    {
        return TRAIN_SESSION_OPT_FIAL;
    }
}


/**@brief     车辆列表指定对象数据更新(当前任务更新)
* @param[in]  int id --- 车辆ID
* @param[in]  _train_task_type task_info --- 车辆任务状态
* @return     NULL
*/
void train_list_map::train_list_map_update_task_info(int id, _train_task_type task_info)
{
	swap_info swap_info_temp;
	

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_task = task_info;
	m_train_dev_list.at(id) = swap_info_temp;
}


/**@brief     获取指定车辆当前任务类型
* @param[in]  int id --- 车辆ID
* @return     车辆任务类型(上包/下包)
*/
_train_task_type train_list_map::train_list_map_get_task_info(int id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if(iter == m_train_dev_list.end())
	{
		return TRAIN_TASK_UNKNOWN;
	}

	return iter->second.v_task;
}

/**@brief    检查软硬件版本号是否为空
* @param[in]  int id --- 车辆ID
* @return     true 为空
*/
bool train_list_map::train_list_map_check_version_empty(int id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if(iter == m_train_dev_list.end())
	{
		return false;
	}

	if((iter->second.sw_version[0] == '\0') && (iter->second.hw_version[0] == '\0'))
		return true;
	else
		return false;
}

/**@brief     获取当前车辆的spdlog执行的logger对象
* @param[in]  int dev_id --- 指定的车辆ID
* @return     对应的spdlog 的logger对象
*/
std::shared_ptr<spdlog::logger> train_list_map::train_log_list_get_logger(int dev_id, bool *valid_flag)
{
	bool result = false;

	train_log_map::iterator  iter = m_train_log_list.find(dev_id);
	
	if( iter == m_train_log_list.end() )
	{
		SPDLOG_INFO( "[LIST] [Logger] no valid logger :{} ", dev_id);
		result = false;
	}
	else
	{
		result = true;
	}

	*valid_flag = result;

	if( result )
	{
		return iter->second;
	}
	else
	{
		return NULL;
	}
}


/**@brief     获取指定对象的最近一次通信的有效序列号,下行sequence
* @param[in]  int swap_id --- 货箱ID
* @return     有效通信序列号
*/
uint32_t train_list_map::swap_list_map_get_comm_sequeue(int swap_id)
{
	swap_comm_sequence_map::iterator  iter = m_swap_comm_sequ_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_swap_comm_sequ_list.end() )
	{
		return 0;
	}

	return iter->second;
}


/**@brief     车辆列表指定对象数据获取(网络数据)
* @param[in]  int id --- 车辆ID
* @param[in]  net_msg msg --- 通过网络上报的车辆状态数据
* @return     NULL
*/
bool train_list_map::train_list_map_get_comm_msg(int id, net_msg *msg)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	memcpy(msg, &iter->second.v_last_msg, sizeof(iter->second.v_last_msg));
	
	return true;
}


/**@brief     车辆列表指定对象数据更新(网络数据)
* @param[in]  int id --- 车辆ID
* @param[in]  train_state_net state --- 通过网络上报的车辆状态数据
* @return     NULL
*/
void train_list_map::train_list_map_update_comm_msg(int id, net_msg msg)
{
	swap_info swap_info_temp;

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));
	swap_info_temp.v_last_msg = msg;
	m_train_dev_list.at(id) = swap_info_temp;

}

/**@brief     车辆列表指定对象数据获取(网络数据)
* @param[in]  int swap_id --- 货箱ID
* @return     NULL
*/
bool train_list_map::platform_list_map_get_comm_ack_flag(int swap_id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator iter = m_train_dev_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	if(iter->second.v_comm_finish_flag == false)
		SPDLOG_INFO( "software log flag swap_id:{}, v_comm_finish_flag:{} ", swap_id, iter->second.v_comm_finish_flag);

		
	return  iter->second.v_comm_finish_flag;
}

/**@brief     车辆任务列表 任务数据存储
* @param[in]  int dev_id --- 指定的车辆号
* @param[in]  auto_exchange_task task --- 该车辆的任务
* @return     NULL
*/
void train_list_map::train_task_list_push(int dev_id, auto_exchange_task task)
{
	std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);

	m_swap_task_list[dev_id].push(task);
}


/**@brief     车辆任务列表非空查询
* @param[in]  int dev_id --- 有任务的车列表
* @param[in]  int dev_cnt --- 任务/车辆的数量
* @return     当前任务队列深度
*/
bool train_list_map::train_task_list_empty_state(int *dev_id, int *dev_cnt)
{
	int cnt_temp = 0x00;

	std::unique_lock<std::mutex> task_lock(swap_task_opt_mutex);
	
	if( m_swap_task_list.empty() )
	{
		*dev_cnt = 0x00;
		task_lock.unlock();
		return false;
	}

	for (auto& x: m_swap_task_list)
	{
		if( !x.second.empty() )
		{
			dev_id[cnt_temp] = x.first;
			cnt_temp++;

			SPDLOG_INFO("soft log m_swap_task_list devid:{}, cnt:{}", x.first, cnt_temp);
		}
	}
    
	*dev_cnt = cnt_temp;
	task_lock.unlock();

	return true;
}



/**@brief     车辆任务列表深度查询
* @param[in]  int dev_id --- 指定的车辆号
* @return     当前任务队列深度
*/
int train_list_map::train_task_list_size(int dev_id)
{
	std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);

	return m_swap_task_list[dev_id].size();
}


/**@brief     车辆任务列表 任务数据存储
* @param[in]  int dev_id --- 指定的车辆号
* @param[in]  auto_exchange_task *task --- 该车辆的任务
* @return     获取数据有效性
* - true    ---  数据有效
* - false   ---  数据无效
*/
bool train_list_map::swap_task_list_pop(int dev_id, auto_exchange_task *task)
{
	auto_exchange_task temp;
	_train_map_opt_tab result;
	
	//首先查找元素是否存在
	result = train_task_list_find(dev_id);

	std::lock_guard<std::mutex> task_lock(swap_task_opt_mutex);
	
	if( TRAIN_SESSION_NOT_FIND == result )
	{
		return false;
	}
	else
	{		
		SPDLOG_INFO( "[LIST] [Task Queue] task push :{} :{} :{}", dev_id, m_swap_task_list[dev_id].empty(), m_swap_task_list[dev_id].size() );
		
		if( !m_swap_task_list[dev_id].empty() )
		{
			m_swap_task_list[dev_id].pop(temp);
			memcpy(task, &temp, sizeof(temp));
			return true;
		}
		else
		{
			return false;
		}
	}
}



/**@brief     获取同当前指定的网络通信套接字绑定的车辆ID
* @param[in]  uint32_t cin_addr --- 指定的网络通信IP
* @return     车辆ID
*/
int train_list_map::train_sock_list_get_id(uint32_t cin_addr)
{
	
	train_info_sock_map::const_iterator result;

	result = m_train_sock_list.find(cin_addr);

	if( result == m_train_sock_list.end() )
	{	
		return 0;
	}
	else
	{
		return result->second;
	}
}

int train_list_map::get_swap_cmd_resend_count(int swap_id)
{
	std::lock_guard<std::mutex> opt_lock(cmd_resend_count_mutex);

	resend_count::iterator  iter = m_swap_cmd_resend_count.find(swap_id);

	if( iter == m_swap_cmd_resend_count.end() )
	{
		m_swap_cmd_resend_count[swap_id] = 0;
		
		return 0;
	}

	return  iter->second;
}

void train_list_map::update_swap_cmd_resend_count(int swap_id, int count)
{
	std::lock_guard<std::mutex> opt_lock(cmd_resend_count_mutex);

	resend_count::iterator  iter = m_swap_cmd_resend_count.find(swap_id);

	if( iter == m_swap_cmd_resend_count.end() )
	{
		m_swap_cmd_resend_count[swap_id] = count;
		return;
	}
	
	m_swap_cmd_resend_count.at(swap_id) = count;
}


/**@brief     获取通信ACK标志
* @param[in]  int id --- 车辆ID
* @param[in]  train_state_net state --- 通过网络上报的车辆状态数据
* @return     NULL
*/
bool train_list_map::train_list_map_get_comm_flag(int id)
{
	swap_info swap_info_temp;
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));

	SPDLOG_INFO( "software log flag devid:{}, v_comm_finish_flag:{} ", id, swap_info_temp.v_comm_finish_flag);

	return swap_info_temp.v_comm_finish_flag;
}

/**@brief     车辆列表当前通信ACK确认完成标志查询
* @param[out]  int *dev_id --- 通信未完成车辆列表
* @param[out]  int *dev_cnt --- 通信未完成的车辆总数
* @return     通信未完成车辆列表及车辆总数
*/
bool train_list_map::train_list_map_get_dev_comm_state(int *dev_id, int *dev_cnt)
{
	int cnt_temp = 0x00;
	
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	if( m_train_dev_list.empty() )
	{
		*dev_cnt = 0x00;
		return false;
	}

	for (auto& x: m_train_dev_list)
	{
		if( !x.second.v_comm_finish_flag )
		{
			dev_id[cnt_temp] = x.first;
			cnt_temp++;
			SPDLOG_INFO( "software log flag no_ack_platform:{}, cnt:{} ", x.first, cnt_temp);
		}
	}
	
	*dev_cnt = cnt_temp;
	
	return true;
}


int train_list_map::exception_cnt_get(uint8_t dev, int component_count)
{
    lock_guard<std::mutex> lck(lock_for_excepts);
    int n = 0;

    for(int i = 0; i <= component_count; i++)
    {
        uint16_t dev_id = dev * 100 + i;
        auto it = pending_excepts.find(dev_id);
        if (it != pending_excepts.end())
        {
            n += it->second.size();
        }
    }
    
    return n;
}


/**@brief     获取指定对象的最新一次数据上行时刻时间信息
* @param[in]  int id --- 车辆ID
* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
* @return     函数执行结果，用来判断数据是否有效
* - false     无效
* - true      有效
*/
bool train_list_map::train_list_map_get_upload_tick(int id, struct timespec *tic_out)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);
	
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	*tic_out = iter->second.v_last_msg_upload_tick;

	return true;
}


/**@brief     获取指定对象的最新一次数据下行时刻时间信息
* @param[in]  int id --- 车辆ID
* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
* @return     函数执行结果，用来判断数据是否有效
* - false     
* - true      
*/
bool train_list_map::train_list_map_get_download_tick(int id, struct timespec *tic_out)
{
	train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能有效返回
	if( iter == m_train_dev_list.end() )
	{
		return false;
	}

	*tic_out = iter->second.v_last_msg_downlink_tick;

	return true;
}


_train_map_opt_tab train_list_map::train_list_map_delete(int id)
{
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	if( m_train_dev_list.find(id) == m_train_dev_list.end())
	{
		return TRAIN_SESSION_NOT_FIND;
	}

	auto result = m_train_dev_list.erase(id);

	if( 0 == result )
	{
		return TRAIN_SESSION_NOT_FIND;
	}
	else
	{
		return TRAIN_SESSION_SUCESS;
	}
}

_train_map_opt_tab train_list_map::train_sock_list_delete(uint32_t cin_addr)
{
	std::unique_lock<std::mutex> del_lock(m_train_delete_mtx);

	if( m_train_sock_list.find(cin_addr) == m_train_sock_list.end())
	{
		del_lock.unlock();
		return TRAIN_SESSION_NOT_FIND;
	}

	auto result = m_train_sock_list.erase(cin_addr);

	del_lock.unlock();

	if( 0 == result )
	{
		return TRAIN_SESSION_NOT_FIND;
	}
	else
	{
		return TRAIN_SESSION_SUCESS;
	}
}


/**@brief     车辆网络列表对象删除
* @return     _train_map_opt_tab 操作结构
* - TRAIN_SESSION_SUCESS    ---  操作成功
* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
* - TRAIN_SESSION_EXCID     ---  当前索引已存在
* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
*/
_train_map_opt_tab train_list_map::train_log_list_delete(int dev_id)
{
	std::unique_lock<std::mutex> del_lock(m_train_delete_mtx);

	if( m_train_log_list.find(dev_id) == m_train_log_list.end())
	{
		del_lock.unlock();
		return TRAIN_SESSION_NOT_FIND;
	}

	auto result = m_train_log_list.erase(dev_id);

	spdlog::drop(to_string(dev_id));

	del_lock.unlock();


	if( 0 == result )
	{
		return TRAIN_SESSION_NOT_FIND;
	}
	else
	{
		return TRAIN_SESSION_SUCESS;
	}
}

#if 0
int train_list_map::exception_occur(const except_info &except)
{
	/*
	 * exception 需要缓存，以便后续恢复。
	 * event 不需要恢复，也不需要缓存
	*/
	lock_guard<std::mutex> lck(lock_for_excepts);
	std::list<except_info> &pending_list = pending_excepts[except.dev];
	for (auto const &e : pending_list)
	{
		/*已存在, 则不再重复插入*/
		if ((e.src == except.src) && (e.dev == except.dev) && (e.code == except.code) && (e.sub_code == except.sub_code))
		{
			return 0;
		}
	}

	SPDLOG_INFO("exception occur: [{}.{}]: {}-{}-({})", except.src, except.dev, except.code, except.sub_code, std::string(except.description));
	pending_list.push_back(except);

	return 1;
}
#endif


_train_map_opt_tab train_list_map::pending_excepts_find(int devid)
{
	train_except_map::const_iterator result;

	std::lock_guard<std::mutex> opt_lock(lock_for_excepts);

	result = pending_excepts.find(devid);

	if( result == pending_excepts.end() )
	{
		return TRAIN_SESSION_NOT_FIND;
	}
	else
	{
		return TRAIN_SESSION_EXCID;
	}
}

#if 1
int train_list_map::exception_occur(uint16_t devid, const except_info &except)
{
	/*
	 * exception 需要缓存，以便后续恢复。
	 * event 不需要恢复，也不需要缓存
	*/
	lock_guard<std::mutex> lck(lock_for_excepts);
	std::list<except_info> &pending_list = pending_excepts[devid];
	for (auto const &e : pending_list)
	{
		/*已存在,则不再重复插入*/
		if ((e.src == except.src) && (e.dev == except.dev) && (e.sub_dev == except.sub_dev) && (e.code == except.code) && (e.sub_code == except.sub_code))
		{
			return 0;
		}
	}

	SPDLOG_INFO("exception occur: [{}.{}.{}]: {}-{}-({})", except.src, except.dev, except.sub_dev, except.code, except.sub_code, std::string(except.description));
	pending_list.push_back(except);

	return 1;
}
#endif

int train_list_map::devid_to_train_carriage(uint8_t id, uint32_t *dev_id, uint32_t *carriage)
{
    train_info_map::iterator  iter = m_train_dev_list.find(id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}

    *dev_id = (id & 0xF0) >> 4;
    *carriage = id & 0x0F;

    return 1;
}


void train_list_map::travese_all_pending_exception_for_swap(int id, int component_count, std::function<void (const except_info&)> f)
{
	std::list<except_info> *excep;
	lock_guard<std::mutex> lck(lock_for_excepts);

	for(int i = 0; i <= component_count; i++)
	{
		int devid = id * 100 + i;
		try
		{
			excep = &pending_excepts.at(devid);
		}
		catch (const std::out_of_range &oor)
		{
			// SPDLOG_ERROR("train[{}] not in exception list. {}", devid, oor.what());
			// std::this_thread::sleep_for(std::chrono::microseconds(1));
			continue;
		}

		for (const auto e : *excep)
		{
			f(e);
			
			SPDLOG_INFO("travese_all_pending_exception_for_swap, devid:{}, code:{}-{}", devid, e.code, e.sub_code);
		}
	}
}


/**@brief     处理异常信息并从异常列表中移除特定的异常
* @param[in]  int carriage_num --- 载货台数量
* @param[in]  const except_info &except --- 待移除的异常信息
* @return     找到并移除的异常信息数量
*/
int train_list_map::exception_resume(int carriage_num, const except_info &except)
{
	std::list<except_info> *pending;
	lock_guard<std::mutex> lck(lock_for_excepts);
	int found = 0;

	for(int i = 0; i <= carriage_num; i++)
	{
		uint16_t devid = except.dev * 100 + i;

		try
		{
			pending = &pending_excepts.at(devid);
		}
		catch (const std::out_of_range &oor)
		{
			SPDLOG_ERROR("exception_resume train[{}] not in exception list. {}", devid, oor.what());
			continue;
		}

		auto f = [&](const except_info &e) -> bool {
			if ((e.dev == except.dev) && ((e.code == except.code) || (except.code == EXCEPTION_ALL)) &&
				((e.sub_code == except.sub_code) || (except.sub_code == EXCEPTION_ALL)))
			{
				++found;
				SPDLOG_INFO("exception reset: [{}.{}.{}]: {}-{}-({})", e.src, e.dev, e.sub_dev, e.code, e.sub_code, std::string(e.description));
				return true;
			}
			else
				return false;
		};

		pending->remove_if(f);
	}

	return found;	
}

/**@brief     处理异常信息并从异常列表中移除对应车辆的所有异常
* @param[in]  int swap_id --- 车号
*/
void train_list_map::exception_resume_all(int swap_id)
{
	lock_guard<std::mutex> lck(lock_for_excepts);

	for (auto it = pending_excepts.begin(); it != pending_excepts.end();)
	{
		if ((it->first >= swap_id) && (it->first < swap_id + 100))
			it = pending_excepts.erase(it);
		else
			++it;
	}
}



/**@brief     车辆列表指定对象数据更新(异常状态更新)
* @param[in]  int id --- 异常车辆ID, 包括车厢号
* @param[in]  int exce_info --- 车辆异常状态数据
* @return     NULL
*/
void train_list_map::train_list_map_update_exce_info(uint8_t id, int exce_info)
{
	swap_info swap_info_temp;
	
	int sub_id = id % 100;
	int swap_id = id / 100;
	
	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));

	swap_info_temp.v_exception[sub_id].exception_code = exce_info;
	
	m_train_dev_list.at(swap_id) = swap_info_temp;
}


//车辆列表指定对象数据更新(上一次心跳中上报的异常码更新)
void train_list_map::train_list_map_update_exce_code(int swap_id, int exce_code, int exce_id)
{
	swap_info swap_info_temp;

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(swap_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));

	swap_info_temp.except_code = exce_code;
	swap_info_temp.except_motor_id = exce_id;
	
	m_train_dev_list.at(swap_id) = swap_info_temp;
}

//获取上一次心跳中上报的异常码
 int train_list_map::train_list_map_get_exce_code(int dev_id, int *exce_code)
 {
	train_info_map::iterator  iter = m_train_dev_list.find(dev_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}

	*exce_code = iter->second.except_code;
	
	return 1;
 }


 //车辆列表指定对象数据更新(设备重启标志)
void train_list_map::train_list_map_update_train_restart_flag(int dev_id, bool flag)
{
	swap_info swap_info_temp;

	std::lock_guard<std::mutex> opt_lock(m_train_dev_list_mtx);

	train_info_map::iterator  iter = m_train_dev_list.find(dev_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return;
	}

	memcpy(&swap_info_temp, &iter->second, sizeof(swap_info_temp));

	swap_info_temp.dev_restart_flag = flag;
	
	m_train_dev_list.at(dev_id) = swap_info_temp;
}

 //获取车辆重启标志
 bool train_list_map::train_list_map_get_train_restart_flag(int dev_id)
 {
	train_info_map::iterator  iter = m_train_dev_list.find(dev_id);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_dev_list.end() )
	{
		return 0;
	}

	return iter->second.dev_restart_flag;
	
 }