cmake_minimum_required(VERSION 3.5)

SET( CROSS_COMPILE OFF )

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()


project(testcase LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 11)

set(CMAKE_CXX_STANDARD_REQUIRED ON)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread ")

add_definitions(-Wall)

add_definitions(-D UNIT_TEST)

include_directories("../")

include_directories("../share/pb/nanopb" ".")

include_directories("../share/libs/include")

link_libraries(spdlog zmq)

add_subdirectory("../share/pb/nanopb" nanopb_binary_dir)
add_subdirectory("../share/pb/idl" idl_binary_dir)


#默认当前文件夹下所有文件均参与编译
aux_source_directory(. DIR_SRCS)

#生成所需文件
add_executable(testcase ${DIR_SRCS}  )

#添加外部库依赖
target_link_libraries(testcase nanopb idl )
