/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include "jd_thingtalk.h"
#include "jd_thingtalk_sdk.h"
#include "jd_thingtalk_sdk_internal.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_mqtt.h"
#include "jd_thingtalk_log.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_time.h"
#include "jd_thingtalk_string.h"
#include "jd_thingtalk_thread.h"

int32_t jd_thingtalk_sdk_test(void)
{
    log_info("hello jd_thingtalk_sdk_c_5.0");
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   创建一个sdk config
 *
 * @return 
 *    sdk config 指针
 * @see None.
 * @note None.
 */
jd_thingtalk_sdk_config_t *jd_thingtalk_sdk_config_create()
{
    jd_thingtalk_sdk_config_t *cfg = NULL;
    cfg = (jd_thingtalk_sdk_config_t *) jd_thingtalk_pal_malloc(sizeof(jd_thingtalk_sdk_config_t));
    if (cfg != NULL) {
        jd_thingtalk_pal_memset(cfg, 0, sizeof(jd_thingtalk_sdk_config_t));
    } else {
        log_error("create jd_thingtalk_sdk_config_t failed");
    }
    return cfg;
}

/**
 * @brief   销毁一个sdk config
 *
 * @param[in] config: sdk 设置参指针
 * @return 
 *	   返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_config_destory(jd_thingtalk_sdk_config_t *config)
{
    if (config != NULL) {
        if (config->hostname != NULL) {
            jd_thingtalk_pal_free(config->hostname);
            config->hostname = NULL;
        }
        if (config->deviceId != NULL) {
            jd_thingtalk_pal_free(config->deviceId);
            config->deviceId = NULL;
        }
        if (config->username != NULL) {
            jd_thingtalk_pal_free(config->username);
            config->username = NULL;
        }
        if (config->password != NULL) {
            jd_thingtalk_pal_free(config->password);
            config->password = NULL;
        }
        if (config->cafile != NULL) {
            jd_thingtalk_pal_free(config->cafile);
            config->cafile = NULL;
        }
        if (config->cert != NULL) {
            jd_thingtalk_pal_free(config->cert);
            config->cert = NULL;
        }
        if (config->key != NULL) {
            jd_thingtalk_pal_free(config->key);
            config->key = NULL;
        }
        if (config->devpubkey != NULL) {
            jd_thingtalk_pal_free(config->devpubkey);
            config->devpubkey = NULL;
        }
        if (config->devprtkey != NULL) {
            jd_thingtalk_pal_free(config->devprtkey);
            config->devprtkey = NULL;
        }
        if (config->platpubkey != NULL) {
            jd_thingtalk_pal_free(config->platpubkey);
            config->platpubkey = NULL;
        }
        jd_thingtalk_pal_free(config);
        config = NULL;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   创建一个sdk
 *
 * @param[in] config: MQTT的设置参指针
 * @return 
 *    sdk 句柄
 * @see None.
 * @note None.
 */
struct jd_thingtalk_sdk_t *jd_thingtalk_sdk_create(jd_thingtalk_sdk_config_t *config)
{
    log_info("Enter jd_thingtalk_sdk_create");
    if (config == NULL) {
        log_info("create sdk fialed, invalidate config!");
        return NULL;
    }

    // 创建 jd_thingtalk_sdk_t
    struct jd_thingtalk_sdk_t *sdk = (struct jd_thingtalk_sdk_t *) jd_thingtalk_pal_malloc(sizeof(struct jd_thingtalk_sdk_t));
    if (sdk == NULL) {
        log_info("create sdk failed");
        return NULL;
    }
    jd_thingtalk_pal_memset(sdk, 0, sizeof(struct jd_thingtalk_sdk_t));

    // 设置配置参数
    sdk->cfg = config;

    // 设置 mqtt 的参数
    jd_thingtalk_pal_strcpy(sdk->mqtt_cfg.protocol, config->protocol);
    sdk->mqtt_cfg.hostname = config->hostname;
    sdk->mqtt_cfg.port = config->port;
    sdk->mqtt_cfg.clientId = config->deviceId;
    sdk->mqtt_cfg.username = config->username;
    if (!jd_thingtalk_pal_strcmp(config->protocol, JD_THINGTALK_SDK_CFG_PROTO_MQTT_TLS)) {
        sdk->mqtt_cfg.password = NULL;
        sdk->mqtt_cfg.cafile = config->cafile;
        sdk->mqtt_cfg.cert = config->cert;
        sdk->mqtt_cfg.key = config->key;
    } 
    else if (!jd_thingtalk_pal_strcmp(config->protocol, JD_THINGTALK_SDK_CFG_PROTO_MQTT_ECC)) {
        // TODO 椭圆加密
        /* 
         * code 
         */
        sdk->mqtt_cfg.password = NULL;
    } else {};
    sdk->mqtt_cfg.insecure = config->insecure;

    // 创建 mqtt 客户端
    sdk->mqtt = jd_thingtalk_pal_mqtt_create(&sdk->mqtt_cfg);
    if (sdk->mqtt == NULL) {
        log_info("create sdk mqtt client failed");
        jd_thingtalk_sdk_destory(sdk);
        return NULL;
    }

    log_info("Leave jd_thingtalk_sdk_create");
    return sdk;
}

/**
 * @brief   销毁一个sdk
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_destory(struct jd_thingtalk_sdk_t *sdk)
{
    if (sdk != NULL) {
        if (sdk->cfg != NULL) {
            sdk->cfg = NULL;
        }
        if (sdk->mqtt != NULL) {
            jd_thingtalk_pal_mqtt_destory(sdk->mqtt);
            sdk->mqtt = NULL;
        }
        jd_thingtalk_pal_free(sdk);
        sdk = NULL;
    }

    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 初始化函数
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_initialise(struct jd_thingtalk_sdk_t *sdk)
{
    log_info("Enter jd_thingtalk_sdk_initialise");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (sdk == NULL) {
        log_error("the input jd_thingtalk_sdk_t pointer is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 设置 mqtt 连接成功回调函数
    jd_thingtalk_pal_mqtt_set_cb_connect(sdk->mqtt, jd_thingtalk_sdk_connect_callback, (void *)sdk);

    // 设置 mqtt 连接断开回调函数
    jd_thingtalk_pal_mqtt_set_cb_disconnect(sdk->mqtt, jd_thingtalk_sdk_disconnect_callback, (void *)sdk);

    // 设置 mqtt 消息回调函数
    jd_thingtalk_pal_mqtt_set_cb_message(sdk->mqtt, jd_thingtalk_sdk_message_callback, (void *)sdk);

    // 设置 mqtt 主题订阅ACK回调函数
    jd_thingtalk_pal_mqtt_set_cb_subscribe(sdk->mqtt, jd_thingtalk_sdk_subscribe_callback, (void *)sdk);

    // 设置 mqtt 消息发布ACK回调函数
    jd_thingtalk_pal_mqtt_set_cb_publish(sdk->mqtt, jd_thingtalk_sdk_publish_callback, (void *)sdk);

    log_info("Leave jd_thingtalk_sdk_initialise");
    return ret;
}

/**
 * @brief   sdk 连接broker函数
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_connect(struct jd_thingtalk_sdk_t *sdk)
{
    log_info("Enter jd_thingtalk_sdk_connect");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;
    if (sdk == NULL) {
        log_error("the input jd_thingtalk_sdk_t pointer is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // mqtt 客户端连接服务器 (broker)
    ret = jd_thingtalk_pal_mqtt_connect(sdk->mqtt, &sdk->mqtt_cfg);

    log_info("Leave jd_thingtalk_sdk_connect");
    return ret;
}

/**
 * @brief   sdk 进入主循环
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_main_loop(struct jd_thingtalk_sdk_t *sdk)
{
    log_info("Enter jd_thingtalk_sdk_main_loop");
    if (sdk == NULL) {
        log_error("the input jd_thingtalk_sdk_t pointer is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // uint32_t yield_time = 200; // milsecond
    uint32_t yield_time_out = 1000;

    // for main loop status
    uint32_t loop_log_gap = 60;
    uint32_t loop_now, loop_last_time = 0;

    while(1) {
        jd_thingtalk_pal_mqtt_yield(sdk->mqtt, yield_time_out);
        
        // TODO 其它逻辑处理
        loop_now = jd_thingtalk_pal_time(NULL);
        if (loop_now - loop_last_time > loop_log_gap) {
            loop_last_time = loop_now;
            log_debug_plain("sdk main loop is alive, utc-%d", loop_now);
        }
    }
    log_info("Leave jd_thingtalk_sdk_main_loop");
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 获取消息id
 *
 * @param[in] prefix: 消息id的前缀
 * @return
 *    消息id字符串地址
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_sdk_gen_uuid(char *uuid)
{
    const char *c = "89ab";
    char *p = uuid;
    int32_t n;

    for( n = 0; n < 16; ++n )
    {
        int32_t b = jd_thingtalk_pal_rand()%255;

        switch( n )
        {
            case 6:
                jd_thingtalk_pal_sprintf(
                    p,
                    "4%x",
                    b%15 );
                break;
            case 8:
                jd_thingtalk_pal_sprintf(
                    p,
                    "%c%x",
                    c[jd_thingtalk_pal_rand()%jd_thingtalk_pal_strlen( c )],
                    b%15 );
                break;
            default:
                jd_thingtalk_pal_sprintf(
                    p,
                    "%02x",
                    b );
                break;
        }

        p += 2;

        switch( n )
        {
            case 3:
            case 5:
            case 7:
            case 9:
                *p++ = '-';
                break;
        }
    }

    *p = 0;

    return 0;
}

/**
 * @brief   sdk 获取消息id
 *
 * @param[in] prefix: 消息id的前缀
 * @return
 *    消息id字符串地址
 * @see None.
 * @note None.
 */
char *jd_thingtalk_sdk_get_messageId(char *prefix)
{
    char *messageId = (char *) jd_thingtalk_pal_malloc(48 * sizeof(char));
    if (messageId == NULL) {
        log_error("memory malloc for get messageId is failded");
    } else {
        jd_thingtalk_sdk_gen_uuid(messageId);
    }
    return messageId;
}

/**
 * @brief   sdk 消息发布函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_message_publish(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;
    if (!jd_thingtalk_pal_strcmp(sdk->cfg->protocol, JD_THINGTALK_SDK_CFG_PROTO_MQTT_ECC)) {
        // TODO 椭圆加密
        ret = jd_thingtalk_pal_mqtt_publish(sdk->mqtt, message);
    }
    else {
        ret = jd_thingtalk_pal_mqtt_publish(sdk->mqtt, message);
    }
    // log_info("Pubish Message: mid ( %d ) \n", message->mid);
    return ret;
}

/**
 * @brief   sdk 消息参数检查
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_message_param_check(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 检查sdk指针
    if (sdk == NULL) {
        log_error("check sdk handle failded: sdk is NULL!");
        return JD_THINGTALK_RET_FAILED;
    }

    // 检查 object namespace
    if (obj_name == NULL) {
        log_error("check object object namesapce failed: object name sapce is NULL!");
        return JD_THINGTALK_RET_FAILED;
    } else {
        if (jd_thingtalk_pal_strcmp(obj_name, "device")) {
            log_error("check obnject namesapce failed: object namespace[%s] is not supportted yet!", obj_name);
            return JD_THINGTALK_RET_FAILED;
        }
    }

    // TODO 检测服务实例名

    return ret;
}


/**
 * @brief   sdk 设置连接成功 回调函数
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_connect: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_connect(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_connect cb_connect)
{
    if ((sdk == NULL) || (cb_connect == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_connect = cb_connect;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设置连接断开 回调函数
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_disconnect: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_disconnect(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_disconnect cb_disconnect)
{
    if ((sdk == NULL) || (cb_disconnect == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_disconnect = cb_disconnect;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设备属性版本号检测
 *
 * @param[in] cur_version: 当前的版本号
 * @param[in] req_version: 请求的版本号
 * @return
 *    返回值：1 表示请求接受，0 表示请求拒绝
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_prop_version_check(uint16_t cur_version, uint16_t req_version)
{
    int32_t cur = (int32_t)cur_version;
    int32_t req = (int32_t)req_version;
    log_info("cur_version:[%d], req_version:[%d] \n", cur_version, req_version);
    int32_t gap = req - cur;
    if (gap > 0) {
        if (gap < JD_THINGTALK_PROP_VERSION_TOLERANCE) {
            return 1;
        } else {
            return 0;
        }
    }
    else if (gap < 0) {
        if (gap + JD_THINGTALK_PROP_VERSION_TOLERANCE < 0) {
            return 1;
        } else {
            return 0;
        }
    }
    else {
        return 0;
    }
}

/**
 * @brief   sdk 设备回调函数设置 属性设置
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_set: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_prop_set(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_prop_set cb_prop_set)
{
    if ((sdk == NULL) || (cb_prop_set == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_prop_set = cb_prop_set;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设备属性设置响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_set_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropSetRes_t *in_res)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if(in_res == NULL) {
        log_error("Input in_res is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_res;
    jd_thingtalk_pal_memset(&msg_res, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_res.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_SET_RES, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_res.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_res.qos = 1;
    msg_res.payload = jd_thingtalk_proto_pack_prop_set_res(in_res);
    if (msg_res.payload != NULL) {
        // 发布响应消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_res);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_res.topic, msg_res.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_res.topic, msg_res.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_res.topic != NULL) {
        jd_thingtalk_pal_free(msg_res.topic);
    }
    if (msg_res.payload != NULL) {
        jd_thingtalk_pal_free(msg_res.payload);
    }

    return ret;
}

/**
 * @brief   sdk 设备回调函数设置 属性设置
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_set: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 当设备断电重启或者断网重连后,云端管理平台会主动下发一次属性设置消息(即使用户不在管理平台或者APP中进行任何下发指令的操作,属性设置指令也会下发),
 *       如果设备断电重启或者断网重连后,自身特有的处理逻辑或者会涉及到一些安全问题,包括但不局限于设备安全,人身安全等,请自行谨慎处理
 */
int32_t jd_thingtalk_sdk_set_dev_cb_prop_get(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_prop_get cb_prop_get)
{
    if ((sdk == NULL) || (cb_prop_get == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_prop_get = cb_prop_get;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设备属性获取响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_get_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropGetRes_t *in_res)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if(in_res == NULL) {
        log_error("Input in_res is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_res;
    jd_thingtalk_pal_memset(&msg_res, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_res.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_GET_RES, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_res.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_res.qos = 1;
    msg_res.payload = jd_thingtalk_proto_pack_prop_get_res(in_res);
    if (msg_res.payload != NULL) {
        // 发布响应消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_res);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_res.topic, msg_res.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_res.topic, msg_res.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_res.topic != NULL) {
        jd_thingtalk_pal_free(msg_res.topic);
    }
    if (msg_res.payload != NULL) {
        jd_thingtalk_pal_free(msg_res.payload);
    }

    return ret;
}

/**
 * @brief   sdk 属性上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 上报的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropPost_t *in_post)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if(in_post == NULL) {
        log_error("Input in_post is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_POST, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;
    msg_mqtt.payload = jd_thingtalk_proto_pack_prop_post(in_post);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    return ret;
}

/**
 * @brief   sdk 设备回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_func_call cb_func_call)
{
    if ((sdk == NULL) || (cb_func_call == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_func_call = cb_func_call;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设备方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_func_call_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoFuncCallRes_t *in_res)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (in_res == NULL) {
        log_error("Input in_res is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_res;
    jd_thingtalk_pal_memset(&msg_res, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_res.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_FUNC_CALL_RES, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_res.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_res.qos = 1;
    msg_res.payload = jd_thingtalk_proto_pack_func_call_res(in_res);
    if (msg_res.payload != NULL) {
        // 发布响应消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_res);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_res.topic, msg_res.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_res.topic, msg_res.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_res.topic != NULL) {
        jd_thingtalk_pal_free(msg_res.topic);
    }
    if (msg_res.payload != NULL) {
        jd_thingtalk_pal_free(msg_res.payload);
    }

    return ret;
}

/**
 * @brief   sdk 事件 上下线状态上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_status: 上下线消息体结构指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_evt_online_status_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoEvtOnlineStatus_t *in_status)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (in_status == NULL) {
        log_error("Input in_status is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    if (in_status->is_online) {
        msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_EVT_ONLINE, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    } else {
        msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_EVT_OFFLINE, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);        
    }
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;
    msg_mqtt.payload = jd_thingtalk_proto_pack_evt_online_status(in_status);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    return ret;
}

/**
 * @brief   sdk 事件 事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_evt_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoEvtPost_t *in_post)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (in_post == NULL) {
        log_error("Input in_post is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_EVT_POST, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;
    msg_mqtt.payload = jd_thingtalk_proto_pack_evt_post(in_post);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存资源
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    return ret;
}

/**
 * @brief   sdk 自动注册请求
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_req: 自动注册请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_reg_request(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoRegReq_t *in_req)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (in_req == NULL) {
        log_error("Input in_req is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_REG, 
                                        obj_name,
                                        sdk->cfg->deviceId,
                                        service_key);
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;
    msg_mqtt.payload = jd_thingtalk_proto_pack_reg_req(in_req);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message topic failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存空间
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    return ret;
}

/**
 * @brief   sdk 设备回调函数设置 自动注册响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_reg_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_reg_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_reg_res cb_reg_res)
{
    if ((sdk == NULL) || (cb_reg_res == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }    
    sdk->dev_cb.on_reg_res = cb_reg_res;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 物模型上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_thing_model_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoThingModelPost_t *in_post)
{
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, service_key)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (in_post == NULL) {
        log_error("Input in_post is NULL");
        return JD_THINGTALK_RET_FAILED;
    }

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST, 
                                    obj_name,
                                    sdk->cfg->deviceId,
                                    service_key);
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;
    msg_mqtt.payload = jd_thingtalk_proto_pack_thing_model_post(in_post);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                    ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存空间
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    return ret;
}

/**
 * @brief   sdk 设备回调函数设置 物模型上报响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_thmd_post_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_thmd_post_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_thmd_post_res cb_thmd_post_res)
{
    if ((sdk == NULL) || (cb_thmd_post_res == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_thmd_post_res = cb_thmd_post_res;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 设备NTP授时请求
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ntp_request(struct jd_thingtalk_sdk_t *sdk)
{
    if (sdk == NULL) {
        return JD_THINGTALK_RET_FAILED;
    }
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // NTP 请求信息结构体
    JDThingTalkProtoNTPReq ntp_req;
    jd_thingtalk_pal_memset(&ntp_req, 0, sizeof(JDThingTalkProtoNTPReq));

    // 定义 jd_thingtalk_mqtt_msg_t 结构体变量
    jd_thingtalk_mqtt_msg_t msg_mqtt;
    jd_thingtalk_pal_memset(&msg_mqtt, 0, sizeof(jd_thingtalk_mqtt_msg_t));

    msg_mqtt.topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_NTP_REQ,
                                    JD_THINGTALK_PROTO_OBJ_NAME_DEV,
                                    sdk->cfg->deviceId,
                                    NULL);
    if (msg_mqtt.topic == NULL) {
        log_error("generate message topic failed");
        return JD_THINGTALK_RET_FAILED;
    }
    msg_mqtt.qos = 1;

    // 请求结构体赋值
    ntp_req.deviceId = sdk->cfg->deviceId;
    ntp_req.messageId = jd_thingtalk_sdk_get_messageId(NULL);
    jd_thingtalk_pal_time_get_timestamp(&ntp_req.devSendTime);

    msg_mqtt.payload = jd_thingtalk_proto_pack_ntp_req(&ntp_req);
    if (msg_mqtt.payload != NULL) {
        // 发布消息
        ret = jd_thingtalk_sdk_message_publish(sdk, &msg_mqtt);
        if (ret == JD_THINGTALK_RET_SUCCESS) {
            log_info("\r\nsdk publish[%s]\r\n%s\r\n",
                    msg_mqtt.topic, msg_mqtt.payload);
        } else {
            log_error("\r\nsdk publish failed[%d] [%s]\r\n%s\r\n",
                ret, msg_mqtt.topic, msg_mqtt.payload);
        }
    } else {
        log_error("generate message payload failed");
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存空间
    if (msg_mqtt.topic != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.topic);
    }
    if (msg_mqtt.payload != NULL) {
        jd_thingtalk_pal_free(msg_mqtt.payload);
    }

    ntp_req.deviceId = NULL;
    jd_thingtalk_proto_free_ntp_req(&ntp_req);

    return ret;
}

/**
 * @brief   sdk 设备回调函数设置 设备NTP授时请求响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_ntp_req_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_ntp_req_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_ntp_req_res cb_ntp_req_res)
{
    if ((sdk == NULL) || (cb_ntp_req_res == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_ntp_req_res = cb_ntp_req_res;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk OTA回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_ota_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_ota_func_call cb_func_call)
{
    if ((sdk == NULL) || (cb_func_call == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_ota_fun_call = cb_func_call;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk OTA方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_func_call_response(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoFuncCallRes_t *in_res)
{
    return jd_thingtalk_sdk_dev_func_call_response(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_OTA, in_res);
}

/**
 * @brief   sdk ota 当前状态上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_state_changed_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name,
            char *task_id, JD_THINGTALK_PROTO_OTA_STATE_T state, JD_THINGTALK_PROTO_OTA_ERROR_T e_code, int8_t progress)
{
    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_OTA)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    JDThingTalkProtoEvtPost_t evt_post;
    jd_thingtalk_pal_memset(&evt_post, 0, sizeof(JDThingTalkProtoEvtPost_t));
    evt_post.deviceId = sdk->cfg->deviceId;
#ifndef JD_THINGTALK_TIMESTAMP_MS
    evt_post.timestamp = jd_thingtalk_pal_time(NULL);
#else
    jd_thingtalk_time_stamp_t now = {0};
    jd_thingtalk_pal_time_get_timestamp(&now);
    evt_post.timestamp = (TIMESTMAP_T)now.second * 1000 + now.ms;
#endif
    evt_post.messageId = jd_thingtalk_sdk_get_messageId(NULL);
    evt_post.evt_num = 1;
    evt_post.events = (JDThingTalkProtoEvtPostEvt_t **) jd_thingtalk_pal_malloc (evt_post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    jd_thingtalk_pal_memset(evt_post.events, 0, evt_post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    evt_post.events[0] = jd_thingtalk_proto_ota_create_current_state(JD_THINGTALK_PROTO_OTA_EVT_PROGRESS, task_id, state, e_code, progress);

    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 发布事件
    ret = jd_thingtalk_sdk_dev_evt_post(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_OTA, &evt_post);

    // 释放内存资源
    evt_post.deviceId = NULL;
    jd_thingtalk_proto_free_evt_post(&evt_post);

    return ret;
}

/**
 * @brief   sdk ota 版本号变化上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] object_id: 子设备的设备标识，可为空，表示自身的
 * @param[in] version: 当前的升级包版本号
 * @param[in] type: 升级对象的类型
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_version_changed_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name,
            char *object_id, char *version, char *type)
{
    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_OTA)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    JDThingTalkProtoEvtPost_t evt_post;
    jd_thingtalk_pal_memset(&evt_post, 0, sizeof(JDThingTalkProtoEvtPost_t));
    evt_post.deviceId = sdk->cfg->deviceId;
#ifndef JD_THINGTALK_TIMESTAMP_MS
    evt_post.timestamp = jd_thingtalk_pal_time(NULL);
#else
    jd_thingtalk_time_stamp_t now = {0};
    jd_thingtalk_pal_time_get_timestamp(&now);
    evt_post.timestamp = (TIMESTMAP_T)now.second * 1000 + now.ms;
#endif
    evt_post.messageId = jd_thingtalk_sdk_get_messageId(NULL);
    evt_post.evt_num = 1;
    evt_post.events = (JDThingTalkProtoEvtPostEvt_t **) jd_thingtalk_pal_malloc (evt_post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    jd_thingtalk_pal_memset(evt_post.events, 0, evt_post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    if (object_id == NULL) {
        evt_post.events[0] = jd_thingtalk_proto_ota_create_evet_version(JD_THINGTALK_PROTO_OTA_EVT_VERSION,
                                    version, sdk->cfg->deviceId, type);
    }
    else {
        evt_post.events[0] = jd_thingtalk_proto_ota_create_evet_version(JD_THINGTALK_PROTO_OTA_EVT_VERSION,
                                    version, object_id, type);
    }

    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 发布事件
    ret = jd_thingtalk_sdk_dev_evt_post(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_OTA, &evt_post);

    // 释放内存资源
    evt_post.deviceId = NULL;
    jd_thingtalk_proto_free_evt_post(&evt_post);

    return ret;
}

/**
 * @brief   sdk 连接代理回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_agent_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_agent_func_call cb_func_call)
{
    if ((sdk == NULL) || (cb_func_call == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_agent_func_call = cb_func_call;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 连接代理方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_func_call_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoFuncCallRes_t *in_res)
{
    return jd_thingtalk_sdk_dev_func_call_response(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_res);
}

/**
 * @brief   sdk 连接代理 设备拓扑事件上报
 *
 */
static int32_t jd_thingtalk_sdk_agent_event_topology(struct jd_thingtalk_sdk_t *sdk, char *key, JDThingTalkProtoSubDeviceInfo_t *sub_dev_info)
{
    JDThingTalkProtoEvtPost_t post;
    jd_thingtalk_pal_memset(&post, 0, sizeof(JDThingTalkProtoEvtPost_t));

    post.evt_num = 1;
    post.events = (JDThingTalkProtoEvtPostEvt_t **) jd_thingtalk_pal_malloc(post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    jd_thingtalk_pal_memset(post.events, 0, post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));

    post.events[0] = (JDThingTalkProtoEvtPostEvt_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoEvtPostEvt_t));
    jd_thingtalk_pal_memset(post.events[0], 0, sizeof(JDThingTalkProtoEvtPostEvt_t));

    // 添加事件实例名
    post.events[0]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(key) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(post.events[0]->key, key);

    // 添加 事件参数
    post.events[0]->param_num = 1;
    post.events[0]->parameters = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
            post.events[0]->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(post.events[0]->parameters, 0, post.events[0]->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    post.events[0]->parameters[0] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    jd_thingtalk_pal_memset(post.events[0]->parameters[0], 0, sizeof(JDThingTalkProtoKeyValue_t));
    post.events[0]->parameters[0]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
    jd_thingtalk_pal_strcpy(post.events[0]->parameters[0]->key, "devices");

    // 子设备信息
    post.events[0]->parameters[0]->value = jd_thingtalk_proto_keyvalue_pack_subdevinfo(sub_dev_info);

    // 其它信息
    post.deviceId = sdk->cfg->deviceId;
    post.messageId = jd_thingtalk_sdk_get_messageId(NULL);
#ifndef JD_THINGTALK_TIMESTAMP_MS
    post.timestamp = jd_thingtalk_pal_time(NULL);
#else
    jd_thingtalk_time_stamp_t now = {0};
    jd_thingtalk_pal_time_get_timestamp(&now);
    post.timestamp = (TIMESTMAP_T)now.second * 1000 + now.ms;
#endif

    // 事件上报
    int32_t ret = jd_thingtalk_sdk_dev_evt_post(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, &post);

    // 释放内存
    post.deviceId = NULL;
    jd_thingtalk_proto_free_evt_post(&post);
    return ret;
}

/**
 * @brief   sdk 连接代理 设备绑定事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] sub_dev_info: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_event_add_devices(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoSubDeviceInfo_t *sub_dev_info)
{
    return jd_thingtalk_sdk_agent_event_topology(sdk, JD_THINGTALK_PROTO_AGENT_EVENT_ADD_DEV, sub_dev_info);
}

/**
 * @brief   sdk 连接代理 设备解绑事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] sub_dev_info: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_event_delete_devices(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoSubDeviceInfo_t *sub_dev_info)
{
    return jd_thingtalk_sdk_agent_event_topology(sdk, JD_THINGTALK_PROTO_AGENT_EVENT_DEL_DEV, sub_dev_info);
}

/**
 * @brief   sdk 子设备下线上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] key: 上线、下线实例名
 * @param[in] sub_dev: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_sdk_sub_dev_online_status_post(struct jd_thingtalk_sdk_t *sdk, char *key, JDThingTalkProtoSubDevice_t *sub_dev)
{
    JDThingTalkProtoEvtPost_t post;
    jd_thingtalk_pal_memset(&post, 0, sizeof(JDThingTalkProtoEvtPost_t));

    post.evt_num = 1;
    post.events = (JDThingTalkProtoEvtPostEvt_t **) jd_thingtalk_pal_malloc(post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));
    jd_thingtalk_pal_memset(post.events, 0, post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));

    post.events[0] = (JDThingTalkProtoEvtPostEvt_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoEvtPostEvt_t));
    jd_thingtalk_pal_memset(post.events[0], 0, sizeof(JDThingTalkProtoEvtPostEvt_t));

    // 添加事件实例名
    post.events[0]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(key) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(post.events[0]->key, key);

    // 添加 事件参数
    post.events[0]->param_num = 1;
    post.events[0]->parameters = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
            post.events[0]->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(post.events[0]->parameters, 0, post.events[0]->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    post.events[0]->parameters[0] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    jd_thingtalk_pal_memset(post.events[0]->parameters[0], 0, sizeof(JDThingTalkProtoKeyValue_t));
    post.events[0]->parameters[0]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
    jd_thingtalk_pal_strcpy(post.events[0]->parameters[0]->key, "devices");

    // 子设备信息
    JDThingTalkProtoSubDeviceInfo_t sub_info;
    sub_info.dev_num = 1;
    sub_info.subDev = (JDThingTalkProtoSubDevice_t **) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoSubDevice_t *));
    sub_info.subDev[0] = sub_dev;
    post.events[0]->parameters[0]->value = jd_thingtalk_proto_keyvalue_pack_subdevinfo(&sub_info);
    jd_thingtalk_pal_free(sub_info.subDev);

    // 其它信息
    post.deviceId = sdk->cfg->deviceId;
    post.messageId = jd_thingtalk_sdk_get_messageId(NULL);
#ifndef JD_THINGTALK_TIMESTAMP_MS
    post.timestamp = jd_thingtalk_pal_time(NULL);
#else
    jd_thingtalk_time_stamp_t now = {0};
    jd_thingtalk_pal_time_get_timestamp(&now);
    post.timestamp = (TIMESTMAP_T)now.second * 1000 + now.ms;
#endif

    // 事件上报
    int32_t ret = jd_thingtalk_sdk_dev_evt_post(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, &post);

    // 释放内存
    post.deviceId = NULL;
    jd_thingtalk_proto_free_evt_post(&post);

    return ret;
}

/**
 * @brief   sdk 子设备上线上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] sub_dev: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_online_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoSubDevice_t *sub_dev)
{
    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_AGENT)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (sub_dev == NULL) {
        log_error("Pointer to Subdevice is Empty!!");
        return JD_THINGTALK_RET_FAILED;
    }
    return jd_thingtalk_sdk_sub_dev_online_status_post(sdk, JD_THINGTALK_PROTO_AGENT_EVENT_ONLINE, sub_dev);
}

/**
 * @brief   sdk 子设备下线上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] sub_dev: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_offline_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoSubDevice_t *sub_dev)
{
    if (jd_thingtalk_sdk_message_param_check(sdk, obj_name, JD_THINGTALK_PROTO_SERVICE_AGENT)) {
        log_error("Input parameter check falied!");
        return JD_THINGTALK_RET_FAILED;
    }
    if (sub_dev == NULL) {
        log_error("Pointer to Subdevice is Empty!!");
        return JD_THINGTALK_RET_FAILED;
    }
    return jd_thingtalk_sdk_sub_dev_online_status_post(sdk, JD_THINGTALK_PROTO_AGENT_EVENT_OFFLINE, sub_dev);
}

/**
 * @brief   sdk 子设备回调函数设置 属性设置
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_set: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 当设备断电重启或者断网重连后,云端管理平台会主动下发一次属性设置消息(即使用户不在管理平台或者APP中进行任何下发指令的操作,属性设置指令也会下发),
 *       如果设备断电重启或者断网重连后,自身特有的处理逻辑或者会涉及到一些安全问题,包括但不局限于设备安全,人身安全等,请自行谨慎处理
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_prop_set(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_prop_set cb_prop_set)
{
    if ((sdk == NULL) || (cb_prop_set == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_sub_prop_set = cb_prop_set;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 子设备属性设置响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_set_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropSetRes_t *in_res)
{
    return jd_thingtalk_sdk_dev_prop_set_response(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_res);
}

/**
 * @brief   sdk 子设备回调函数设置 属性获取
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_get: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_prop_get(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_prop_get cb_prop_get)
{
    if ((sdk == NULL) || (cb_prop_get == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_sub_prop_get = cb_prop_get;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 子设备属性获取响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_get_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropGetRes_t *in_res)
{
    return jd_thingtalk_sdk_dev_prop_get_response(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_res); 
}

/**
 * @brief   sdk 子设备回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_func_call cb_func_call)
{
    if ((sdk == NULL) || (cb_func_call == NULL)) {
        return JD_THINGTALK_RET_FAILED;
    }
    sdk->dev_cb.on_sub_func_call = cb_func_call;
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 子设备方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_func_call_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoFuncCallRes_t *in_res)
{
    return jd_thingtalk_sdk_dev_func_call_response(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_res);
}

/**
 * @brief   sdk 子设备属性上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_post: 上报的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_post(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropPost_t *in_post)
{
    return jd_thingtalk_sdk_dev_prop_post(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_post);
}

/**
 * @brief   sdk 子设备事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_evt_post(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoEvtPost_t *in_post)
{
    return jd_thingtalk_sdk_dev_evt_post(sdk, JD_THINGTALK_PROTO_OBJ_NAME_DEV, JD_THINGTALK_PROTO_SERVICE_AGENT, in_post);
}

// end of file
