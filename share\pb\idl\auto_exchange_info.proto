syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";

enum auto_exchange_enable_state
{
	DEV_ENABLE_STATE_RESERVE = 0;	
	DEV_ENABLE_STATE_ENABLE = 1;	
	DEV_ENABLE_STATE_DISABLE = 2;	
};

message workstation_basic_info
{
	uint32 dev_id = 1;
	auto_exchange_enable_state state = 2;
}



message auto_exchange_basic_info
{
	uint32 dev_id = 1;
	auto_exchange_enable_state state = 2;
	string ip = 3 [(nanopb).max_size=16];
	string sw_ver = 4 [(nanopb).max_size=16];
	string hw_ver = 5 [(nanopb).max_size=16];
	
	repeated workstation_basic_info workstation_info = 6 [(nanopb).max_count = 4];
}



message auto_exchange_basic_info_mutilp
{
	repeated auto_exchange_basic_info dev_info = 1 [(nanopb).max_count = 30];
}

