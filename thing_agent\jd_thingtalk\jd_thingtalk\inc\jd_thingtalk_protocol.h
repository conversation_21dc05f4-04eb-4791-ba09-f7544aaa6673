/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 头文件，提供外部使用的相关定义和接口
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#ifndef __JD_THINGTALK_PROTOCOL_H__
#define __JD_THINGTALK_PROTOCOL_H__
#include <stdbool.h>

#include "jd_thingtalk.h"

#include "jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"

#ifdef __cplusplus
extern "C"{
#endif /* __cplusplus */

/**
 * @brief   宏定义 属性个数最大值，方法调用入参最大个数
 *
 */
#define JD_THINGTALK_PROTO_PROP_MAX_NUM        (64)
#define JD_THINGTALK_PROTO_FUNC_IN_MAX_NUM     (64)

/**
 * @brief   对象空间宏定义
 *
 */
#define JD_THINGTALK_PROTO_OBJ_NAME_DEV      ("device")
#define JD_THINGTALK_PROTO_OBJ_NAME_EDGE     ("edge")
#define JD_THINGTALK_PROTO_OBJ_NAME_GROUP    ("group")

/**
 * @brief   内置服务名称宏定义
 *
 */
#define JD_THINGTALK_PROTO_SERVICE_AGENT    ("connection_agent")
#define JD_THINGTALK_PROTO_SERVICE_OTA      ("ota")

/**
 * @brief   connection agent 服务内置子设备操作方法
 *
 */
#define JD_THINGTALK_PROTO_AGENT_FUNC_ADD    ("connection_agent.add-devices")
#define JD_THINGTALK_PROTO_AGENT_FUNC_DEL    ("connection_agent.delete-devices")
#define JD_THINGTALK_PROTO_AGENT_FUNC_UPDATE ("connection_agent.update-devices")
#define JD_THINGTALK_PROTO_AGENT_FUNC_GET    ("connection_agent.get-devices")

/**
 * @brief   connection agent 服务内置 子设备上线下关键字
 *
 */
#define JD_THINGTALK_PROTO_AGENT_EVENT_ONLINE    ("connection_agent.online")
#define JD_THINGTALK_PROTO_AGENT_EVENT_OFFLINE   ("connection_agent.offline")
#define JD_THINGTALK_PROTO_AGENT_EVENT_ADD_DEV   ("connection_agent.add-devices")
#define JD_THINGTALK_PROTO_AGENT_EVENT_DEL_DEV   ("connection_agent.delete-devices")

/**
 * @brief   ota 服务 内置属性、方法和事件
 *
 */
#define JD_THINGTALK_PROTO_OTA_AUTO_UPDATE       ("ota.auto-update")
#define JD_THINGTALK_PROTO_OTA_FUNC_OTA          ("ota.ota")
#define JD_THINGTALK_PROTO_OTA_FUNC_CANCEL_TASK  ("ota.cancel-task")
#define JD_THINGTALK_PROTO_OTA_FUNC_QUERY_STATE  ("ota.query-ota-state")
#define JD_THINGTALK_PROTO_OTA_FUNC_EXEC_TASK    ("ota.exec-task")
#define JD_THINGTALK_PROTO_OTA_EVT_PROGRESS      ("ota.ota-state-changed")
#define JD_THINGTALK_PROTO_OTA_EVT_VERSION       ("ota.ota-version-changed")
#define JD_THINGTALK_PROTO_OTA_TASK_ID           ("task-id")
#define JD_THINGTALK_PROTO_OTA_OBJECT_ID         ("object-id")
#define JD_THINGTALK_PROTO_OTA_VERSION           ("version")
#define JD_THINGTALK_PROTO_OTA_PKG_NAME          ("package-name")
#define JD_THINGTALK_PROTO_OTA_NAME              ("name")
#define JD_THINGTALK_PROTO_OTA_DIFF_BASE_VER     ("diff-base-version")
#define JD_THINGTALK_PROTO_OTA_OTA_LIST          ("ota.ota-list")
#define JD_THINGTALK_PROTO_OTA_OBJECT_TYPE       ("object-type")

/**
 * @brief  设备ota属性实例名宏定义
 * 
 */
#define JD_THINGTALK_PROTO_OTA_PROP_MODEL        ("ota.model")
#define JD_THINGTALK_PROTO_OTA_PROP_AC_DIFF      ("ota.accept-diff")
#define JD_THINGTALK_PROTO_OTA_PROP_SIG_ALG      ("ota.signature-algorithm")
#define JD_THINGTALK_PROTO_OTA_PROP_LIST         ("ota.ota-list")

/**
 * @brief  定义时间戳精度，默认秒级别，支持毫秒级别
 * 
 */
#define JD_THINGTALK_TIMESTAMP_MS
#ifndef JD_THINGTALK_TIMESTAMP_MS
    typedef uint32_t TIMESTMAP_T;
#else
    typedef int64_t TIMESTMAP_T;
#endif

/**
 * @brief  消息解析的层级, 如果定义属性相关报文 "properties" 节点支持json字符串形式
 * 
 */
// #define JD_THINGTALK_PROTO_PROPS_JSON_STR

/**
 * @brief  消息解析的层级, 如果定义方法相关报文 "functions" 节点支持json字符串形式
 * 
 */
// #define JD_THINGTALK_PROTO_FUNCS_JSON_STR

/**
 * @brief  消息解析的层级, 如果定义
 *      JD_THINGTALK_PROTO_EVENTS_JSON_STR 
 *          事件相关报文 "events" 节点支持json字符串形式
 * 
 *  如果定义
 *      JD_THINGTALK_PROTO_EVENT_PARAMS_JSON_STR
 *          事件报文中 "events" 数组中 event的参数列表支持json字符串形式  
 * 
 */
// #define JD_THINGTALK_PROTO_EVENTS_JSON_STR
// #define JD_THINGTALK_PROTO_EVENT_PARAMS_JSON_STR

/**
 * @brief  通用消息主题种类 枚举定义
 */
typedef enum {
    JD_THINGTALK_PROTO_TOPIC_AUTH_POST,
    JD_THINGTALK_PROTO_TOPIC_AUTH_POST_RES,

    JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST,
    JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST_RES,

    JD_THINGTALK_PROTO_TOPIC_EVT_ONLINE,
    JD_THINGTALK_PROTO_TOPIC_EVT_OFFLINE,

    JD_THINGTALK_PROTO_TOPIC_HB_POST,
    JD_THINGTALK_PROTO_TOPIC_HB_POST_RES,
    JD_THINGTALK_PROTO_TOPIC_HB_SET,

    JD_THINGTALK_PROTO_TOPIC_EVT_POST,

    JD_THINGTALK_PROTO_TOPIC_PROP_SET,
    JD_THINGTALK_PROTO_TOPIC_PROP_SET_RES,
    JD_THINGTALK_PROTO_TOPIC_PROP_GET,
    JD_THINGTALK_PROTO_TOPIC_PROP_GET_RES,
    JD_THINGTALK_PROTO_TOPIC_PROP_POST,

    JD_THINGTALK_PROTO_TOPIC_FUNC_CALL,
    JD_THINGTALK_PROTO_TOPIC_FUNC_CALL_RES,

    JD_THINGTALK_PROTO_TOPIC_REG,
    JD_THINGTALK_PROTO_TOPIC_REG_RES,

    JD_THINGTALK_PROTO_TOPIC_NTP_REQ,
    JD_THINGTALK_PROTO_TOPIC_NTP_REQ_RES,
} JD_THINGTALK_PROTO_TOPIC_E;

/**
 * @brief  通用消息主题 类型定义
 */
typedef int32_t  JD_THINGTALK_PROTO_TOPIC_T;

/**
 * @brief  协议 键-值对 结构体定义
 */
typedef struct {
    char        *key;
    char        *value; // cJSON string
} JDThingTalkProtoKeyValue_t;

/**
 * @brief  物模型(thing-model)消息主题 物模型结构体定义
 */
typedef struct {
    char   *id;
    char   *version; // thing-model-version
} JDThingTalkProtoThingModel_t;

/**
 * @brief  物模型(thing-model)消息主题 物模型上报结构体
 */
typedef struct {
    char                    *deviceId;
    TIMESTMAP_T             timestamp;
    char                    *messageId;
    JDThingTalkProtoThingModel_t    thing_model;
} JDThingTalkProtoThingModelPost_t;

/**
 * @brief  物模型(thing-model)消息主题 物模型上报响应结构体
 */
typedef struct {
    char                *deviceId;
    TIMESTMAP_T         timestamp;
    char                *messageId;
    int32_t             code;
    char                *message;
} JDThingTalkProtoThingModelPostRes_t;

/**
 * @brief  属性(properties)消息主题 属性设置 消息体
 */
typedef struct {
    char                *deviceId;
    TIMESTMAP_T         timestamp;
    char                *messageId;
    uint16_t            version;
    int32_t             prop_num;
    JDThingTalkProtoKeyValue_t  **properties;
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
    char                *prop_json;
#endif
} JDThingTalkProtoPropSet_t;

/**
 * @brief  属性(properties)消息主题 属性设置响应 消息体
 */
typedef struct {
    char                *deviceId;
    TIMESTMAP_T          timestamp;
    char                *messageId;
    uint16_t            version;
    int32_t             code;
    char                *message;
    int32_t             prop_num;
    JDThingTalkProtoKeyValue_t  **properties;
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
    char                *prop_json;
#endif
} JDThingTalkProtoPropSetRes_t;

/**
 * @brief  属性(properties)消息主题 属性获取 消息体
 */
typedef struct {
    char                *deviceId;
    TIMESTMAP_T         timestamp;
    char                *messageId;
    int32_t             prop_num;
    JDThingTalkProtoKeyValue_t  **properties;
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
    char                *prop_json;
#endif
} JDThingTalkProtoPropGet_t;

/**
 * @brief  属性(properties)消息主题 属性响应 消息体
 */
typedef JDThingTalkProtoPropSetRes_t JDThingTalkProtoPropGetRes_t;

/**
 * @brief  属性(properties)消息主题 属性上报 消息体
 */
typedef JDThingTalkProtoPropSet_t    JDThingTalkProtoPropPost_t;

/**
 * @brief   属性(properties)消息主题 释放属性设置响应结构体 成员变量的内存空间
 *
 * @param[in] in_set: 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_set_res(JDThingTalkProtoPropSetRes_t *in_res);

/**
 * @brief   属性(properties)消息主题 释放属性获取响应结构体 成员变量的内存空间
 *
 * @param[in] in_res: 属性获取响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_get_res(JDThingTalkProtoPropGetRes_t *in_res);

/**
 * @brief   属性(properties)消息主题 释放属性上报结构体 成员变量的内存空间
 *
 * @param[in] in_post: 属性上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_post(JDThingTalkProtoPropPost_t *in_post);

/**
 * @brief  方法(functions)消息主题 方法调用 消息体 functions成员定义
 */
typedef struct {
    char                *key;
    int32_t             in_num;
    JDThingTalkProtoKeyValue_t  **in;
} JDThingTalkProtoFuncCallFunc_t;

/**
 * @brief  方法(functions)消息主题 方法调用 消息体
 */
typedef struct {
    char                    *deviceId;
    TIMESTMAP_T             timestamp;
    char                    *messageId;
    int32_t                 func_num;
    JDThingTalkProtoFuncCallFunc_t  **functions;
#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
    char                    *func_json;
#endif
} JDThingTalkProtoFuncCall_t;

/**
 * @brief  方法(functions)消息主题 方法调用响应 消息体 functions 成员变量
 */
typedef struct {
    char                *key;
    int32_t             out_num;
    JDThingTalkProtoKeyValue_t  **out;
    char *out_json;
} JDThingTalkProtoFuncCallResFunc_t;

/**
 * @brief  方法(functions)消息主题 方法调用响应 消息体
 */
typedef struct {
    char                        *deviceId;
    TIMESTMAP_T                 timestamp;
    char                        *messageId;
    int32_t                     code;
    char                        *message;
    int32_t                     func_num;
    JDThingTalkProtoFuncCallResFunc_t   **functions;
#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
    char                        *func_json;
#endif
} JDThingTalkProtoFuncCallRes_t;

/**
 * @brief   方法(functions)消息主题 释放方法调用响应结构体 成员变量的内存空间
 *
 * @param[in] in_call: 方法调用结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_func_call_res(JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief  事件(events)消息主题  上下线消息体
 */
typedef struct {
    char                    *deviceId;
    TIMESTMAP_T             timestamp;
    char                    *messageId;
    bool                    is_online;
    JDThingTalkProtoThingModel_t    thing_model;
} JDThingTalkProtoEvtOnlineStatus_t;

/**
 * @brief  事件(events)消息主题  上报消息体 events 成员
 */
typedef struct {
    char                *key;
    int32_t             param_num;
    JDThingTalkProtoKeyValue_t  **parameters;
#ifdef JD_THINGTALK_PROTO_EVENT_PARAMS_JSON_STR
    char *param_json;
#endif
}JDThingTalkProtoEvtPostEvt_t;

/**
 * @brief  事件(events)消息主题  上报消息体
 */
typedef struct {
    char                    *deviceId;
    TIMESTMAP_T             timestamp;
    char                    *messageId;
    int32_t                 evt_num;
    JDThingTalkProtoEvtPostEvt_t    **events;
#ifdef JD_THINGTALK_PROTO_EVENTS_JSON_STR
    char                    *evt_json;
#endif
} JDThingTalkProtoEvtPost_t;

/**
 * @brief   事件(events)消息主题 释放事件上报结构体 成员变量的内存空间
 *
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_evt_post(JDThingTalkProtoEvtPost_t *in_post);

/**
 * @brief   键值对 解析布尔值
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_bool(char *in_value, bool *out_val);

/**
 * @brief   键值对 打包布尔值
 *
 * @param[in] in_bool: 待打包的bool值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_bool(bool in_bool);

/**
 * @brief   键值对 解析 int32_t
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_int32(char *in_value, int32_t *out_val);

/**
 * @brief   键值对 打包 int32_t
 *
 * @param[in] in_int32: 待打包的int32值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_int32(int32_t in_int32);

/**
 * @brief   键值对 解析 double
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_double(char *in_value, double *out_val);

/**
 * @brief   键值对 打包 double
 *
 * @param[in] in_double: 待打包的double值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_double(double in_double);

/**
 * @brief   键值对 解析 string
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回 解析后的值 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_parse_string(char *in_value);

/**
 * @brief   键值对 解析 string
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_string1(char *in_value, char *out_val);

/**
 * @brief   键值对 打包 string
 *
 * @param[in] in_string: 待打包的string
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_string(char *in_string);

/**
 * @brief  自动注册(register) 请求结构体 devcies成员 扩展项定义
 */
typedef struct {
    char        *key;
    char        *value;
    char        *desc;
} JDThingTalkProtoRegReqDevExt_t;

/**
 * @brief  自动注册(register) 请求结构体 devcies 成员结构体定义
 */
typedef struct {
    char                    *hardwareId;
    char                    *vendor;
    char                    *protocol;
    char                    *product;
    char                    *model;
    int32_t                 ext_num;
    JDThingTalkProtoRegReqDevExt_t  **extensions;
} JDThingTalkProtoRegReqDev_t;

/**
 * @brief  自动注册(register) 请求结构体
 */
typedef struct {
    char                    *deviceId;
    TIMESTMAP_T             timestamp;
    char                    *messageId;
    int32_t                 dev_num;
    JDThingTalkProtoRegReqDev_t     **devices;
    char                    cerType[8];
} JDThingTalkProtoRegReq_t;

/**
 * @brief  自动注册(register) 请求响应结构体 devcies成员 配置项定义
 */
typedef struct {
    char        *name;
    char        *type;
    char        *content;
} JDThingTalkProtoRegReqResDevCfg_t;

/**
 * @brief  自动注册(register) 请求响应结构体 devcies成员结构体定义
 */
typedef struct {
    char                        *deviceId;
    char                        *hardwareId;
    uint32_t                    cfg_num;
    JDThingTalkProtoRegReqResDevCfg_t   **config;
} JDThingTalkProtoRegReqResDev_t;

/**
 * @brief  自动注册(register) 请求响应结构体
 */
typedef struct {
    char                        *deviceId;
    TIMESTMAP_T                 timestamp;
    char                        *messageId;
    int32_t                     code;
    char                        *message;
    uint32_t                    dev_num;
    JDThingTalkProtoRegReqResDev_t      **devices;
} JDThingTalkProtoRegReqRes_t;

/**
 * @brief   connection agent 子设备定义
 */
typedef struct {
    char    *name;
    char    *deviceId;
    char    *description;
    char    *modelId;
    char    *modelVersion;
    char    *appCode;
    char    *hardwareId;
    char    *protocolName;
    char    *protocolSpec;
}JDThingTalkProtoSubDevice_t;

/**
 * @brief   内置服务(connection agent) 释放 子设备结构体成员变量
 *
 * @param[in] in_dev: 子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_sub_dev(JDThingTalkProtoSubDevice_t *in_dev);

/**
 * @brief   内置服务(connection agent) 拷贝 子设备结构体成员变量
 *
 * @param[in] dest_dev: 目的子设备信息结构体指针
 * @param[in] src_dev: 源子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_copy_sub_dev(JDThingTalkProtoSubDevice_t *dest_dev, JDThingTalkProtoSubDevice_t *src_dev);

/**
 * @brief   connection agent 子设备信息
 */
typedef struct {
    int32_t             dev_num;
    JDThingTalkProtoSubDevice_t **subDev;
}JDThingTalkProtoSubDeviceInfo_t;

/**
 * @brief   内置服务(connection agent) 释放 子设备信息结构体成员变量
 *
 * @param[in] in_info: 子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_sub_dev_info(JDThingTalkProtoSubDeviceInfo_t *in_info);

/**
 * @brief   内置服务(connection agent) 键值对 解析 connection agent 子设备信息
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_info: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_subdevinfo(char *in_value, JDThingTalkProtoSubDeviceInfo_t *out_info);

/**
 * @brief   内置服务(connection agent) 键值对 打包 connection agent 子设备信息
 *
 * @param[in] in_info: connection agent 子设备信息
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_subdevinfo(JDThingTalkProtoSubDeviceInfo_t *in_info);

/**
 * @brief   内置服务(connection agent) 创建操作子设备方法响应
 *
 * @param[in] func_key: 操作方法的名字
 * @param[in] dev: 子设备信息指针，除get-devices外其余填NULL
 * @param[in] code: 内置方法 返回值
 * @param[in] message: 内置方法 返回信息字符串指针
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_agent_create_opsub_func_Res(
                            char *func_key, 
                            JDThingTalkProtoSubDeviceInfo_t *dev,
                            int32_t code, 
                            char *message);

/**
 * @brief   内置服务(connection agent) 创建子设备在线状态事件
 *
 * @param[in] evt_key: 事件的名字
 * @param[in] dev: 子设备信息指针
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_agent_create_online_event(
                            char *evt_key, 
                            JDThingTalkProtoSubDeviceInfo_t *dev);

/**
 * @brief   ota 创建ota方法调用响应
 *
 * @param[in] func_key: 方法调用的实例名
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_ota_create_func_res(char *func_key);

/**
 * @brief  ota 当前状态枚举
 */
typedef enum {
    JD_THINGTALK_OTA_ST_PREPARE,           // 正在准备
    JD_THINGTALK_OTA_ST_CF_DOWNLOAD,       // 等待用户确认下载
    JD_THINGTALK_OTA_ST_DOWNLOADIND,       // 正在下载
    JD_THINGTALK_OTA_ST_DOWNLOAD_DONE,     // 下载完成
    JD_THINGTALK_OTA_ST_CF_INSTALL,        // 等待用户确认安装
    JD_THINGTALK_OTA_ST_INSTALLING,        // 正在安装
    JD_THINGTALK_OTA_ST_INSTALL_DONE,      // 完成
    JD_THINGTALK_OTA_ST_FAILURE,           // 失败
} JD_THINGTALK_PROTO_OTA_STATE_E;
typedef int32_t  JD_THINGTALK_PROTO_OTA_STATE_T;

/**
 * @brief  ota 状态错误码
 */
typedef enum {
    JD_THINGTALK_OTA_ERROR_NONE,             // 无异常
    JD_THINGTALK_OTA_ERROR_TASK_CL,          // OTA任务已取消
    JD_THINGTALK_OTA_ERROR_DOWNLOAD_CL,      // 用户取消下载
    JD_THINGTALK_OTA_ERROR_INSTALL_CL,       // 用户取消安装
    JD_THINGTALK_OTA_ERROR_DPG_VER_FAIL,     // 差分包基包验证失败
    JD_THINGTALK_OTA_ERROR_DOWNLOAD_FAIL,    // 下载失败
    JD_THINGTALK_OTA_ERROR_SIG_VER_FAIL,     // 签名验证失败
    JD_THINGTALK_OTA_ERROR_DPG_RVT_FAIL,     // 差分包还原失败
    JD_THINGTALK_OTA_ERROR_INSTALL_FAIL,     // 安装失败
} JD_THINGTALK_PROTO_OTA_ERROR_E;
typedef int32_t  JD_THINGTALK_PROTO_OTA_ERROR_T;

/**
 * @brief   ota 创建ota方法调用 状态查询响应
 *
 * @param[in] func_key: 方法调用的实例名
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_ota_create_func_query_res(char *func_key,
                                                             char *task_id,
                                                             int32_t state,
                                                             JD_THINGTALK_PROTO_OTA_ERROR_T e_code,
                                                             int8_t progress);

/**
 * @brief   ota ota-list 属性 数组成员定义
 */
typedef struct {
    char        *package_name;
    int32_t     version;
    int32_t     diff_base_version;
} JDThingTalkProtoOtaListMember_t;

/**
 * @brief   ota ota-list 属性 结构体定义
 */
typedef struct {
    int32_t                 list_len;
    JDThingTalkProtoOtaListMember_t **members;
} JDThingTalkProtoOtaList_t;

/**
 * @brief   ota 键值对 打包 ota-list 信息
 *
 * @param[in] in_list: ota-list 数组
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_ota_list(JDThingTalkProtoOtaList_t *in_list);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __JD_THINGTALK_PROTOCOL_H__  */
