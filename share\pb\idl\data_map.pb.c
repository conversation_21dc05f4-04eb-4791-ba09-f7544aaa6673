/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "data_map.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(data_map, data_map, 4)


PB_BIND(data_map_tunnel, data_map_tunnel, AUTO)


PB_BIND(data_map_container_info, data_map_container_info, AUTO)


PB_BIND(data_map_containers_info, data_map_containers_info, 4)


PB_BIND(data_map_scanner_info, data_map_scanner_info, AUTO)


PB_BIND(data_map_feeders_infomation, data_map_feeders_infomation, AUTO)


PB_BIND(data_map_gray_camera_info, data_map_gray_camera_info, AUTO)


PB_BIND(data_map_feeders_info, data_map_feeders_info, 2)


PB_BIND(data_map_calib_point_info, data_map_calib_point_info, AUTO)


PB_BIND(train_para, train_para, AUTO)


PB_BIND(coordinate_conv_para, coordinate_conv_para, AUTO)







