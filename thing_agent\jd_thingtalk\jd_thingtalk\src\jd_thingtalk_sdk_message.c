/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 消息处理逻辑相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include "jd_thingtalk.h"
#include "jd_thingtalk_sdk.h"
#include "jd_thingtalk_sdk_internal.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_mqtt.h"
#include "jd_thingtalk_log.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_string.h"
#include "jd_thingtalk_time.h"

/**
 * @brief   消息处理逻辑 连接回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] user_data: 用户数据
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_connect_callback(jd_thingtalk_mqtt_t mqtt, void *user_data)
{
    log_info("Enter jd_thingtalk_sdk_connect_callback");
    struct jd_thingtalk_sdk_t *sdk = (struct jd_thingtalk_sdk_t *)user_data;

    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 订阅消息主题 通配订阅
    /*
    char *sub_topic = jd_thingtalk_proto_wildcard_topic("+", sdk->cfg->deviceId);
    if (sub_topic != NULL) {
        log_info("sdk subscribe device wildcard topic:%s", sub_topic);            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1);
        jd_thingtalk_pal_free(sub_topic);
    }
    */
    // 订阅消息主题 只订阅需要的消息主题
    char *sub_topic = NULL;

    // 订阅消息主题  设备NTP授时响应
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_NTP_REQ_RES, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 物模型上报响应 (thing model post response)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST_RES, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 属性设置 (properties set)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_SET, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_SET, "+", sdk->cfg->deviceId, "+");
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 属性获取 (properties get)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_GET, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_PROP_GET, "+", sdk->cfg->deviceId, "+");
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 方法调用 (functions call)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_FUNC_CALL, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_FUNC_CALL, "+", sdk->cfg->deviceId, "+");
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 认证响应 (auth response)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_AUTH_POST_RES, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_AUTH_POST_RES, "+", sdk->cfg->deviceId, "+");
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }

    // 订阅消息主题 自动注册响应 (register response)
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_REG_RES, "+", sdk->cfg->deviceId, NULL);
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }
    sub_topic = jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_REG_RES, "+", sdk->cfg->deviceId, "+");
    if (sub_topic != NULL) {            
        jd_thingtalk_pal_mqtt_subscribe(mqtt, sub_topic, 1, NULL);
        log_info("sdk subscribe topic:%s", sub_topic);
        jd_thingtalk_pal_free(sub_topic);
        sub_topic = NULL;
    }


    // 调用设备回调函数 设备连接成功处理函数
    if (sdk->dev_cb.on_connect != NULL) {
        log_info("call function of devcie callback for connected");
        sdk->dev_cb.on_connect(sdk);
    }
    
    log_info("Leave jd_thingtalk_sdk_connect_callback");
    return ret;
}

/**
 * @brief   sdk 消息处理逻辑 连接断开回调函数
 *
 * @param[in] mqtt: mqtt_handler: MQTT 客户端句柄
 * @param[in] user_data: 用户数据
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_disconnect_callback(jd_thingtalk_mqtt_t mqtt, void *user_data)
{
    log_info("Enter jd_thingtalk_sdk_disconnect_callback");
    struct jd_thingtalk_sdk_t *sdk = (struct jd_thingtalk_sdk_t *)user_data;

    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 调用设备回调函数 断开连接处理函数
    if (sdk->dev_cb.on_disconnect != NULL) {
        log_info("call function of devcie callback for disconnected");
        sdk->dev_cb.on_disconnect(sdk);
    }

    log_info("Leave jd_thingtalk_sdk_disconnect_callback");

    return ret;
}

/**
 * @brief   sdk 消息处理逻辑 消息回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_message_callback(jd_thingtalk_mqtt_t mqtt, jd_thingtalk_mqtt_msg_t *message, void *user_data)
{
    log_info("Enter jd_thingtalk_sdk_message_callback");
    log_info("Revc message on topic:\r\n\t%s\r\n", message->topic);
    struct jd_thingtalk_sdk_t *sdk = (struct jd_thingtalk_sdk_t *)user_data;

    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 解析消息主题
    JDThingTalkProtoTopic_t *parsed_topic = jd_thingtalk_proto_topic_parse(message->topic);
    if (parsed_topic != NULL) {
        /* 
        log_info("\r\nretain:%s\r\nversion:%s\r\nobj_name:%s\r\ndeviceId:%s\r\nserver_key:%s\r\ntype_name:%s\r\ncommand:%s\r\nresponse:%s\r\n", 
                parsed_topic->retain, 
                parsed_topic->version,
                parsed_topic->obj_name,
                parsed_topic->deviceId,
                parsed_topic->service_key,
                parsed_topic->type_name,
                parsed_topic->command,
                parsed_topic->response);
        */

        // 根据消息主题 进行消息分发处理
        if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_PROP)) {
            // 属性 (properties) 相关消息处理
            if (parsed_topic->response != NULL) {
                log_info("[warnning]response for properties command [%s] is ignored", parsed_topic->command);
            } else {
                if (!jd_thingtalk_pal_strcmp(parsed_topic->command, "set")) {
                    // 处理 属性设置 (properties set) 消息
                    log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                    jd_thingtalk_msg_proc_prop_set(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
                } 
                else if (!jd_thingtalk_pal_strcmp(parsed_topic->command, "get")) {
                    // 处理 属性获取 (properties get) 消息
                    log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                    jd_thingtalk_msg_proc_prop_get(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
                } 
                else {
                    // TODO 其它不支持
                    log_info("[warnning]properties command [%s] is ignored", parsed_topic->command);
                }
            }
        } 
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_EVT)) {
            // 事件 (events) 相关消息处理
        }
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_FUNC)) {
            // 方法 (functions) 相关消息处理
            if (parsed_topic->response != NULL) {
                log_info("[warnning]response for functions command [%s] is ignored", parsed_topic->command);
            } else {
                if (!jd_thingtalk_pal_strcmp(parsed_topic->command, "call")) {
                    // 处理 方法调用(functions call) 消息
                    log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                    jd_thingtalk_msg_proc_func_call(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
                }
                else
                {
                    log_info("[warnning]properties command [%s] is ignored", parsed_topic->command);
                }
            }
        }
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_REG)) {
            // 自动注册 (regist) 相关消息处理
            if (parsed_topic->command != NULL) {
                // TODO [目前只有一种] 处理 自动注册响应 (register response) 消息
                log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                jd_thingtalk_msg_proc_reg_res(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
            } else {
                log_info("[warnning]register whitout command is ignored");
            }
        }
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_AUTH)) {
            // TODO 认证 (auth) 相关消息处理
        }
        // process heartbeat
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_HB)) {
            // TODO 心跳 (heartbeat) 相关消息处理
        }
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_THMD)) {
            // 物模型 (thing-model) 相关消息处理
            if (parsed_topic->response != NULL) {
                // 处理 物模型上报响应 (thing-model post response) 消息
                log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                jd_thingtalk_msg_proc_thmd_post_res(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
            } else {
                log_info("[warnning]thingmodel command[%s] whitout response is ignored", parsed_topic->command);
            }
        }
        else if (!jd_thingtalk_pal_strcmp(parsed_topic->type_name, JD_THINGTALK_PROTO_TOPIC_TYPE_NTP)) {
            // 设备NTP授时响应
            if (parsed_topic->command != NULL) {
                // 处理 设备NTP授时响应 消息
                log_info("\r\nmessage[%s]:\r\n%s\r\n", message->topic, message->payload);
                jd_thingtalk_msg_proc_ntp_req_res(sdk, message, parsed_topic->obj_name, parsed_topic->service_key);
            } else {
                log_info("[warnning]NTP whitout command is ignored");
            }
        }
        else {};

        // 释放内存
        jd_thingtalk_proto_topic_free(parsed_topic);
    } else {
        log_error("parse topic [%s] failed!", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    log_info("Leave jd_thingtalk_sdk_message_callback");
    return ret;
}

/**
 * @brief   sdk 消息处理逻辑 订阅ACK回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] mid: MQTT 消息ID
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_subscribe_callback(jd_thingtalk_mqtt_t mqtt, int32_t mid, void *user_data)
{
    // log_info("Received SUBACK of mid ( %d )\n", mid);
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   sdk 消息处理逻辑 发布ACK回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] mid: MQTT 消息ID
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_publish_callback(jd_thingtalk_mqtt_t mqtt, int32_t mid, void *user_data)
{
    // log_info("Received PUBACK of mid ( %d )\n", mid);
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   属性设置主题 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_prop_set(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message, char *obj_name, char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_prop_set");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 定义属性设置结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoPropSet_t prop_set;
    jd_thingtalk_pal_memset(&prop_set, 0, sizeof(JDThingTalkProtoPropSet_t));

    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_prop_set(message->payload, &prop_set)) {
        log_info("parse payload successfully!");
        
        log_info("\tdeviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tversion:%d\r\n\tprop_num:%d\r\n\tproperties:[%s:%s]", 
                prop_set.deviceId,
                prop_set.timestamp,
                prop_set.messageId,
                prop_set.version,
                prop_set.prop_num,
                prop_set.properties[0]->key,
                prop_set.properties[0]->value
                );
        
        // 调用设备回调函数 属性设置处理函数
        if ((service_key != NULL)
            &&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_AGENT)
            &&(sdk->dev_cb.on_sub_prop_set != NULL)
            && jd_thingtalk_pal_strcmp(prop_set.deviceId, sdk->cfg->deviceId)) {
            
            sdk->dev_cb.on_sub_prop_set(sdk, &prop_set);
        }
        else if (sdk->dev_cb.on_prop_set != NULL) {
            sdk->dev_cb.on_prop_set(sdk, obj_name, service_key, &prop_set);
        }
        else {};
    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_prop_set(&prop_set);

    log_info("Leave jd_thingtalk_msg_proc_prop_set");
    return ret;
}

/**
 * @brief   属性获取主题 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_prop_get(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message, char *obj_name, char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_prop_get");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 定义属性获取结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoPropGet_t prop_get;
    jd_thingtalk_pal_memset(&prop_get, 0, sizeof(JDThingTalkProtoPropGet_t));

    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_prop_get(message->payload, &prop_get)) {
        log_info("parse payload successfully!");
        /*
        log_info("\tdeviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tproperties:%s", 
                prop_get.deviceId,
                prop_get.timestamp,
                prop_get.messageId,
                prop_get.properties);
        */

        // 调用设备回调函数 属性获取处理函数
        if ((service_key != NULL)
            &&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_AGENT)
            &&(sdk->dev_cb.on_sub_prop_get != NULL)
            && jd_thingtalk_pal_strcmp(prop_get.deviceId, sdk->cfg->deviceId)) {

            sdk->dev_cb.on_sub_prop_get(sdk, &prop_get);
        }
        else if (sdk->dev_cb.on_prop_get != NULL) {
            sdk->dev_cb.on_prop_get(sdk, obj_name, service_key, &prop_get);
        }
        else {};
    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_prop_get(&prop_get);

    log_info("Leave jd_thingtalk_msg_proc_prop_get");
    return ret;
}

/**
 * @brief   方法调用 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_func_call(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_func_call");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 定义方法调用结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoFuncCall_t func_call;
    jd_thingtalk_pal_memset(&func_call, 0, sizeof(JDThingTalkProtoFuncCall_t));

    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_func_call(message->payload, &func_call)) {
        log_info("parse payload successfully!");
        /*
        log_info("\tdeviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tproperties:%s", 
                func_call.deviceId,
                func_call.timestamp,
                func_call.messageId,
                func_call.functions);
        */

        // 调用设备会回调函数 方法调用处理函数
        if ((service_key != NULL)
            &&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_AGENT)
            &&(sdk->dev_cb.on_agent_func_call != NULL)
            &&!jd_thingtalk_pal_strcmp(func_call.deviceId, sdk->cfg->deviceId)) {

            sdk->dev_cb.on_agent_func_call(sdk, &func_call);
        }
        else if ((service_key != NULL)
            &&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_AGENT)
            &&(sdk->dev_cb.on_sub_func_call != NULL)
            && jd_thingtalk_pal_strcmp(func_call.deviceId, sdk->cfg->deviceId)) {

            sdk->dev_cb.on_sub_func_call(sdk, &func_call);
        }
        else if ((service_key != NULL)
            &&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_OTA)
            &&(sdk->dev_cb.on_ota_fun_call != NULL)) {

            sdk->dev_cb.on_ota_fun_call(sdk, obj_name, &func_call);
        }
        else if (sdk->dev_cb.on_func_call != NULL) {
            sdk->dev_cb.on_func_call(sdk, obj_name, service_key, &func_call);
        }
        else {};
    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_func_call(&func_call);

    log_info("Leave jd_thingtalk_msg_proc_func_call");
    return ret;
}

/**
 * @brief   自动注册响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_reg_res(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_reg_res");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 定义自动注册响应结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoRegReqRes_t reg_res;
    jd_thingtalk_pal_memset(&reg_res, 0, sizeof(JDThingTalkProtoRegReqRes_t));
    
    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_reg_req_res(message->payload, &reg_res)) {
        log_info("parse payload successfully!");
        /*
        log_info("\tdeviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tcode:%d\r\n\tproperties:%s", 
                reg_res.deviceId,
                reg_res.timestamp,
                reg_res.messageId,
                reg_res.code,
                reg_res.devices);
        */

        // 调用设备回调函数 自动注册响应处理函数
        if (sdk->dev_cb.on_reg_res != NULL) {
            sdk->dev_cb.on_reg_res(sdk, obj_name, service_key, &reg_res);
        }

    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_reg_req_res(&reg_res);

    log_info("Leave jd_thingtalk_msg_proc_reg_res");
    return ret;
}

/**
 * @brief   物模型上报响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_thmd_post_res(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_thmd_post_res");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // 定义物模型上报应结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoThingModelPostRes_t post_res;
    jd_thingtalk_pal_memset(&post_res, 0, sizeof(JDThingTalkProtoThingModelPostRes_t));
    
    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_thing_model_post_res(message->payload, &post_res)) {
        log_info("parse payload successfully!");
        /*
        log_info("\tdeviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tcode:%d", 
                post_res.deviceId,
                post_res.timestamp,
                post_res.messageId,
                post_res.code);
        */

        // 调用设备回调函数 物模型上报响应处理函数
        if (sdk->dev_cb.on_thmd_post_res != NULL) {
            sdk->dev_cb.on_thmd_post_res(sdk, obj_name, service_key, &post_res);
        }

    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_thing_model_post_res(&post_res);

    log_info("Leave jd_thingtalk_msg_proc_thmd_post_res");
    return ret;
}

/**
 * @brief   设备NTP授时响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_ntp_req_res(struct jd_thingtalk_sdk_t *sdk,
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key)
{
    log_info("Enter jd_thingtalk_msg_proc_ntp_req_res");
    int32_t ret = JD_THINGTALK_RET_SUCCESS;

    // FIXME 设备接收时间
    jd_thingtalk_time_stamp_t devRecvTime;
    jd_thingtalk_pal_time_get_timestamp(&devRecvTime);

    // 设置时间
    jd_thingtalk_time_stamp_t set_time;

    // 定义临时变量，用来计算NTP时间
    int32_t time_ms = 0;
    int32_t tmp_int1 = 0;
    int32_t tmp_int2 = 0;

    // 定义设备NTP响应结构体变量 用于暂存消息解析后的内容
    JDThingTalkProtoNTPReqRes req_res;
    jd_thingtalk_pal_memset(&req_res, 0, sizeof(JDThingTalkProtoNTPReqRes));

    // 进行消息解析和处理
    if (!jd_thingtalk_proto_parse_ntp_req_res(message->payload, &req_res)) {
        log_info("parse payload successfully!");

        // 计算校准时间
        // log_info("Device Send Time [%d.%d]", req_res.devSendTime.second, req_res.devSendTime.ms);
        log_info("Device Recv Time [%d.%d]", devRecvTime.second, devRecvTime.ms);
        // log_info("Server Revc Time [%d.%d]", req_res.serRecvTime.second, req_res.serRecvTime.ms);
        // log_info("Server Send Time [%d.%d]", req_res.serSendTime.second, req_res.serSendTime.ms);

        // 计算毫秒部分
        time_ms  = (int32_t) devRecvTime.ms;
        time_ms -= (int32_t) req_res.devSendTime.ms;
        time_ms -= (int32_t) req_res.serSendTime.ms;
        time_ms += (int32_t) req_res.serRecvTime.ms;
        time_ms = time_ms >> 1;
        time_ms += (int32_t) req_res.serSendTime.ms;

        // 计算秒部分 FIXME
        tmp_int1 = (int32_t)devRecvTime.second - (int32_t)req_res.devSendTime.second;
        tmp_int2 = (int32_t)req_res.serSendTime.second - (int32_t)req_res.serRecvTime.second;
        tmp_int1 -= tmp_int2;
        if ((tmp_int1 % 2) == 1) {
            time_ms += 500;
        }
        set_time.second  = req_res.serSendTime.second + (tmp_int1 >> 1);

        // 计算最后结果
        tmp_int1 = time_ms / 1000;
        set_time.second += tmp_int1;
        if (time_ms < 0) {
            set_time.second -= 1;
            set_time.ms = (uint32_t) (1000 + (time_ms - tmp_int1 * 1000));
        }
        else {
            set_time.ms = (uint32_t)(time_ms - tmp_int1 * 1000);
        }

        log_info("Final NTP Result: [%d.%d]", set_time.second, set_time.ms);

        // 调用设备回调函数 物模型上报响应处理函数
        if (sdk->dev_cb.on_ntp_req_res != NULL) {
            sdk->dev_cb.on_ntp_req_res(sdk, &set_time);
        }

    } else {
        log_error("parse message for topic:[%s] is faild", message->topic);
        ret = JD_THINGTALK_RET_FAILED;
    }

    // 释放内存
    jd_thingtalk_proto_free_ntp_req_res(&req_res);

    log_info("Leave jd_thingtalk_msg_proc_ntp_req_res");
    return ret;
}

// end of file
