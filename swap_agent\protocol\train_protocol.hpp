#ifndef __PROTOCOL_TRAIN_PROTOCOL_HPP__
#define __PROTOCOL_TRAIN_PROTOCOL_HPP__

#include <stdint.h>
#include "../multi_swap_manager.hpp"
#include <ctime>


#define TRAIN_PROTOCOL_DATA_VALUE_MAX_LEN											(150)


// UDP 协议字段设计
#define TRAIN_PROTOCOL_SESSION_REGISTER												( 0x70 )
#define TRAIN_PROTOCOL_SESSION_REGISTER_ACK											( 0x71 )
#define TRAIN_PROTOCOL_SESSION_CMD_OPT												( 0x90 )
#define TRAIN_PROTOCOL_SESSION_HEART_BEAT											( 0xA0 )
#define TRAIN_PROTOCOL_SESSION_EXCEPTION											( 0x80 )



#define TRAIN_PROTOCOL_CMD_VALUE_MAX_LEN											( 256 )

#define TRAIN_PROTOCOL_UDP_HEAD_STRING												0xFE, 0xFE, 0xFE
#define TRAIN_PROTOCOL_UDP_TAIL_STRING												0x16



/**@struct train_protocol_cmd
* @brief 格式化结构体
*/
typedef struct _train_protocol_cmd
{
	uint8_t cmd_type;
	uint8_t cmd_value[TRAIN_PROTOCOL_CMD_VALUE_MAX_LEN];
}train_protocol_cmd;


/**@enum train_protocol_err_tab
* @brief 根据UDP协议进行解码的结果枚举量
*/
typedef enum
{
	TRAIN_PROTOCOL_SUCESS   = 0,     ///< 解码正常
	TRAIN_PROTOCOL_HEAD_FALT = 1,    ///< UDP数据头错误
	TRAIN_PROTOCOL_TAIL_FALT = 2,    ///< UDP数据尾错误
	TRAIN_PROTOCOL_LEN_FALT  = 3,     ///< UDP数据长度错误
	TRAIN_PROTOCOL_OPT_FALT = 4,      ///< UDP数据操作类型错误
	TRAIN_PROTOCOL_CRC_FALT = 5      ///< UDP数据CRC错误
}train_protocol_err_tab;


/**@struct train_protocol_
* @brief data数据部分结构体
*/
typedef struct _train_protocol_data
{
	uint16_t data_type;  
	uint16_t data_len;
	uint32_t data_value[TRAIN_PROTOCOL_DATA_VALUE_MAX_LEN];	
}train_protocol_data;


/**@brief     车辆段数据协议头匹配
* @param[in]  uint8_t *buff --- 接收到的源数据包
* @param[in]  uint16_t buff_len --- 接收到的源数据包长度
* @return     train_protocol_err_tab 解码操作结果
* - TRAIN_PROTOCOL_SUCESS     ---   正常解码
* - TRAIN_PROTOCOL_HEAD_FALT  ---   接收数据包头不正确
*/
extern train_protocol_err_tab train_protocol_head_match(uint8_t *buff, uint16_t len);


/**@brief     车辆段数据协议尾匹配
* @param[in]  uint8_t *buff --- 接收到的源数据包
* @param[in]  uint16_t buff_len --- 接收到的源数据包长度
* @return     train_protocol_err_tab 解码操作结果
* - TRAIN_PROTOCOL_SUCESS     ---   正常解码
* - TRAIN_PROTOCOL_HEAD_FALT  ---   接收数据包头不正确
*/
extern train_protocol_err_tab train_protocol_tail_match(uint8_t *buff, uint16_t len);


/**@brief     车辆端协议编码API
* @param[in]  uint8_t *buff --- 接收到的源数据包
* @param[in]  uint16_t buff_len --- 接收到的源数据包长度
* @param[out] uint8_t *data_buf  --- 解码得到的data字段数据缓冲区
* @param[out] int *data_cnt 	---  解码得到的data字段长度
* @param[out] uint32_t *comm_seq --- 解码得到的当前源数据包UDP 序列号
* @param[out] uint32_t *dev_id --- 解码得到的当前源数据包UDP 中的设备ID
* @return     train_protocol_err_tab 解码操作结果
* - VEHICLE_PROTOCOL_SUCESS     ---   正常解码
* - VEHICLE_PROTOCOL_HEAD_FALT  ---   接收数据包头不正确
* - VEHICLE_PROTOCOL_LEN_FALT   ---   接收数据长度不正确
* - VEHICLE_PROTOCOL_OPT_FALT   ---   接收数据操作类型不正确
*/
extern train_protocol_err_tab train_protocol_decodec(uint8_t *buff, uint16_t buff_len, uint8_t *data_buf, int *data_cnt, uint32_t *comm_seq, uint8_t *dev_id);


/**@brief     车辆端协议编码API
 * @param[in]  dev_agent_cfg *cfg --- 待ack的配置参数
 * @param[out] uint8_t *send --- 编码完成后输出的数据
 * @param[out] uint16_t *send_len --- 编码完成后输出的数据帧长度
 * @param[in]  uint8_t id --- 设备ID，填充至TCP协议中的指定位置
 * @param[in]  uint8_t type --- 指令类型，填充至TCP协议中的指定位置
 * @param[in]  uint32_t sequeue --- 指令序列号，填充至TCP协议中的指定位置
 * @return     NULL
 */
extern void train_protocol_codec_cfg(dev_agent_cfg *cfg, uint8_t *send, uint16_t *send_len, uint8_t id, uint8_t type, uint32_t sequeue);



/**@brief     车辆端协议编码API
 * @param[in]  train_protocol_cmd *cmd_buf
 * @param[out] uint8_t *buffer --- 编码完成后输出的数据
 * @param[out] uint16_t *data_len --- 协议中data字段的长度
 * @param[in]  uint8_t id --- 设备ID，填充至TCP协议中的指定位置
 * @param[in]  uint8_t type --- 指令类型，填充至TCP协议中的指定位置
 * @param[in]  uint32_t sequeue --- 指令序列号，填充至TCP协议中的指定位置
 * @return     NULL
 */
extern void train_protocol_codec(train_protocol_cmd *cmd_buf, uint8_t *buffer, uint16_t *data_len, uint8_t id, uint8_t type, uint32_t sequeue);

void train_protocol_codec_data(train_protocol_cmd *cmd_buf, uint8_t *data_buf, uint16_t *data_len);


/**@brief     车辆端协议编码API
 * @param[in]  uint8_t dev_id  ---设备id
 * @param[in]  dev_agent_cfg *cfg --- 待ack的配置参数
 * @param[out] uint8_t *data --- 编码完成后输出的数据
 * @param[out] uint16_t *data_len --- 编码完成后输出的数据帧长度
 * @return     NULL
 */
void train_protocol_codec_data_cfg(uint8_t dev_id, dev_agent_cfg *cfg, uint8_t * data, uint16_t *data_len);

void get_current_time_32bit(uint32_t *time_stamp);

uint16_t calc_crc16_net_msg(uint8_t *data, int length);



#endif