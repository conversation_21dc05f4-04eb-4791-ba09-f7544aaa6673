#ifndef __TRAIN_MANAGE_TRAIN_LIST_HPP__
#define __TRAIN_MANAGE_TRAIN_LIST_HPP__

#include "../threadpool/blocking_queue.hpp"

#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/exception.pb.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>

#include <iostream>

#include <sys/socket.h>
#include <arpa/inet.h>
#include "cfg.hpp"

#include <list>


using namespace std;


/**@enum _vheicle_map_opt_tab
* @brief 定义unordered_map的操作结果
*/
typedef enum
{//暂时没使用
	TRAIN_TASK_NONE                             = 0,    ///< 无任务
    TRAIN_TASK_CARRIAGR_MOTOR_ZERO_CALIBRATION  = 1,    ///<车厢电机零点校准
    TRAIN_TASK_CARRIAGR_MOTOR_MOVE              = 2,    ///<车厢电机移动
    TRAIN_TASK_CARRIAGR_BELT_MOTOR_ROTATE       = 3,    ///<车厢皮带转动
    TRAIN_TASK_CARRIAGR_BELT_MOTOR_ZERO         = 4,    ///<车厢皮带回零
    TRAIN_TASK_CARRIAGR_PACK                    = 5,    ///<车厢上包
    TRAIN_TASK_CARRIAGR_UNPACK                  = 6,    ///<车厢卸包
    // TRAIN_TASK_QUERY_PARA                       = 7,    ///<小车运行参数
    TRAIN_TASK_UNKNOWN                          = 8     ///< 无法解析
}_train_task_type;

// typedef enum
// {
//     TASK_RESULT_UNKNOW = 0,
//     TASK_RESULT_SUCCEED = 1,
//     TASK_RESULT_RUNNING = 2,
//     TASK_RESULT_ERROR = 3
// }_train_task_result;

typedef struct _task_info
{
    uint8_t id;
    uint8_t task_type;
    uint8_t task_result;
}task_info;

typedef struct _task_state_
{
    uint8_t carriage_count;
    task_info carriages_task[30];
}task_st;


typedef struct _net_msg
{
    uint32_t cin_addr;  //网络字节序
	uint16_t data_len;
	uint8_t msg_data[256];
}net_msg;


typedef struct _exception_info_t
{
    // uint8_t carriage_id;
    int  exception_code;
}exception_info_t;



/**@struct train_info
* @brief 车辆信息数据结构体
*/
typedef struct _train_info
{
	/// 网络通信相关信息

    uint32_t cin_addr;
	struct sockaddr_in v_addr;
    char sw_version[16] = "";
    char hw_version[16] = "";

	/// 车辆最后一次有效通信时间，用于心跳检测
	struct timespec v_last_msg_upload_tick;

	/// 车辆最后一次数据下行时间, 用于进行发送就绪条件检测
	struct timespec v_last_msg_downlink_tick;

	train_state_net v_last_state;   //网络消息状态

	exception_info_t v_exception[100];

	volatile uint32_t v_comm_seque;

	_train_task_type v_task;
    task_st carriage_task_state;
    task_st platform_task_state;

    //通信完成标志
	bool v_comm_finish_flag;
    
	uint32_t v_last_report_seque;

    //车头故障码, 用于调度系统上报
	uint32_t train_error_no = 0;

	//车辆最后一条消息
	net_msg v_last_msg;

    //上一次心跳中上报的异常码
    int except_code;

    //上一次心跳中上报的异常载货台
    int platform_id;

    //上一次心跳中伺服异常错误码，用作伺服异常子码
    int except_motor_error_code;

    bool train_restart_flag = false;

}train_info;


typedef struct _platform_task_info
{
    //通信完成标志
	bool v_comm_finish_flag;

    //上行sequence: 1 车辆重启; 0:注册
    volatile uint32_t v_comm_uplink_seque;

    //车辆最后一条消息
	net_msg v_last_msg;
}platform_task_info;

typedef struct _train_position_info
{
    uint16_t speed;
    uint32_t pos;
    bool is_locate;     //true: 已定位; false: 未定位

}train_position_info;


typedef unordered_map<int, std::shared_ptr<spdlog::logger>> train_log_map;
typedef unordered_map<uint32_t, int> train_info_sock_map;    //<cin_addr -> dev_id>
typedef unordered_map<int, train_info> train_info_map;  //<devid -> train_info>
typedef unordered_map<int, platform_task_info> platform_task_map;  //<platform_id -> platform_task_info>
typedef unordered_map<int, train_task_state> train_task_info_map;  //<platform_id -> train_task_state>
typedef unordered_map<int, train_position_info> train_position_info_map;  //<devid -> train_position_info>
typedef unordered_map< int, blocking_queue<train_task> > train_task_map;
typedef unordered_map<int, uint32_t> train_comm_sequence_map;

typedef unordered_map< int, std::list<except_info> > train_except_map;


/**@enum _train_map_opt_tab
* @brief 定义unordered_map的操作结果
*/
typedef enum
{
	TRAIN_SESSION_SUCESS   = 0,  ///< 当前操作成功
	TRAIN_SESSION_NOT_FIND = 1,  ///< 指定对象不存在
	TRAIN_SESSION_EXCID    = 2,  ///< 指定对象已存在 
	TRAIN_SESSION_OPT_FIAL = 3   ///< 当前操作失败
}_train_map_opt_tab;






class train_list_map
{
public:

    // train_list_map class构造函数
	explicit train_list_map();

    ~train_list_map();

	/**@brief	  车辆列表指定对象列表当前存储数据量
	* @param[in]  NULl
	* @return	  当前列表大小
	*/
	int train_list_map_get_size(void)
	{
		return m_train_dev_list.size();
	}

    const train_info_map& get_train_dev_list(void) const
    {
        return m_train_dev_list;
    }

    static train_list_map* get_instance(void)
    {
        static train_list_map instance;
        return &instance;
    }


    /**@brief     更新车辆的 socket 列表, 确保每个车辆 ID 只对应一个最新的 IP
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @param[in]  int id      --- 指定的车辆ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_change(uint32_t cin_addr, int id);


    /**@brief	  车辆列表指定对象删除
	* @param[in]  int id --- 车辆ID
	* @return	  _train_map_opt_tab 操作结构
	* - TRAIN_SESSION_SUCESS	  ---  操作成功
	* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
	* - TRAIN_SESSION_EXCID	  ---  当前索引已存在
	* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
	*/
	_train_map_opt_tab train_list_map_delete(int id);


    /**@brief     车辆网络列表对象删除
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_delete(uint32_t cin_addr);


    /**@brief     车辆网络列表对象删除
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_delete(int dev_id);


    /**@brief     获取指定对象的网络通信IP
     * @param[in]  int id --- 指定的车辆ID
    * @return     网络通信套接字
    */
    uint32_t train_list_map_get_dev_addr(int id);


    /**@brief     获取指定对象的网络通信套接字
     * @param[in]  int id --- 指定的车辆ID
    * @return     网络通信套接字
    */
    int train_sock_list_get_socket_addr(int dev_id);


    /**@brief	  车辆列表指定对象数据更新(上行tick更新)
	* @param[in]  int id --- 车辆ID
	* @return	  NULL
	*/
	void train_list_map_update_upload_tick(int id);


    /**@brief	  车辆列表指定对象数据更新(车辆状态)
	* @param[in]  int id --- 车辆ID
	* @param[in]  train_state_net state --- 通过网络上报的车辆状态数据
	* @return	  NULL
	*/
	void train_list_map_update_state(int id, train_state_net state);

    /**@brief	  车辆列表指定对象数据更新(Y轴及载货台任务状态)
	* @param[in]  int id --- 车辆ID
	* @param[in]  train_state_net state --- 通过网络上报的车辆状态数据
	* @return	  NULL
	*/
	void train_list_map_update_task_state(int id, task_st carriage_task_st, task_st platform_task_st);


	/**@brief	  车辆列表指定对象数据更新(异常状态更新)
    * @param[in]  int devid --- 异常车辆ID, 包括车厢号
	* @param[in]  int exce_info --- 车辆异常状态数据
	* @return	  NULL
	*/
	void train_list_map_update_exce_info(uint8_t devid, int exce_info);

    /**@brief     车辆列表指定对象数据更新(下行tick更新)
    * @param[in]  int id --- 车辆ID
    * @param[in]  struct timespec tick --- 下行数据时间点
    * @return     NULL
    */
    void train_list_map_update_downlink_tick(int id);

    /**@brief     车辆列表指定对象数据更新(软硬件版本更新)
    * @param[in]  int id --- 车辆ID
    * @param[in] char *sw_version ---软件版本
    * @param[in] char *hw_version ---硬件版本
    * @return     NULL
    */
    void train_list_map_update_sw_hw_version(int id, char *sw_version, char *hw_version);

    /**@brief     车辆列表指定对象数据更新(车头故障码更新)
    * @param[in]  int id --- 车辆ID
    * @param[in]  uint32_t train_error_no ---车头故障码
    * @return     NULL
    */
    void train_list_map_update_train_error_no(int id, uint32_t train_error_no);

    //获取m_train_dev_list列表中的车辆信息数据
    int train_list_map_get_train_info(int id, train_info &info);

    void train_list_map_update_exce_code(int train_id, int exce_code, int exce_platform_id);
    void train_list_map_update_exce_motor_error_code(int train_id, int exce_motor_error_code);
    int train_list_map_get_exce_code(int train_id, int *exce_code, int *exce_platform_id);
    int train_list_map_get_exce_motor_error_code(int train_id, int *exce_motor_code);

    //车辆列表指定对象数据更新(车辆重启标志)
    void train_list_map_update_train_restart_flag(int train_id, bool flag);
    bool train_list_map_get_train_restart_flag(int train_id);


    //获取m_train_dev_list列表中的车辆train_error_no
    uint32_t train_list_map_get_train_error_no(int id);


    /**@brief     车辆任务列表 任务数据存储
	* @param[in]  int dev_id --- 指定的车辆号
	* @param[in]  train_task task --- 该车辆的任务
	* @return     NULL
	*/
	void train_task_list_push(int dev_id, train_task task);


    /**@brief     车辆任务列表 任务数据存储
	* @param[in]  int dev_id --- 指定的车辆号
	* @param[in]  train_task *task --- 该车辆的任务
	* @return     获取数据有效性
	* - true    ---  数据有效
	* - false   ---  数据无效
	*/
	bool train_task_list_pop(int dev_id, train_task *task);


    /**@brief     车辆任务列表非空查询
    * @param[in]  int dev_id --- 有任务的车列表
    * @param[in]  int dev_cnt --- 任务/车辆的数量
    * @return     当前任务队列深度
    */
    bool train_task_list_empty_state(int *dev_id, int *dev_cnt);


    /**@brief     创建指定车辆的logger对象，用来生成日志数据
    * @param[in]  int dev_id --- 指定的车辆ID
    * @return     NULL
    */
    void train_log_list_init_logger(int dev_id);


    /**@brief     获取当前车辆的spdlog执行的logger对象
    * @param[in]  int dev_id --- 指定的车辆ID
    * @return     对应的spdlog 的logger对象
    */
    std::shared_ptr<spdlog::logger> train_log_list_get_logger(int dev_id, bool *valid_flag);


	/**@brief	  获取指定车辆当前任务类型
	* @param[in]  int id --- 车辆ID
	* @return	  车辆任务信息
	*/
    _train_task_type train_list_map_get_task_info(int id);


    /**@brief	  车辆列表指定对象数据更新(当前任务更新)
	* @param[in]  int id --- 车辆ID
	* @param[in]  _train_task_type task_info --- 车辆任务状态
	* @return	  NULL
	*/
	void train_list_map_update_task_info(int id, _train_task_type task_info);


    /**@brief	  车辆列表指定对象数据重置(当前任务重置)
	* @param[in]  int id --- 车辆ID
	* @return	  NULL
	*/
	void train_list_map_reset_task_info(int id);


    /**@brief    检查软硬件版本号是否为空
    * @param[in]  int id --- 车辆ID
    * @return     true 不为空
    */
    bool train_list_map_check_version_empty(int id);


    /**@brief     车辆日志列表指定对象查找
    * @param[in]  int dev_id --- 设备ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_find(int dev_id);


    /**@brief	  车辆日志列表对象插入
        * @param[in]  int dev_id --- 设备ID
        * @return	  _train_map_opt_tab 操作结构
        * - TRAIN_SESSION_SUCESS	  ---  操作成功
        * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
        * - TRAIN_SESSION_EXCID	  ---  当前索引已存在
        * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_log_list_insert(int dev_id, std::shared_ptr<spdlog::logger> logger);


    /**@brief     车辆网络列表对象插入
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @param[in]  int id --- 该网络通信套接字对应的车辆ID信息
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_insert(uint32_t cin_addr, int id);

    /**@brief     车辆网络列表指定对象查找
    * @param[in]  uint32_t cin_addr --- 指定的网络通信IP
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_sock_list_find(uint32_t cin_addr);


    /**@brief     当前车辆列表所有对象显示(调试使用)
    * @param[in]  NULL
    * @return     NULL
    */
    void train_dev_list_display(void);


    /**@brief	  当前通信所有对象显示(调试使用)
	* @param[in]  NULL
	* @return	  NULL
	*/
	void train_sock_list_display(void);


    /**@brief     车辆列表数据插入
    * @param[in]  int id --- 车辆ID
    * @param[in]  train_info dev_info --- 车辆信息数据结构体，具体内容见头文件
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_list_map_insert(int id, train_info dev_info);

    _train_map_opt_tab platform_list_map_insert(int platform_id, platform_task_info dev_info);


    /**@brief     车辆列表指定对象查找
    * @param[in]  int id --- 车辆ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_list_map_find(int id);


    /**@brief     车辆实时位置指定对象查找
    * @param[in]  int id --- 车辆ID
    * @return     _train_map_opt_tab 操作结构
    * - TRAIN_SESSION_SUCESS    ---  操作成功
    * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
    * - TRAIN_SESSION_EXCID     ---  当前索引已存在
    * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
    */
    _train_map_opt_tab train_list_pos_find(int id);

    _train_map_opt_tab train_list_platform_task_state_find(int platform_id);
    _train_map_opt_tab train_list_carriage_task_state_find(int carriage_id);

    /**@brief     车辆实时位置信息插入
     * @param[in]  int id --- 车辆ID
     * @param[in]  train_position_info pos_info --- 车辆实时位置信息结构体
     * @return     _train_map_opt_tab 操作结构
     * - TRAIN_SESSION_SUCESS    ---  操作成功
     * - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
     * - TRAIN_SESSION_EXCID     ---  当前索引已存在
     * - TRAIN_SESSION_OPT_FIAL  ---  操作失败
     */
    _train_map_opt_tab train_list_position_insert(int id, train_position_info pos_info);

    _train_map_opt_tab train_list_platform_task_state_insert(int platform_id, train_task_state m_platform_task_state);
    _train_map_opt_tab train_list_carriage_task_state_insert(int platform_id, train_task_state m_cariage_task_state);

    /**@brief     获取指定车辆当前实时位置信息
    * @param[in]  int id --- 车辆ID
    * @return     位置信息
    */
    train_position_info train_list_get_position_info(int id);

    /**@brief	  车辆实时位置列表指定对象删除
	* @param[in]  int id --- 车辆ID
	* @return	  _train_map_opt_tab 操作结构
	* - TRAIN_SESSION_SUCESS	  ---  操作成功
	* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
	* - TRAIN_SESSION_EXCID	  ---  当前索引已存在
	* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
	*/
	_train_map_opt_tab train_list_position_delete(int id);


    /**@brief     车辆任务列表对象初始化 - 清除设备ID对应的任务列表
    * @param[in]  int dev_id --- 指定的车辆号
    * @return     bool类型 操作结果
    * - true    ---  操作成功
    * - false  ---  操作失败
    */
    bool train_task_list_init(int dev_id);


    /**@brief     车辆列表指定对象数据获取(网络消息)
    * @param[in]  int id --- 车辆ID
    * @param[out]  train_state_net *state --- 通过网络上报的车辆状态数据
    * @return     bool类型 操作结果
    * - true    ---  操作成功
    * - false  ---  操作失败    
    */
	bool train_list_map_get_train_state(int id, train_state_net *state);

    bool train_list_map_get_train_task_state(int id, task_st *carriage_task_state, task_st *platform_task_state);

    bool train_list_map_get_train_platform_task_state(int platform_id, train_task_state *platform_task_state);
    bool train_list_map_get_train_carriage_task_state(int platform_id, train_task_state *carriage_task_state);

    /**@brief     车辆任务列表指定对象查找
	* @param[in]  int dev_id	 --- 指定的车辆号
	* @return     _train_map_opt_tab 操作结构
	* - TRAIN_SESSION_SUCESS    ---  操作成功
	* - TRAIN_SESSION_NOT_FIND  ---  当前索引不存在
	* - TRAIN_SESSION_EXCID     ---  当前索引已存在
	* - TRAIN_SESSION_OPT_FIAL  ---  操作失败
	*/
	_train_map_opt_tab train_task_list_find(int dev_id);


    /**@brief     车辆列表指定对象数据更新(车辆信息)
    * @param[in]  int id --- 车辆ID
    * @return     NULL
    */
    void train_list_map_update_comm_flag(int id, bool flag);

    /**@brief     载货台列表指定对象数据更新(通信完成标志)
    * @param[in]  int platform_id --- 载货台ID
    * @param[in]  bool flag --- 通信ack标志
    * @return     NULL
    */
    void platform_list_map_update_comm_ack_flag(int platform_id, bool flag);

   /**@brief     获取整列车组中是否存在通信ack未完成的载货台
    * @param[in]  int train_id --- 车辆ID
    * @return   true:车组ack全部完成；
                false:车组中存在未完成ack的载货台
    */
    bool train_list_map_get_comm_ack_flag(int train_id);


    /**@brief     车辆列表当前通信ACK确认完成标志查询
    * @param[out]  int *dev_id --- 通信未完成车辆列表
    * @param[out]  int *dev_cnt --- 通信未完成的车辆总数
    * @return     通信未完成车辆列表及车辆总数
    */
	bool train_list_map_get_dev_comm_state(int *dev_id, int *dev_cnt);


    /**@brief     获取通信ACK标志
    * @param[in]  int id --- 车辆ID
    * @return     NULL
    */
    bool train_list_map_get_comm_flag(int id);

    bool platform_list_map_get_comm_ack_flag(int platform_id);



    /**@brief     下行通信序列号初始化 - m_train_comm_sequ_list.insert
    * @param[in]  int id --- 车辆ID
    * @return     NULL
    */
    void train_list_map_update_comm_sequeue(int id);

    /**@brief     下行通信序列号初始化 - m_train_comm_sequ_list.insert
    * @param[in]  int platform_id --- 载货台ID
    * @return     NULL
    */
    void platform_list_map_insert_comm_sequeue(int platform_id);


    /**@brief     下行通信序列号更新
    * @param[in]  int platform_id --- 载货台ID
    * @return     NULL
    */
    void platform_list_map_update_comm_sequeue(int platform_id, uint32_t sequence);


    /**@brief     获取指定对象的最近一次通信的有效序列号, 上行sequence
    * @param[in]  int platform_id --- 载货台ID
    * @return     有效通信序列号
    */
    uint32_t platform_list_map_get_uplink_comm_sequeue(int platform_id);



    /**@brief	  获取指定对象的最近一次通信的有效序列号,下行sequence
	* @param[in]  int platform_id --- 载货台ID
	* @return	  有效通信序列号
	*/
	uint32_t platform_list_map_get_comm_sequeue(int platform_id);	

    /**@brief     载货台列表指定对象数据更新(上行sequence)
    * @param[in]  int id --- 载货台ID
    * @return     NULL
    */
    void platform_list_map_update_uplink_comm_squence(int platform_id, int seuq_cnt);

    /**@brief     车辆列表指定对象数据获取(网络数据)
    * @param[in]  int id --- 车辆ID
    * @param[in]  net_msg *msg --- 待发送的网络消息
    * @return     NULL
    */
    bool train_list_map_get_comm_msg(int id, net_msg *msg);

    /**@brief     载货台列表指定对象数据获取(网络数据)
    * @param[in]  int platform_id --- 载货台ID
    * @param[in]  net_msg *msg --- 待发送的网络消息
    * @return     NULL
    */
    bool platform_list_map_get_comm_msg(int platform_id, net_msg *msg);


    /**@brief     车辆列表指定对象数据更新(网络数据)
    * @param[in]  int id --- 车辆ID
    * @param[in]  net_msg msg --- 通过网络上报的车辆状态数据
    * @return     NULL
    */
    void train_list_map_update_comm_msg(int id, net_msg msg);

    /**@brief     载货台列表指定对象数据更新(网络数据)
    * @param[in]  int platform_id --- 载货台ID
    * @param[in]  net_msg msg --- 通过网络上报的车辆状态数据
    * @return     NULL
    */
    void platform_list_map_update_comm_msg(int platform_id, net_msg msg);


    /**@brief	  获取指定对象的最新一次数据上行时刻时间信息
	* @param[in]  int id --- 车辆ID
	* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
	* @return	  函数执行结果，用来判断数据是否有效
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool train_list_map_get_upload_tick(int id, struct timespec *tic_out);



    /**@brief	  获取同当前指定的网络通信ip绑定的车辆ID
	* @param[in]  uint32_t cin_addr --- 指定的网络通信IP
	* @return	  车辆ID
	*/
	int train_sock_list_get_id(uint32_t cin_addr);

    
    /**@brief	  获取指定对象的最新一次数据下行时刻时间信息
	* @param[in]  int id --- 车辆ID
	* @param[in]  struct timespec *tic_out --- 上行数据时间结构体指针
	* @return	  函数执行结果，用来判断数据是否有效
	* - false	  
	* - true	  
	*/
	bool train_list_map_get_download_tick(int id, struct timespec *tic_out);

    int devid_to_train_carriage(uint8_t id_temp, uint32_t *dev_id, uint32_t *carriage);

    void travese_all_pending_exception_for_train(int id, int carriage_num, std::function<void (const except_info&)> f);


    /**@brief     车辆任务列表深度查询
	* @param[in]  int dev_id --- 指定的车辆号
	* @return     当前任务队列深度
	*/
	int train_task_list_size(int dev_id);


    /**@brief	  获取当前所有活跃车辆的ID信息
	* @param[out]  int *id_queue --- 车辆ID数据缓冲区
	* @return	  NULL
	*/
	void train_list_map_get_all_id(int *id_queue);



    /**@brief     车辆任务列表非空查询
	* @param[in]  int dev_id --- 指定的车辆号
	* @return     空满状态
	* - true    ---  空
	* - false   ---  非空
	*/
	bool train_task_list_empty(int dev_id);

    void train_task_list_clear(int dev_id);

    int exception_cnt_get(uint16_t &devid);
    int exception_cnt_get(uint8_t devid, int carriage_num);
    int exception_occur(const except_info&);
    int exception_resume(int carriage_num, const except_info&);
    void exception_resume_all(int train_id);


	/**@brief	  将pending_list中没有的异常插入到异常表中
    * @param[in]  uint8_t devid --- 异常车辆ID, 包括车厢号
	* @param[in]  const except_info& --- 车辆异常结构体指针
	* @return	  异常表中是否存在该异常
        * - 0    ---  存在
	    * - 1   ---  不存在
	*/
    int exception_occur(uint16_t devid, const except_info&);

    _train_map_opt_tab pending_excepts_find(int devid);

private:

    train_comm_sequence_map m_train_comm_sequ_list;     ///<下行sequence通信列表 <platform_id -> sequence>

    train_info_sock_map m_train_sock_list;              ///< 通信列表 <cin_addr -> dev_id>
    train_info_map m_train_dev_list;                    ///< 车辆列表 <dev_id -> train_info>
    platform_task_map m_platform_dev_list;
    train_task_info_map m_train_platform_task_state_list;
    train_task_info_map m_train_carriage_task_state_list;
    train_position_info_map m_train_position_list;      ///< 车辆实时位置信息列表 <dev_id -> train_position_info>
    train_task_map m_train_task_list; 			        ///< 任务缓存列表 <dev_id -> blocking_queue<train_task>>

    train_log_map m_train_log_list;                     ///< 车辆日志列表 <dev_id -> logger>

    std::mutex m_train_lot_mtx;	           		// 车辆日志互斥锁
    std::mutex m_train_dev_list_mtx;            //车辆列表互斥锁<dev_id -> train_info>
    std::mutex m_platform_dev_list_mtx;            //车辆列表互斥锁<dev_id -> net_msg>
    std::mutex m_train_dev_pos_mtx;             //车辆位置信息互斥锁<dev_id -> train_position_info>
    std::mutex m_train_dev_platform_task_state_mtx;      //车辆载货台任务状态互斥锁<dev_id -> m_train_task_state_list>
    std::mutex m_train_dev_carriage_task_state_mtx;      //车辆Y轴任务状态互斥锁<dev_id -> m_train_task_state_list>
    std::mutex m_train_dev_comm_mtx;	        // 车辆列表互斥锁<id -> sequence>
    std::mutex train_task_opt_mutex;
    std::mutex m_train_delete_mtx;	           	// 车辆列表删除互斥锁
    std::mutex m_train_dev_seq_mtx;	           // 车辆通信序列号互斥锁

    std::mutex lock_for_excepts;
    train_except_map pending_excepts;           ///< 异常信息列表 <dev_id -> except_info>

};


#endif