# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include net/CMakeFiles/lib_net.dir/depend.make

# Include the progress variables for this target.
include net/CMakeFiles/lib_net.dir/progress.make

# Include the compile flags for this target's objects.
include net/CMakeFiles/lib_net.dir/flags.make

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o: net/CMakeFiles/lib_net.dir/flags.make
net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o: ../net/tcp_socket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_net.dir/tcp_socket.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_net.dir/tcp_socket.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp > CMakeFiles/lib_net.dir/tcp_socket.cpp.i

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_net.dir/tcp_socket.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp -o CMakeFiles/lib_net.dir/tcp_socket.cpp.s

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.requires:

.PHONY : net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.requires

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.provides: net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.requires
	$(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.provides.build
.PHONY : net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.provides

net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.provides.build: net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o


net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o: net/CMakeFiles/lib_net.dir/flags.make
net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o: ../net/epoll_poller.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_net.dir/epoll_poller.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp

net/CMakeFiles/lib_net.dir/epoll_poller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_net.dir/epoll_poller.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp > CMakeFiles/lib_net.dir/epoll_poller.cpp.i

net/CMakeFiles/lib_net.dir/epoll_poller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_net.dir/epoll_poller.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp -o CMakeFiles/lib_net.dir/epoll_poller.cpp.s

net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.requires:

.PHONY : net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.requires

net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.provides: net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.requires
	$(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.provides.build
.PHONY : net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.provides

net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.provides.build: net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o


net/CMakeFiles/lib_net.dir/udp_socket.cpp.o: net/CMakeFiles/lib_net.dir/flags.make
net/CMakeFiles/lib_net.dir/udp_socket.cpp.o: ../net/udp_socket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object net/CMakeFiles/lib_net.dir/udp_socket.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_net.dir/udp_socket.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp

net/CMakeFiles/lib_net.dir/udp_socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_net.dir/udp_socket.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp > CMakeFiles/lib_net.dir/udp_socket.cpp.i

net/CMakeFiles/lib_net.dir/udp_socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_net.dir/udp_socket.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp -o CMakeFiles/lib_net.dir/udp_socket.cpp.s

net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.requires:

.PHONY : net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.requires

net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.provides: net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.requires
	$(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.provides.build
.PHONY : net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.provides

net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.provides.build: net/CMakeFiles/lib_net.dir/udp_socket.cpp.o


# Object files for target lib_net
lib_net_OBJECTS = \
"CMakeFiles/lib_net.dir/tcp_socket.cpp.o" \
"CMakeFiles/lib_net.dir/epoll_poller.cpp.o" \
"CMakeFiles/lib_net.dir/udp_socket.cpp.o"

# External object files for target lib_net
lib_net_EXTERNAL_OBJECTS =

net/liblib_net.a: net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o
net/liblib_net.a: net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o
net/liblib_net.a: net/CMakeFiles/lib_net.dir/udp_socket.cpp.o
net/liblib_net.a: net/CMakeFiles/lib_net.dir/build.make
net/liblib_net.a: net/CMakeFiles/lib_net.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library liblib_net.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && $(CMAKE_COMMAND) -P CMakeFiles/lib_net.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lib_net.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
net/CMakeFiles/lib_net.dir/build: net/liblib_net.a

.PHONY : net/CMakeFiles/lib_net.dir/build

net/CMakeFiles/lib_net.dir/requires: net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o.requires
net/CMakeFiles/lib_net.dir/requires: net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o.requires
net/CMakeFiles/lib_net.dir/requires: net/CMakeFiles/lib_net.dir/udp_socket.cpp.o.requires

.PHONY : net/CMakeFiles/lib_net.dir/requires

net/CMakeFiles/lib_net.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net && $(CMAKE_COMMAND) -P CMakeFiles/lib_net.dir/cmake_clean.cmake
.PHONY : net/CMakeFiles/lib_net.dir/clean

net/CMakeFiles/lib_net.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/net /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net/CMakeFiles/lib_net.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : net/CMakeFiles/lib_net.dir/depend

