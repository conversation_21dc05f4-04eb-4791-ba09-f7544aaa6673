﻿
/**@file  epoll_poller.hpp
* @brief       基于epoll的并发客户端管理软件头文件
* @details     NULL
* <AUTHOR>
* @date        2021-06-22
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持多客户端连接              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对epoll 机制API进行二次封装，使用class成员函数实现功能调用 
* -# 使用virtual定义epoll的主循环和事件处理 
* </table>
*
**********************************************************************************
*/



#ifndef __NET_EPOLL_POLLER_HPP__
#define __NET_EPOLL_POLLER_HPP__

#include "tcp_socket.hpp"

#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <sys/epoll.h>
#include <functional>
#include <iostream>
#include <string>
#include <unordered_map>
#include <memory>
#include <stdlib.h>
#include <string.h>
#include <mutex>

using namespace std;

/**
* @brief 实现epoll API的二次封装，并设计unordered_map用来保存现有活跃连接
*/

class epoll_poller 
{
public:

#define EPOLL_POLLER_CREATE_ERR					( -1 )
#define EPOLL_POLLER_MAX_EVENT_CNT				( 1000 )

	/**@brief  epoll_poller class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	explicit epoll_poller();


	/**@brief  epoll_poller class析构函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	~epoll_poller();


	/**@brief	  TCP 通信中 Server 构造
	* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	void epoll_init(int listen_fd);


	/**@brief	  epoll事件等待
	* @param[in]  int timeout --- 等待超时函数，避免长时间阻塞等待
	* @return	  需要处理处理的事件数目
	*/
	int epoll_wait_fd(int timeout);
	
	/**@brief	  向已创建的epoll fd中添加待监听的client描述
	* @param[in]  int fd --- 待添加的client设备的 fd
	* @param[in]  int mode --- 指定的epoll触发模式
	* @ref			EPOLLIN  水平触发
	* @ref			EPOLLET  边沿触发
	* @param[in]  bool one_shot --- 是否触发一次的选项
	* @ref			true  设置为单次触发，后续若需要再次触发需二次处理
	* @ref			false 设置为自动触发
	* @return	  函数执行结果
	* - false	  添加失败
	* - true	  添加成功
	*/
	bool epoll_add_fd(int fd, int mode, bool one_shot);


	/**@brief	  向已创建的epoll fd中移除指定的client
	* @param[in]  int fd --- 待处理的client设备的 fd
	* @return	  函数执行结果
	* - false	  移除失败
	* - true	  移除成功
	*/
	bool epoll_remove_fd(int fd);

	
	/**@brief	  重置已有的epoll对象，整体流程同添加操作类似
	* @param[in]  int fd --- 待添加的client设备的 fd
	* @param[in]  int mode --- 指定的epoll触发模式
	* @ref			EPOLLIN  水平触发
	* @ref			EPOLLET  边沿触发
	* @param[in]  bool one_shot --- 是否触发一次的选项
	* @ref			true  设置为单次触发，后续若需要再次触发需二次处理
	* @ref			false 设置为自动触发
	* @return	  函数执行结果
	* - false	  移除失败
	* - true	  移除成功
	*/
	bool epoll_reset_fd(int fd, int mode, bool one_shot);
	
	
	/**@brief	  epoll监听停止，暂时没用
	* @param[in]  NULL
	* @return	  NULL
	*/
	void epoll_poller_stop();

	
	/**@brief	  在已有活跃对象中查找指定IP是否存在(暂时没用)
	* @param[in]  uint32_t cin_addr	--- 待查找的 IP
	* @return	  查找执行结果
	* - false	  查找失败，该IP不存在
	* - true	  查找成功，该IP存在
	*/
	bool epoll_poller_dev_find(uint32_t cin_addr);


	/**@brief	  向当前列表中插入新连接
	* @param[in]  uint32_t cin_addr --- 待插入的client设备的 IP
	* @param[in]  struct sockaddr_in sock_info --- 该连接对应的IP地址及端口号
	* @return	  函数执行结果
	* - false	  插入失败
	* - true	  插入成功
	*/
	bool epoll_poller_dev_insert(uint32_t cin_addr, struct sockaddr_in sock_info);


	/**@brief	  向当前列表中移除连接
	* @param[in]  uint32_t cin_addr --- 待处理的client设备的 ip
	* @return	  函数执行结果
	* - false	  移除失败
	* - true	  移除成功
	*/
	bool epoll_poller_dev_delete(uint32_t cin_addr);


	/**@brief	  查找指定的ip的IP地址等信息
	* @param[in]  uint32_t cin_addr --- 待处理的client设备的 ip
	* @return	  该连接的 IP地址等信息
	*/
	struct sockaddr_in epoll_poller_dev_get_sock_addr(uint32_t cin_addr);

	/**@brief     显示当前活跃连接的所有内容(调试使用)
    * @param[in]  NULL
    * @return     NULL
    */
	void display(void);

	/**@brief	  epoll事件监听主循环
	* @param[in]  int timeout --- 事件监听超时设置
	* @return	  该连接的 IP地址等信息
	*/
	virtual int epoll_main_loop(int timeout)= 0;

	/**@brief	  epoll新建连接处理函数
	* @param[in]  int sockfd --- 新建连接的 fd
	* @return	  NULL 
	*/
	virtual void epoll_newConnection(int sockfd) = 0;

	/**@brief	  epoll已有连接的事件处理函数
	* @param[in]  int sockfd --- 触发事件信号的 fd
	* @return	  NULL 
	*/
	virtual void epoll_existConnection(int sockfd) = 0;
	
	
protected:
	
	epoll_event m_events[EPOLL_POLLER_MAX_EVENT_CNT];

	int m_epollfd;
	int m_server_fd; 
	int m_eventfd;	//暂没使用

	bool m_isLooping;

	unordered_map<uint32_t, struct sockaddr_in> m_epoll_dev_list;

	std::mutex m_opt_lock;


};



#endif
