#ifndef __SCHEDULER_MSG_SCHEDULER_MSG_HPP__
#define __SCHEDULER_MSG_SCHEDULER_MSG_HPP__

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include "threadpool/blocking_queue.hpp"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/train_info.pb.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/ack.pb.h"
#include "train_manage/train_list.hpp"
#include "fsm_manager/fsm_manager.hpp"


#include <iostream>
#include "train_agent_debug.h"

#define _IS_ALL_DEV_TASK(n)				(9999==n)

/**@enum __MSG_PUB_TYPE
* @brief 定义的需要PUB的消息类型，用来解析后续的数据是何种消息
*/
typedef enum
{
	TRAIN_STATE_PUB = 0,
	TRAIN_AGENT_STATE_PUB = 1,
	TRAIN_CARRIAGE_TASK_STATE_PUB = 2,
	TRAIN_PLATFORM_TASK_STATE_PUB = 3,
	TRAIN_EXCEP_PUB = 4,
	TRAIN_TASK_OUT = 5,		//调度发过来的小车任务
	TRAIN_RESET_PUB = 6,
	TRAIN_TASK_STATE_HB_PUB = 7,
	
}__MSG_PUB_TYPE;


/**@struct msg_queue
* @brief 定义的用来构成blocking queue的数据结构，使用枚举定义类型，用来指定后续data的解析
*/
typedef struct _msg_queue
{
	__MSG_PUB_TYPE type;	// 任务可执行对象
	int  train_id;
	int carriage_id;
	uint8_t msg_data[train_state_size];
}msg_queue;


typedef struct _map_calib_point_info
{
    uint8_t id;
    uint32_t position;
} map_calib_point_info;

typedef struct _train_info_t
{
	uint8_t train_id;
	uint8_t carriage_cnt;
	uint8_t platform_type;
}train_info_t;

//用于回复注册报文的消息
typedef struct _ack_map_info
{
	// uint8_t train_count;
	// train_info_t train_info[20];
	train_basic_info_mutilp train_init_info;

	uint32_t map_total_length;
	uint16_t carriage_max_travel;
    uint8_t map_dev_calib_point_cnt;

    map_calib_point_info map_calib_points[20];
}ack_map_info;


class scheduler_manager
{

public:

    ~scheduler_manager();

    /**@brief	  scheduler_manager class构造函数，在构造列表里构造ZMQ socket
	* @param[in]  zmq::context_t &context ZMQ创建的上下文
	* @return	  NULL
	*/
	explicit scheduler_manager(zmq::context_t &context);

    /**@brief	  scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  server创建成功
	*/
	bool scheduler_manager_init(ack_map_info *map_info);

	
	/**@brief	  scheduler_manager 运行函数，创建线程并运行
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  server创建成功
	*/
	bool scheduler_manager_run(fsm_manager& fsm); 
	

	/**@brief	  blocking queue push函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue &data --- 待操作的数据
	* @return	  NULL
	*/
	void scheduler_manager_queue_push(msg_queue &data);


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue &data --- 待操作的数据
	* @return	  NULL
	*/
	void scheduler_manager_queue_pop(msg_queue &data);

	/**@brief	  车辆设备消息的发布，基于ZMQ 的PUB-SUB模式
	* @param[in]  train_state *dev_state --- 待操作的车辆状态数据结构体指针
	* @return	  操作结构
	* - true	  发布成功
	* - false	  发布失败
	*/
	bool scheduler_manager_train_state_pub(train_state *dev_state);

	bool scheduler_manager_train_agent_state_pub(train_agent_state *agent_state);
	bool scheduler_manager_train_carriage_task_state_pub(train_task_state *task_state);
	bool scheduler_manager_train_platform_task_state_pub(train_task_state *task_state);

	/**@brief	  blocking queue push函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue *data --- 待操作的数据指针
	* @return	  NULL
	*/
	void scheduler_manager_task_msg_queue_push(msg_queue &data);



	/**@brief	  车辆设备异常消息的发布，基于ZMQ 的PUB-SUB模式
	* @param[in]  exception_info *info --- 待操作的车辆异常数据结构体指针
	* @return	  操作结构
	* - true	  发布成功
	* - false	  发布失败
	*/
	bool scheduler_manager_train_exception_pub(event_exception *info, fsm_manager& fsm); 

	
	/**@brief     从database获取当前的设备map信息
	* @param[out]  data_map *map --- 获取的map信息数据
	* @return     操作结构
	* - true      获取成功
	* - false     获取失败
	*/
	bool scheduler_manager_get_data_map_info(data_map *map);


	/**@brief	  通过REQ-REP模式从coreserver获取数据
	* @param[out] train_basic_info_mutilp *dev_list  --- 获取到的coreserver发布的设备列表
	* @return	  当前函数执行结果，用于判断 dev_list的有效性
	* - true	  成功
	* - false	  失败
	*/
	bool scheduler_manager_get_train_info(train_basic_info_mutilp &dev_list);



	/**@brief	  同scheduler通信的线程，基于REQ-REP模式，接收scheduler发送的车辆任务，并通过PUB-SUB模式发布消息
	* @param[in]  NULL
	* @return	  PB的执行结果(其实没用到)
	* - true	  成功
	* - false	  失败
	*/
	bool scheduler_manager_thread_task_reply(void);


	void scheduler_manager_thead_sendmsg(fsm_manager& fsm);

	void schedule_test(fsm_manager& fsm);

	
	/**@brief     提取map信息中，用于回复注册报文的消息
	* @param[in]  train_basic_info_mutilp m_train_basic_info --- 从coreserver获取到的车辆信息
	* @param[in]  data_map map --- 获取的map信息数据
	* @param[out]  ack_map_info *map_info --- 提取到的ack数据
	* @return     操作结构
	* - true      获取成功
	* - false     获取失败
	*/
	int get_register_ack_info(train_basic_info_mutilp m_train_basic_info, data_map map, ack_map_info *map_info);


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue *data --- 待操作的数据指针
	* @return	  NULL
	*/
	void scheduler_manager_task_msg_queue_pop(msg_queue *data);
	bool scheduler_manager_task_msg_queue_empty(void);
	int scheduler_manager_task_msg_queue_size(void);


private:

    zmq::socket_t m_train_state_publisher;          ///车辆状态socket PUB 类型
    zmq::socket_t m_train_agent_state_publisher;  ///<车代理状态socket PUB 类型
	zmq::socket_t m_train_task_platform_state_publisher;  ///<车辆任务状态socket PUB 类型
	zmq::socket_t m_train_task_carriage_state_publisher;  ///<车辆任务状态socket PUB 类型
	zmq::socket_t m_train_excep_publisher;  	///< 车辆异常socket PUB 类型

	zmq::socket_t m_data_requester;             ///< coreserver通信socket REQ 类型
	zmq::socket_t m_task_replayer;   			///< scheduler消息响应socket REP 类型

	blocking_queue<msg_queue> m_scheduler_msg_queue_ptr;
	
	// blocking_queue<msg_queue> m_scheduler_all_dev_task_msg;

	blocking_queue<msg_queue> m_scheduler_task_msg;
	
	std::mutex m_train_basic_info_mtx;	           	// train_basic_info获取互斥锁

};







#endif  





