/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_FEEDER_INTERFACE_PB_H_INCLUDED
#define PB_FEEDER_INTERFACE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _action {
    action_RESERVE = 0, /* 无意义 */
    action_START = 1, /* 启动命令 */
    action_STOP = 2, /* 停止命令 */
    action_RESET = 3, /* 重启命令 */
    action_ROLL_FORWARD = 4, /* 供包台皮带前向转动，用于测试 */
    action_ROLL_BACKWARD = 5, /* 供包台皮带后向转动，用于测试和商品回退 */
    action_SUPPLY = 6, /* 供包命令 */
    action_SCANNING = 7, /* 扫码命令，强制扫码 */
    action_SET = 8
} action;

typedef enum _action_para {
    action_para_BELT_RESERVE = 0, /* 无意义 */
    action_para_BELT_1 = 1, /* 1段皮带 */
    action_para_BELT_2 = 2, /* 2段皮带 */
    action_para_BELT_3 = 3, /* 3段皮带 */
    action_para_ROLL_4 = 4, /* 4段皮带，用于测试 */
    action_para_ROLL_ALL = 5 /* 所有皮带， */
} action_para;

typedef enum _feeder_supply_state_tab {
    feeder_supply_state_tab_SUPPLY_RESERVE = 0,
    feeder_supply_state_tab_SUPPLY_IDLE = 1,
    feeder_supply_state_tab_SUPPLY_START = 2,
    feeder_supply_state_tab_SUPPLY_RUNNING = 3,
    feeder_supply_state_tab_SUPPLY_FINISH = 4,
    feeder_supply_state_tab_SUPPLY_ERROR = 5
} feeder_supply_state_tab;

typedef enum _feeder_belt_motor_id {
    feeder_belt_motor_id_BELT_MOTOR_1 = 0,
    feeder_belt_motor_id_BELT_MOTOR_2 = 1,
    feeder_belt_motor_id_BELT_MOTOR_3 = 2,
    feeder_belt_motor_id_BELT_MOTOR_4 = 3,
    feeder_belt_motor_id_BELT_MOTOR_5 = 4,
    feeder_belt_motor_id_BELT_MOTOR_6 = 5
} feeder_belt_motor_id;

typedef enum _feeder_belt_sensor_id {
    feeder_belt_sensor_id_SENSOR_1 = 0,
    feeder_belt_sensor_id_SENSOR_2 = 1,
    feeder_belt_sensor_id_SENSOR_3 = 2,
    feeder_belt_sensor_id_SENSOR_4 = 3,
    feeder_belt_sensor_id_SENSOR_5 = 4,
    feeder_belt_sensor_id_SENSOR_6 = 5,
    feeder_belt_sensor_id_SENSOR_7 = 6,
    feeder_belt_sensor_id_SENSOR_8 = 7
} feeder_belt_sensor_id;

typedef enum _feeder_dev_work_state {
    feeder_dev_work_state_DEV_WORK_STATE_INIT = 0,
    feeder_dev_work_state_DEV_WORK_STATE_POWER_UP = 1,
    feeder_dev_work_state_DEV_WORK_STATE_BUSY = 2,
    feeder_dev_work_state_DEV_WORK_STATE_IDEL = 3,
    feeder_dev_work_state_DEV_WORK_STATE_SHIELD = 4
} feeder_dev_work_state;

typedef enum _feeder_dev_state {
    feeder_dev_state_DEV_STATE_NORMAL = 0,
    feeder_dev_state_DEV_STATE_ERR = 1,
    feeder_dev_state_DEV_STATE_UNKNOWN = 2
} feeder_dev_state;

typedef enum _feeder_sensor_trigger_state {
    feeder_sensor_trigger_state_ON = 0,
    feeder_sensor_trigger_state_OFF = 1,
    feeder_sensor_trigger_state_UNKNOW = 2
} feeder_sensor_trigger_state;

typedef enum _goods_position {
    /* 手持扫码，商品尚未放到皮带上。有可能用不到。 */
    goods_position_UNDETERMINED = 0,
    goods_position_FIRST_BELT = 1,
    goods_position_SECOND_BELT = 2,
    goods_position_THIRD_BELT = 3,
    goods_position_FOURTH_BELT = 4,
    goods_position_LAST_BELT = 255
} goods_position;

typedef enum _scanner {
    scanner_NONE = 0
} scanner;

/* Struct definitions */
typedef struct _feeder_cmd {
    uint32_t sequnce;
    uint32_t feeder_id;
    uint32_t sub_dev_id;
    action act;
    action_para para;
    int32_t belt_speed;
    uint32_t train_id;
    uint32_t platform_id;
    uint32_t manual_length;
} feeder_cmd;

typedef struct _feeder_supply_state {
    uint32_t feeder_id;
    uint32_t sub_dev_id;
    feeder_supply_state_tab state;
} feeder_supply_state;

typedef struct _belt_motor_single_state {
    feeder_belt_motor_id dev_id;
    feeder_dev_work_state wk_state;
    feeder_dev_state state;
    int32_t speed;
    uint32_t state_code;
} belt_motor_single_state;

typedef struct _belt_sensor_single_state {
    feeder_belt_sensor_id dev_id;
    feeder_sensor_trigger_state state;
} belt_sensor_single_state;

typedef struct _feeder_belt_sensor_state_multiple {
    pb_size_t sensor_count;
    belt_sensor_single_state sensor[8];
} feeder_belt_sensor_state_multiple;

typedef struct _feeder_belt_motor_state_multiple {
    pb_size_t motor_count;
    belt_motor_single_state motor[6];
} feeder_belt_motor_state_multiple;

typedef struct _scanner_state {
    feeder_dev_work_state wk_state;
    feeder_dev_state state;
    uint32_t err_code;
    uint32_t feeder_id;
} scanner_state;

typedef struct _charger_state {
    feeder_dev_work_state wk_state;
    feeder_dev_state state;
    uint32_t err_code;
    uint32_t charger_vol;
    uint32_t charger_curr;
    uint32_t feeder_id;
} charger_state;

typedef struct _feeder_dev_state_total {
    uint32_t dev_id;
    bool has_belt_motor;
    feeder_belt_motor_state_multiple belt_motor;
    bool has_auto_scanner;
    scanner_state auto_scanner;
    bool has_manual_scanner;
    scanner_state manual_scanner;
    bool has_charger;
    charger_state charger;
    bool has_belt_sensor;
    feeder_belt_sensor_state_multiple belt_sensor;
    uint32_t err_code;
    uint32_t feeder_id;
    component_state state;
    int32_t has_goods;
    bool ready_state;
    uint32_t excp_handle;
    uint32_t sequence;
} feeder_dev_state_total;

typedef struct _goods_info {
    /* 单个商品会有多个码，传到调度系统的是一串字符串由逗号隔开。调度系统内部也不做解析，沿用此格式即可。 */
    char codes[351];
    goods_position position;
    /* 扫码信息上报时，scanner_id按照实际扫码设备上报。上报末端商品信息时，scanner_id 设NONE即可。 */
    uint32_t scanner_id;
} goods_info;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _action_MIN action_RESERVE
#define _action_MAX action_SET
#define _action_ARRAYSIZE ((action)(action_SET+1))

#define _action_para_MIN action_para_BELT_RESERVE
#define _action_para_MAX action_para_ROLL_ALL
#define _action_para_ARRAYSIZE ((action_para)(action_para_ROLL_ALL+1))

#define _feeder_supply_state_tab_MIN feeder_supply_state_tab_SUPPLY_RESERVE
#define _feeder_supply_state_tab_MAX feeder_supply_state_tab_SUPPLY_ERROR
#define _feeder_supply_state_tab_ARRAYSIZE ((feeder_supply_state_tab)(feeder_supply_state_tab_SUPPLY_ERROR+1))

#define _feeder_belt_motor_id_MIN feeder_belt_motor_id_BELT_MOTOR_1
#define _feeder_belt_motor_id_MAX feeder_belt_motor_id_BELT_MOTOR_6
#define _feeder_belt_motor_id_ARRAYSIZE ((feeder_belt_motor_id)(feeder_belt_motor_id_BELT_MOTOR_6+1))

#define _feeder_belt_sensor_id_MIN feeder_belt_sensor_id_SENSOR_1
#define _feeder_belt_sensor_id_MAX feeder_belt_sensor_id_SENSOR_8
#define _feeder_belt_sensor_id_ARRAYSIZE ((feeder_belt_sensor_id)(feeder_belt_sensor_id_SENSOR_8+1))

#define _feeder_dev_work_state_MIN feeder_dev_work_state_DEV_WORK_STATE_INIT
#define _feeder_dev_work_state_MAX feeder_dev_work_state_DEV_WORK_STATE_SHIELD
#define _feeder_dev_work_state_ARRAYSIZE ((feeder_dev_work_state)(feeder_dev_work_state_DEV_WORK_STATE_SHIELD+1))

#define _feeder_dev_state_MIN feeder_dev_state_DEV_STATE_NORMAL
#define _feeder_dev_state_MAX feeder_dev_state_DEV_STATE_UNKNOWN
#define _feeder_dev_state_ARRAYSIZE ((feeder_dev_state)(feeder_dev_state_DEV_STATE_UNKNOWN+1))

#define _feeder_sensor_trigger_state_MIN feeder_sensor_trigger_state_ON
#define _feeder_sensor_trigger_state_MAX feeder_sensor_trigger_state_UNKNOW
#define _feeder_sensor_trigger_state_ARRAYSIZE ((feeder_sensor_trigger_state)(feeder_sensor_trigger_state_UNKNOW+1))

#define _goods_position_MIN goods_position_UNDETERMINED
#define _goods_position_MAX goods_position_LAST_BELT
#define _goods_position_ARRAYSIZE ((goods_position)(goods_position_LAST_BELT+1))

#define _scanner_MIN scanner_NONE
#define _scanner_MAX scanner_NONE
#define _scanner_ARRAYSIZE ((scanner)(scanner_NONE+1))

#define feeder_cmd_act_ENUMTYPE action
#define feeder_cmd_para_ENUMTYPE action_para

#define feeder_supply_state_state_ENUMTYPE feeder_supply_state_tab

#define belt_motor_single_state_dev_id_ENUMTYPE feeder_belt_motor_id
#define belt_motor_single_state_wk_state_ENUMTYPE feeder_dev_work_state
#define belt_motor_single_state_state_ENUMTYPE feeder_dev_state

#define belt_sensor_single_state_dev_id_ENUMTYPE feeder_belt_sensor_id
#define belt_sensor_single_state_state_ENUMTYPE feeder_sensor_trigger_state



#define scanner_state_wk_state_ENUMTYPE feeder_dev_work_state
#define scanner_state_state_ENUMTYPE feeder_dev_state

#define charger_state_wk_state_ENUMTYPE feeder_dev_work_state
#define charger_state_state_ENUMTYPE feeder_dev_state

#define feeder_dev_state_total_state_ENUMTYPE component_state

#define goods_info_position_ENUMTYPE goods_position


/* Initializer values for message structs */
#define feeder_cmd_init_default                  {0, 0, 0, _action_MIN, _action_para_MIN, 0, 0, 0, 0}
#define feeder_supply_state_init_default         {0, 0, _feeder_supply_state_tab_MIN}
#define belt_motor_single_state_init_default     {_feeder_belt_motor_id_MIN, _feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0}
#define belt_sensor_single_state_init_default    {_feeder_belt_sensor_id_MIN, _feeder_sensor_trigger_state_MIN}
#define feeder_belt_sensor_state_multiple_init_default {0, {belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default, belt_sensor_single_state_init_default}}
#define feeder_belt_motor_state_multiple_init_default {0, {belt_motor_single_state_init_default, belt_motor_single_state_init_default, belt_motor_single_state_init_default, belt_motor_single_state_init_default, belt_motor_single_state_init_default, belt_motor_single_state_init_default}}
#define scanner_state_init_default               {_feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0}
#define charger_state_init_default               {_feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0, 0, 0}
#define feeder_dev_state_total_init_default      {0, false, feeder_belt_motor_state_multiple_init_default, false, scanner_state_init_default, false, scanner_state_init_default, false, charger_state_init_default, false, feeder_belt_sensor_state_multiple_init_default, 0, 0, _component_state_MIN, 0, 0, 0, 0}
#define goods_info_init_default                  {"", _goods_position_MIN, 0}
#define feeder_cmd_init_zero                     {0, 0, 0, _action_MIN, _action_para_MIN, 0, 0, 0, 0}
#define feeder_supply_state_init_zero            {0, 0, _feeder_supply_state_tab_MIN}
#define belt_motor_single_state_init_zero        {_feeder_belt_motor_id_MIN, _feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0}
#define belt_sensor_single_state_init_zero       {_feeder_belt_sensor_id_MIN, _feeder_sensor_trigger_state_MIN}
#define feeder_belt_sensor_state_multiple_init_zero {0, {belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero, belt_sensor_single_state_init_zero}}
#define feeder_belt_motor_state_multiple_init_zero {0, {belt_motor_single_state_init_zero, belt_motor_single_state_init_zero, belt_motor_single_state_init_zero, belt_motor_single_state_init_zero, belt_motor_single_state_init_zero, belt_motor_single_state_init_zero}}
#define scanner_state_init_zero                  {_feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0}
#define charger_state_init_zero                  {_feeder_dev_work_state_MIN, _feeder_dev_state_MIN, 0, 0, 0, 0}
#define feeder_dev_state_total_init_zero         {0, false, feeder_belt_motor_state_multiple_init_zero, false, scanner_state_init_zero, false, scanner_state_init_zero, false, charger_state_init_zero, false, feeder_belt_sensor_state_multiple_init_zero, 0, 0, _component_state_MIN, 0, 0, 0, 0}
#define goods_info_init_zero                     {"", _goods_position_MIN, 0}

/* Field tags (for use in manual encoding/decoding) */
#define feeder_cmd_sequnce_tag                   1
#define feeder_cmd_feeder_id_tag                 2
#define feeder_cmd_sub_dev_id_tag                3
#define feeder_cmd_act_tag                       4
#define feeder_cmd_para_tag                      5
#define feeder_cmd_belt_speed_tag                6
#define feeder_cmd_train_id_tag                  7
#define feeder_cmd_platform_id_tag               8
#define feeder_cmd_manual_length_tag             9
#define feeder_supply_state_feeder_id_tag        1
#define feeder_supply_state_sub_dev_id_tag       2
#define feeder_supply_state_state_tag            3
#define belt_motor_single_state_dev_id_tag       1
#define belt_motor_single_state_wk_state_tag     2
#define belt_motor_single_state_state_tag        3
#define belt_motor_single_state_speed_tag        4
#define belt_motor_single_state_state_code_tag   5
#define belt_sensor_single_state_dev_id_tag      1
#define belt_sensor_single_state_state_tag       2
#define feeder_belt_sensor_state_multiple_sensor_tag 2
#define feeder_belt_motor_state_multiple_motor_tag 2
#define scanner_state_wk_state_tag               1
#define scanner_state_state_tag                  2
#define scanner_state_err_code_tag               3
#define scanner_state_feeder_id_tag              4
#define charger_state_wk_state_tag               1
#define charger_state_state_tag                  2
#define charger_state_err_code_tag               3
#define charger_state_charger_vol_tag            4
#define charger_state_charger_curr_tag           5
#define charger_state_feeder_id_tag              6
#define feeder_dev_state_total_dev_id_tag        1
#define feeder_dev_state_total_belt_motor_tag    2
#define feeder_dev_state_total_auto_scanner_tag  3
#define feeder_dev_state_total_manual_scanner_tag 4
#define feeder_dev_state_total_charger_tag       5
#define feeder_dev_state_total_belt_sensor_tag   6
#define feeder_dev_state_total_err_code_tag      7
#define feeder_dev_state_total_feeder_id_tag     8
#define feeder_dev_state_total_state_tag         9
#define feeder_dev_state_total_has_goods_tag     10
#define feeder_dev_state_total_ready_state_tag   11
#define feeder_dev_state_total_excp_handle_tag   12
#define feeder_dev_state_total_sequence_tag      13
#define goods_info_codes_tag                     1
#define goods_info_position_tag                  2
#define goods_info_scanner_id_tag                3

/* Struct field encoding specification for nanopb */
#define feeder_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequnce,           1) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         2) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        3) \
X(a, STATIC,   SINGULAR, UENUM,    act,               4) \
X(a, STATIC,   SINGULAR, UENUM,    para,              5) \
X(a, STATIC,   SINGULAR, INT32,    belt_speed,        6) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          7) \
X(a, STATIC,   SINGULAR, UINT32,   platform_id,       8) \
X(a, STATIC,   SINGULAR, UINT32,   manual_length,     9)
#define feeder_cmd_CALLBACK NULL
#define feeder_cmd_DEFAULT NULL

#define feeder_supply_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         1) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        2) \
X(a, STATIC,   SINGULAR, UENUM,    state,             3)
#define feeder_supply_state_CALLBACK NULL
#define feeder_supply_state_DEFAULT NULL

#define belt_motor_single_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    wk_state,          2) \
X(a, STATIC,   SINGULAR, UENUM,    state,             3) \
X(a, STATIC,   SINGULAR, INT32,    speed,             4) \
X(a, STATIC,   SINGULAR, UINT32,   state_code,        5)
#define belt_motor_single_state_CALLBACK NULL
#define belt_motor_single_state_DEFAULT NULL

#define belt_sensor_single_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2)
#define belt_sensor_single_state_CALLBACK NULL
#define belt_sensor_single_state_DEFAULT NULL

#define feeder_belt_sensor_state_multiple_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  sensor,            2)
#define feeder_belt_sensor_state_multiple_CALLBACK NULL
#define feeder_belt_sensor_state_multiple_DEFAULT NULL
#define feeder_belt_sensor_state_multiple_sensor_MSGTYPE belt_sensor_single_state

#define feeder_belt_motor_state_multiple_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  motor,             2)
#define feeder_belt_motor_state_multiple_CALLBACK NULL
#define feeder_belt_motor_state_multiple_DEFAULT NULL
#define feeder_belt_motor_state_multiple_motor_MSGTYPE belt_motor_single_state

#define scanner_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    wk_state,          1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UINT32,   err_code,          3) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         4)
#define scanner_state_CALLBACK NULL
#define scanner_state_DEFAULT NULL

#define charger_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    wk_state,          1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UINT32,   err_code,          3) \
X(a, STATIC,   SINGULAR, UINT32,   charger_vol,       4) \
X(a, STATIC,   SINGULAR, UINT32,   charger_curr,      5) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         6)
#define charger_state_CALLBACK NULL
#define charger_state_DEFAULT NULL

#define feeder_dev_state_total_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  belt_motor,        2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  auto_scanner,      3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  manual_scanner,    4) \
X(a, STATIC,   OPTIONAL, MESSAGE,  charger,           5) \
X(a, STATIC,   OPTIONAL, MESSAGE,  belt_sensor,       6) \
X(a, STATIC,   SINGULAR, UINT32,   err_code,          7) \
X(a, STATIC,   SINGULAR, UINT32,   feeder_id,         8) \
X(a, STATIC,   SINGULAR, UENUM,    state,             9) \
X(a, STATIC,   SINGULAR, INT32,    has_goods,        10) \
X(a, STATIC,   SINGULAR, BOOL,     ready_state,      11) \
X(a, STATIC,   SINGULAR, UINT32,   excp_handle,      12) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,         13)
#define feeder_dev_state_total_CALLBACK NULL
#define feeder_dev_state_total_DEFAULT NULL
#define feeder_dev_state_total_belt_motor_MSGTYPE feeder_belt_motor_state_multiple
#define feeder_dev_state_total_auto_scanner_MSGTYPE scanner_state
#define feeder_dev_state_total_manual_scanner_MSGTYPE scanner_state
#define feeder_dev_state_total_charger_MSGTYPE charger_state
#define feeder_dev_state_total_belt_sensor_MSGTYPE feeder_belt_sensor_state_multiple

#define goods_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   codes,             1) \
X(a, STATIC,   SINGULAR, UENUM,    position,          2) \
X(a, STATIC,   SINGULAR, UINT32,   scanner_id,        3)
#define goods_info_CALLBACK NULL
#define goods_info_DEFAULT NULL

extern const pb_msgdesc_t feeder_cmd_msg;
extern const pb_msgdesc_t feeder_supply_state_msg;
extern const pb_msgdesc_t belt_motor_single_state_msg;
extern const pb_msgdesc_t belt_sensor_single_state_msg;
extern const pb_msgdesc_t feeder_belt_sensor_state_multiple_msg;
extern const pb_msgdesc_t feeder_belt_motor_state_multiple_msg;
extern const pb_msgdesc_t scanner_state_msg;
extern const pb_msgdesc_t charger_state_msg;
extern const pb_msgdesc_t feeder_dev_state_total_msg;
extern const pb_msgdesc_t goods_info_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define feeder_cmd_fields &feeder_cmd_msg
#define feeder_supply_state_fields &feeder_supply_state_msg
#define belt_motor_single_state_fields &belt_motor_single_state_msg
#define belt_sensor_single_state_fields &belt_sensor_single_state_msg
#define feeder_belt_sensor_state_multiple_fields &feeder_belt_sensor_state_multiple_msg
#define feeder_belt_motor_state_multiple_fields &feeder_belt_motor_state_multiple_msg
#define scanner_state_fields &scanner_state_msg
#define charger_state_fields &charger_state_msg
#define feeder_dev_state_total_fields &feeder_dev_state_total_msg
#define goods_info_fields &goods_info_msg

/* Maximum encoded size of messages (where known) */
#define FEEDER_INTERFACE_PB_H_MAX_SIZE           goods_info_size
#define belt_motor_single_state_size             23
#define belt_sensor_single_state_size            4
#define charger_state_size                       28
#define feeder_belt_motor_state_multiple_size    150
#define feeder_belt_sensor_state_multiple_size   48
#define feeder_cmd_size                          51
#define feeder_dev_state_total_size              314
#define feeder_supply_state_size                 14
#define goods_info_size                          362
#define scanner_state_size                       16

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
