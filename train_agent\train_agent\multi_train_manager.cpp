#include "multi_train_manager.hpp"
#include <spdlog/spdlog.h>
#include "train_agent_debug.h"
#include <arpa/inet.h>
#include "protocol/train_protocol.hpp"
#include "train_manage/train_manage.hpp"
#include "share/pb/idl/exception.pb.h"
#include "exception/dev_except.hpp"
#include "share/event_code.hpp"
#include "scheduler_msg/scheduler_msg.hpp"
#include "train_manage/train_list.hpp"
#include "share/global_def.h"



/**@brief     multi_train_manager class构造函数，在构造列表里构造ZMQ socket
* @param[in]  zmq::context_t &context ZMQ创建的上下文
* @return     NULL
*/
multi_train_manager::multi_train_manager(zmq::context_t &context)
:m_scheduler_msg(context)
,m_train_info_replayer(zmq::socket_t(context, ZMQ_REP))
,m_dev_fsm(context)
{
	m_dev_curr_state.dev_st.emerg_pressed = false;	// 设置紧急按钮被按下
	m_dev_curr_state.dev_st.safty_door_open = false;	

	m_dev_downlink_hb_msg.clear();
	m_dev_reset_msg.clear();
}

/**@brief     multi_vehicle_manager class析构函数
* @param[in]  NULL
* @return     NULL
*/
multi_train_manager::~multi_train_manager()
{
	for (auto& x: m_epoll_dev_list)
	{
		close(x.first);
	}
}


/**@brief     查找指定对象是否在本地列表中
* @param[in]  int dev_id --- 指定设备ID
* @return     NULL 
*/
bool multi_train_manager::multi_train_manager_train_list_find(int dev_id)
{
	int i;
	bool result = false;

	for(i = 0; i < m_database_train_cnt; i++)
	{
		if(m_database_train_list[i] == ((uint32_t )dev_id))
		{
			result = true;
			return result;
		}
	}

	result = false;
	return result;
}


/**@brief     车辆消息处理函数，主要实现车辆端消息的接收、解码及解析执行
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_train_manager::multi_train_dev_ctrl_func(int fd)
{
	struct sockaddr_in clnt_addr;
    uint8_t buffer[20480] = {0};
	uint8_t data_buf[20480] = {0};
    int recv_len = 0;
	train_protocol_err_tab head_match, tail_match;
	TRAIN_MSG_TYPE msg_type;
	net_msg msg_temp;
	uint32_t cli_addr_temp;
	uint32_t cli_addr_check = 0x00;
	uint32_t seque_temp;
	uint32_t seque_up_temp;
	uint32_t seque_down_temp;

	uint8_t id_temp, carriage_cnt_temp;
	uint16_t except_dev_id;
	uint8_t excep_level;
	uint32_t excep_code, excep_subcode;
	int excep_code_old, exce_platform_id_old;
	uint8_t excep_carriage_id;
	int data_cnt;

	train_info temp;
	platform_task_info platform_temp;
	msg_queue state_data;

	_train_map_opt_tab train_opt_temp;
	_train_map_opt_tab train_id_opt_temp;
	train_info train_info_temp;
	train_state dev_state_temp;
	train_state_net dev_state_inline;
    task_st c_task_state;
	task_st p_task_state;
    task_st p_task_state_temp;
	task_st c_task_state_temp;

	train_task_state m_task_platform_state;
	train_task_state m_task_carriage_state;

	event_exception exce_info;
	train_run_para para;
	std::shared_ptr<spdlog::logger> logger; 
	char soft_version[16];
    char hard_version[16];

	struct timespec tic_dev;
	struct timespec tic_curr;
	struct timespec tick;
	struct timespec tick_down_temp;
	long timedif;
	
	bool logger_valid = false;
	char data[4];
	string net_msg_temp = {""};
	string hb_net_msg;
	
	std::lock_guard<std::mutex> dev_lock(m_train_mtx);

	m_locol_server.udp_server_socket_recv_msg(&recv_len, buffer, &clnt_addr);
	
	if((recv_len > 14) && (htons(clnt_addr.sin_port) == m_client_port))
	{
		cli_addr_temp = clnt_addr.sin_addr.s_addr;
		SPDLOG_INFO("recv msg from fd:{}, cli_addr:{}, cli_port:{}, msg_len:{}", fd, inet_ntoa(clnt_addr.sin_addr), htons(clnt_addr.sin_port), recv_len);

		// 将客户端地址信息插入到设备管理中
    	epoll_poller_dev_insert(cli_addr_temp, clnt_addr);	
		display();
	}
	else
	{
		SPDLOG_INFO("recvfrom error, fd:{}, client_info:{}-{}", fd, inet_ntoa(clnt_addr.sin_addr), htons(clnt_addr.sin_port));
		return;
	}

	head_match = train_protocol_head_match(buffer, recv_len);
	tail_match = train_protocol_tail_match(buffer, recv_len);
	if((TRAIN_PROTOCOL_SUCESS == head_match) && (TRAIN_PROTOCOL_SUCESS == tail_match))
	{
		msg_type = train_manage_msg_type(buffer, recv_len, data_buf, &data_cnt, &id_temp, &seque_temp);
		if((TRAIN_MSG_UNDEFINED == msg_type)||(0x00 == id_temp))
		{
			return ;
		}

		for(int i = 0; i < m_dev_cfg.train_cfg_info.train_info_count; i++)
		{
			if(m_dev_cfg.train_cfg_info.train_info[i].train_id == id_temp)
			{
				carriage_cnt_temp = m_dev_cfg.train_cfg_info.train_info[i].carriage_cnt;
				break;
			}
		}

		train_opt_temp = m_train_list.train_sock_list_find(cli_addr_temp);		//m_train_sock_list <cin_addr -> dev_id>
		if(TRAIN_SESSION_NOT_FIND == train_opt_temp)
		{
			std::unique_lock<std::mutex> insert_lock(m_insert_lock);

			train_id_opt_temp = m_train_list.train_list_map_find(id_temp);	//m_train_dev_list <dev_id -> train_info>
			if(TRAIN_SESSION_NOT_FIND == train_id_opt_temp)
			{
				m_train_list.train_sock_list_insert(cli_addr_temp, id_temp);	//<cin_addr -> dev_id>
				train_info_temp.cin_addr = cli_addr_temp;
				train_info_temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &train_info_temp.v_last_msg_upload_tick);
				train_info_temp.v_comm_seque = 1;
				train_info_temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, train_info_temp);
				m_train_list.train_log_list_init_logger(id_temp);

				platform_temp.v_comm_finish_flag = true;
				m_train_list.platform_list_map_insert(id_temp*100, platform_temp);

				try
				{
					m_task_mtx.try_lock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}

				m_train_list.train_task_list_init(id_temp);
				try
				{
					m_task_mtx.unlock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}
		
				// m_train_list.train_list_map_update_comm_flag(id_temp, true);
				m_train_list.platform_list_map_update_comm_ack_flag(id_temp * 100, true);

				m_train_list.platform_list_map_insert_comm_sequeue(id_temp * 100);	//下行sequence,insert
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.platform_list_map_update_uplink_comm_squence(id_temp * 100, 1);	//上行sequence

				/*复位心跳超时*/
				exce_info.which_evt_except = event_exception_except_tag;
				except_info e = dev_except::train_offline(id_temp);
				e.state = exception_state_STATE_RESET;
				exce_info.evt_except.except = e;

				state_data.type = TRAIN_EXCEP_PUB;
				state_data.train_id = id_temp;
				memcpy(state_data.msg_data, (uint8_t *)(&exce_info), sizeof(exce_info));
				m_scheduler_msg.scheduler_manager_queue_push(state_data);

				// for(int i = 0; i < m_dev_cfg.train_count; i++)
				// {
				// 	if(m_dev_cfg.train_info[i].train_id == id_temp)
				// 		// m_train_list.exception_resume(m_dev_cfg.train_info[i].carriage_num, e);
				// }
				// m_train_list.exception_resume_all(id_temp * 100);
			}
			else
			{
				m_train_list.train_sock_list_change(cli_addr_temp, id_temp);
				temp.cin_addr = cli_addr_temp;
				temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &temp.v_last_msg_upload_tick);
				temp.v_comm_seque = 1;
				temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, temp);

				platform_temp.v_comm_finish_flag = true;
				m_train_list.platform_list_map_insert(id_temp * 100, platform_temp);

				SPDLOG_INFO("id :{} already exit", id_temp);
			}

			insert_lock.unlock();

#ifdef 	MULTI_DEV_DEBUG
			m_train_list.train_sock_list_display();
			m_train_list.train_dev_list_display();
#endif	
		}
		else
		{
			//校验ip地址和dev_id的匹配性
			cli_addr_check = m_train_list.train_list_map_get_dev_addr(id_temp);

			if(cli_addr_temp != cli_addr_check)
			{
				SPDLOG_INFO("id :{} need refresh ip addr", id_temp);
				m_train_list.train_sock_list_change(cli_addr_temp, id_temp);
				temp.cin_addr = cli_addr_temp;
				temp.v_addr = epoll_poller_dev_get_sock_addr(cli_addr_temp);
				clock_gettime(CLOCK_MONOTONIC, &temp.v_last_msg_upload_tick);
				temp.v_comm_seque = 1;
				temp.v_task = TRAIN_TASK_NONE;
				m_train_list.train_list_map_insert(id_temp, temp);

				platform_temp.v_comm_finish_flag = true;
				m_train_list.platform_list_map_insert(id_temp * 100, platform_temp);
			}
		}

		logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);

		for(int j = 0; j < recv_len; j++)
		{
			sprintf(data, "%x ", buffer[j]);
			data[3] = 0x00;
			net_msg_temp += data;
		}

		if( logger_valid )
		{
			string filename = basename((char *)(__FILE__));
			logger->info( "[{}: {}: {}] [NET] [IN] msglen:{} msg_type:{} / dev_id:{} / sequence:{}", filename, __FUNCTION__, __LINE__, \
																	 recv_len, msg_type, id_temp, seque_temp);
			if(msg_type == TRAIN_MSG_STATE)
			{
				hb_log_decode(buffer, data_buf, &hb_net_msg);
				logger->info( "[{}: {}: {}] [NET] [IN] msg:{} ", filename, __FUNCTION__, __LINE__, hb_net_msg); 
			}
				
			else
				logger->info( "[{}: {}: {}] [NET] [IN] msg:{} ", filename, __FUNCTION__, __LINE__, net_msg_temp); 	
		}

		if(multi_train_manager_train_list_find(id_temp))
		{
			if(TRAIN_MSG_REG == msg_type)
			{
				SPDLOG_INFO("register info: devid:{}, software_version:{}, hardware_version:{}", id_temp, *(uint32_t *)&data_buf[0], *(uint32_t *)&data_buf[4]);
				
				m_train_list.train_list_map_update_train_restart_flag(id_temp, true);

				snprintf(soft_version, sizeof(temp.sw_version), "%u", *(uint32_t *)&data_buf[0]);
				snprintf(hard_version, sizeof(temp.hw_version), "%u", *(uint32_t *)&data_buf[4]);
				m_train_list.train_list_map_update_sw_hw_version(id_temp, soft_version, hard_version);
		
				//如果是注册消息，则在内部进行处理, 获取相关信息;
				//若无，则插入, 并且生成注册成功ACK返回
				m_train_list.train_list_map_update_upload_tick(id_temp);
				m_train_list.train_list_map_reset_task_info(id_temp);

				seque_up_temp = 0x00;
				m_train_list.platform_list_map_update_uplink_comm_squence(id_temp * 100, 0x00);	//上行sequence
				
				logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);	
				if( logger_valid )
				{
					logger->info( "[{}: {}: {}] [NET] [IN] TRAIN_MSG_REG ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);
				}
				
				seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(id_temp * 100);	//下行sequence
				train_manage_train_register_ack(id_temp, seque_down_temp + 1, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg);
			
				msg_temp.cin_addr = cli_addr_temp;

				try
				{
					m_task_mtx.try_lock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}

				m_net_msg_queue.push(msg_temp);

				seque_down_temp++;
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.platform_list_map_update_comm_sequeue(id_temp * 100, seque_down_temp);	//下行sequence
				
				//处理行走指令缓存信息
				train_position_info  pos_info_temp;
				pos_info_temp.pos = 0;
				pos_info_temp.speed = 0;
				pos_info_temp.is_locate = false;
				m_train_list.train_list_position_insert(id_temp, pos_info_temp);

				//处理原有任务
				if( !m_train_list.train_task_list_empty(id_temp) )
				{
					m_train_list.train_task_list_clear(id_temp);
				}

				try
				{
					m_task_mtx.unlock();
				}
				catch(const std::exception& e)
				{
					SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
				}

				//生成设备复位消息
				exce_info.which_evt_except = event_exception_evt_tag;
				exce_info.evt_except.evt.src = exception_src_TRAIN;
				exce_info.evt_except.evt.code = DEV_RESET;
				exce_info.evt_except.evt.sub_code = 0x00;
				exce_info.evt_except.evt.dev = id_temp;
				exce_info.evt_except.evt.sub_dev = 0x00;
				state_data.type = TRAIN_RESET_PUB;
				state_data.train_id = id_temp;
				state_data.carriage_id = 0x00;
				memcpy(state_data.msg_data, (uint8_t *)(&exce_info), sizeof(exce_info));
				m_scheduler_msg.scheduler_manager_queue_push(state_data);


				//如果车辆异常列表为空，且20秒内重启了，说明车辆异常重启了，需要上报一个异常报文
				clock_gettime(CLOCK_MONOTONIC, &tic_curr);
				if( 0x00 == m_train_list.exception_cnt_get(id_temp, carriage_cnt_temp) )
				{
					if( m_train_list.train_list_map_get_upload_tick(id_temp, &tic_dev) )
					{
						//计算时间差 (微秒)
						timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/10;

						SPDLOG_INFO("log timedif :{} ", timedif);

						if( timedif < (m_dev_cfg.heartbeat_timeout*1000*1000) && (timedif > (3)) )
						{
							if( multi_train_reset_dev_find(id_temp) )	//m_train_reset_dev_map < devid -> 待重启标志true >
							{
								multi_train_reset_dev_clear(id_temp);
							}
							else
							{
								exce_info.which_evt_except = event_exception_except_tag;

								/*TODO: 取异常子码 (目前异常子码先填0)*/
								dev_except::dev_except except(id_temp, 0, 99, 0);
								exce_info.evt_except.except = except.get_except_info();
								except_dev_id = id_temp * 100;
								if (m_train_list.exception_occur(except_dev_id, exce_info.evt_except.except) > 0)
								{
									dev_state_temp.train_state = train_dev_state_DEV_UNKNOWN;
									dev_state_temp.train_error_no = TRAIN_SELF_RESET;
									dev_state_temp.motor_state = train_dev_state_DEV_UNKNOWN;
									dev_state_temp.work_state = train_work_state_WORK_FATAL;
									dev_state_temp.motion_positon_valid_flag = false;
									dev_state_temp.dev_comm_ack_state = false;
									dev_state_temp.motion_positon = 0;
									dev_state_temp.train_id = id_temp;
									dev_state_temp.carriage_cnt = carriage_cnt_temp;
									dev_state_temp.carriage_st_count = carriage_cnt_temp;

									for(int j = 0; j < m_dev_cfg.train_cfg_info.train_info_count; j++)
									{
										if(id_temp == m_dev_cfg.train_cfg_info.train_info[j].train_id)
										{
											for(int z = 0; z < (int)dev_state_temp.carriage_cnt; z++)
											{
												dev_state_temp.carriage_st[z].carriage_id = m_dev_cfg.train_cfg_info.train_info[j].carriage_info[z].carriage_id;
												dev_state_temp.carriage_st[z].y_axis_encoder_state = train_dev_state_DEV_UNKNOWN;
												dev_state_temp.carriage_st[z].load_platform_state = train_dev_state_DEV_UNKNOWN;
											}
										
											break;
										}
									}
								
									state_data.type = TRAIN_STATE_PUB;
									state_data.train_id = id_temp;
									memcpy(&state_data.msg_data[0], &dev_state_temp, sizeof(dev_state_temp));
									m_scheduler_msg.scheduler_manager_queue_push(state_data);
								}
							}
						}
					}
				}
							
				// m_train_list.train_list_map_update_comm_flag(id_temp, true);
				m_train_list.platform_list_map_update_comm_ack_flag(id_temp * 100, true);
				

				/*对该车处于pending状态的异常，逐个异常恢复消息*/
				// state_data.type = TRAIN_EXCEP_PUB;
				// state_data.train_id = id_temp;
				// event_exception *excep_resume = (event_exception *)(&state_data.msg_data[0]);
				// excep_resume->which_evt_except = event_exception_except_tag;
				// auto f = [&](const except_info &excp)
				// {
				// 	excep_resume->evt_except.except = excp;
				// 	excep_resume->evt_except.except.state = exception_state_STATE_RESET;
				//  	m_scheduler_msg.scheduler_manager_queue_push(state_data);
				// };

				// for(int i = 0; i < m_dev_cfg.train_cfg_info.train_info_count; i++)
				// {
				// 	if(m_dev_cfg.train_cfg_info.train_info[i].train_id == id_temp)
				// 	{
				// 		m_train_list.travese_all_pending_exception_for_train(id_temp, carriage_cnt_temp, f);
				// 		m_train_list.exception_resume_all(id_temp * 100);

				// 		/*清空对应车辆处于pending状态的异常*/
				// 		except_info except = {exception_src_TRAIN, exception_level_FATAL, EXCEPTION_ALL, EXCEPTION_ALL,
				// 			"all", id_temp, 0, exception_state_STATE_RESET};

				// 		m_train_list.exception_resume(carriage_cnt_temp, except);
				// 	}			
				// }
			}

			else if( TRAIN_MSG_STATE == msg_type )	//心跳数据
			{
				if( logger_valid )
				{
					logger->info( "[{}: {}: {}] [NET] [IN] TRAIN_MSG_STATE ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
				}

				seque_up_temp = m_train_list.platform_list_map_get_uplink_comm_sequeue((id_temp * 100));	//上行sequence
				if( seque_temp < seque_up_temp )
					return;
				

				//如果是上报状态消息
				train_manage_get_dev_state(&dev_state_temp, &dev_state_inline, data_buf, id_temp, &excep_carriage_id, &excep_level, &excep_code);
				m_train_list.train_list_map_update_state(id_temp, dev_state_inline);
				m_train_list.train_list_map_get_exce_code(id_temp, &excep_code_old, &exce_platform_id_old);
				m_train_list.train_list_map_update_upload_tick(id_temp);
				// dev_state_temp.dev_comm_ack_state = m_train_list.train_list_map_get_comm_flag(id_temp);
				dev_state_temp.dev_comm_ack_state = m_train_list.train_list_map_get_comm_ack_flag(id_temp);

				SPDLOG_INFO("all dev_state_temp.dev_comm_ack_state:{}", dev_state_temp.dev_comm_ack_state);

				m_train_list.train_list_map_get_train_task_state(id_temp, &c_task_state_temp, &p_task_state_temp);

				//获取任务状态
				c_task_state.carriage_count = dev_state_inline.carriage_count;
				p_task_state.carriage_count = dev_state_inline.carriage_count;
				
				for(int j = 0; j < dev_state_inline.carriage_count; j++)
				{
					c_task_state.carriages_task[j].id = dev_state_inline.carriages_info[j].carriage_id;
					c_task_state.carriages_task[j].task_type = dev_state_inline.carriages_info[j].carriage_current_cmd_Y;
					c_task_state.carriages_task[j].task_result = dev_state_inline.carriages_info[j].carriage_cmd_result_Y;

					p_task_state.carriages_task[j].id = dev_state_inline.carriages_info[j].carriage_id;
					p_task_state.carriages_task[j].task_type = dev_state_inline.carriages_info[j].belt_motor_current_cmd;
					p_task_state.carriages_task[j].task_result = dev_state_inline.carriages_info[j].belt_motor_cmd_ack;
				}
				m_train_list.train_list_map_update_task_state(id_temp, c_task_state, p_task_state);


				for(int i = 0; i < p_task_state.carriage_count; i++)
				{	
					int platform_id = id_temp * 100 + p_task_state.carriages_task[i].id;

					// SPDLOG_INFO("task state temp, platform_id:{}, task_result:{}-{}, type:{}, flag:{}", platform_id, p_task_state_temp.carriages_task[i].task_result, p_task_state.carriages_task[i].task_result, p_task_state.carriages_task[i].task_type, m_train_list.platform_list_map_get_comm_ack_flag(platform_id));
		
					if(m_train_list.platform_list_map_get_comm_ack_flag(platform_id))
					{
						if(m_train_list.train_list_map_get_train_platform_task_state(platform_id, &m_task_platform_state))
						{
							if(0x06 == p_task_state.carriages_task[i].task_type)
								m_task_platform_state.type = task_type_CARRIAGE_SHIFT_IN;
							if(0x07 == p_task_state.carriages_task[i].task_type)
								m_task_platform_state.type = task_type_CARRIAGE_SHIFT_OUT;

							if(0x01 == p_task_state.carriages_task[i].task_result)
								m_task_platform_state.state = task_state_RUNNING;

							if((0x01 == p_task_state_temp.carriages_task[i].task_result) && (0x00 == p_task_state.carriages_task[i].task_result))
								m_task_platform_state.state = task_state_SUCCEED_OVER;

							if((0x02 == p_task_state_temp.carriages_task[i].task_result) && (0x00 == p_task_state.carriages_task[i].task_result))
								m_task_platform_state.state = task_state_SUCCEED_OVER;
							
							if(0x00 == p_task_state.carriages_task[i].task_result)										
							{

							}
							if(0x02 == p_task_state.carriages_task[i].task_result)
							{
								m_task_platform_state.state = task_state_ERROR;
								SPDLOG_INFO("m_task_platform_state error, id:{}, task_result:{}", platform_id, p_task_state.carriages_task[i].task_result);
							}

							m_train_list.train_list_platform_task_state_insert(platform_id, m_task_platform_state);

						}

						if(m_train_list.train_list_map_get_train_carriage_task_state(platform_id, &m_task_carriage_state))
						{
							if(0x02 == c_task_state.carriages_task[i].task_type)
								m_task_carriage_state.type = task_type_Y_MOVE;

							if(0x01 == c_task_state.carriages_task[i].task_result)
							m_task_carriage_state.state = task_state_RUNNING;
					
							if((0x01 == c_task_state_temp.carriages_task[i].task_result) && (0x00 == c_task_state.carriages_task[i].task_result))
								m_task_carriage_state.state = task_state_SUCCEED_OVER;
						
							if((0x02 == c_task_state_temp.carriages_task[i].task_result) && (0x00 == c_task_state.carriages_task[i].task_result))
								m_task_carriage_state.state = task_state_SUCCEED_OVER;
							
							if(0x00 == c_task_state.carriages_task[i].task_result)										
							{
						
							}
							if(0x02 == c_task_state.carriages_task[i].task_result)
							{
								m_task_carriage_state.state = task_state_ERROR;
								SPDLOG_INFO("m_task_carriage_state error, id:{}, task_result:{}", platform_id, c_task_state.carriages_task[i].task_result);
							}
						
							m_train_list.train_list_carriage_task_state_insert(platform_id, m_task_carriage_state);						
						}					
					}
					else
					{
						if(0x06 == p_task_state.carriages_task[i].task_type)
						{
							m_task_platform_state.state = task_state_INIT;
							m_task_platform_state.type = task_type_CARRIAGE_SHIFT_IN;
							m_train_list.train_list_platform_task_state_insert(platform_id, m_task_platform_state);
						}
						if(0x07 == p_task_state.carriages_task[i].task_type)
						{
							m_task_platform_state.state = task_state_INIT;
							m_task_platform_state.type = task_type_CARRIAGE_SHIFT_OUT;
							m_train_list.train_list_platform_task_state_insert(platform_id, m_task_platform_state);
						}
						
						if(0x02 == c_task_state.carriages_task[i].task_type)
						{
							m_task_carriage_state.state = task_state_INIT;
							m_task_carriage_state.type = task_type_Y_MOVE;
							m_train_list.train_list_carriage_task_state_insert(platform_id, m_task_carriage_state);
						}
					}

					// SPDLOG_INFO("task state, platform_id:{}, state:{}, type:{}, flag:{}", platform_id, m_task_platform_state.state, m_task_platform_state.type, m_train_list.platform_list_map_get_comm_ack_flag(platform_id));
				}
			

				//处理异常消息
				SPDLOG_INFO("except_info, train_id:{}, platform_id:{}, excep_level:{}, excep_code:{}", id_temp, excep_carriage_id, excep_level, excep_code);

				m_train_list.train_list_map_update_exce_code(id_temp, excep_code, excep_carriage_id);
				if(0x00 != excep_code)
				{
					if(excep_code == 0x04)	//行走伺服异常
						excep_subcode = dev_state_inline.train_motor_error_code;
					else if(excep_code == 0x06)	// 载货台伺服异常
					{
						for(int i = 0; i < dev_state_inline.carriage_count; i++)
						{
							if(dev_state_inline.carriages_info[i].carriage_id == excep_carriage_id)
							{
								excep_subcode = dev_state_inline.carriages_info[i].carriage_motor_error_code_Y;
								break;
							}
						}
					}
					else if(excep_code == 0x64)	//皮带伺服异常
					{
						for(int i = 0; i < dev_state_inline.carriage_count; i++)
						{
							if(dev_state_inline.carriages_info[i].carriage_id == excep_carriage_id)
							{
								excep_subcode = dev_state_inline.carriages_info[i].belt_motor_error_code;
								break;
							}
						}
					}
					else if(excep_code == 0x6F)	//翻斗伺服异常
					{
						for(int i = 0; i < dev_state_inline.carriage_count; i++)
						{
							if(dev_state_inline.carriages_info[i].carriage_id == excep_carriage_id)
							{
								excep_subcode = dev_state_inline.carriages_info[i].belt_motor_error_code;
								break;
							}
						}
					}
					else
						excep_subcode = 0;

					// m_train_list.train_list_map_update_exce_motor_error_code(id_temp, excep_subcode);


					exce_info.which_evt_except = event_exception_except_tag;
					dev_except::dev_except except(id_temp, excep_carriage_id, excep_code, excep_subcode);
					exce_info.evt_except.except = except.get_except_info();

					dev_state_temp.train_error_no = exce_info.evt_except.except.code;
					m_train_list.train_list_map_update_train_error_no(id_temp, exce_info.evt_except.except.code);
					SPDLOG_INFO("excet info, src:{}, excep_code:{}, train_error_no:{}", exce_info.evt_except.except.src, excep_code, dev_state_temp.train_error_no);

					except_dev_id = id_temp * 100 + excep_carriage_id;
					m_train_list.train_list_map_update_exce_info(except_dev_id, excep_code);

					if (m_train_list.exception_occur(except_dev_id, exce_info.evt_except.except) > 0)
					{
						state_data.type = TRAIN_EXCEP_PUB;
						state_data.train_id = id_temp;
						memcpy(&state_data.msg_data[0], &exce_info, sizeof(exce_info));
						m_scheduler_msg.scheduler_manager_queue_push(state_data);
					}
				}
				else
				{
					// if(excep_code_old != 0)
					if(0x00 == dev_state_inline.train_run_st)
					{
						/*对该车处于pending状态的异常，逐个异常恢复消息*/
						state_data.type = TRAIN_EXCEP_PUB;
						state_data.train_id = id_temp;
						event_exception *excep_resume = (event_exception *)(&state_data.msg_data[0]);
						excep_resume->which_evt_except = event_exception_except_tag;
						auto f = [&](const except_info &excp)
						{
							excep_resume->evt_except.except = excp;
							excep_resume->evt_except.except.state = exception_state_STATE_RESET;
							m_scheduler_msg.scheduler_manager_queue_push(state_data);
						};
						m_train_list.travese_all_pending_exception_for_train(id_temp, dev_state_inline.carriage_count, f);
						m_train_list.exception_resume_all(id_temp * 100);

						/*清空对应车辆处于pending状态的异常*/
						// except_info except = {exception_src_TRAIN, exception_level_FATAL, EXCEPTION_ALL, EXCEPTION_ALL,
						// 	"all", id_temp, 0, exception_state_STATE_RESET};

						// m_train_list.exception_resume(dev_state_inline.carriage_count, except);
						
					}			
				}


				// seque_up_temp = m_train_list.train_list_map_get_uplink_comm_sequeue((id_temp));	//上行sequence
				if( seque_temp > seque_up_temp )
				{
					m_train_list.platform_list_map_update_uplink_comm_squence(id_temp * 100, seque_temp);

					state_data.type = TRAIN_STATE_PUB;
					state_data.train_id = id_temp;
					memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
					m_scheduler_msg.scheduler_manager_queue_push(state_data);
				}
				else
				{
					if( logger_valid )
					{
						logger->info( "[{}: {}: {}] [NET] [IN] up load sequence error :{} :{}", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, seque_up_temp, seque_temp);		
					}
				}
			}

			else if(TRAIN_MSG_EXCEPTIONAL == msg_type)
			{			
				if( logger_valid )
				{
					try
					{
						logger->info( "[{}: {}: {}] [NET] [IN] TRAIN_MSG_EMERGENCY ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
					}
					catch(const std::exception& e)
					{
						SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
					}
				}
					
				//如果是紧急上报消息
				exce_info.which_evt_except = event_exception_except_tag;
				dev_except::dev_except except(id_temp, data_buf[0], *(uint32_t *)&data_buf[2], 0);
				exce_info.evt_except.except = except.get_except_info();
	
				except_dev_id = id_temp * 100 + data_buf[0];
				m_train_list.train_list_map_update_exce_info(except_dev_id, *(uint32_t *)&data_buf[2]);
				m_train_list.train_list_map_update_upload_tick(id_temp);

				if (m_train_list.exception_occur(except_dev_id, exce_info.evt_except.except) > 0)
				{
					state_data.type = TRAIN_EXCEP_PUB;
					state_data.train_id = id_temp;
					memcpy(&state_data.msg_data[0], &exce_info, sizeof(exce_info));
					m_scheduler_msg.scheduler_manager_queue_push(state_data);
				}
			}
				
			else if(TRAIN_MSG_CMD_ACK == msg_type)	
			{
				if( logger_valid )
				{
					try
					{
						logger->info( "[{}: {}: {}] [NET] [IN] TRAIN_MSG_CMD_ACK ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__);		
					}
					catch(const std::exception& e)
					{
						SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
					}
				}
				
				m_train_list.train_list_map_update_upload_tick(id_temp);
				seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(id_temp * 100 + data_buf[1]);

				if(data_buf[0] == TRAIN_PROTOCOL_CMD_QUERY_PARA_ACK)
				{
					train_query_para_recv(&para, &data_buf[1]);
					SPDLOG_INFO("train query para ack train_id:{}", id_temp);
				}
				
				if(data_buf[0] == TRAIN_PROTOCOL_CMD_VERSION_INFO_ACK)
				{
					snprintf(soft_version, sizeof(temp.sw_version), "%u", *(uint32_t *)&data_buf[1]);
					snprintf(hard_version, sizeof(temp.hw_version), "%u", *(uint32_t *)&data_buf[5]);
					m_train_list.train_list_map_update_sw_hw_version(id_temp, soft_version, hard_version);
					SPDLOG_INFO("train version info ack devid, train_id:{}, sw:{}, hw:{}", id_temp, *(uint32_t *)&data_buf[1], *(uint32_t *)&data_buf[5]);
				}

				else
				{
					SPDLOG_INFO("train task sequence ack:{}-{}", data_buf[1], *(uint32_t *)&data_buf[2]);
					// if( !m_train_list.train_list_map_get_comm_flag(id_temp) )
					if( !m_train_list.train_list_map_get_comm_ack_flag(id_temp) )
					{
						SPDLOG_INFO("seque_down_temp:{}-{}", seque_down_temp, *(uint32_t *)&data_buf[2]);
						if( seque_down_temp <= (*(uint32_t *)&data_buf[2] + 1) )
						{
							clock_gettime(CLOCK_MONOTONIC, &tick);
							m_train_list.train_list_map_get_download_tick(id_temp, &tick_down_temp);
							timedif = MILLION*(tick.tv_sec-tick_down_temp.tv_sec)+(tick.tv_nsec-tick_down_temp.tv_nsec)/1000/1000;
							if( logger_valid )
							{
								if(timedif > 1000)
								{
									try
									{
										logger->info( "[{}: {}: {}] [NET] [IN] curr dev msg confirm delay too long :{} ", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, timedif);		
									}
									catch(const std::exception& e)
									{
										SPDLOG_INFO("log :{} error! :{}", id_temp, e.what() );
									}
								}
							}

							int platform_id  = id_temp * 100 + data_buf[1];
							m_train_list.platform_list_map_update_comm_ack_flag(platform_id, true);
						}
					}
				}
			}

			if(m_train_list.train_list_map_check_version_empty(id_temp))
			{
				seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(id_temp * 100);	//下行sequence
				train_manage_train_register_ack(id_temp, seque_down_temp + 1, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg);
				msg_temp.cin_addr = cli_addr_temp;
				m_net_msg_queue.push(msg_temp);
				
				seque_down_temp++;
				m_train_list.train_list_map_update_downlink_tick(id_temp);	//下行tick
				m_train_list.platform_list_map_update_comm_sequeue(id_temp * 100, seque_down_temp);	//下行sequence
			}
		}
	}
	else
		SPDLOG_INFO("recvfrom HEAD/TAIL error, H:{}, T:{}", head_match, tail_match);
	
	return;
}


/**@brief     epoll监听过程中，已有接入节点消息处理---从线程池中获取空闲线程执行操作
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_train_manager::epoll_existConnection(int fd) 
{
	m_recv_worker.add_task(std::bind(&multi_train_manager::multi_train_dev_ctrl_func, this, fd));
}


/**@brief     epoll监听过程中，新接入节点处理
* @param[in]  int fd --- client 文件描述符
* @return     NULL 
*/
void multi_train_manager::epoll_newConnection(int fd) 
{

}

/**@brief     epoll监听主循环
* @param[in]  int timeout      ---   epoll时间监听的超时等待时间
* @return     NULL 
*/
int multi_train_manager::epoll_main_loop(int timeout)
{
	int event_cnt = -1;
	msg_queue data_temp;
	train_task task_pop_temp;
	train_position_info pos_info;

	while(1)
	{
		event_cnt = epoll_wait_fd(timeout);
		
		if((event_cnt != -1) && (event_cnt != 0))
		{
			for(int i = 0; i != event_cnt; i++)
			{
				if(m_events[i].data.fd == m_server_fd)
				{
					epoll_existConnection(m_events[i].data.fd);
				}
			}
		}

		// 车辆需要下发的任务消息处理
		if(!m_scheduler_msg.scheduler_manager_task_msg_queue_empty())
		{
			int task_cnt = m_scheduler_msg.scheduler_manager_task_msg_queue_size();

			//获取系统内指定的下发任务消息
			m_scheduler_msg.scheduler_manager_task_msg_queue_pop(&data_temp);
			
			memcpy(&task_pop_temp, data_temp.msg_data, sizeof(task_pop_temp));				
			SPDLOG_INFO("123 task_pop_temp, train_id:{}, platform_id:{}, seq:{}, which_task:{}, task_cnt:{}", task_pop_temp.dev_id, task_pop_temp.carriage_id, task_pop_temp.sequence, task_pop_temp.which_task, task_cnt);

			if(train_task_hb_time_sync_tag == task_pop_temp.which_task)
				m_dev_downlink_hb_msg.push(task_pop_temp);

			//多车硬链接
			else if((train_task_move_tag == task_pop_temp.which_task) && (0xFFFF == task_pop_temp.dev_id))
			{
				pos_info.pos = task_pop_temp.task.move.target;
				pos_info.speed = (uint16_t)task_pop_temp.task.move.speed_limit;
				if(task_pop_temp.task.move.type == task_type_MOVE_SPEED)
					pos_info.is_locate = true;
				else
					pos_info.is_locate = false;

				for(int j = 0; j < m_dev_cfg.train_cfg_info.train_info_count; j++)
					m_train_list.train_list_position_insert(m_dev_cfg.train_cfg_info.train_info[j].train_id, pos_info);
			}

			else
			{
				if(train_task_cmd_tag == task_pop_temp.which_task)
					m_dev_reset_msg.push(task_pop_temp);
				else
					m_train_list.train_task_list_push(data_temp.train_id, task_pop_temp);
			}			
		}

		m_dev_curr_state = m_dev_fsm.fsm_manager_get_sys_state(); 

		
		if(m_dev_curr_state.state == e_wkstate_SYS_ERROR_RECOVERY)
		{
			pos_info.pos = 0;
			pos_info.speed = 0;
			for(int i = 0; i < m_dev_cfg.train_cfg_info.train_info_count; i++)
				m_train_list.train_list_position_insert(m_dev_cfg.train_cfg_info.train_info[i].train_id, pos_info);			
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(1));

	}
}


//处理多车辆管理中的设备重置任务
void multi_train_manager::multi_train_manager_task_dev_reset_gen(void)
{
	net_msg msg_temp;
	
	uint32_t cin_addr_temp = 0;
	int addr_train_id = 0;

	int want_rst_dev = 0x00;
	int rst_cnt = 0;
	bool rst_dev_flag = false;
	volatile int seque_down_temp;
	train_task task_pop_temp;

	while(1)
	{
		if( !m_dev_reset_msg.empty() )
		{
			m_dev_reset_msg.pop(task_pop_temp);

			seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100);
			seque_down_temp++;
			task_pop_temp.sequence = seque_down_temp;
			
			train_position_info pos_info_temp = m_train_list.train_list_get_position_info(task_pop_temp.dev_id);
			// SPDLOG_INFO("1 pos_info:{}-{}-{}-{}",task_pop_temp.dev_id, pos_info_temp.speed, pos_info_temp.pos, pos_info_temp.is_locate);
			train_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, &m_dev_curr_state, &pos_info_temp);

			msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);
						
			addr_train_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

			//自检socekt addr
			if( (int)task_pop_temp.dev_id != addr_train_id )
			{
				cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(task_pop_temp.dev_id);
				SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} " ,task_pop_temp.dev_id, msg_temp.cin_addr, cin_addr_temp);
				msg_temp.cin_addr = cin_addr_temp;
			}

			if( 0x00 != addr_train_id )
			{
				m_net_msg_queue.push(msg_temp);
			}
					
			if(task_pop_temp.which_task == train_task_cmd_tag)
			{
				if(task_pop_temp.task.cmd == dev_cmd_tab_RESTART)
				{
					m_train_list.platform_list_map_update_uplink_comm_squence(task_pop_temp.dev_id * 100, 1);
					// m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);
					m_train_list.platform_list_map_update_comm_ack_flag(task_pop_temp.dev_id * 100, true);


					msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);								
					want_rst_dev = task_pop_temp.dev_id;
					rst_dev_flag = true;
					rst_cnt = 0x01;

					multi_train_reset_dev_add(task_pop_temp.dev_id);
				}
				else
				{
					int platform_id = task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id;
					// m_train_list.train_list_map_update_comm_msg(task_pop_temp.dev_id, msg_temp);
					m_train_list.platform_list_map_update_comm_msg(platform_id, msg_temp);
					m_train_list.platform_list_map_update_comm_sequeue(platform_id, seque_down_temp);
					m_train_list.train_list_map_update_downlink_tick(task_pop_temp.dev_id);
					m_train_list.platform_list_map_update_comm_ack_flag(task_pop_temp.dev_id * 100, false);
				}
			}
			
			SPDLOG_INFO("reset msg send to net downlink queue devid:{}, client_addr:{}, which_task:{}, seque_down_temp:{} - {}", task_pop_temp.dev_id, msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id));

		}

		if(rst_dev_flag)
		{
			rst_cnt++;
			if((rst_cnt%10) == 0)
			{
				uint32_t addr = m_train_list.train_list_map_get_dev_addr(want_rst_dev);
				SPDLOG_INFO("train:{} restart, ip:{}", want_rst_dev, addr);

				m_train_list.train_task_list_clear(want_rst_dev);

				rst_dev_flag = false;
			
			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(10));		
	}
}




bool multi_train_manager::multi_train_reset_dev_find(int dev)
{

	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		return false;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		return false;
	}
	else
	{
		return m_train_reset_dev_map.at(dev);
	}
}


void multi_train_manager::multi_train_reset_dev_clear(int dev)
{
	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, false));
		return;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, false));
		return ;
	}
	else
	{
		m_train_reset_dev_map.at(dev) = false;
		return ;
	}
}


//网络数据重发线程
void multi_train_manager::multi_train_manager_task_resend_thread_exe()
{
	struct timespec tic_dev;
	struct timespec tic_curr;
	long timedif;
	
	int i;	
	int dev_comm_unfinish_list_temp[32];
	int dev_comm_unfinish_cnt = 0x00;
	bool msg_valid_flag;
	net_msg msg_temp;

	while(1)
	{
		if( (m_dev_curr_state.dev_st.emerg_pressed) || (m_dev_curr_state.dev_st.safty_door_open) )
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(10));
			continue;
		}

		try
		{
			if( m_task_mtx.try_lock() )
			{
				clock_gettime(CLOCK_MONOTONIC, &tic_curr);	

				if( m_train_list.train_list_map_get_dev_comm_state(dev_comm_unfinish_list_temp, &dev_comm_unfinish_cnt) )
				{
					if( 0 != dev_comm_unfinish_cnt )
					{
						for(i = 0; i < dev_comm_unfinish_cnt; i++)
						{
							SPDLOG_INFO("train_task_list_comm_unfinish platform_id:{}", dev_comm_unfinish_list_temp[i]);
					
							m_train_list.train_list_map_get_download_tick(dev_comm_unfinish_list_temp[i] / 100, &tic_dev);
							timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/1000;

							SPDLOG_INFO("task resend timedif :{}", timedif);

							if( timedif > (m_dev_cfg.resend_timeout *1000))//resend_timeout -> 单位us
							{					
								SPDLOG_INFO("task log unfinish_list_temp[{}]={}, cnt_total:{}", i, dev_comm_unfinish_list_temp[i], dev_comm_unfinish_cnt);
						
								msg_valid_flag = m_train_list.platform_list_map_get_comm_msg(dev_comm_unfinish_list_temp[i], &msg_temp);
						
								if( msg_valid_flag )
								{
									msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(dev_comm_unfinish_list_temp[i] / 100);
						
									int addr_train_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

									if( (dev_comm_unfinish_list_temp[i] / 100) != addr_train_id )
									{
										uint32_t cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(dev_comm_unfinish_list_temp[i] / 100);
										SPDLOG_INFO("msg send to net downlink queue addr error, train_id:{}, cin_addr:{}-{} ", dev_comm_unfinish_list_temp[i] / 100, msg_temp.cin_addr, cin_addr_temp);
										msg_temp.cin_addr = cin_addr_temp;
									}

									m_train_list.train_list_map_update_downlink_tick(dev_comm_unfinish_list_temp[i] / 100);
									m_net_msg_queue.push(msg_temp);
								}
							}
						}
			
						try
						{
							m_task_mtx.unlock();
						}
						catch(const std::exception& e)
						{
							SPDLOG_INFO("log :{} error! :{}", dev_comm_unfinish_list_temp[i], e.what() );
						}
					}
				}
			}
			else
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(1));	
				continue;
			}
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_comm_unfinish_list_temp[i], e.what() );
		}
		
		std::this_thread::sleep_for(std::chrono::milliseconds(5));	
	}
}


////车辆任务管理线程，根据调度及心跳行走任务，生成网络数据下发
void multi_train_manager::multi_train_manager_task_new_gen_thread_exe()
{
	net_msg msg_temp;

	int dev_id_list_temp[32];
	int dev_task_valid_cnt = 0x00;
	int i;
	int task_type_temp;
	train_position_info pos_info;
	uint32_t cin_addr_temp = 0;
	int addr_train_id = 0;
	int want_rst_dev = 0x00;
	int rst_cnt = 0;
	bool rst_dev_flag = false;

	train_task_state m_task_platform_state;
	train_task_state m_task_carriage_state;

	while(1)
	{
		if( (m_dev_curr_state.dev_st.emerg_pressed) || (m_dev_curr_state.dev_st.safty_door_open) )
		{
			SPDLOG_INFO("emerg_pressed:{}, safty_door_open:{}", m_dev_curr_state.dev_st.emerg_pressed, m_dev_curr_state.dev_st.safty_door_open);
			std::this_thread::sleep_for(std::chrono::milliseconds(100));
			continue;
		}
	
		//存在非空的任务缓存，执行发送机制
		try
		{
			m_task_mtx.try_lock();
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_id_list_temp[0], e.what() );
		}

		if( m_train_list.train_task_list_empty_state(dev_id_list_temp, &dev_task_valid_cnt) )
		{
			if( 0 != dev_task_valid_cnt )
			{
				for(i = 0; i < dev_task_valid_cnt; i++)
				{
					SPDLOG_INFO("train_task_list_empty_state devid:{}, dev_task_valid_cnt:{}", dev_id_list_temp[i], dev_task_valid_cnt);

					// if( m_train_list.train_list_map_get_comm_flag(dev_id_list_temp[i]) )
					// {
					SPDLOG_INFO("train list dev_id_list_temp[{}]={}, size:{}", i, dev_id_list_temp[i], m_train_list.train_task_list_size(dev_id_list_temp[i]));

					volatile int seque_down_temp;
					train_task task_pop_temp;

					//获取系统内指定的下发任务消息
					m_train_list.train_task_list_pop( dev_id_list_temp[i], &task_pop_temp );
					SPDLOG_INFO("task_pop_temp, train_id:{}-{}, which_task:{}", dev_id_list_temp[i], task_pop_temp.dev_id, task_pop_temp.which_task);

					// if( m_train_list.train_list_map_get_comm_flag(dev_id_list_temp[i]) )
					if(m_train_list.platform_list_map_get_comm_ack_flag(task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id))
					{
						//组帧并控制发送
						seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id);	//下行sequence

						seque_down_temp++;
						task_pop_temp.sequence = seque_down_temp;

						if(task_pop_temp.which_task == train_task_move_tag)
						{
							pos_info.pos = task_pop_temp.task.move.target;
							pos_info.speed = (uint16_t)task_pop_temp.task.move.speed_limit;
							if(task_pop_temp.task.move.type == task_type_MOVE_SPEED)
								pos_info.is_locate = true;
							else
								pos_info.is_locate = false;

							m_train_list.train_list_position_insert(dev_id_list_temp[i], pos_info);
							continue;
						}

						if(task_pop_temp.which_task == train_task_inte_task_tag)
						{
							pos_info.pos = task_pop_temp.task.inte_task.task_info_move_pos;
							pos_info.speed = (uint16_t)task_pop_temp.task.inte_task.task_info_move_speed_limit;
							pos_info.is_locate = true;
							m_train_list.train_list_position_insert(dev_id_list_temp[i], pos_info);
						}

						train_position_info pos_info_temp = m_train_list.train_list_get_position_info(dev_id_list_temp[i]);
						task_type_temp = train_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, &m_dev_curr_state, &pos_info_temp);
						if(-1 == task_type_temp)
							continue;
							
						if( task_pop_temp.which_task == train_task_shift_tag)
						{
							m_task_platform_state.state = task_state_START;
							m_task_platform_state.type = task_pop_temp.task.shift.type;
							int platform_id = task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id;
							m_train_list.train_list_platform_task_state_insert(platform_id, m_task_platform_state);


							switch(task_type_temp)
							{
								case 4:	
									m_train_list.train_list_map_update_task_info(dev_id_list_temp[i], TRAIN_TASK_CARRIAGR_PACK);
									break;

								case 5:
									m_train_list.train_list_map_update_task_info(dev_id_list_temp[i], TRAIN_TASK_CARRIAGR_UNPACK);
									break;

								default:
									m_train_list.train_list_map_reset_task_info(dev_id_list_temp[i]);
									break;
							}
						}

						if( task_pop_temp.which_task == train_task_lifting_tag)
						{
							if(task_pop_temp.task.lifting.type == task_type_Y_MOVE)
							{
								if(task_pop_temp.task.lifting.target > m_dev_cfg.carriage_max_travel)
									return;
									
								m_task_carriage_state.state = task_state_START;
								m_task_carriage_state.type = task_type_Y_MOVE;
								int carriage_id = task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id;
								m_train_list.train_list_carriage_task_state_insert(carriage_id, m_task_carriage_state);
							}
						}
						
						//网络下发数据队列push，由线程处理下发
						msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(dev_id_list_temp[i]);
						
						addr_train_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);
						if( dev_id_list_temp[i] != addr_train_id )
						{
							cin_addr_temp = m_train_list.train_sock_list_get_socket_addr(dev_id_list_temp[i]);
							SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} ", dev_id_list_temp[i], msg_temp.cin_addr, cin_addr_temp);
							msg_temp.cin_addr = cin_addr_temp;
						}

						if( 0x00 != addr_train_id )
						{
							m_net_msg_queue.push(msg_temp);
						}
						
						// m_train_list.train_list_map_update_comm_msg(dev_id_list_temp[i], msg_temp);
						int platform_id = task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id;
						m_train_list.platform_list_map_update_comm_msg(platform_id, msg_temp);
						m_train_list.platform_list_map_update_comm_sequeue(platform_id, seque_down_temp);
						m_train_list.train_list_map_update_downlink_tick(dev_id_list_temp[i]);

						if( task_pop_temp.which_task == train_task_cmd_tag)
						{
							if(task_pop_temp.task.cmd == dev_cmd_tab_RESTART)
							{
								m_train_list.platform_list_map_update_uplink_comm_squence(task_pop_temp.dev_id * 100, 1);
								// m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);
								m_train_list.platform_list_map_update_comm_ack_flag(task_pop_temp.dev_id * 100, true);
							}
						}
						else if( (task_pop_temp.which_task == train_task_hb_time_sync_tag) || (task_pop_temp.which_task == train_task_move_tag))
						{
							// m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);
							m_train_list.platform_list_map_update_comm_ack_flag(task_pop_temp.dev_id * 100, true);
						}
						else
						{
							// m_train_list.train_list_map_update_comm_flag(dev_id_list_temp[i], false); 
							int platform_id = task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id;
							m_train_list.platform_list_map_update_comm_ack_flag(platform_id, false);
						}

						SPDLOG_INFO("msg send to net downlink queue train_id:{}, cin_addr:{}, which_task:{}, sequence:{} - {}", dev_id_list_temp[i], msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100 + task_pop_temp.carriage_id));


						if( task_pop_temp.which_task == train_task_cmd_tag)
						{
							if(task_pop_temp.task.cmd == dev_cmd_tab_RESTART)
							{							
								want_rst_dev = dev_id_list_temp[i];
								rst_dev_flag = true;
								rst_cnt = 0x01;

								multi_train_reset_dev_add(dev_id_list_temp[i]);
							}
						}
					}
					else
					{
						SPDLOG_INFO("train log");
						continue;
					}
				}
			}			
		}

		try
		{
			m_task_mtx.unlock();
		}
		catch(const std::exception& e)
		{
			SPDLOG_INFO("log :{} error! :{}", dev_id_list_temp[i], e.what() );
		}
		

		if(rst_dev_flag)
		{
			rst_cnt++;
			if((rst_cnt % 10) == 0)
			{
				uint32_t addr = m_train_list.train_list_map_get_dev_addr(want_rst_dev);
				SPDLOG_INFO("train:{} restart, ip:{}", want_rst_dev, addr);

				m_train_list.train_task_list_clear(want_rst_dev);

				rst_dev_flag = false;
			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(10));	
	}
}


void multi_train_manager::multi_train_reset_dev_add(int dev)
{
	std::lock_guard<std::mutex> info_lock(m_reset_info_mtx);

	if( m_train_reset_dev_map.empty() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, true));
		return;
	}

	std::unordered_map<int, bool>::iterator  iter = m_train_reset_dev_map.find(dev);

	//首先查找当前索引是否存在，若存在才能更新
	if( iter == m_train_reset_dev_map.end() )
	{
		m_train_reset_dev_map.insert(std::make_pair(dev, true));
	}
	else
	{
		m_train_reset_dev_map.at(dev) = true;
	}

	return;
}



//网络数据下发线程
void multi_train_manager::multi_train_manager_net_msg_thread_exe()
{
	net_msg msg_temp;
	_train_map_opt_tab train_opt_temp;
	std::shared_ptr<spdlog::logger> logger;
	string temp;
	train_info dev_info;
	char data[4];
	int i;
	int id_temp;
	bool logger_valid = false;

	while(1)
	{
		m_net_msg_queue.pop(msg_temp);
		dev_info.v_addr = epoll_poller_dev_get_sock_addr(msg_temp.cin_addr);

#if 1
		SPDLOG_INFO("msg downlink fd:{}, client_addr:{}, data_len:{}", m_server_fd, inet_ntoa(dev_info.v_addr.sin_addr), msg_temp.data_len);
		printf("net msg send data: ");
		for(int i = 0; i < msg_temp.data_len; i++)
			printf("%02x ", msg_temp.msg_data[i]);
		printf("\n\n");
#endif

		if(0 > sendto(m_server_fd, msg_temp.msg_data, msg_temp.data_len, MSG_NOSIGNAL, (struct sockaddr *)&dev_info.v_addr, sizeof(dev_info.v_addr)))
			SPDLOG_INFO("net msg send error, errno:{}", errno);
			
		SPDLOG_INFO("msg downlink addr:{}, data_len:{}", msg_temp.cin_addr, msg_temp.data_len);

		train_opt_temp = m_train_list.train_sock_list_find(msg_temp.cin_addr);	
			
		if( TRAIN_SESSION_EXCID == train_opt_temp )
		{
			id_temp = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);

			temp = {""};
			for(i = 0; i < msg_temp.data_len; i++)
			{
				sprintf(data, "%x ", msg_temp.msg_data[i]);
				data[3] = 0x00;
				temp += data;
			}

			logger = m_train_list.train_log_list_get_logger(id_temp, &logger_valid);	

			if( logger_valid )
			{
				logger->info( "[{}: {}: {}] [NET] [OUT] msglen:{} msg:{}", basename((char *)(__FILE__)), __FUNCTION__, __LINE__, msg_temp.data_len, temp);		
			}
		}
		
		// std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
}
    

////定时下发心跳报文
void multi_train_manager::multi_train_manager_hb_time_sync_exe()
{
	train_task task_pop_temp;
	msg_queue data_temp;
	int i;

	std::this_thread::sleep_for(std::chrono::seconds(10));

	while(1)
	{
		// std::this_thread::sleep_for(std::chrono::seconds(m_dev_cfg.heartbeat_cycle));
		std::this_thread::sleep_for(std::chrono::milliseconds(m_dev_cfg.heartbeat_cycle));


		//遍历目前系统里的车辆
		for(i = 0; i < m_database_train_cnt; i++)
		{
			task_pop_temp.dev_id = m_database_train_list[i];
			SPDLOG_INFO("hb create m_database_train_list[{}]={}, dev_id:{}", i, m_database_train_list[i], task_pop_temp.dev_id);

			task_pop_temp.sequence = 40;
			task_pop_temp.which_task = train_task_hb_time_sync_tag;
			task_pop_temp.task.hb_time_sync = 0x01;

			memcpy(data_temp.msg_data, (uint8_t *)(&task_pop_temp), sizeof(task_pop_temp));
			data_temp.train_id = task_pop_temp.dev_id;
			data_temp.carriage_id = 0;
			data_temp.type = TRAIN_TASK_OUT;
			m_scheduler_msg.scheduler_manager_task_msg_queue_push(data_temp);

			// std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}
	}
}


void multi_train_manager::multi_train_manager_downlink_heartbeat_msg(void)
{
	net_msg msg_temp;

	int cli_addr_temp = 0;
	int addr_train_id = 0;

	volatile int seque_down_temp;
	train_task task_pop_temp;

	while(1)
	{
		if( !m_dev_downlink_hb_msg.empty() )
		{
			m_dev_downlink_hb_msg.pop(task_pop_temp);

			seque_down_temp = m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100);
			seque_down_temp++;
			task_pop_temp.sequence = seque_down_temp;
			
			train_position_info pos_info_temp = m_train_list.train_list_get_position_info(task_pop_temp.dev_id);
			train_manage_release_task(&task_pop_temp, msg_temp.msg_data, &msg_temp.data_len, &m_dev_cfg, &m_dev_curr_state, &pos_info_temp);

			msg_temp.cin_addr = m_train_list.train_list_map_get_dev_addr(task_pop_temp.dev_id);
		
			addr_train_id = m_train_list.train_sock_list_get_id(msg_temp.cin_addr);
	
			if( (int)task_pop_temp.dev_id != addr_train_id )
			{
				cli_addr_temp = m_train_list.train_sock_list_get_socket_addr(task_pop_temp.dev_id);
				SPDLOG_INFO("msg send to net downlink queue addr error :{} :{} :{} ", task_pop_temp.dev_id, msg_temp.cin_addr, cli_addr_temp);
				msg_temp.cin_addr = cli_addr_temp;
			}

			if( 0x00 != addr_train_id )
			{
				m_net_msg_queue.push(msg_temp);
			}

			if( (task_pop_temp.which_task == train_task_hb_time_sync_tag) || (task_pop_temp.which_task == train_task_move_tag))
			{
				// m_train_list.train_list_map_update_comm_flag(task_pop_temp.dev_id, true);
				m_train_list.platform_list_map_update_comm_ack_flag(task_pop_temp.dev_id * 100, true);
				m_train_list.platform_list_map_update_comm_sequeue(task_pop_temp.dev_id * 100, seque_down_temp);
			}
			
			SPDLOG_INFO("heartbeat msg send to net downlink queue train_id:{}, cin_addr:{}, which_task:{}, sequence:{}-{}", task_pop_temp.dev_id, msg_temp.cin_addr, task_pop_temp.which_task, seque_down_temp, m_train_list.platform_list_map_get_comm_sequeue(task_pop_temp.dev_id * 100));
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(2));
	}
}



bool multi_train_manager::train_agent_get_train_list(train_list &dev_info)
{
	std::string file_name;

	std::string home_path = getenv("HOME");

	file_name = home_path + "/auto_sort_high_efficient/cfg_file/train_list.json";
	std::cout << "log>>: dir name curr_dir "<< file_name << std::endl;
	
    nlohmann::json j;
    ifstream jfile(file_name.c_str());

	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());
		
		return false;
	}

	dev_info.train_count = j["train_list"]["train_count"];

	for(int i = 0; i < dev_info.train_count; i++)
	{
		dev_info.train_info[i].train_id = j["train_list"]["train_info"][i]["train_id"];
		dev_info.train_info[i].train_state = j["train_list"]["train_info"][i]["train_state"];
   
		SPDLOG_INFO("train_list_config_info: train_count:{}, train_id:{} train_state:{}", dev_info.train_count, dev_info.train_info[i].train_id, dev_info.train_info[i].train_state);
	}

	return true;
}


void multi_train_manager::multi_train_manager_train_list_update(train_basic_info_mutilp list)
{
	int i;
	int cnt = 0;

	m_database_train_cnt = list.train_info_count;

	SPDLOG_INFO("m_database_train_cnt :{} :{}", m_database_train_cnt, list.train_info_count);

	for(i = 0; i < list.train_info_count; i++)
	{
		if(enable_state_DEV_ENABLE_STATE_ENABLE == list.train_info[i].state)
		{
			m_database_train_list[cnt] = list.train_info[i].train_id;
			SPDLOG_INFO("m_database_train_list :{} :{} :{} :{}", i, cnt, m_database_train_list[cnt], list.train_info[i].train_id);
			cnt++;
		}
	}
}


//从list中删除已下线的车辆
static void delete_outof_system(train_basic_info_mutilp &list)
{
	int i;
	int to_delete_idx = -1;

	for(i = 0; i < list.train_info_count; i++)
	{
		if(list.train_info[i].state == enable_state_DEV_ENABLE_STATE_ENABLE)
		{
			if(to_delete_idx >= 0)
			{
				list.train_info[to_delete_idx] = list.train_info[i];
				to_delete_idx++;
			}
		}
		else if (to_delete_idx < 0)
			to_delete_idx = i;
	}

	if ((to_delete_idx >= 0) && (list.train_info_count > to_delete_idx))
	{
		list.train_info_count = to_delete_idx;
	}

	SPDLOG_INFO("{} train in system.", list.train_info_count);
	for(i = 0; i < list.train_info_count; i++)
		SPDLOG_INFO("cnt:{}, devid:{} - state:{}", list.train_info_count, list.train_info[i].train_id, list.train_info[i].state);
}


void multi_train_manager::multi_train_manager_heart_beat_thread_exe()
{
	train_state dev_state_temp;
	train_agent_state dev_agent_state;
	msg_queue state_data;
	int list_id_temp[20];
	event_exception exce_temp;
	int dev_cnt = 0;
	long start_timedif;
	long timedif;
	_train_map_opt_tab dev_find_result;
	struct timespec tic_dev;
	struct timespec tic_curr;
	uint32_t cin_addr;
	uint16_t except_dev_id;
	train_state_net train_net_info;
	bool state_valid_flag;
	int i;

	
	while(1)
	{

		delete_outof_system(m_train_info);

		if( 0 == m_train_info.train_info_count )
		{
			m_train_info.train_info_count = 0x00;
		}
		else
			multi_train_manager_train_list_update(m_train_info);

	
		//获取当前的单调时钟时间
		clock_gettime(CLOCK_MONOTONIC, &tic_curr);	

		dev_cnt = m_train_list.train_list_map_get_size();

		//计算两个时间点之间的时间差，微秒
		start_timedif = MILLION*(tic_curr.tv_sec-m_start_tick.tv_sec)+(tic_curr.tv_nsec-m_start_tick.tv_nsec)/1000;

		// if( start_timedif > (m_dev_cfg.heartbeat_timeout * 1000 * 1000) ) 
		if( start_timedif > START_UP_HEART_BEAT_TIME_OUT )
		{
			//首先查找是否有database存在但是list列表中没有的车，若存在，需要上报异常
			for(i = 0; i < m_train_info.train_info_count; i++)
			{
				dev_find_result = m_train_list.train_list_map_find(m_train_info.train_info[i].train_id);

				if( TRAIN_SESSION_NOT_FIND == dev_find_result )
				{
					dev_state_temp.train_id = m_train_info.train_info[i].train_id;
					dev_state_temp.train_state = train_dev_state_DEV_UNKNOWN;
					dev_state_temp.motion_positon_valid_flag = false;
					dev_state_temp.dev_comm_ack_state = false;
				
					for(int j = 0; j < m_dev_cfg.train_cfg_info.train_info_count; j++)
					{
						if(m_train_info.train_info[i].train_id == m_dev_cfg.train_cfg_info.train_info[j].train_id)
						{
							dev_state_temp.carriage_cnt = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;
							dev_state_temp.carriage_st_count = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;

							for(int z = 0; z < (int)dev_state_temp.carriage_cnt; z++)
							{
								dev_state_temp.carriage_st[z].carriage_id = m_dev_cfg.train_cfg_info.train_info[j].carriage_info[z].carriage_id;
								dev_state_temp.carriage_st[z].y_axis_encoder_state = train_dev_state_DEV_UNKNOWN;
								dev_state_temp.carriage_st[z].load_platform_state = train_dev_state_DEV_UNKNOWN;
							}
						
							break;
						}
					}

					state_data.type = TRAIN_STATE_PUB;
					state_data.train_id = m_train_info.train_info[i].train_id;
					memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
					m_scheduler_msg.scheduler_manager_queue_push(state_data);

					exce_temp.which_evt_except = event_exception_except_tag;
					except_info except = dev_except::train_offline(m_train_info.train_info[i].train_id);
					except.state = exception_state_STATE_OCCURED;
					exce_temp.evt_except.except = except;

					except_dev_id = m_train_info.train_info[i].train_id * 100;
					if (m_train_list.exception_occur(except_dev_id, exce_temp.evt_except.except) > 0)
					{
						if(multi_train_manager_train_list_find(except.dev))
						{
							state_data.type = TRAIN_EXCEP_PUB;
							state_data.train_id = except.dev;
							state_data.carriage_id = except.sub_dev;
							memcpy(&state_data.msg_data[0], &exce_temp, sizeof(exce_temp));
							m_scheduler_msg.scheduler_manager_queue_push(state_data);
						}
					}
					
					SPDLOG_INFO("train :{} not in the locol list", m_train_info.train_info[i].train_id);
				}
			}
		}

	
		// clock_gettime(CLOCK_MONOTONIC, &tic_curr);

		// 检查已有的车辆中，是否有超时时间发生
		if( 0 != dev_cnt )
		{
			m_train_list.train_list_map_get_all_id(list_id_temp);

			for( i = 0; i < dev_cnt; i++ )
			{
				
				if( !multi_train_manager_train_list_find(list_id_temp[i]) )
				{
					SPDLOG_INFO("vh[{}] not in the curr list.", list_id_temp[i]);
					
					dev_state_temp.train_id = list_id_temp[i];
					dev_state_temp.train_state = train_dev_state_DEV_UNKNOWN;
					dev_state_temp.motion_positon_valid_flag = false;
					dev_state_temp.dev_comm_ack_state = false;
					
					for(int j = 0; j < m_dev_cfg.train_cfg_info.train_info_count; j++)
					{
						if(m_train_info.train_info[i].train_id == m_dev_cfg.train_cfg_info.train_info[j].train_id)
						{
							dev_state_temp.carriage_cnt = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;
							dev_state_temp.carriage_st_count = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;

							for(int z = 0; z < (int)dev_state_temp.carriage_cnt; z++)
							{
								dev_state_temp.carriage_st[z].carriage_id = m_dev_cfg.train_cfg_info.train_info[j].carriage_info[z].carriage_id;
								dev_state_temp.carriage_st[z].y_axis_encoder_state = train_dev_state_DEV_UNKNOWN;
								dev_state_temp.carriage_st[z].load_platform_state = train_dev_state_DEV_UNKNOWN;
							}
						
							break;
						}
					}

					state_data.type = TRAIN_STATE_PUB;
					state_data.train_id = list_id_temp[i];
					memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
					m_scheduler_msg.scheduler_manager_queue_push(state_data);

					continue;
				}

				if( m_train_list.train_list_map_get_upload_tick(list_id_temp[i], &tic_dev) )
				{
					//计算时间差
					timedif = MILLION*(tic_curr.tv_sec-tic_dev.tv_sec)+(tic_curr.tv_nsec-tic_dev.tv_nsec)/1000;
					
					if( timedif > (m_dev_cfg.heartbeat_timeout * 1000 * 1000) ) 
					{
						exce_temp.which_evt_except = event_exception_except_tag;
						except_info except = dev_except::train_offline(list_id_temp[i]);
						except.state = exception_state_STATE_OCCURED;
						exce_temp.evt_except.except = except;
						except_dev_id = list_id_temp[i] * 100;
						if (m_train_list.exception_occur(except_dev_id, exce_temp.evt_except.except) > 0)
						{
							if(multi_train_manager_train_list_find(except.dev))
							{
								state_data.type = TRAIN_EXCEP_PUB;
								state_data.train_id = except.dev;
								state_data.carriage_id = except.sub_dev;
								memcpy(&state_data.msg_data[0], &exce_temp, sizeof(exce_temp));
								m_scheduler_msg.scheduler_manager_queue_push(state_data);
							}				
						}

						SPDLOG_INFO("train :{}  heart beat time out ", exce_temp.evt_except.except.dev);

						
						//车辆掉线状态上报
						dev_state_temp.train_id = list_id_temp[i];
						dev_state_temp.train_state = train_dev_state_DEV_UNKNOWN;
						dev_state_temp.motion_positon_valid_flag = false;
						dev_state_temp.dev_comm_ack_state = false;
						
						for(int j = 0; j < m_dev_cfg.train_cfg_info.train_info_count; j++)
						{
							if(m_train_info.train_info[i].train_id == m_dev_cfg.train_cfg_info.train_info[j].train_id)
							{
								dev_state_temp.carriage_cnt = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;
								dev_state_temp.carriage_st_count = m_dev_cfg.train_cfg_info.train_info[j].carriage_cnt;

								for(int z = 0; z < (int)dev_state_temp.carriage_cnt; z++)
								{
									dev_state_temp.carriage_st[z].carriage_id = m_dev_cfg.train_cfg_info.train_info[j].carriage_info[z].carriage_id;
									dev_state_temp.carriage_st[z].y_axis_encoder_state = train_dev_state_DEV_UNKNOWN;
									dev_state_temp.carriage_st[z].load_platform_state = train_dev_state_DEV_UNKNOWN;
								}
							
								break;
							}
						}

						state_data.type = TRAIN_STATE_PUB;
						state_data.train_id = list_id_temp[i];
						memcpy(state_data.msg_data, (uint8_t *)(&dev_state_temp), sizeof(dev_state_temp));
						m_scheduler_msg.scheduler_manager_queue_push(state_data);


						//添加心跳超时处理, 删除车辆
						std::unique_lock<std::mutex> delete_lock(m_delete_lock);
						cin_addr = m_train_list.train_list_map_get_dev_addr(list_id_temp[i]);
						m_train_list.train_list_map_delete(list_id_temp[i]);
						m_train_list.train_sock_list_delete(cin_addr);
						m_train_list.train_log_list_delete(list_id_temp[i]);
						m_train_list.train_list_position_delete(list_id_temp[i]);
			
						// 从设备map中删除
						epoll_poller_dev_delete(cin_addr);
					
#ifdef MULTI_DEV_DEBUG
						m_train_list.train_dev_list_display();
#endif		
						delete_lock.unlock();
					
					}
				}
			}
		}


		dev_agent_state.train_agent_work_state = component_state_C_INIT;
		dev_agent_state.work_state = train_work_state_WORK_INIT;
		dev_agent_state.train_state = train_dev_state_DEV_UNKNOWN;
		dev_agent_state.train_error_no = 0;

		if(0 == m_train_info.train_info_count)
		{
			dev_agent_state.train_agent_work_state = component_state_C_IDLE;
		}

		for(i = 0; i < m_train_info.train_info_count; i++)
		{
			state_valid_flag = m_train_list.train_list_map_get_train_state(m_train_info.train_info[i].train_id, &train_net_info);
			if(state_valid_flag)
			{
				if(0x00 == train_net_info.train_run_st)
				{
					dev_agent_state.train_agent_work_state = component_state_C_RUNNING;
					break;
				}
			}
		}

		for(i = 0; i < m_train_info.train_info_count; i++)
		{
			state_valid_flag = m_train_list.train_list_map_get_train_state(m_train_info.train_info[i].train_id, &train_net_info);
			if(state_valid_flag)
			{
				SPDLOG_INFO("hb locate_st:{}", train_net_info.train_locate_st);
				if((0x01 == train_net_info.train_locate_st) || (0x02 == train_net_info.train_locate_st))
					dev_agent_state.work_state = train_work_state_WORK_WORK;
				else
				{
					dev_agent_state.work_state = train_work_state_WORK_CALIB;
					break;
				}
			}
		}

		for(i = 0; i < m_train_info.train_info_count; i++)
		{
			dev_agent_state.train_id = m_train_info.train_info[i].train_id;
			state_valid_flag = m_train_list.train_list_map_get_train_state(m_train_info.train_info[i].train_id, &train_net_info);
			if(state_valid_flag)
			{
				dev_agent_state.train_id = m_train_info.train_info[i].train_id;
				dev_agent_state.carriage_cnt = train_net_info.carriage_count;
				
				if(00 != train_net_info.exception_level)
				{
					dev_agent_state.train_error_no = m_train_list.train_list_map_get_train_error_no(m_train_info.train_info[i].train_id);

					if(0x04 == train_net_info.exception_level)
						dev_agent_state.train_state = train_dev_state_DEV_ERROR;
					else
						dev_agent_state.train_state = train_dev_state_DEV_FATAL;
				}
				else
					dev_agent_state.train_state = train_dev_state_DEV_NORMAL;

				state_data.type = TRAIN_AGENT_STATE_PUB;
				state_data.train_id = m_train_info.train_info[i].train_id;
				memcpy(&state_data.msg_data[0], &dev_agent_state, sizeof(dev_agent_state));
				m_scheduler_msg.scheduler_manager_queue_push(state_data);

			}
			else
			{
				dev_agent_state.carriage_cnt = 0;
				dev_agent_state.train_state = train_dev_state_DEV_UNKNOWN;
				dev_agent_state.train_error_no = 0;
				dev_agent_state.train_agent_work_state = component_state_C_UNKNOW;
				dev_agent_state.work_state = train_work_state_WORK_FATAL;

				state_data.type = TRAIN_AGENT_STATE_PUB;
				state_data.train_id = m_train_info.train_info[i].train_id;
				memcpy(&state_data.msg_data[0], &dev_agent_state, sizeof(dev_agent_state));
				m_scheduler_msg.scheduler_manager_queue_push(state_data);

			}
		}

		std::this_thread::sleep_for(std::chrono::seconds(2));
	}
}


void multi_train_manager::multi_train_manager_task_state_report_thread_exe()
{
	train_task_state platform_task_state, carriage_task_state;
	msg_queue state_data;
	int i, j, z;
	bool state_valid_flag = false;

	std::unordered_map<int, timer> platform_timers;
    std::unordered_map<int, train_task_state> platform_old_states;
    std::unordered_map<int, train_task_state> carriage_old_states;

	for(i = 0; i < (int)m_train_info.train_info_count; i++)
	{
		for(j = 0; j < (int)m_train_info.train_info[i].carriage_cnt; j++)
		{
			for(z = 0; z < (int)m_train_info.train_info[i].carriage_info[j].platform_cnt; z++)
			{
				int platform_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id;
				platform_timers[platform_id].start();
                platform_old_states[platform_id] = {};
                carriage_old_states[platform_id] = {};
			}
		}
	}

	while(1)
	{
		for(i = 0; i < (int)m_train_info.train_info_count; i++)
		{
			for(j = 0; j < (int)m_train_info.train_info[i].carriage_cnt; j++)
			{
				for(z = 0; z < (int)m_train_info.train_info[i].carriage_info[j].platform_cnt; z++)
				{
					int platform_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id;
					state_valid_flag = m_train_list.train_list_map_get_train_platform_task_state(platform_id, &platform_task_state);
					if(state_valid_flag)
					{
						if(platform_task_state.state == task_state_ERROR)
							SPDLOG_INFO("platform_task_state error:{}-{}-{}", platform_id, platform_task_state.type, platform_task_state.state);

						if((platform_old_states[platform_id].type != platform_task_state.type)
						|| (platform_old_states[platform_id].state != platform_task_state.state)
						|| platform_timers[platform_id].execute_time() > 2000)
						{
							platform_task_state.dev_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id / 100;
							platform_task_state.carriage_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id % 100;

							state_data.type = TRAIN_PLATFORM_TASK_STATE_PUB;
							state_data.train_id = m_train_info.train_info[i].train_id;
							memcpy(&state_data.msg_data[0], &platform_task_state, sizeof(platform_task_state));
							m_scheduler_msg.scheduler_manager_queue_push(state_data);

							platform_old_states[platform_id] = platform_task_state;
							platform_timers[platform_id].start();
						}
					}


					state_valid_flag = m_train_list.train_list_map_get_train_carriage_task_state(platform_id, &carriage_task_state);
					if(state_valid_flag)
					{
						if((carriage_old_states[platform_id].type != carriage_task_state.type)
						|| (carriage_old_states[platform_id].state != carriage_task_state.state)
						|| platform_timers[platform_id].execute_time() > 2000)
						{
							carriage_task_state.dev_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id / 100;
							carriage_task_state.carriage_id = m_train_info.train_info[i].carriage_info[j].platform_info[z].platform_id % 100;

							state_data.type = TRAIN_CARRIAGE_TASK_STATE_PUB;
							state_data.train_id = m_train_info.train_info[i].train_id;
							memcpy(&state_data.msg_data[0], &carriage_task_state, sizeof(carriage_task_state));
							m_scheduler_msg.scheduler_manager_queue_push(state_data);

							carriage_old_states[platform_id] = carriage_task_state;
							platform_timers[platform_id].start();
						}
					}
				}
			}
		}
		
		std::this_thread::sleep_for(std::chrono::milliseconds(2));
	}
}


void multi_train_manager::multi_train_manager_train_basic_info_summary()
{
	train_info info;
	train_basic_info_mutilp train_basic_info;

	while(1)
	{
		if(m_scheduler_msg.scheduler_manager_get_train_info(train_basic_info))
		{
			for(int i = 0; i < train_basic_info.train_info_count; i++)
			{
				if(m_train_list.train_list_map_get_train_info(train_basic_info.train_info[i].train_id, info))
				{
					strcpy(train_basic_info.train_info[i].ip, inet_ntoa(info.v_addr.sin_addr));
					strcpy(train_basic_info.train_info[i].sw_ver, info.sw_version);
					strcpy(train_basic_info.train_info[i].hw_ver, info.hw_version);
					
					// SPDLOG_INFO("333IP: {}-{}", inet_ntoa(info.v_addr.sin_addr), train_basic_info.train_info[i].ip);

				}
			}
		}
		else
		{
			SPDLOG_INFO("get train list from coreserver failed");
			std::this_thread::sleep_for(std::chrono::milliseconds(10));

			continue;
		}

		m_train_info = train_basic_info;


		std::this_thread::sleep_for(std::chrono::seconds(2));
	}

}

void multi_train_manager::multi_train_manager_reply_train_info_thread()
{
	while(1)
	{
		scheduler_manager_reply_train_info();
		std::this_thread::sleep_for(std::chrono::milliseconds(1));		
	}
}


/**@brief	  通过REQ-REP模式向coreserver填充数据
* @param[out] train_basic_info_mutilp *dev_list  --- 获取到的coreserver发布的设备列表
* @return	  当前函数执行结果，用于判断 dev_list的有效性
* - true	  成功
* - false	  失败
*/

int multi_train_manager::scheduler_manager_reply_train_info(void) 
{
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t req_msg;
	uint8_t rep_msg_train_basic_info[train_basic_info_mutilp_size];
	uint8_t rep_msg_train_config_para[train_config_para_size];
	train_config_para rep_config_para;

	get_rep_train_para(&rep_config_para);
	
	// 首先检查当前socket的连接状态
	if( !m_train_info_replayer.connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
	}

	m_train_info_replayer.recv(req_msg, zmq::recv_flags::none);	

	if (req_msg.size() == 0)
    	return 0;
	
	stream_in = pb_istream_from_buffer((const uint8_t*)req_msg.data(), req_msg.size());
	if (!pb_decode(&stream_in, data_request_fields, &request))
	{
		spdlog::error("pb decode error: {}", stream_in.errmsg);
		return 0;
	}

	if(strncmp(request.key, DATA_KEY_TRAIN_BASE_INFO, sizeof(request.key)) == 0)
	{
		if(request.type == data_request_cmd_READ)
		{
			stream_out = pb_ostream_from_buffer(rep_msg_train_basic_info, train_basic_info_mutilp_size);
			if (!pb_encode(&stream_out, train_basic_info_mutilp_fields, &m_train_info))
			{
				SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
				return 0;
			}
			else
				m_train_info_replayer.send(zmq::buffer(rep_msg_train_basic_info, stream_out.bytes_written), zmq::send_flags::none);
		}
		else if(request.type == data_request_cmd_WRITE)	
			SPDLOG_INFO("not surpport write command to train_basic_info");
	}

	else if(strncmp(request.key, DATA_KEY_TRAIN_CFG_PARA, sizeof(request.key)) == 0)
	{
		if(request.type == data_request_cmd_READ)
		{
			stream_out = pb_ostream_from_buffer(rep_msg_train_config_para, train_config_para_size);
			if(!pb_encode(&stream_out, train_config_para_fields, &rep_config_para))
			{
				SPDLOG_LOGGER_DEBUG(spdlog::get("rep_config_para"), "pb encode error: {}", stream_out.errmsg);
				return 0;
			}
			else
				m_train_info_replayer.send(zmq::buffer(rep_msg_train_config_para, stream_out.bytes_written), zmq::send_flags::none);
		}
	}

	else
		SPDLOG_ERROR("error key command: {}", std::string(request.key));


	return 1;
}

void multi_train_manager::get_rep_train_para(train_config_para *rep_config_para)
{
	rep_config_para->x_speed = m_dev_cfg.walk_motor_speed;
	rep_config_para->x_acc = m_dev_cfg.walk_motor_acc;
	rep_config_para->x_d_acc = m_dev_cfg.walk_motor_dec;
	rep_config_para->y_speed = m_dev_cfg.carriage_motor_speed;
	rep_config_para->y_acc = m_dev_cfg.carriage_motor_acc;
	rep_config_para->y_d_acc = m_dev_cfg.carriage_motor_dec;
	rep_config_para->z_speed = m_dev_cfg.belt_motor_speed; 
	rep_config_para->z_cali_speed = m_dev_cfg.belt_zero_speed; 
	rep_config_para->z_acc = m_dev_cfg.belt_motor_acc; 
	rep_config_para->z_limit = m_dev_cfg.belt_rotation_distance; 
}

//将里程信息写入配置文件
void multi_train_manager::mileage_info_write_to_cfg_file()
{
	bool state_valid_flag = false;
	train_state_net train_net_info;
	std::string mileage_file_name = "/home/<USER>/auto_sort_high_efficient/cfg_file/mileage_info.json";
	nlohmann::json j;
    std::ifstream jfile(mileage_file_name.c_str());

	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("read file occurs error: {} -{}", fe.what(), mileage_file_name);
	}

	jfile.close();

	
	for(int i = 0; i < m_train_info.train_info_count; i++)
	{
		state_valid_flag = m_train_list.train_list_map_get_train_state(m_train_info.train_info[i].train_id, &train_net_info);
		if(state_valid_flag)
		{

			for (auto& item : j["mileage_info"])
			{
				if (item["train_id"] == m_train_info.train_info[i].train_id)
				{
					std::ofstream o(mileage_file_name);
					
					if(train_net_info.mileage < 0 || train_net_info.mileage_reference < 0)
					{
						train_net_info.mileage = 0;
						train_net_info.mileage_reference = 0;
					}
					
					if(m_train_list.train_list_map_get_train_restart_flag(m_train_info.train_info[i].train_id))
					{
						SPDLOG_INFO("train:{} restart", m_train_info.train_info[i].train_id);
						item["encoder_mileage"] = 0;
						item["current_mileage"] = 0;
						o << std::setw(4) << j << std::endl;

						m_train_list.train_list_map_update_train_restart_flag(m_train_info.train_info[i].train_id, false);
					}


					std::string encoder_total_mileage_string = item["encoder_total_mileage"].get<std::string>();
					double encoder_total_mileage = std::stod(encoder_total_mileage_string);

					std::string current_total_mileage_string = item["current_total_mileage"].get<std::string>();
					double current_total_mileage = std::stod(current_total_mileage_string);

					int32_t encoder_mileage = item["encoder_mileage"].get<int32_t>();
					int32_t current_mileage = item["current_mileage"].get<int32_t>();
					
					encoder_total_mileage = encoder_total_mileage + (train_net_info.mileage - encoder_mileage) / 1000.0;
					current_total_mileage = current_total_mileage + (train_net_info.mileage_reference - current_mileage) / 1000.0;
					
					std::string encoder_total_mileage_temp = std::to_string(encoder_total_mileage);
					std::string current_total_mileage_temp = std::to_string(current_total_mileage);
					
                    item["encoder_total_mileage"] = encoder_total_mileage_temp;
                    item["current_total_mileage"] = current_total_mileage_temp;

					item["encoder_mileage"] = train_net_info.mileage;
					item["current_mileage"] = train_net_info.mileage_reference;
										
					o << std::setw(4) << j << std::endl;

				}
			}
		}
	}
}


void multi_train_manager::multi_train_manager_mileage_info_statistic_thread()
{
	std::this_thread::sleep_for(std::chrono::seconds(10));

	while(1)
	{
		mileage_info_write_to_cfg_file();

		std::this_thread::sleep_for(std::chrono::seconds(60));
	}	
}



void multi_train_manager::udp_net_init()
{
	m_locol_server.udp_server_socket_init();

	m_locol_server.udp_server_socket_server_cfg(m_server_addr, m_server_port);
	SPDLOG_INFO("udp_server_socket_server_cfg({}/{}) init ok", m_server_addr, m_server_port);

	m_locol_server.udp_server_socket_bind();

	m_locol_server.udp_server_socket_set_reuseaddr(true);

	m_locol_server.udp_server_socket_set_nonblocking();

    epoll_init(m_locol_server.udp_server_socket_get_fd());

	epoll_add_fd(m_locol_server.udp_server_socket_get_fd(), EPOLLIN, false);

    SPDLOG_INFO("epoll init ok, add:{} to epoll event", m_locol_server.udp_server_socket_get_fd());

	m_recv_worker.start(); 
	SPDLOG_INFO("thread pool init ok");

	clock_gettime(CLOCK_MONOTONIC, &m_start_tick);	
}

void multi_train_manager::multi_train_manager_config(train_agent_cfg *cfg)
{
	m_server_addr = cfg->server_info.server_addr;
	m_server_port = cfg->server_info.server_port;
	m_client_port = cfg->server_info.client_port;
	m_dev_cfg = *cfg;
}



void multi_train_manager::train_agent_get_config_from_data_map(train_agent_cfg *cfg, ack_map_info &map_info)
{
	// cfg->train_count = map_info.train_count;
	// for(int i = 0; i < cfg->train_count; i++)
	// {
	// 	cfg->train_info[i].train_id = map_info.train_info[i].train_id;
	// 	cfg->train_info[i].platform_type = map_info.train_info[i].platform_type;
	// 	cfg->train_info[i].carriage_num = map_info.train_info[i].carriage_cnt;
	// }
	
	cfg->train_cfg_info = map_info.train_init_info;

	// cfg->carriage_max_travel = map_info.carriage_max_travel;
	cfg->carriage_max_travel = 1700;
	cfg->map_total_length = map_info.map_total_length;
	cfg->map_dev_calib_point_cnt = map_info.map_dev_calib_point_cnt;

	for(int i = 0; i < cfg->map_dev_calib_point_cnt; i++)
	{
		cfg->map_calib_points[i].id = map_info.map_calib_points[i].id;
		cfg->map_calib_points[i].position = map_info.map_calib_points[i].position;
	}
}


void multi_train_manager::multi_train_manager_init(train_agent_cfg *cfg)
{
	ack_map_info map_info;
	train_task_state m_task_platform_state;
	train_task_state m_task_carriage_state;

	m_scheduler_msg.scheduler_manager_init(&map_info);
	SPDLOG_INFO("train scheduler_manager_init msg(zmq) init ok");

	train_agent_get_config_from_data_map(cfg, map_info);
	multi_train_manager_config(cfg);
	std::this_thread::sleep_for(std::chrono::milliseconds(10));
	
	m_task_platform_state.state = task_state_INIT;
	m_task_platform_state.type = task_type_TASK_NULL;
	m_task_carriage_state.state = task_state_INIT;
	m_task_carriage_state.type = task_type_TASK_NULL;
	platform_task_info dev_info;
	dev_info.v_comm_finish_flag = true;

	for(int i = 0; i < (int)m_dev_cfg.train_cfg_info.train_info_count; i++)
	{
		for(int j = 0; j < (int)m_dev_cfg.train_cfg_info.train_info[i].carriage_cnt; j++)
		{
			for(int z = 0; z < (int)m_dev_cfg.train_cfg_info.train_info[i].carriage_info[j].platform_cnt; z++)
			{
				m_train_list.train_list_platform_task_state_insert(m_dev_cfg.train_cfg_info.train_info[i].carriage_info[j].platform_info[z].platform_id, m_task_platform_state);
				m_train_list.train_list_carriage_task_state_insert(m_dev_cfg.train_cfg_info.train_info[i].carriage_info[j].platform_info[z].platform_id, m_task_carriage_state);
				m_train_list.platform_list_map_insert(m_dev_cfg.train_cfg_info.train_info[i].carriage_info[j].platform_info[z].platform_id, dev_info);
				m_train_list.platform_list_map_insert_comm_sequeue(m_dev_cfg.train_cfg_info.train_info[i].carriage_info[j].platform_info[z].platform_id);
				std::this_thread::sleep_for(std::chrono::milliseconds(10));
			}
		}
	}

	m_dev_fsm.fsm_manager_init();

	m_train_info_replayer.bind(SERVICE_TARIN_BASE_INFO);


    udp_net_init();

}

void multi_train_manager::multi_train_manager_run()
{
	m_scheduler_msg.scheduler_manager_run(get_fsm());
	m_dev_fsm.fsm_manager_run();

	task_msg_new_thread = new std::thread(&multi_train_manager::multi_train_manager_task_new_gen_thread_exe, this);
	SPDLOG_INFO("multi_train_manager_task_new_gen_thread_exe thread init ok");

	heart_beat_thread = new std::thread(&multi_train_manager::multi_train_manager_heart_beat_thread_exe, this);
	SPDLOG_INFO("multi_train_manager_heart_beat_thread_exe thread init ok");

	net_msg_send_thread = new std::thread(&multi_train_manager::multi_train_manager_net_msg_thread_exe, this);
	SPDLOG_INFO("multi_train_manager_net_msg_thread_exe thread init ok");

	task_msg_resend_thread = new std::thread(&multi_train_manager::multi_train_manager_task_resend_thread_exe, this);
	SPDLOG_INFO("multi_train_manager_task_resend_thread_exe thread init ok");

	hb_time_sync_thread = new std::thread(&multi_train_manager::multi_train_manager_hb_time_sync_exe, this);
	task_msg_hb_thread = new std::thread(&multi_train_manager::multi_train_manager_downlink_heartbeat_msg, this);
	SPDLOG_INFO("multi_train_manager_hb_time_sync_exe thread init ok");

	if( m_dev_cfg.m_dev_reset_force_send_flag )
	{
		dev_rst_msg_thread = new std::thread(&multi_train_manager::multi_train_manager_task_dev_reset_gen, this);
		SPDLOG_INFO("multi_train_manager_task_dev_reset_gen thread init ok");
	}

	train_info_summary_thread = new std::thread(&multi_train_manager::multi_train_manager_train_basic_info_summary, this);
	SPDLOG_INFO("multi_train_manager_train_basic_info_summary thread init ok");

	train_info_reply_thread = new std::thread(&multi_train_manager::multi_train_manager_reply_train_info_thread, this);
	SPDLOG_INFO("multi_train_manager_reply_train_info_thread thread init ok");

	task_report_thread = new std::thread(&multi_train_manager::multi_train_manager_task_state_report_thread_exe, this);
	SPDLOG_INFO("multi_train_manager_task_state_report_thread_exe thread init ok");

	mileage_info_thread = new std::thread(&multi_train_manager::multi_train_manager_mileage_info_statistic_thread, this);
	SPDLOG_INFO("multi_train_manager_mileage_info_statistic_thread thread init ok");

    epoll_main_loop(3);
}