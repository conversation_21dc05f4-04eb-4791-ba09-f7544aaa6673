/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 消息主题相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

/**
 * @brief   通用消息主题的头（包括 版本）
 *
 */
static char *topic_prefix = "$iot/v1/";

/**
 * @brief   静态函数 消息主题字符串分割
 *
 * @param[in] topic_str: 消息主题字符串
 * @param[in] result:分割的结果
 * @param[in] max_num:result 中子字串的个数
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
 static int32_t jd_thingtalk_proto_topic_str_splite(char *topic_str, char **result, int32_t max_num)
{
    // int32_t pos_c = 0;
    int32_t len = jd_thingtalk_pal_strlen(topic_str);
    int32_t sub_len = 0;
    int32_t ii, num_now;
    if (len == 0) {
        log_error("the input message topic string is zeros");
        return JD_THINGTALK_RET_FAILED;
    }
    num_now = 0;
    // char *tmp_str;
    for (ii = 0; ii < len; ii ++) {
        if (topic_str[ii] == '/') {
            if (sub_len != 0) {
                result[num_now] = (char *) jd_thingtalk_pal_malloc((sub_len + 1) * sizeof(char));
                jd_thingtalk_pal_memset(result[num_now], 0, (sub_len + 1) * sizeof(char));
                jd_thingtalk_pal_memcpy(result[num_now], &topic_str[ii - sub_len], sub_len);
                sub_len = 0;
                num_now = num_now + 1;
                if (num_now >= max_num) {
                    log_error("the input topic string is wrong:%s", topic_str);
                    return JD_THINGTALK_RET_FAILED;
                }
            }
        } else {
            sub_len ++;
        }
    }
    
    result[num_now] = (char *) jd_thingtalk_pal_malloc((sub_len + 1) * sizeof(char));
    jd_thingtalk_pal_memset(result[num_now], 0, (sub_len + 1) * sizeof(char));
    jd_thingtalk_pal_memcpy(result[num_now], &topic_str[len - sub_len], sub_len);
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   解析消息主题
 *
 * @param[in] topic_str: 消息主题字符串
 * @return 
 *    解析后的消息主题结构体指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
JDThingTalkProtoTopic_t *jd_thingtalk_proto_topic_parse(char *topic_str)
{
    JDThingTalkProtoTopic_t *topic = NULL;
    int32_t max_num = 8;
    int32_t ii;
    int32_t is_service = 1;
    char **result = (char **) jd_thingtalk_pal_malloc(max_num * sizeof(char *));
    jd_thingtalk_pal_memset(result, 0, max_num * sizeof(char *));
    if (0 == jd_thingtalk_proto_topic_str_splite(topic_str, result, max_num)) {
        topic = (JDThingTalkProtoTopic_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoTopic_t));
        jd_thingtalk_pal_memset(topic, 0, sizeof(JDThingTalkProtoTopic_t));

        // retain
        topic->retain = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[0]) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(topic->retain, result[0]);

        // version
        topic->version = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[1]) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(topic->version, result[1]);

        // object name
        topic->obj_name = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[2]) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(topic->obj_name, result[2]);

        // deviceId
        topic->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[3]) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(topic->deviceId, result[3]);

        // 判断是不是 service
        if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_PROP)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_EVT)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_FUNC)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_REG)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_AUTH)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_HB)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_THMD)) {
            is_service = 0;
        } else if (!jd_thingtalk_pal_strcmp(result[4], JD_THINGTALK_PROTO_TOPIC_TYPE_NTP)) {
            is_service = 0;
        } else {}; //

        // 根据是不是 service 来决定 类型名字 (type name) 的索引
        ii = 4;
        if (is_service) {
            topic->service_key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[4]) + 1) * sizeof(char));
            jd_thingtalk_pal_strcpy(topic->service_key, result[4]);

            ii = 5;
        }

        // type name
        topic->type_name = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[ii]) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(topic->type_name, result[ii]);
        ii++;

        // command
        if (result[ii] != NULL) {
            topic->command = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[ii]) + 1) * sizeof(char));
            jd_thingtalk_pal_strcpy(topic->command, result[ii]);
            ii ++;

            // response
            if (result[ii] != NULL) {
                topic->response = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(result[ii]) + 1) * sizeof(char));
                jd_thingtalk_pal_strcpy(topic->response, result[ii]);
            }
        }
    }

    // 释放内存
    if (result != NULL) {
        for (ii = 0; ii < max_num; ii++) {
            if(result[ii] != NULL) {
                jd_thingtalk_pal_free(result[ii]);
            }
        }
        jd_thingtalk_pal_free(result);
    }

    return topic;
}

/**
 * @brief   消息主题结构提资源释放
 *
 * @param[in] topic: 待解析的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_topic_free(JDThingTalkProtoTopic_t *topic)
{
    if (topic != NULL) {
        if(topic->retain != NULL) {
            jd_thingtalk_pal_free(topic->retain);
            topic->retain = NULL;
        }
        if(topic->version != NULL) {
            jd_thingtalk_pal_free(topic->version);
            topic->version = NULL;
        }
        if(topic->obj_name != NULL) {
            jd_thingtalk_pal_free(topic->obj_name);
            topic->obj_name = NULL;
        }
        if(topic->deviceId != NULL) {
            jd_thingtalk_pal_free(topic->deviceId);
            topic->deviceId = NULL;
        }
        if(topic->service_key != NULL) {
            jd_thingtalk_pal_free(topic->service_key);
            topic->service_key = NULL;
        }
        if(topic->type_name != NULL) {
            jd_thingtalk_pal_free(topic->type_name);
            topic->type_name = NULL;
        }
        if(topic->command != NULL) {
            jd_thingtalk_pal_free(topic->command);
            topic->command = NULL;
        }
        if(topic->response != NULL) {
            jd_thingtalk_pal_free(topic->response);
            topic->response = NULL;
        }

        jd_thingtalk_pal_free(topic);
        topic = NULL;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   产生消息主题字符串
 *
 * @param[in] type: 消息主题的类型
 * @param[in] deviceId: 设备标识
 * @param[in] service_key: 服务关键字，如果消息主题类型不是服务类，该参数可填空
 * @return 
 *    消息主题字符串指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
char *jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_T type, char *obj_name, char *deviceId, char *service_key)
{
    char *topic = NULL;
    int32_t prefix_len = jd_thingtalk_pal_strlen(topic_prefix);
    int32_t obj_len = jd_thingtalk_pal_strlen(obj_name);
    int32_t id_len = jd_thingtalk_pal_strlen(deviceId);
    int32_t key_len = 0;
    if (service_key == NULL) {
        key_len = 0;
    } else {
        key_len = jd_thingtalk_pal_strlen(service_key);
    }
    char *tail_str = NULL;
    int32_t tail_len = 0;
    switch (type) {
        case JD_THINGTALK_PROTO_TOPIC_AUTH_POST:
            tail_str = "/auth/post";
            break;
        case JD_THINGTALK_PROTO_TOPIC_AUTH_POST_RES:
            tail_str = "/auth/post/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_EVT_ONLINE:
            tail_str = "/events/online";
            break;
        case JD_THINGTALK_PROTO_TOPIC_EVT_OFFLINE:
            tail_str = "/events/offline";
            break;
        case JD_THINGTALK_PROTO_TOPIC_HB_POST:
            tail_str = "/hearbeat/post";
            break;
        case JD_THINGTALK_PROTO_TOPIC_HB_POST_RES:
            tail_str = "/hearbeat/post/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_HB_SET:
            tail_str = "/hearbeat/set";
            break;
        case JD_THINGTALK_PROTO_TOPIC_EVT_POST:
            tail_str = "/events/post";
            break;
        case JD_THINGTALK_PROTO_TOPIC_PROP_SET:
            tail_str = "/properties/set";
            break;
        case JD_THINGTALK_PROTO_TOPIC_PROP_SET_RES:
            tail_str = "/properties/set/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_PROP_GET:
            tail_str = "/properties/get";
            break;
        case JD_THINGTALK_PROTO_TOPIC_PROP_GET_RES:
            tail_str = "/properties/get/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_PROP_POST:
            tail_str = "/properties/post";
            break;
        case JD_THINGTALK_PROTO_TOPIC_FUNC_CALL:
            tail_str = "/functions/call";
            break;
        case JD_THINGTALK_PROTO_TOPIC_FUNC_CALL_RES:
            tail_str = "/functions/call/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_REG:
            tail_str = "/register";
            break;
        case JD_THINGTALK_PROTO_TOPIC_REG_RES:
            tail_str = "/register/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST:
            tail_str = "/thing-model/post";
            break;
        case JD_THINGTALK_PROTO_TOPIC_THINGMODEL_POST_RES:
            tail_str = "/thing-model/post/response";
            break;
        case JD_THINGTALK_PROTO_TOPIC_NTP_REQ:
            tail_str = "/ntp/request";
            break;
        case JD_THINGTALK_PROTO_TOPIC_NTP_REQ_RES:
            tail_str = "/ntp/response";
            break;
        default:
            log_error("unsupport topic type:%d", type);
            return NULL;
    }
    
    // 产生消息主题
    tail_len = jd_thingtalk_pal_strlen(tail_str);
    topic = (char *) jd_thingtalk_pal_malloc((prefix_len + obj_len + id_len + key_len + tail_len + 3) * sizeof(char));
    if (topic == NULL) {
        log_error("memory malloc for topic is failed");
        return NULL;
    }
    if (key_len > 0) {
        jd_thingtalk_pal_sprintf(topic, "%s%s%s%s%s%s%s", topic_prefix, obj_name, "/", deviceId, "/", service_key, tail_str);
    } else {
        jd_thingtalk_pal_sprintf(topic, "%s%s%s%s%s", topic_prefix, obj_name, "/", deviceId, tail_str);
    }
    return topic;
}

/**
 * @brief   产生通配消息主题字符串
 *
 * @param[in] obj_name: 对象空间名字
 * @param[in] deviceId: 设备标识
 * @return 
 *    消息主题字符串指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
char *jd_thingtalk_proto_wildcard_topic(char *obj_name, char *deviceId)
{
    int32_t prefix_len = jd_thingtalk_pal_strlen(topic_prefix);
    int32_t obj_len = jd_thingtalk_pal_strlen(obj_name);
    int32_t id_len = jd_thingtalk_pal_strlen(deviceId);
    char *topic = (char *) jd_thingtalk_pal_malloc((prefix_len + obj_len + id_len + 4) * sizeof(char));
    if (topic == NULL) {
        log_error("memory malloc for topic is failed");
        return NULL;    
    }
    jd_thingtalk_pal_sprintf(topic, "%s%s%s%s%s", topic_prefix, obj_name, "/", deviceId, "/#");
    
    return topic;
}
// end of file
