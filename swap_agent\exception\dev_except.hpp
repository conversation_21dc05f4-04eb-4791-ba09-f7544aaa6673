#include <cstdint>
#include <string>

#include "share/pb/idl/exception.pb.h"
#include "share/exception_code.hpp"

namespace dev_except
{
	inline except_info swap_offline(uint8_t n)
	{
		except_info e = {exception_src_AUTO_EXCHANGE_AGENT, exception_level_FATAL, AUTO_EXCHANGE_OFFLINE, 0, "", (uint32_t)n, 0, exception_state_STATE_RESET};

		std::string text = "自动取换箱[" + std::to_string(n) + "]掉线:";
		strncpy(e.description, text.c_str(), sizeof(e.description));
		return e;
	}

	inline except_info unrecognized_except(exception_src src, uint8_t dev_id, uint8_t sub_dev_id, uint32_t code)  
	{
		except_info e = {src, exception_level_ERROR, UNRECOGNIZED_DEV_ERROR, code, "未识别的设备异常", (uint32_t)dev_id, (uint32_t)sub_dev_id, exception_state_STATE_OCCURED};
		return e;
	}

	/*设备上报的异常码转为系统内定义的异常*/
	int errcode_to_exception(uint32_t err_code, uint32_t subcode, except_info &except);

	/*系统内定义的异常转为设备上报的异常码*/
	int exception_to_errcode(const except_info &except,  uint32_t &err_code, uint32_t &subcode);

	class dev_except
	{
	public:
		/*error和subcode为设备上报的异常码和子码*/
		explicit dev_except(uint8_t dev_id, uint8_t sub_dev_id, uint32_t error, uint32_t subcode)
		{
			origin_error = error;
			
			//无法识别的异常, 不在预先定义的异常表内
			if (errcode_to_exception(error, subcode, except) < 0)
			{
				unrecognized = true;
				except = unrecognized_except(exception_src_AUTO_EXCHANGE_AGENT, dev_id, sub_dev_id, error);
			}
			else
			{
				except.dev = dev_id;
				except.sub_dev = sub_dev_id;

				unrecognized = false;
			}
		}

		except_info get_except_info(void)
		{
			return this->except;
		}

		uint32_t get_errcode(void)
		{
			return origin_error;
		}

	private:
		bool unrecognized;
		uint32_t origin_error; //设备上报的异常码
		except_info except;
	};

}