#ifndef __CFG_HPP__
#define __CFG_HPP__

#pragma pack(push, 1)

/**@struct dev_state_net
* @brief 网络端传输过来的车辆状态数据
*/
typedef struct _dev_state_net
{
    uint8_t dev_run_st;              ///< 设备运行状态
    int8_t dev_locate_st;            ///<设备定位状态
    uint8_t excep_id;                ///<异常设备ID
    uint8_t exception_level;             ///<异常等级
    uint32_t exception_code;            ///<异常码
    uint32_t curr_task_id;              ///<当前任务id
    uint8_t task_type;                  ///<任务类型
    uint8_t task_control_object;        ///<任务控制对象
    uint8_t task_state;                 ///<任务执行状态
    uint8_t sub_task_state;             ///<子任务状态
    uint8_t box_state;                  ///<货箱状态

    uint32_t target_position_x;         ///x轴目标位置
    uint32_t curr_position_x;           ///x轴当前位置
    uint16_t cmd_speed_x;           ///<x轴指令速度
    uint16_t curr_speed_x;          ///<x轴当前速度
    uint16_t adjust_speed_x;        ///<x轴调速值
    uint8_t state_x;                ///<x轴状态，0：正常， 1：异常
    uint16_t speed_x1;              ///<x1轴速度
    uint32_t position_x1;           ///<x1轴位置
    uint16_t motor_error_code_x1;   ///<X1轴电机错误码
    uint16_t speed_x2;              ///<x2轴速度
    uint32_t position_x2;           ///<x2轴位置
    uint16_t motor_error_code_x2;   ///<X2轴电机错误码

    uint16_t target_position_y;         ///y轴目标位置
    uint16_t curr_position_y;           ///y轴当前位置
    uint16_t cmd_speed_y;           ///<y轴指令速度
    uint16_t curr_speed_y;          ///<y轴当前速度
    uint16_t adjust_speed_y;        ///<y轴调速值
    uint8_t state_y;                ///<y轴状态
    uint16_t speed_y1;              ///<y1轴速度
    uint16_t position_y1;           ///<y1轴位置
    uint16_t motor_error_code_y1;   ///<y1轴电机错误码
    uint16_t speed_y2;              ///<y2轴速度
    uint16_t position_y2;           ///<y2轴位置
    uint16_t motor_error_code_y2;   ///<y2轴电机错误码

    uint16_t target_position_z1;         ///z1轴目标位置
    uint16_t curr_position_z1;           ///z1轴当前位置
    uint8_t action_state_z1;            ///<z1轴当前任务状态，0x00 空闲/原点、0x01伸、0x02勾、0x03送，0x04回，0x0F 异常
    uint16_t motor_error_code_z1;        ///<Z1轴电机错误码
    uint16_t target_position_z2;         ///z2轴目标位置
    uint16_t curr_position_z2;           ///z2轴当前位置
    uint8_t action_state_z2;            ///<z2轴当前任务状态，0x00 空闲/原点、0x01伸、0x02勾、0x03送，0x04回，0x0F 异常
    uint16_t motor_error_code_z2;        ///<Z2轴电机错误码
    uint16_t target_position_z3;         ///z3轴目标位置
    uint16_t curr_position_z3;           ///z3轴当前位置
    uint8_t action_state_z3;            ///<z3轴当前任务状态，0x00 空闲/原点、0x01伸、0x02勾、0x03送，0x04回，0x0F 异常
    uint16_t motor_error_code_z3;        ///<z3轴电机错误码
    int32_t mileage;                    ///<行驶里程, 旋转编码器值计算所得
    int32_t mileage_reference;          ///<行驶里程参考
    	
} dev_state_net;

//注册报文回复
typedef struct _dev_reg_ack
{
    uint16_t heartbeat_cycle;   ///心跳周期
    uint16_t resend_timeout;    ///回复超时时间
    uint16_t speed_x;           ///X向速度
    uint16_t acc_x;             ///X向加速度
    uint16_t dec_x;             ///x向减速度
    uint16_t speed_y;           ///y向速度
    uint16_t acc_y;             ///y向加速度
    uint16_t dec_y;             ///y向减速度
    uint16_t no_load_speed_z;   ///Z向空载速度
    uint16_t load_speed_z;      ///Z向带载速度
    uint16_t unlad_acc_z;       ///Z向空载加速度
    uint16_t load_acc_z;        ///Z向带载加速度
    uint32_t max_travel_x;     ///X向最大行程
    uint32_t max_travel_y;     ///y向最大行程
    uint32_t max_travel_z1;     ///z1向最大行程
    uint32_t max_travel_z3;     ///z3向最大行程
    uint32_t max_travel_negative_z3;     ///z3负向最大行程
    uint32_t box_disable_state;     ///货箱禁用状态
    int8_t find_zero_dir_x;         ///X寻零方向
    uint32_t time_stamp;            ///当前系统时间戳  
}dev_reg_ack;

// 设备运行参数回复结构体
typedef struct _dev_run_para
{
    uint16_t heartbeat_cycle;   ///心跳周期
    uint16_t resend_timeout;    ///回复超时时间
    uint8_t box_num;            ///货箱数量
    uint16_t speed_x;           ///X向速度
    uint16_t acc_x;             ///X向加速度
    uint16_t dec_x;             ///x向减速度
    uint16_t speed_y;           ///y向速度
    uint16_t acc_y;             ///y向加速度
    uint16_t dec_y;             ///y向减速度
    uint16_t no_load_speed_z;   ///Z向空载速度
    uint16_t load_speed_z;      ///Z向带载速度
    uint16_t acc_z;            ///Z向加速度
    uint32_t max_travel_x;     ///X向最大行程
    uint16_t max_travel_y;     ///y向最大行程
    uint16_t max_travel_z1;     ///z1向最大行程
    uint16_t max_travel_z3;     ///z3向最大行程
    uint16_t max_travel_negative_z3;     ///z3负向最大行程

}dev_run_para;


//指令下发结构体
typedef struct _swap_cmd_send
{
    int32_t load_pos_x;
    int16_t load_pos_y;
    uint32_t unload_pos_x;
    uint16_t unload_pos_y;
    uint16_t move_distance_z;
    uint16_t acc_x;
    uint16_t speed_x;
    uint16_t acc_y;
    uint16_t speed_y;
    uint16_t acc_load_z;
    uint16_t acc_unload_z;
    uint16_t speed_load_z;
    uint16_t speed_unload_z;

}swap_cmd_send;




#pragma pack(pop)




#endif