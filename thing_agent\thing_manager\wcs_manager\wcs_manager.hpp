#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/nlohmann_json/json.hpp"
#include "func_event.hpp"


class wcs_manager
{
public:

    inline static wcs_manager *get_instance()
    {
        static wcs_manager instance;
        return &instance;
    }

    int init(zmq::context_t &ctx);
    int run();

    std::unique_ptr<wcs_func_interface> func_accept();
    int evt_report(const std::string &data);



private:
    wcs_manager() {}
    ~wcs_manager() {}
    zmq::socket_t *func_recv;
    zmq::socket_t *evt_send;

    std::mutex event_mtx;
};

