/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "feeder_interface.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(feeder_cmd, feeder_cmd, AUTO)


PB_BIND(feeder_supply_state, feeder_supply_state, AUTO)


PB_BIND(belt_motor_single_state, belt_motor_single_state, AUTO)


PB_BIND(belt_sensor_single_state, belt_sensor_single_state, AUTO)


PB_BIND(feeder_belt_sensor_state_multiple, feeder_belt_sensor_state_multiple, AUTO)


PB_BIND(feeder_belt_motor_state_multiple, feeder_belt_motor_state_multiple, AUTO)


PB_BIND(scanner_state, scanner_state, AUTO)


PB_BIND(charger_state, charger_state, AUTO)


PB_BIND(feeder_dev_state_total, feeder_dev_state_total, 2)


PB_BIND(goods_info, goods_info, 2)













