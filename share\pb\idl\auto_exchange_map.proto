syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";

message auto_exchange_map
{
	enum calib_polarity_type
	{
		TYPE_RESERVE = 0; 	//无定义
		TYPE_POSTIVE = 1; 	//正向
		TYPE_NEGATIVE =2;	//负向
		TYPE_LEFT = 3;
		TYPE_RIGHT = 4;
	};

	message pack_station_dev_info
	{
		uint32 id = 1;
		float dev_pos_x = 2;
		float dev_pos_y = 3;			
		float dev_height = 4;
		bool valid_flag = 5;
		int32 curr_active_device = 6;
	};
	
	message pack_station_map
	{
		uint32 dev_cnt = 1;
		repeated pack_station_dev_info pack_station_info = 2 [(nanopb).max_count=60];
	}
	
	enum calib_point_type
	{
		CALIB_POINT_ORDINARY = 0;
		CALIB_POINT_ORIGIN = 1;
	};
	
	message auto_exchange_map_para
	{
		uint32 id = 1;
		uint32 dev_width = 2;
		uint32 dev_height = 3;
		uint32 grab_dis = 4;
		uint32 unload_dis = 5;
		uint32 unload_negative_dis = 6;
		uint32 bind_slot_sect_cnt = 7;
		uint32 bind_slot_min = 8;
		uint32 bind_slot_max = 9;
		int32 zero_inner_pos = 10;
		int32 zero_global_pos = 11;
		uint32 calib_polarity = 12;
		int32 calib_len = 13;
		uint32 bind_pack_staion_no = 14;
		pack_station_dev_info bind_pack_station_info = 15;
	};
	
	
	float total_length = 1;
	float total_height = 2;
	
	int32 dev_total_cnt = 3;
	repeated auto_exchange_map_para auto_exch_map = 4 [(nanopb).max_count = 32];
	
	int32 pack_station_total_cnt = 5;
	repeated pack_station_dev_info pack_stat_map = 6 [(nanopb).max_count = 32];
	
	
};



message auto_exchange_conv_para
{
	int32 x_axis_z1_offset = 1;
	int32 y_axis_z1_offset = 2;
	int32 x_axis_z2_offset = 3;
	int32 y_axis_z2_offset = 4;
	int32 x_axis_z3_offset = 5;
	int32 y_axis_z3_offset = 6;
}