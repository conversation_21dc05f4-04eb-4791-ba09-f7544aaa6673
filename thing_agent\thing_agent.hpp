#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>
#include <stdio.h>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "thing_manager/thing_manager.hpp"
#include "thing_interface/thing_interface.hpp"
#include "setting/setting.hpp"

#include "jd_thingtalk/cJSON/cJSON.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_protocol.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_proto_internal.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk_internal.h"

#include "jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_time.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_string.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_memory.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_log.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_thread.h"

class thing_agent
{
public:

    int init(jd_thingtalk_sdk_t **my_sdk, zmq::context_t &context);

    int run();

    static thing_agent *get_instance(void)
    {
        static thing_agent instance;
        return &instance;
    }

private:

    int init_sdk(jd_thingtalk_sdk_t **my_sdk);

};
