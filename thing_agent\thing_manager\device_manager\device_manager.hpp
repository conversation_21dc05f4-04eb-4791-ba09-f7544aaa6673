#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/task.pb.h"
// #include "share/pb/idl/feeder_goods.pb.h"
#include "share/global_def.h"

#include "share/pb/idl/sys_interface.pb.h"
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"

#include "setting/setting.hpp"
#include "ipc_interface/container_interface.hpp"
#include "ipc_interface/feeder_interface.hpp"
// #include "ipc_interface/vehicle_interface.hpp"

#include "ipc_interface/plane_interface.hpp"

// #include "share/pb/idl/sys_state.pb.h"

#include "thing_manager/sys_manager/sys_manager.hpp"
#include "exception/dev_except.hpp"
#include "exception/exception_list.hpp"
#include "device.hpp"

#define DEVICE_INIT_TIME_LIMIT              30000
#define DEVICE_HEARTBEAT_TIME_LIMIT         15000
#define DEVICE_WORK_STATUS_SET_START        1
#define DEVICE_WORK_STATUS_SET_STOP         0

class timer
{
private:
    std::chrono::high_resolution_clock::time_point start_time;   //定时查询用
    std::chrono::high_resolution_clock::time_point end_time;

public:
    void start()
    {
        start_time = std::chrono::high_resolution_clock::now();
    }

    uint32_t execute_time()
    {
        end_time = std::chrono::high_resolution_clock::now();
        std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        return interval.count();
    }
};

class device_manager
{
public:

    int init(zmq::context_t &ctx);
    int run();
    bool is_device_init_done();
    bool is_device_idle();
    // bool is_emgbt_triggered();

    static device_manager *get_instance(void)
    {
        static device_manager instance;
        return &instance;
    }

//格口相关
    int get_container_rfid(box_info_multiple &sts, bool get_all);
    int get_container_satr(std::vector<slot_state> &sts, bool get_all);
    bool is_container_full(const uint32_t &id);
    int get_container_list(std::vector<uint32_t> &con_list);
    int get_hospice_con_id();
    int issue_container_contain(const uint32_t &id);
    int issue_thingtalk_container_state(container_seal_state_single &state);
    int issue_lamp_state(led_info &color);
    int issue_container_lamp_test_start();
    int issue_container_lamp_test_stop();

    void set_report_seal_state_function(const std::function<int (const container_seal_state_single &state)> &func)
    {
        report_container_seal_state = func;
    }

//供包台相关

    int get_button_state(const int &feeder_id, std::map<key_id, key_evt_type> &state);
    int get_feeder_state(const int &feeder_id, feeder_dev_state_total &state);
    bool get_feeder_belt_state_changed(const int &feeder_id);
    bool set_feeder_belt_state_changed(const int &feeder_id,bool value);        
    int get_feeder_ids(std::vector<int> &ids);

    int issue_feeder_belt_cmd(uint32_t &feeder_id, uint32_t &dev_id, int32_t &speed);
    int issue_feeder_scan(uint32_t &feeder_id);
    void set_sys_state_key_stop();          //用于最高级别异常恢复时状态按键置位

    // bool is_emerg();
    bool is_feeder_sys_key_start();
    bool is_feeder_sys_key_stop();

    int issue_work_status_set(int &status);
    int issue_exception_handle_set(uint32_t &value);

    int issue_control_feeder_rotate_belt_forward(uint32_t &feeder_id, uint32_t speed, uint32_t limit);
    int issue_control_feeder_rotate_belt_backward(uint32_t &feeder_id, uint32_t speed, uint32_t limit);
    int issue_control_feeder_rotate_belt_stop(uint32_t &feeder_id, uint32_t speed, uint32_t limit);

//分播架相关

    // int get_switcher_state(const int &id, switch_state_single &state);
    // int get_switcher_state(std::unordered_map<int, switch_state_single> &states);
    // int get_sfdr_state(door_evt_type &state);
    // int issue_switcher_open(int &id);
    // int issue_switcher_close(int &id);
    // int issue_switcher_zero_set(int &id);
    int issue_buzzer_on();
    int issue_buzzer_off();
    int issue_shelf_lock(uint32_t shelf_no);
    int issue_shelf_unlock(uint32_t shelf_no);

    int issue_hmi_lamp_on();
    int issue_hmi_lamp_off();

//车辆相关

    struct vehicle_total_state
    {
        // vehicle_basic_info info;
        vehicle::vehicle_running_state running_state;
    };

    int get_vehicle_state(std::unordered_map<int, vehicle_total_state> &st);
    int register_vehicle(uint32_t &id);
    int get_vehicle_state(std::unordered_map<int, vehicle_total_state> &st, uint32_t &id);
    int unregister_vehicle(uint32_t &id);
    int reset_vehicle(uint32_t &id);
    int issue_vehicle_to_feeder(uint32_t &vehicle_id, uint32_t level_speed, uint32_t turn_speed, int feeder_id = -1);
    int issue_vehicle_to_grayscale_camera(uint32_t &vehicle_id, uint32_t camera_id, uint32_t level_speed, uint32_t turn_speed);
    int issue_vehicle_to_slot(uint32_t &vehicle_id, uint32_t slot_id, uint32_t level_speed, uint32_t turn_speed);
    // int issue_vehicle_to_unregister_point(uint32_t &vehicle_id);
    // int issue_vehicle_target_position(uint32_t &vehicle_id, int32_t position_x, int32_t position_y, int32_t position_z);
    int issue_vehicle_move_forward(uint32_t &vehicle_id, uint32_t &length, uint32_t level_speed, uint32_t turn_speed);
    int issue_vehicle_belt_forward_control(uint32_t &vehicle_id, uint32_t &carriage_id);
    int issue_vehicle_belt_backward_control(uint32_t &vehicle_id, uint32_t &carriage_id);
    int issue_vehicle_belt_stop(uint32_t &vehicle_id, uint32_t &carriage_id);

    int issue_platform_command_reset_control(std::string &dev_id);
    int issue_platform_command_enable_control(std::string &dev_id);
    int issue_platform_command_disable_control(std::string &dev_id);

    int issue_carriage_control_y_move(std::string &dev_id, uint16_t distance, int dir, uint16_t speed);
    int issue_carriage_control_y_zero_calibration(std::string &dev_id);
    int issue_carriage_control_belt_rotate(std::string &dev_id, uint16_t distance, uint16_t speed, int dir);
    int issue_carriage_control_belt_zeor_calibration(std::string &dev_id);
    int issue_carriage_control_dump_truck_rotate(std::string &dev_id);
    int issue_carriage_control_dump_truck_zero_calibration(std::string &dev_id);
    

private:

    timer feeder_state_timer;
    timer plane_state_timer;
    timer vehicle_state_timer;
    timer vehicle_ext_state_timer;
    exception_list exceptions;

    std::thread *sub_sys_state_thread;
    void detect_sub_sys_state();
    int run_detection();

    int report_dev_heartbeat_overtime(const except_info &exp_info, exception_state st);

//格口相关
    struct container_dev
    {
        container con;
        bool rfid_changed = false;
        bool satr_changed = false;

        container_dev(container _c): con(_c) {};
        container_dev() {};
    };

    std::mutex container_mutex;
    std::map<uint32_t, container_dev> con_devs;
    int hospice_con_id;

    int init_container();
    int run_container();
    int create_containers();
    void update_con_rfid_state();
    void update_con_satr_state();
    void update_con_seal_state();

    std::thread *container_rfid_thread;
    std::thread *container_satr_thread;
    std::thread *container_seal_thread;

    std::function<int (const container_seal_state_single &state)> report_container_seal_state;

    bool is_container_testing = false;
    std::thread *container_test_thread;     //持续测试时，单独开线程
    void test_container_lamp();

//供包台相关

    std::vector<feeder> feeder_devs;
    bool is_feeder_sys_heartbeat_error = false;
    std::mutex feeder_mutex;

    int init_feeder();
    int run_feeder();
    void update_fdr_button();
    void set_sys_key_state(feeder *fdr, key_event &key);
    void update_fdr_state();
    int get_feeder_sys_state(component_state &st);
    feeder *get_feeder(const uint32_t &id);
    void set_sys_state_key_start();

    std::thread *feeder_state_thread;
    std::thread *feeder_button_thread;

//分播架相关

    plane plane_dev;
    // fsm_state plane_sys_state = fsm_state_UNKNOW;
    bool is_plane_sys_heartbeat_error = false;

    std::mutex plane_mutex;

    int init_plane();
    int run_plane();
    // void update_sw_state();
    // void update_emerg_dev_state();
    // int get_plane_sys_state(fsm_state &st);

    // std::thread *switcher_state_thread;
    // std::thread *safty_door_thread;

//车辆相关
    std::unordered_map<int, vehicle> vehicle_devs;
    component_state vehicle_sys_state = component_state_C_UNKNOW;
    bool is_vehicle_sys_heartbeat_error = false;
    bool is_scheduler_heartbeat_error = false;

    std::mutex vehicle_mutex;

    int create_vehicles();
    int init_vehicle();
    int run_vehicle();
    void update_vh_state();
    void update_vh_ext_state();
    void update_vh_sys_state();
    int get_vehicle_sys_state(component_state &st);

    std::thread *vehicle_state_thread;
    std::thread *vehicle_ext_state_thread;
    std::thread *vehicle_agent_state_thread;
};
