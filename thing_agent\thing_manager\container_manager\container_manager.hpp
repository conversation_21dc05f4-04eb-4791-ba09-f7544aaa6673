#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/nlohmann_json/json.hpp"
#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/container_interface.pb.h"
#include "share/pb/idl/ack.pb.h"
// #include "share/pb/idl/sys_cmd.pb.h"
// #include "share/pb/idl/plane_slot.pb.h"
#include "setting/setting.hpp"

class container_manager
{
public:

    inline static container_manager *get_instance()
    {
        static container_manager instance;
        return &instance;
    }

    int init(zmq::context_t &ctx);

    int seal_evt_report(const container_seal_state_single &st);
    int slot_evt_report(const slot_state &st);


private:
    container_manager() {}
    ~container_manager() {}
    zmq::socket_t *seal_evt_send;
    zmq::socket_t *slot_evt_send;

    std::mutex seal_event_mtx;
    std::mutex slot_event_mtx;
};

