#include "sys_manager.hpp"
#include <spdlog/spdlog.h>

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/exception_code.hpp"
#include "share/event_code.hpp"

int sys_manager::init(zmq::context_t &ctx)
{
	sys_state_interface::get_instance()->init(ctx);
	exception_interface::get_instance()->init(ctx);

	event_vector::event_handler handler;

	handler.e = event_vector::EVT_PATTERN_ALL;
	handler.func = std::bind(&sys_manager::on_exception, this, std::placeholders::_1);	//h.func(evt_except);
	handler.priority = 1;
	vec.add_handler(handler);

	state_issue_timer.start();

	return 0;
}


bool sys_manager::is_sys_state_changed()
{
	if (sys_state_changed_thing == true)
	{
		sys_state_changed_thing = false;
		sys_state_changed_falut_thing = false;
		return true;
	}
	if(sys_state_changed_falut_thing == true)
	{
		sys_state_changed_falut_thing = false;
		return true;
	}
	else
		return false;
}

int sys_manager::get_sys_state(sys_mode_state &st)
{
	st = sys_st;
	return 0;
}

int sys_manager::set_manual_mode()
{
	return sys_state_interface::get_instance()->issue_set_manual_mode();
}

int sys_manager::set_auto_mode()
{
	return sys_state_interface::get_instance()->issue_set_auto_mode();
}

int sys_manager::issue_mcu_reboot()
{
	return sys_state_interface::get_instance()->issue_mcu_reboot();
}

int sys_manager::issue_program_restart()
{
	return sys_state_interface::get_instance()->issue_mcu_reset();
}


int sys_manager::issue_vehicle_start_demo()
{
    return sys_state_interface::get_instance()->issue_vehicle_start_moving();
}

int sys_manager::issue_vehicle_stop_demo()
{
    return sys_state_interface::get_instance()->issue_vehicle_stop_moving();
}

int sys_manager::remove_vehicle_exception(const uint32_t &id)
{
	// e_errstate old_err = sys_st.err;

	{
		std::lock_guard<std::mutex> lock(error_list_lock);
		for (auto err = fatal_exception_list.begin(); err != fatal_exception_list.end();)
		{
			if (err->src == exception_src_TRAIN && err->dev == id && err->state == exception_state_STATE_OCCURED)
			{
				SPDLOG_DEBUG("vehicle {} unregister, remove fatal exception", id);
				err = fatal_exception_list.erase(err);
				if (fatal_exception_list.empty())
					device_manager::get_instance()->set_sys_state_key_stop();
			}
			else
				++err;
		}
		for (auto err = error_exception_list.begin(); err != error_exception_list.end();)
		{
			if (err->src == exception_src_TRAIN && err->dev == id && err->state == exception_state_STATE_OCCURED)
			{
				SPDLOG_DEBUG("vehicle {} unregister, remove error exception", id);
				err = error_exception_list.erase(err);
			}
			else
				++err;
		}
		for (auto err = warning_exception_list.begin(); err != warning_exception_list.end();)
		{
			if (err->src == exception_src_TRAIN && err->dev == id && err->state == exception_state_STATE_OCCURED)
			{
				SPDLOG_DEBUG("vehicle {} unregister, remove warning exception", id);
				err = warning_exception_list.erase(err);
			}
			else
				++err;
		}
	}

	// update_err_state();
	// if(sys_st.err != old_err)
	// {
	// 	SPDLOG_DEBUG("vehicle {} unregister, error state changed: {}->{}", id, old_err, sys_st.err);
	// }

	return 0;

}

static int error_list_add(std::list<except_info> &list, const except_info &e)
{
	for (const auto &except : list)
	{
		if ((e.src == except.src) && (e.dev == except.dev) && (e.sub_dev == except.sub_dev) && (e.code == except.code) && (e.sub_code == except.sub_code))
		{
			return 0;
		}
	}

	list.push_back(e);
	return 1;
}

static int error_list_del(std::list<except_info> &list, const except_info &e)
{
	int cnt = 0;

	auto f = [&](const except_info except) 
	{
		if ((e.src == except.src) && (e.dev == except.dev) && (e.sub_dev == except.sub_dev) && (e.code == except.code) && (e.sub_code == except.sub_code))
		{
			cnt++;
			return true;
		}
		else return false;
	};

	list.remove_if(f);

	return cnt;
}

/*维护全局异常状态*/
int sys_manager::on_exception(const event_exception &e)
{
	if(e.which_evt_except != event_exception_except_tag)
		return 0;

	// e_errstate old_err = sys_st.err;
	//更新异常列表
	{
		std::lock_guard<std::mutex> lock(error_list_lock);
		switch(e.evt_except.except.level)
		{
			case exception_level_WARNNING:
				if (e.evt_except.except.state == exception_state_STATE_OCCURED)
					error_list_add(warning_exception_list, e.evt_except.except);
				else 
					error_list_del(warning_exception_list, e.evt_except.except);
				SPDLOG_DEBUG("warn exception remain {}", warning_exception_list.size());
				break;

			case exception_level_ERROR:
				if (e.evt_except.except.state == exception_state_STATE_OCCURED)
					error_list_add(error_exception_list, e.evt_except.except);
				else 
					error_list_del(error_exception_list, e.evt_except.except);
				SPDLOG_DEBUG("error exception remain {}", error_exception_list.size());
				break;

			case exception_level_FATAL:
				if (e.evt_except.except.state == exception_state_STATE_OCCURED)
					error_list_add(fatal_exception_list, e.evt_except.except);
				else 
					error_list_del(fatal_exception_list, e.evt_except.except);
				if (fatal_exception_list.size() == 0)
					device_manager::get_instance()->set_sys_state_key_stop();
				SPDLOG_DEBUG("fatal exception remain {}", fatal_exception_list.size());
				break;
			/*不需要做什么，只是为了消除编译警告*/
			default:
				SPDLOG_ERROR("exception level error");
				break;
		}
	}

	//更新全局异常状态
	// update_err_state();
	// if(sys_st.err != old_err)
	// {
	// 	sys_state_changed_falut_thing = true;
	// 	SPDLOG_INFO("error state changed: {}->{}", old_err, sys_st.err);
	// }
    std::unique_lock<std::mutex> l(except_queue_mutex);
    except_report_queue.emplace(e.evt_except.except);
	except_cv.notify_one();	//todo: sys_manager::get_instance()->get_exception(exception);

	return 0;
}

int sys_manager::get_exception(except_info &e)
{
	std::unique_lock<std::mutex> l(except_queue_mutex);
	while (except_report_queue.empty())
	{
		except_cv.wait(l);
	}

	e = except_report_queue.front();
	except_report_queue.pop();

	return except_report_queue.size();
}

int sys_manager::update_err_state()
{
	std::lock_guard<std::mutex> lock(error_list_lock);
	if(!fatal_exception_list.empty()) 	
	{
		sys_st.err = e_errstate_GLOBAL_EMERG_ERROR;
		sys_st.error_code = fatal_exception_list.back().code;
	}
	else if(!error_exception_list.empty())
	{
		sys_st.err = e_errstate_GLOBAL_ERROR;
		sys_st.error_code = error_exception_list.back().code;
	}
	else if(!warning_exception_list.empty())
	{
		sys_st.err = e_errstate_LOCAL_ERROR;
		sys_st.error_code = warning_exception_list.back().code;
	}
	else
	{
		sys_st.err = e_errstate_NOERROR;
	}

	return 0;
}

void sys_manager::exception_thread_manage()
{
	while (true)
	{
		event_exception evt_expt;
		if (exception_interface::get_instance()->get_event_exception(evt_expt))
			vec.process_event(evt_expt);
	}
}

void sys_manager::state_thread_manage()
{
	SPDLOG_INFO("state manager thread run...");

    while (true)
    {
		auto old_state = sys_st.state;
		if(sys_state_interface::get_instance()->issue_sys_state(sys_st))
		{
			if (old_state != sys_st.state)
			{
				sys_state_changed_thing = true;
				SPDLOG_DEBUG("sys_state changed:{}->{}", old_state, sys_st.state);
			}
		}

		SPDLOG_INFO("sys state mode:{}-{}", sys_st.mode, sys_st.state);

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}


int sys_manager::run()
{
	state_manage = new std::thread(&sys_manager::state_thread_manage, this);

	except_manage = new std::thread(&sys_manager::exception_thread_manage, this);

    return 0;
}
