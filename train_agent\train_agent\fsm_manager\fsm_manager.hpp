﻿
/**@file  	   device_manage.hpp
 * @brief       本播墙调度系统的设备管理接口文件
 * @details     NULL
 * <AUTHOR>
 * @date        2022-02-14
 * @version     v1.0.0
 * @copyright   Copyright (c) 2050
 **********************************************************************************
 * @attention
 * 主程序版本：v1.1.0
 * @par 修改日志:
 * <table>
 * <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
 * <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
 * </table>
 *
 **********************************************************************************
 */

#ifndef __FSM_MANAGER_HPP__
#define __FSM_MANAGER_HPP__

#include "../threadpool/blocking_queue.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/sys_interface.pb.h"


#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <mutex>
#include <thread>

using namespace std;







/**
 * @brief 基于unordered_map设计的车辆动态管理模块
 */
class fsm_manager
{
public:
	/**@brief  vehicle_list_map class构造函数
	 * @param[in]  NULL
	 * @return	  NULL
	 */
	explicit fsm_manager(zmq::context_t &context);

	/**@brief  vehicle_list_map class析构函数
	 * @param[in]  NULL
	 * @return	  NULL
	 */
	~fsm_manager();

	void fsm_manager_init(void) ;


	void fsm_manager_run(void);


	void fsm_manager_main_thread(void);

	bool fsm_manager_get_fsm_state(void);

	e_wkstate fsm_manager_get_fsm(void);

	sys_mode_state fsm_manager_get_sys_state(void);

	bool fsm_manager_get_exce_run_flag(void);



private:

	zmq::socket_t m_dev_fsm_subscriber; 	///< 地图数据获取socket REQ 类型

	bool m_mode_log_switch;
		
	std::thread *m_dev_state_get_thread;

	e_wkstate m_dev_fsm;

	std::mutex m_dev_sys_state_mutex;
	sys_mode_state m_dev_sys_state;

	bool m_dev_excp_run_flag;

};



#endif