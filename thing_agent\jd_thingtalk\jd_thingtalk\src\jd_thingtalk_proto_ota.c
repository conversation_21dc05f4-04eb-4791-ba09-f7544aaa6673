/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 ota 服务 相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   ota 状态参数实例名
 *
 */
#define JD_THINGTALK_PROTO_OTA_ST_ARG_STATE      ("ota-state")
#define JD_THINGTALK_PROTO_OTA_ST_ARG_ERR_CODE   ("error-code")
#define JD_THINGTALK_PROTO_OTA_ST_ARG_PROGRESS   ("progress")

/**
 * @brief   ota 创建ota方法调用响应
 *
 * @param[in] func_key: 方法调用的实例名
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_ota_create_func_res(char *func_key)
{
    JDThingTalkProtoFuncCallResFunc_t *out = NULL;
    // int32_t ii;

    // 创建响应的输出
    out = (JDThingTalkProtoFuncCallResFunc_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoFuncCallResFunc_t));
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoFuncCallResFunc_t));
    if (out->key == NULL) {
        out->key = (char *) jd_thingtalk_pal_malloc(
                (jd_thingtalk_pal_strlen(func_key) + 1) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->key, func_key);

    return out;
}

/**
 * @brief   ota 获取ota状态字符串
 *
 * @param[in] state: 当前状态
 * @param[in] out_value: 对应的字符串值
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_proto_ota_get_state_str(int32_t state, char *out_value)
{
    char *str_state = out_value;
    switch (state)
    {
    case JD_THINGTALK_OTA_ST_PREPARE:
        jd_thingtalk_pal_strcpy(str_state, "1");
        break;
    case JD_THINGTALK_OTA_ST_CF_DOWNLOAD:
        jd_thingtalk_pal_strcpy(str_state, "2");
        break;
    case JD_THINGTALK_OTA_ST_DOWNLOADIND:
        jd_thingtalk_pal_strcpy(str_state, "3");
        break;
    case JD_THINGTALK_OTA_ST_DOWNLOAD_DONE:
        jd_thingtalk_pal_strcpy(str_state, "4");
        break;
    case JD_THINGTALK_OTA_ST_CF_INSTALL:
        jd_thingtalk_pal_strcpy(str_state, "5");
        break;
    case JD_THINGTALK_OTA_ST_INSTALLING:
        jd_thingtalk_pal_strcpy(str_state, "6");
        break;
    case JD_THINGTALK_OTA_ST_INSTALL_DONE:
        jd_thingtalk_pal_strcpy(str_state, "99");
        break;
    case JD_THINGTALK_OTA_ST_FAILURE:
        jd_thingtalk_pal_strcpy(str_state, "-1");
        break;
    default:
        log_error("unsupport current state type: %d", state);
        break;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   ota 获取ota错误码字符串
 *
 * @param[in] e_code: 错误码
 * @param[in] out_value: 对应的字符串值
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_proto_ota_get_error_code_str(int32_t e_code, char *out_value)
{
    char *str_state = out_value;
    switch (e_code)
    {
    case JD_THINGTALK_OTA_ERROR_NONE:
        jd_thingtalk_pal_strcpy(str_state, "0");
        break;
    case JD_THINGTALK_OTA_ERROR_TASK_CL:
        jd_thingtalk_pal_strcpy(str_state, "1001");
        break;
    case JD_THINGTALK_OTA_ERROR_DOWNLOAD_CL:
        jd_thingtalk_pal_strcpy(str_state, "2001");
        break;
    case JD_THINGTALK_OTA_ERROR_INSTALL_CL:
        jd_thingtalk_pal_strcpy(str_state, "2002");
        break;
    case JD_THINGTALK_OTA_ERROR_DPG_VER_FAIL:
        jd_thingtalk_pal_strcpy(str_state, "3001");
        break;
    case JD_THINGTALK_OTA_ERROR_DOWNLOAD_FAIL:
        jd_thingtalk_pal_strcpy(str_state, "3002");
        break;
    case JD_THINGTALK_OTA_ERROR_SIG_VER_FAIL:
        jd_thingtalk_pal_strcpy(str_state, "3003");
        break;
    case JD_THINGTALK_OTA_ERROR_DPG_RVT_FAIL:
        jd_thingtalk_pal_strcpy(str_state, "3004");
    case JD_THINGTALK_OTA_ERROR_INSTALL_FAIL:
        jd_thingtalk_pal_strcpy(str_state, "3005");
        break;
    default:
        log_error("unsupport current error code type: %d", e_code);
        break;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   ota 创建ota方法调用 状态查询响应
 *
 * @param[in] func_key: 方法调用的实例名
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_ota_create_func_query_res(char *func_key,
                                                             char *task_id,
                                                             int32_t state,
                                                             JD_THINGTALK_PROTO_OTA_ERROR_T e_code,
                                                             int8_t progress)
{
    JDThingTalkProtoFuncCallResFunc_t *out = NULL;
    if (task_id == NULL || func_key == NULL) {
        log_error("task_id:%s or func_key: %s is Empty!!", task_id, func_key);
        return NULL;
    }
    int32_t ii;

    // 创建响应的输出
    out = (JDThingTalkProtoFuncCallResFunc_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoFuncCallResFunc_t));
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoFuncCallResFunc_t));
    if (out->key == NULL) {
        out->key = (char *) jd_thingtalk_pal_malloc(
                (jd_thingtalk_pal_strlen(func_key) + 1) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->key, func_key);

    // 出参
    out->out_num = 4;
    out->out = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
                out->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out->out, 0, out->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (ii = 0; ii < out->out_num; ii++) {
        out->out[ii] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
        jd_thingtalk_pal_memset(out->out[ii], 0, sizeof(JDThingTalkProtoKeyValue_t));
    }

    // task-id
    out->out[0]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_TASK_ID) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->out[0]->key, JD_THINGTALK_PROTO_OTA_TASK_ID);
    out->out[0]->value = jd_thingtalk_proto_keyvalue_pack_string(task_id);

    // current-state
    out->out[1]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_STATE) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->out[1]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_STATE);
    char str_state[16];
    jd_thingtalk_pal_memset(str_state, 0, 16 * sizeof(char));
    jd_thingtalk_proto_ota_get_state_str(state, str_state);
    out->out[1]->value = jd_thingtalk_proto_keyvalue_pack_string(str_state);

    // progress
    out->out[2]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_PROGRESS) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->out[2]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_PROGRESS);
    out->out[2]->value = jd_thingtalk_proto_keyvalue_pack_int32((int32_t)progress);

    // error code
    out->out[3]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_ERR_CODE) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->out[3]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_ERR_CODE);
    jd_thingtalk_pal_memset(str_state, 0, 16 * sizeof(char));
    jd_thingtalk_proto_ota_get_error_code_str(e_code, str_state);
    out->out[3]->value = jd_thingtalk_proto_keyvalue_pack_string(str_state);

    return out;
}

/**
 * @brief   ota 创建当前状态
 *
 * @param[in] evt_key: 事件的实例名的实例名
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_ota_create_current_state(char *evt_key,
                                                             char *task_id,
                                                             int32_t state,
                                                             int32_t e_code,
                                                             int8_t progress)
{
    JDThingTalkProtoEvtPostEvt_t *out = NULL;
    int32_t ii;

    out = (JDThingTalkProtoEvtPostEvt_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoEvtPostEvt_t));
    if (out == NULL) {
        log_error("malloc memory for JDThingTalkProtoEvtPostEvt_t* failed!!");
        return NULL;
    }
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoEvtPostEvt_t));
    out->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(evt_key) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->key, evt_key);

    // 添加 parameters
    out->param_num = 4;
    out->parameters = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out->parameters, 0, out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (ii = 0; ii < out->param_num; ii++) {
        out->parameters[ii] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
        jd_thingtalk_pal_memset(out->parameters[ii], 0, sizeof(JDThingTalkProtoKeyValue_t));
    }

    // task-id
    out->parameters[0]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_TASK_ID) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[0]->key, JD_THINGTALK_PROTO_OTA_TASK_ID);
    out->parameters[0]->value = jd_thingtalk_proto_keyvalue_pack_string(task_id);

    // current-state
    out->parameters[1]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_STATE) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[1]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_STATE);
    char str_state[16];
    jd_thingtalk_pal_memset(str_state, 0, 16 * sizeof(char));
    jd_thingtalk_proto_ota_get_state_str(state, str_state);
    out->parameters[1]->value = jd_thingtalk_proto_keyvalue_pack_string(str_state);

    // error code
    out->parameters[2]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_ERR_CODE) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[2]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_ERR_CODE);
    jd_thingtalk_pal_memset(str_state, 0, 16 * sizeof(char));
    jd_thingtalk_proto_ota_get_error_code_str(e_code, str_state);
    out->parameters[2]->value = jd_thingtalk_proto_keyvalue_pack_string(str_state);

    // progress
    out->parameters[3]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_ST_ARG_PROGRESS) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[3]->key, JD_THINGTALK_PROTO_OTA_ST_ARG_PROGRESS);
    out->parameters[3]->value = jd_thingtalk_proto_keyvalue_pack_int32((int32_t)progress);

    return out;
}

#define JD_THINGTALK_PROTO_OTA_VER_ARG_VER       JD_THINGTALK_PROTO_OTA_VERSION
/**
 * @brief   ota 创建当前版本号事件
 *
 * @param[in] evt_key: 事件的实例名的实例名
 * @param[in] version: 当前升级包版本
 * @param[in] object_id: 子设备的设备标识，可为空，表示自身的
 * @param[in] type: 升级对象类型
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_ota_create_evet_version(char *evt_key, char *version, char *object_id, char *type)
{
    JDThingTalkProtoEvtPostEvt_t *out = NULL;
    int32_t ii;

    out = (JDThingTalkProtoEvtPostEvt_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoEvtPostEvt_t));
    if (out == NULL) {
        log_error("malloc memory for JDThingTalkProtoEvtPostEvt_t* failed!!");
        return NULL;
    }
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoEvtPostEvt_t));
    out->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(evt_key) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->key, evt_key);

    // 添加 parameters
    if (object_id == NULL) {
        out->param_num = 2;
    } else {
        out->param_num = 3;
    }
    out->parameters = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out->parameters, 0, out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (ii = 0; ii < out->param_num; ii++) {
        out->parameters[ii] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
        jd_thingtalk_pal_memset(out->parameters[ii], 0, sizeof(JDThingTalkProtoKeyValue_t));
    }

    // 升级包版本号 version
    out->parameters[0]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_VER_ARG_VER) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[0]->key, JD_THINGTALK_PROTO_OTA_VER_ARG_VER);
    out->parameters[0]->value = jd_thingtalk_proto_keyvalue_pack_string(version);

    int idx = 1;

    // 子设备标识
    if (object_id != NULL) {
        out->parameters[1]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_OBJECT_ID) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(out->parameters[1]->key, JD_THINGTALK_PROTO_OTA_OBJECT_ID);
        out->parameters[1]->value = jd_thingtalk_proto_keyvalue_pack_string(object_id);
        idx ++;
    }

    // 升级对象类型
    out->parameters[idx]->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(JD_THINGTALK_PROTO_OTA_OBJECT_TYPE) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(out->parameters[idx]->key, JD_THINGTALK_PROTO_OTA_OBJECT_TYPE);
    out->parameters[idx]->value = jd_thingtalk_proto_keyvalue_pack_string(type);

    return out;
}

/**
 * @brief   ota 键值对 打包 ota-list 信息
 *
 * @param[in] in_list: ota-list 数组
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_ota_list(JDThingTalkProtoOtaList_t *in_list)
{
    if(NULL == in_list) {
        return NULL;
    }

    if ((in_list->list_len == 0) || (in_list->members == NULL)) {
        return NULL;
    }

    int32_t ii;
    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateArray();
    if(NULL == root){
        goto RET;
    }

    cJSON   *member = NULL;
    for (ii = 0; ii < in_list->list_len; ii ++) {
        if (in_list->members[ii] == NULL) {
            continue;
        }
        member = cJSON_CreateObject();
        if (member == NULL) {
            cJSON_Delete(root);
            goto RET;
        }
        // 添加 OTA 包名 (package-name)
        if (in_list->members[ii]->package_name != NULL) {
            cJSON_AddStringToObject(member, JD_THINGTALK_PROTO_OTA_PKG_NAME, in_list->members[ii]->package_name);
        } else {
            cJSON_AddStringToObject(member, JD_THINGTALK_PROTO_OTA_PKG_NAME, "");
        }

        // 添加 ota 升级包版本号
        cJSON_AddNumberToObject(member, JD_THINGTALK_PROTO_OTA_VERSION, in_list->members[ii]->version);

        // 添加 ota 差分基包版本号
        cJSON_AddNumberToObject(member, JD_THINGTALK_PROTO_OTA_DIFF_BASE_VER, in_list->members[ii]->diff_base_version);

        // 添加 成员
        cJSON_AddItemToArray(root, member);
    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

// end of file
