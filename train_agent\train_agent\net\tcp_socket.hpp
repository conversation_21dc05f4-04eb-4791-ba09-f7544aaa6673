﻿
/**@file  tcp_socket.hpp
* @brief       基于TCP的socket操作软件二次封装对应头文件
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持TCP服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* </table>
*
**********************************************************************************
*/




#ifndef __NET_TCP_SOCKET_HPP__
#define __NET_TCP_SOCKET_HPP__


#include <string>
#include <netinet/ip.h>

#include <iostream>


/**
* @brief 实现TCP Socket通信的二次封装，将常用的socket操作封装于tcp_socket 类中
*/

class tcp_socket
{
	

#define TCP_SOCKET_MIN_PORT	 (1000)
#define TCP_SOCKET_MAX_PORT	 (10000)

#define is_TCP_SOCKET_PORT_VALID(port)				( (port>=TCP_SOCKET_MIN_PORT)&&(port<=TCP_SOCKET_MAX_PORT) )

public:

	/**@brief  tcp_socket class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
    explicit tcp_socket();

	/**@brief  tcp_socket class析构函数，调用时关闭tcp服务器的文件描述符
	* @param[in]  NULL
	* @return	  NULL
	*/
    ~tcp_socket();

	
	/**@brief	  TCP 通信中 Server 构造
	* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool tcp_socket_init(int maxWaiter);

	/**@brief	  TCP 通信中 Server 构造
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool tcp_socket_init(void);
	

	/**@brief	  TCP 通信中 Server 地址及端口绑定
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server绑定失败
	* - true	  server绑定成功
	*/
	bool tcp_socket_bind(void);
	

	/**@brief	  向TCP Server分配IP地址及端口号
	* @param[in]  const std::string &ip --- 服务器IP地址
	* @param[in]  int port --- 服务器端口号
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_server_cfg(const std::string &ip, int _port) ;


	/**@brief	  设置TCP 服务器 TCP_NODELAY特性
	* @param[in]  bool option --- TCP_NODELAY开启操作
	* @ref			true  设置TCP NODELAY，禁能nagle算法
	* @ref			false 使能nagle算法
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_tcp_no_delay(bool option)  ;


	/**@brief	  设置TCP 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启地址 reuse
	* @ref			false 禁止地址 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_reuseaddr(bool option)  ;

	
	/**@brief	  设置TCP 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启端口 reuse
	* @ref			false 禁止端口 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_reuseport(bool option)  ;

	
	/**@brief	  设置TCP 服务器 KeepAlive特性
	* @param[in]  bool option --- keepalive特性开启操作
	* @ref			true  开启TCP keepalive
	* @ref			false 关闭TCP keepalive
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_keep_alive(bool option);	

	
	/**@brief	  将服务器套接字转换为被动套接字，状态转换为LISTEN，等待client连接
	* @param[in]  int listener --- 该套接字排队的最大连接数量
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_listen_on(int listener);	
	
	
	/**@brief	  将服务器套接字转换为被动套接字，状态转换为LISTEN，等待client连接
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_listen_on(void);

	
	/**@brief	  设置TCP 通信 quick ACK特性
	* @param[in]  NULl
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_set_quick_ack(void);


	/**@brief	  获取TCP 服务器端口号
	* @param[in]  NULl
	* @return	  TCP 服务器端口号
	*/	
	unsigned int tcp_socket_get_port() const ;	
	

	/**@brief	  设置 套接字 与指定IP地址/端口的连接
	* @param[in]  NULl
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool tcp_socket_connect_server(void) ;	


	/**@brief	  响应客户端连接
	* @param[out]  struct sockaddr_in *clnt_addr  --- 客户端 IP地址
	* @return	  client 描述符
	*/
	int tcp_socket_accept_client( struct sockaddr_in *clnt_addr );

	
	/**@brief	  响应客户端连接
	* @param[in]  int server_fd  ---  TCP 服务器 描述符
	* @param[out] struct sockaddr_in *clnt_addr  --- 客户端 IP地址
	* @return	  client 描述符
	*/
	int tcp_socket_accept_client(int server_fd,  struct sockaddr_in *clnt_addr );

	
	/**@brief	  close 套接字
	* @param[in]  int fd
	* @return	  函数执行结果
	* - false	  失败
	* - true	  成功
	*/	
	bool tcp_socket_close(int fd );

	
	/**@brief	  设置描述符为非阻塞形式
	* @param[in]  int server_fd  ---  待操作描述符
	* @return	   函数执行结果
	* - false	  失败
	* - true	  成功
	*/
	bool tcp_socket_set_nonblocking(int sock_fd);

	bool tcp_socket_set_so_linger(int sock_fd, bool option, uint32_t timeout);


	/**@brief	  获取当前服务器 描述符
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int tcp_socket_get_fd() const 
	{
		return m_server_sockfd; 
	}

	


private:


	int m_server_sockfd;	 ///< Server套接字描述符
	int m_client_sockfd;     ///< Client套接字描述符

    struct sockaddr_in m_serv_addr;  ///< Server IP 地址及端口结构体


	int m_default_processor;  ///< 

	int m_default_listener;  ///< 默认最大连接数
			
};


#endif
