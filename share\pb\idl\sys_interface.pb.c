/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "sys_interface.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(dev_state, dev_state, AUTO)


PB_BIND(sys_mode_state, sys_mode_state, AUTO)


PB_BIND(train_move_para, train_move_para, AUTO)


PB_BIND(dev_move_para, dev_move_para, AUTO)


PB_BIND(manual_cmd, manual_cmd, AUTO)


PB_BIND(set_state, set_state, AUTO)


PB_BIND(task_cmd, task_cmd, AUTO)


PB_BIND(reset_exception, reset_exception, AUTO)


PB_BIND(sys_cmd, sys_cmd, AUTO)












