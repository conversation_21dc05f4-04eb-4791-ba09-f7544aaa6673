/usr/bin/g++    -m64 -std=c++11 -pthread    CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o CMakeFiles/autoswap_agent.dir/readme.c.o CMakeFiles/autoswap_agent.dir/main.cpp.o CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o  -o autoswap_agent  -L/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/libs/x86/lib -lspdlog -lzmq nanopb_binary_dir/libnanopb.a idl_binary_dir/libidl.a net/liblib_net.a threadpool/liblib_threadpool.a scheduler_msg/liblib_msg.a protocol/liblib_protocol.a swap_manage/liblib_swap_manage.a fsm_manager/liblib_fsm_manager.a -lspdlog -lzmq -Wl,-rpath,/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/libs/x86/lib 
