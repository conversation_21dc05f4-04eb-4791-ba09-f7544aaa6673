#pragma once

#ifndef __MULTI_TRAIN_MANAGER_HPP__
#define __MULTI_TRAIN_MANAGER_HPP__

#include "net/udp_socket.hpp"
#include "net/epoll_poller.hpp"
#include "threadpool/thread_pool.hpp"
#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include "train_manage/train_list.hpp"

#include "fsm_manager/fsm_manager.hpp"
#include "share/nlohmann_json/json.hpp"
#include "share/pb/idl/train_interface.pb.h"
#include <fstream>
#include "scheduler_msg/scheduler_msg.hpp"


using namespace std;

#define MILLION											( 1000000 )
#define START_UP_HEART_BEAT_TIME_OUT					( 60 * 1000 * 1000  )	//60秒




class udp_client_socket;
class udp_server_socket;

typedef struct _server_info_t
{
	std::string server_addr;
	int server_port;
	int client_port;

}server_info_t;

typedef struct _train_info_conf
{
	uint8_t train_id;
    uint8_t carriage_num;
	uint8_t platform_type;

}train_info_conf;

typedef struct _train_agent_cfg
{
	server_info_t server_info; 

	int heartbeat_timeout;
	uint16_t heartbeat_cycle;
    uint16_t resend_timeout;
	uint8_t proximity_sensors_tolerant_num;		//接近传感器容错数量

	uint32_t map_total_length;
	uint8_t map_dev_calib_point_cnt;
	map_calib_point_info map_calib_points[20];

    uint16_t walk_motor_speed;
    uint16_t walk_motor_acc;
    uint16_t walk_motor_dec;
    uint16_t carriage_motor_speed;
    uint16_t carriage_motor_acc;
    uint16_t carriage_motor_dec;
    uint16_t belt_motor_speed;
    uint16_t belt_zero_speed;
    uint16_t belt_motor_acc;
    uint16_t belt_rotation_distance;
	uint16_t carriage_max_travel;			//载货台Y轴最大行程
    uint16_t unpack_exceed_threshold;		//下包超限阈值
    uint8_t unpack_sensor_switch;
	bool m_dev_reset_force_send_flag;	//允许车辆重启信号在安全门打开时发送

	// uint8_t train_count;
	// train_info_conf train_info[20];
	train_basic_info_mutilp train_cfg_info;

} train_agent_cfg;


typedef struct _train_list_info
{
	int train_id;
	bool train_state;	//车辆在线状态

}train_list_info;

typedef struct _train_list
{
	int train_count;
	train_list_info train_info[20];

}train_list;


class multi_train_manager
:protected epoll_poller
,public std::enable_shared_from_this<multi_train_manager>
{

public:

    /**@brief	  multi_train_manager class构造函数，在构造列表里构造ZMQ socket
	* @param[in]  zmq::context_t &context ZMQ创建的上下文
	* @return	  NULL
	*/
	explicit multi_train_manager(zmq::context_t &context);

    ~multi_train_manager();

    void multi_train_manager_run();
    void multi_train_manager_init(train_agent_cfg *cfg);
	void multi_train_manager_config(train_agent_cfg *cfg);
	void train_agent_get_config_from_data_map(train_agent_cfg *cfg, ack_map_info &map_info);
	void mileage_info_write_to_cfg_file(void);
	void get_rep_train_para(train_config_para *rep_config_para);

	/**@brief     查找指定对象是否在本地列表中
	* @param[in]  int dev_id --- 指定设备ID
	* @return     NULL 
	*/
	bool multi_train_manager_train_list_find(int dev_id);


    /**@brief	  multi_train_manager 对UDP服务器，epoll监听，zmq消息等进行初始化
	* @param[in]  NULL
	* @return	  NULL
	*/
    void udp_net_init();


    /**@brief	  epoll监听过程中，新接入节点处理
	* @param[in]  int fd --- client 文件描述符
	* @return	  NULL 
	*/
	void epoll_newConnection(int fd) override;

	
	/**@brief	  epoll监听过程中，已有接入节点消息处理---从线程池中获取空闲线程执行操作
	* @param[in]  int fd --- client 文件描述符
	* @return	  NULL 
	*/
	void epoll_existConnection(int fd) override;


	/**@brief	  epoll监听主循环
	* @param[in]  int timeout	   ---	 epoll时间监听的超时等待时间
	* @return	  NULL 
	*/
	int epoll_main_loop(int timeout) override;


	/**@brief	  车辆消息处理函数，主要实现车辆端消息的接收、解码及解析执行
	* @param[in]  int fd --- client 文件描述符
	* @return	  NULL 
	*/
	void multi_train_dev_ctrl_func(int fd);

	//处理多车辆管理中的设备重置任务
	void multi_train_manager_task_dev_reset_gen(void);


	void multi_train_reset_dev_add(int dev);


	//车辆任务管理线程，根据调度任务，生成网络数据下发
	void multi_train_manager_task_new_gen_thread_exe();

	
	/**@brief	  multi_train_manager 车辆心跳管理线程
	* @param[in]  NULL 
	* @return	  NULL 
	*/
	void multi_train_manager_heart_beat_thread_exe();


	/**@brief	  multi_train_manager 网络数据下发线程
	* @param[in]  NULL 
	* @return	  NULL 
	*/
	void multi_train_manager_net_msg_thread_exe();


	/**@brief	  multi_train_manager 网络数据重发线程
	* @param[in]  NULL 
	* @return	  NULL 
	*/
	void multi_train_manager_task_resend_thread_exe();


	/**@brief	  multi_train_manager 车辆信息汇总线程
	* @param[in]  NULL 
	* @return	  NULL 
	*/
	void multi_train_manager_train_basic_info_summary();

	void multi_train_manager_reply_train_info_thread();

	int scheduler_manager_reply_train_info(void); 


	//定时下发心跳报文
	void multi_train_manager_hb_time_sync_exe();

	//下行心跳数据生成
	void multi_train_manager_downlink_heartbeat_msg(void);

	bool multi_train_reset_dev_find(int dev);
	void multi_train_reset_dev_clear(int dev);

	bool train_agent_get_train_list(train_list &dev_info);
	void multi_train_manager_train_list_update(train_basic_info_mutilp list);

	void multi_train_manager_task_state_report_thread_exe(void);

	void multi_train_manager_mileage_info_statistic_thread(void);

	fsm_manager & get_fsm()
	{
		return m_dev_fsm;
	} 


private:

    udp_server_socket m_locol_server;
    udp_client_socket m_locol_reporter;
	train_basic_info_mutilp m_train_info;
	// train_task_state m_task_platform_state;
	// train_task_state m_task_carriage_state;
	
	thread_pool m_recv_worker;

	scheduler_manager m_scheduler_msg;

	zmq::socket_t m_train_info_replayer;        ///< 车代理消息响应socket REP 类型

	sys_mode_state m_dev_curr_state;
	fsm_manager m_dev_fsm;
	

	train_list_map m_train_list;


	struct timespec m_start_tick;

   	train_agent_cfg m_dev_cfg;
	std::string m_server_addr;
	int m_server_port;
	int m_client_port;

	uint32_t m_database_train_list[20];
	int m_database_train_cnt;			// 本地列表中车辆数量

	std::mutex m_train_mtx;           // 任务操作互斥锁
	std::mutex m_task_mtx;           // 任务操作互斥锁
	std::mutex m_reset_info_mtx;    // 任务操作互斥锁
	std::unordered_map<int, bool> m_train_reset_dev_map;	// 重启车辆列表 < devid -> 待重启标志true >

	std::mutex m_insert_lock;	// m_train_sock_list列表insert锁

	std::mutex m_delete_lock;

	blocking_queue<net_msg> m_net_msg_queue;	//回复UDP消息

	blocking_queue<train_task> m_dev_downlink_hb_msg;

	blocking_queue<train_task> m_dev_reset_msg;	//重启车辆列表

	struct timer
    {
        std::chrono::high_resolution_clock::time_point start_time;
        std::chrono::high_resolution_clock::time_point end_time;

        void start()
        {
            start_time = std::chrono::high_resolution_clock::now();
        }

        int execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

	timer carriage_task_report_timer[100];
	timer platform_task_report_timer[100];

	std::thread *net_msg_send_thread;	//网络数据发送线程
	std::thread *task_msg_resend_thread;	//任务数据重发线程
	std::thread *task_msg_new_thread;		//任务数据生成线程
	std::thread *hb_time_sync_thread;	//心跳检测线程
	std::thread *task_msg_hb_thread;		//任务数据生成线程
	std::thread *heart_beat_thread;		//心跳检测线程
	std::thread *dev_rst_msg_thread;		//任务数据生成线程
	std::thread *train_info_summary_thread;		//车辆信息汇总线程
	std::thread *train_info_reply_thread;		//车辆信息汇总reply线程
	std::thread *task_report_thread;
	std::thread *mileage_info_thread;
};

#endif