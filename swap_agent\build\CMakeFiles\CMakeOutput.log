The target system is: Linux -  - 
The host system is: Linux - 4.15.0-142-generic - x86_64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/g++ 
Build flags: ;-m64
Id flags: 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/3.5.1/CompilerIdCXX/a.out"

Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/gcc 
Build flags: 
Id flags: 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/3.5.1/CompilerIdC/a.out"

Determining if the CXX compiler works passed with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_cf669/fast"
/usr/bin/make -f CMakeFiles/cmTC_cf669.dir/build.make CMakeFiles/cmTC_cf669.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_cf669.dir/testCXXCompiler.cxx.o
/usr/bin/g++      -o CMakeFiles/cmTC_cf669.dir/testCXXCompiler.cxx.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_cf669
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cf669.dir/link.txt --verbose=1
/usr/bin/g++        CMakeFiles/cmTC_cf669.dir/testCXXCompiler.cxx.o  -o cmTC_cf669 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_4d12a/fast"
/usr/bin/make -f CMakeFiles/cmTC_4d12a.dir/build.make CMakeFiles/cmTC_4d12a.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/g++      -o CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.5/Modules/CMakeCXXCompilerABI.cpp
Linking CXX executable cmTC_4d12a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4d12a.dir/link.txt --verbose=1
/usr/bin/g++       -v CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_4d12a  
Using built-in specs.
COLLECT_GCC=/usr/bin/g++
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4d12a' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccF1X62h.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_4d12a /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_4d12a/fast"]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_4d12a.dir/build.make CMakeFiles/cmTC_4d12a.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/usr/bin/g++      -o CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.5/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Linking CXX executable cmTC_4d12a]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4d12a.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/g++       -v CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_4d12a  ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/g++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4d12a' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccF1X62h.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_4d12a /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccF1X62h.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--sysroot=/] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_4d12a] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..]
    arg [CMakeFiles/cmTC_4d12a.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtend.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  remove lib [gcc_s]
  remove lib [gcc]
  remove lib [gcc_s]
  remove lib [gcc]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5] ==> [/usr/lib/gcc/x86_64-linux-gnu/5]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> [/usr/lib]
  implicit libs: [stdc++;m;c]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/5;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []




Detecting CXX [-std=c++14] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_99cec/fast"
/usr/bin/make -f CMakeFiles/cmTC_99cec.dir/build.make CMakeFiles/cmTC_99cec.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_99cec.dir/feature_tests.cxx.o
/usr/bin/g++     -std=c++14 -o CMakeFiles/cmTC_99cec.dir/feature_tests.cxx.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_99cec
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_99cec.dir/link.txt --verbose=1
/usr/bin/g++        CMakeFiles/cmTC_99cec.dir/feature_tests.cxx.o  -o cmTC_99cec 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++11] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_41058/fast"
/usr/bin/make -f CMakeFiles/cmTC_41058.dir/build.make CMakeFiles/cmTC_41058.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_41058.dir/feature_tests.cxx.o
/usr/bin/g++     -std=c++11 -o CMakeFiles/cmTC_41058.dir/feature_tests.cxx.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_41058
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_41058.dir/link.txt --verbose=1
/usr/bin/g++        CMakeFiles/cmTC_41058.dir/feature_tests.cxx.o  -o cmTC_41058 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++98] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_1ad92/fast"
/usr/bin/make -f CMakeFiles/cmTC_1ad92.dir/build.make CMakeFiles/cmTC_1ad92.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_1ad92.dir/feature_tests.cxx.o
/usr/bin/g++     -std=c++98 -o CMakeFiles/cmTC_1ad92.dir/feature_tests.cxx.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_1ad92
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1ad92.dir/link.txt --verbose=1
/usr/bin/g++        CMakeFiles/cmTC_1ad92.dir/feature_tests.cxx.o  -o cmTC_1ad92 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:0cxx_alias_templates
    Feature record: CXX_FEATURE:0cxx_alignas
    Feature record: CXX_FEATURE:0cxx_alignof
    Feature record: CXX_FEATURE:0cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:0cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:0cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:0cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:0cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:0cxx_default_function_template_args
    Feature record: CXX_FEATURE:0cxx_defaulted_functions
    Feature record: CXX_FEATURE:0cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:0cxx_delegating_constructors
    Feature record: CXX_FEATURE:0cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:0cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:0cxx_explicit_conversions
    Feature record: CXX_FEATURE:0cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:0cxx_extern_templates
    Feature record: CXX_FEATURE:0cxx_final
    Feature record: CXX_FEATURE:0cxx_func_identifier
    Feature record: CXX_FEATURE:0cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:0cxx_inheriting_constructors
    Feature record: CXX_FEATURE:0cxx_inline_namespaces
    Feature record: CXX_FEATURE:0cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:0cxx_local_type_template_args
    Feature record: CXX_FEATURE:0cxx_long_long_type
    Feature record: CXX_FEATURE:0cxx_noexcept
    Feature record: CXX_FEATURE:0cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:0cxx_nullptr
    Feature record: CXX_FEATURE:0cxx_override
    Feature record: CXX_FEATURE:0cxx_range_for
    Feature record: CXX_FEATURE:0cxx_raw_string_literals
    Feature record: CXX_FEATURE:0cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:0cxx_right_angle_brackets
    Feature record: CXX_FEATURE:0cxx_rvalue_references
    Feature record: CXX_FEATURE:0cxx_sizeof_member
    Feature record: CXX_FEATURE:0cxx_static_assert
    Feature record: CXX_FEATURE:0cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:0cxx_thread_local
    Feature record: CXX_FEATURE:0cxx_trailing_return_types
    Feature record: CXX_FEATURE:0cxx_unicode_literals
    Feature record: CXX_FEATURE:0cxx_uniform_initialization
    Feature record: CXX_FEATURE:0cxx_unrestricted_unions
    Feature record: CXX_FEATURE:0cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:0cxx_variadic_macros
    Feature record: CXX_FEATURE:0cxx_variadic_templates
Determining if the C compiler works passed with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_bb65d/fast"
/usr/bin/make -f CMakeFiles/cmTC_bb65d.dir/build.make CMakeFiles/cmTC_bb65d.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_bb65d.dir/testCCompiler.c.o
/usr/bin/gcc     -o CMakeFiles/cmTC_bb65d.dir/testCCompiler.c.o   -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp/testCCompiler.c
Linking C executable cmTC_bb65d
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bb65d.dir/link.txt --verbose=1
/usr/bin/gcc       CMakeFiles/cmTC_bb65d.dir/testCCompiler.c.o  -o cmTC_bb65d 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_821c5/fast"
/usr/bin/make -f CMakeFiles/cmTC_821c5.dir/build.make CMakeFiles/cmTC_821c5.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o
/usr/bin/gcc     -o CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.5/Modules/CMakeCCompilerABI.c
Linking C executable cmTC_821c5
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_821c5.dir/link.txt --verbose=1
/usr/bin/gcc      -v CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o  -o cmTC_821c5  
Using built-in specs.
COLLECT_GCC=/usr/bin/gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_821c5' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccT4h6Kd.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_821c5 /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_821c5/fast"]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_821c5.dir/build.make CMakeFiles/cmTC_821c5.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o]
  ignore line: [/usr/bin/gcc     -o CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.5/Modules/CMakeCCompilerABI.c]
  ignore line: [Linking C executable cmTC_821c5]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_821c5.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/gcc      -v CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o  -o cmTC_821c5  ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/gcc]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_821c5' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccT4h6Kd.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_821c5 /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccT4h6Kd.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--sysroot=/] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_821c5] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..]
    arg [CMakeFiles/cmTC_821c5.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtend.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  remove lib [gcc]
  remove lib [gcc_s]
  remove lib [gcc]
  remove lib [gcc_s]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5] ==> [/usr/lib/gcc/x86_64-linux-gnu/5]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> [/usr/lib]
  implicit libs: [c]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/5;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []




Detecting C [-std=c11] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_500af/fast"
/usr/bin/make -f CMakeFiles/cmTC_500af.dir/build.make CMakeFiles/cmTC_500af.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_500af.dir/feature_tests.c.o
/usr/bin/gcc    -std=c11 -o CMakeFiles/cmTC_500af.dir/feature_tests.c.o   -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_500af
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_500af.dir/link.txt --verbose=1
/usr/bin/gcc       CMakeFiles/cmTC_500af.dir/feature_tests.c.o  -o cmTC_500af 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:1c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c99] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_1d8d4/fast"
/usr/bin/make -f CMakeFiles/cmTC_1d8d4.dir/build.make CMakeFiles/cmTC_1d8d4.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_1d8d4.dir/feature_tests.c.o
/usr/bin/gcc    -std=c99 -o CMakeFiles/cmTC_1d8d4.dir/feature_tests.c.o   -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_1d8d4
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1d8d4.dir/link.txt --verbose=1
/usr/bin/gcc       CMakeFiles/cmTC_1d8d4.dir/feature_tests.c.o  -o cmTC_1d8d4 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c90] compiler features compiled with the following output:
Change Dir: /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_a71dc/fast"
/usr/bin/make -f CMakeFiles/cmTC_a71dc.dir/build.make CMakeFiles/cmTC_a71dc.dir/build
make[1]: Entering directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_a71dc.dir/feature_tests.c.o
/usr/bin/gcc    -std=c90 -o CMakeFiles/cmTC_a71dc.dir/feature_tests.c.o   -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_a71dc
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a71dc.dir/link.txt --verbose=1
/usr/bin/gcc       CMakeFiles/cmTC_a71dc.dir/feature_tests.c.o  -o cmTC_a71dc 
make[1]: Leaving directory '/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:0c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:0c_variadic_macros
