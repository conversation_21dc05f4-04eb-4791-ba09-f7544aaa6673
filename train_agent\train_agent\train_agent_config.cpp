#include "train_agent_config.hpp"
#include "multi_train_manager.hpp"


using namespace std;


static spdlog::level::level_enum train_agent_log_level_convers(string &str_in)
{
	if( !strcmp(str_in.c_str(), LOG_LEVEL_TRACE) )
	{
		return spdlog::level::trace;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_DEBUG) )
	{
		return spdlog::level::debug;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_INFO) )
	{
		return spdlog::level::info;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_WARN) )
	{
		return spdlog::level::warn;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_ERR) )
	{
		return spdlog::level::err;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_OFF) )
	{
		return spdlog::level::off;
	}
	else
	{
		return spdlog::level::off;
	}
}

typedef struct _log_cfg_locol
{
	string log_level;
}log_cfg_locol;

#if 0
void from_json(const nlohmann::json& j, train_agent_cfg& v) 
{
	j.at("heartbeat_timeout").get_to(v.heartbeat_timeout);
	j.at("heartbeat_cycle").get_to(v.heartbeat_cycle);
	j.at("resend_timeout").get_to(v.resend_timeout);
	j.at("carriage_max_travel").get_to(v.carriage_max_travel);
	j.at("proximity_sensors_tolerant_num").get_to(v.proximity_sensors_tolerant_num);
	j.at("carriage_num").get_to(v.carriage_num);
	j.at("walk_motor_speed").get_to(v.walk_motor_speed);
	j.at("walk_motor_acc").get_to(v.walk_motor_acc);
	j.at("walk_motor_dec").get_to(v.walk_motor_dec);
	j.at("carriage_motor_speed").get_to(v.carriage_motor_speed);
	j.at("carriage_motor_acc").get_to(v.carriage_motor_acc);
	j.at("carriage_motor_dec").get_to(v.carriage_motor_dec);
	j.at("belt_motor_speed").get_to(v.belt_motor_speed);
	j.at("belt_zero_speed").get_to(v.belt_zero_speed);
	j.at("belt_motor_acc").get_to(v.belt_motor_acc);
	j.at("belt_rotation_distance").get_to(v.belt_rotation_distance);
	j.at("unpack_exceed_threshold").get_to(v.unpack_exceed_threshold);
	j.at("unpack_sensor_switch").get_to(v.unpack_sensor_switch);
	j.at("reset_force_send_flag").get_to(v.m_dev_reset_force_send_flag);
}
#endif

void from_json(const nlohmann::json& j, server_info_t & v) 
{
	j.at("server_addr").get_to(v.server_addr);
	j.at("server_port").get_to(v.server_port);
}

void from_json(const nlohmann::json& j, log_cfg_locol& v) 
{
	j.at("log_level").get_to(v.log_level);
}

bool train_agent_get_config(log_cfg *log_config, train_agent_cfg *dev_cfg)
{
	std::string file_name;

	std::string home_path = getenv("HOME");
	file_name = home_path + "/auto_sort_high_efficient/cfg_file/train_agent_config.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;

    nlohmann::json j;
    ifstream jfile(file_name.c_str());


#if 1
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());

		dev_cfg->server_info.server_addr = "0.0.0.0";
		dev_cfg->server_info.server_port = 9000;
		dev_cfg->server_info.client_port = 9001;
		log_config->log_level = spdlog::level::debug;
		
		return false;
	}
#endif


    log_cfg_locol log = j.at("log");
	std::cout << log.log_level << std::endl;

	server_info_t cfg = j.at("server");
	log_config->log_level = train_agent_log_level_convers(log.log_level);

	dev_cfg->server_info.server_addr = j["server"]["server_addr"];
	dev_cfg->server_info.server_port = j["server"]["server_port"];
	dev_cfg->server_info.client_port = j["server"]["client_port"];

	// dev_cfg->server_info.server_addr = cfg.server_addr;
	// dev_cfg->server_info.server_port = cfg.server_port;
	// std::cout << "server_addr: " << dev_cfg->server_info.server_addr << std::endl; 
	// std::cout << "server_port: " << dev_cfg->server_info.server_port << std::endl; 

    dev_cfg->heartbeat_timeout = j["config"]["heartbeat_timeout"];	//单位：秒
	dev_cfg->heartbeat_cycle = j["config"]["heartbeat_cycle"];
	dev_cfg->resend_timeout = j["config"]["resend_timeout"];
	dev_cfg->proximity_sensors_tolerant_num = j["config"]["proximity_sensors_tolerant_num"];
	dev_cfg->walk_motor_speed = j["config"]["walk_motor_speed"];
	dev_cfg->walk_motor_acc = j["config"]["walk_motor_acc"];
	dev_cfg->walk_motor_dec = j["config"]["walk_motor_dec"];
	dev_cfg->carriage_motor_speed = j["config"]["carriage_motor_speed"];
	dev_cfg->carriage_motor_acc = j["config"]["carriage_motor_acc"];
	dev_cfg->carriage_motor_dec = j["config"]["carriage_motor_dec"];
	dev_cfg->belt_motor_speed = j["config"]["belt_motor_speed"];
	dev_cfg->belt_zero_speed = j["config"]["belt_zero_speed"];
	dev_cfg->belt_motor_acc = j["config"]["belt_motor_acc"];
	dev_cfg->belt_rotation_distance = j["config"]["belt_rotation_distance"];
	dev_cfg->unpack_exceed_threshold = j["config"]["unpack_exceed_threshold"];
	dev_cfg->unpack_sensor_switch = j["config"]["unpack_sensor_switch"];
    dev_cfg->m_dev_reset_force_send_flag = j["config"]["reset_force_send_flag"];

	return true;
}


