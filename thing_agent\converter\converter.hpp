#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>
#include <stdlib.h>
#include <condition_variable>
#include <unordered_map>
#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "jd_thingtalk/cJSON/cJSON.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_protocol.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_proto_internal.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk_internal.h"

#include "thing_manager/device_manager/device_manager.hpp"
#include "thing_manager/sys_manager/sys_manager.hpp"
#include "thing_manager/task_manager/task_manager.hpp"



//指令回复
#define FUNC_RESPONSE_SUCCESS                                           1
#define FUNC_RESPONSE_FAIL                                              0

#define FUNC_RESPONSE_KEY                                               ("response")


//主体方法
#define FUNC_MODE_SWITCH_MODE_MANUAL                                    0
#define FUNC_MODE_SWITCH_MODE_AUTO                                      1
#define FUNC_MODE_SWITCH_MODE_SEMI_AUTO                                 2
#define FUNC_MODE_SWITCH_SUB_MODE_DEMONSTRATE                           0

#define FUNC_PALLET_TO_GRID_NOTIFY_TASK_TYPE_NORMAL                     0
#define FUNC_PALLET_TO_GRID_NOTIFY_TASK_TYPE_DISCLOSED                  1
#define FUNC_PALLET_TO_GRID_NOTIFY_TASK_TYPE_SIMULATIVE                 2

#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_EXTINCT                     0
#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_YELLOW                      1
#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_GREEN                       2
#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_RED                         3
#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_BLUE                        4
#define FUNC_GRID_LAMP_STATUS_NOTIFY_STATUS_PURPLE                      5

#define FUNC_GRID_LAMP_STATUS_NOTIFY_BLINK_FREQUENCY_0                  0
#define FUNC_GRID_LAMP_STATUS_NOTIFY_BLINK_FREQUENCY_50                 1


#define FUNC_SORTS_CONTINUATION_RUN_SORTS_MODE_STOP                     0
#define FUNC_SORTS_CONTINUATION_RUN_SORTS_MODE_START                    1

#define FUNC_PILOT_LAMP_STATUS_NOTIFY_STATUS_OFF                        0
#define FUNC_PILOT_LAMP_STATUS_NOTIFY_STATUS_ON                         1

#define FUNC_BUZZER_CONTROL_STATUS_OFF                                  0
#define FUNC_BUZZER_CONTROL_STATUS_ON                                   1

#define FUNC_SHELF_CONTROL_ACTION_LOCK                                  0
#define FUNC_SHELF_CONTROL_ACTION_UNLOCK                                1

#define FUNC_GRID_IN_ORDER_CONTROL_STATUS_OFF                           0
#define FUNC_GRID_IN_ORDER_CONTROL_STATUS_ON                            1

#define FUNC_AUTO_CODE_SCAN_MODE_OFF                                    0
#define FUNC_AUTO_CODE_SCAN_MODE_ON                                     1


#define FUNC_KEY_MODE_SWITCH                                            ("autorebin.mode-switch")
#define FUNC_MODE_SWITCH_KEY_MODE                                       ("mode")
#define FUNC_MODE_SWITCH_KEY_SUB_MODE                                   ("sub-mode")

#define FUNC_KEY_GRID_LAMP_STATUS_NOTIFY                                ("autorebin.grid-lamp-status-notify")
#define FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_GRID_LAMP_STATUS_INFO          ("grid-lamp-status-info")
#define FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_GRID_NO                        ("grid-no")
#define FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_STATUS                         ("status")
#define FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_BLINK_FREQUENCY                ("blink-frequency")

#define FUNC_KEY_GRID_SEAL_STATUS_NOTIFY                                ("autorebin.grid-sub-status-notify")
#define FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_GRID_LAMP_STATUS_INFO            ("grid-sub-status-info")
#define FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_GRID_NO                          ("grid-no")
#define FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_STATUS                           ("status")

#define FUNC_KEY_PALLET_TO_GRID_NOTIFY                                  ("autorebin.pallet-to-grid-notify")
#define FUNC_PALLET_TO_GRID_NOTIFY_KEY_GRID_NO                          ("grid-no")
#define FUNC_PALLET_TO_GRID_NOTIFY_KEY_TASK_TYPE                        ("task-type")
#define FUNC_PALLET_TO_GRID_NOTIFY_KEY_TASK_NO                          ("task-no")
#define FUNC_PALLET_TO_GRID_NOTIFY_KEY_FEEDER_NO                        ("feeder-no")
#define FUNC_PALLET_TO_GRID_NOTIFY_KEY_ROTATE_SPEED                     ("rotate-speed")

#define FUNC_KEY_ACKNOWLEDGE                                            ("autorebin.acknowledge")
#define FUNC_ACKNOWLEDGE_KEY_EVENT_NAME                                 ("event-name")
#define FUNC_ACKNOWLEDGE_KEY_MESSAGE_ID                                 ("message-id")

#define FUNC_KEY_SORTS_CONTINUATION_RUN                                 ("autorebin.sorts-continuation-run")
#define FUNC_SORTS_CONTINUATION_RUN_KEY_SORTS_MODE                      ("sorts-mode")

#define FUNC_KEY_PILOT_LAMP_STATUS_NOTIFY                               ("autorebin.pilot-lamp-status-notify")
#define FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_PILOT_LAMP_STATUS_INFO        ("pilot-lamp-status-info")
#define FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_PILOT_NO                      ("pilot-no")
#define FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_STATUS                        ("status")

#define FUNC_KEY_BUZZER_CONTROL                                         ("autorebin.buzzer-control")
#define FUNC_BUZZER_CONTROL_KEY_BUZZER_INFO                             ("buzzer-info")
#define FUNC_BUZZER_CONTROL_KEY_BUZZER_NO                               ("buzzer-no")
#define FUNC_BUZZER_CONTROL_KEY_BUZZER_STATUS                           ("status")

#define FUNC_KEY_GRID_IN_ORDER_CONTROL                                  ("autorebin.grid-in-order-control")
#define FUNC_GRID_IN_ORDER_CONTROL_KEY_STATUS                           ("status")

#define FUNC_KEY_AUTO_CODE_SCAN                                         ("autorebin.auto-code-scan")
#define FUNC_AUTO_CODE_SCAN_KEY_CODE_SCANNER_INFO                       ("code-scanner-info")
#define FUNC_AUTO_CODE_SCAN_KEY_CODE_SCAN_NO                            ("scan-no")
#define FUNC_AUTO_CODE_SCAN_KEY_MODE                                    ("mode")


//同步方法
#define WORK_STATUS_SET_START                                           1
#define WORK_STATUS_SET_STOP                                            0
#define FUNC_WORK_STATUS_SET                                            ("autorebin.work-status-set")
#define FUNC_WORK_STATUS_SET_VALUE                                      ("status")

#define FUNC_CONFIG_SET                                                 ("autorebin.config-set")
#define FUNC_ONFIG_SET_KEY_CONFIGS                                      ("configs")
#define FUNC_ONFIG_SET_KEY_NAME                                         ("name")
#define FUNC_ONFIG_SET_KEY_VALUE                                        ("value")
#define FUNC_ONFIG_SET_KEY_RESULT                                       ("result")

#define FUNC_CONFIG_QUERY                                               ("autorebin.config-get")
#define FUNC_CONFIG_QUERY_KEY_CONFIGS                                   ("configs")
#define FUNC_CONFIG_QUERY_KEY_NAMES                                     ("name")
#define FUNC_CONFIG_QUERY_KEY_VALUE                                     ("value")
#define FUNC_CONFIG_QUERY_KEY_TYPE                                      ("FEEDER_EXCEP_MODE")


//货架锁控制
#define FUNC_SHELF_LOCK_CONTROL                                         ("autorebin.shelf-lock-control")
#define FUNC_SHELF_LOCK_CONTROL_KEY_SHELF_LOCK_INFO                     ("shelf-lock-info")
#define FUNC_SHELF_LOCK_CONTROL_KEY_SEGMENT_NO                          ("segment-no")
#define FUNC_SHELF_LOCK_CONTROL_KEY_ACTION                              ("action")


#define FUNC_FEEDER_BELT_STATUS_QUERY                                   ("autorebin.feeder-belt-status-get")
#define FUNC_FEEDER_BELT_STATUS_KEY_FEEDER_NO                           ("feeder-no")
#define FUNC_FEEDER_BELT_STATUS_KEY_BELTS                               ("belts")
#define FUNC_FEEDER_BELT_STATUS_KEY_BELT_INFO_NO                        ("belt-line-no")
#define FUNC_FEEDER_BELT_STATUS_KEY_STATUS                              ("status")
#define FUNC_FEEDER_BELT_STATUS_KEY_SEQUENCE                            ("sequence")
#define FUNC_FEEDER_BELT_STATUS_KEY_BELT_NO                             ("1")

#define FUNC_FEEDER_BELT_STATUS_KEY_STATUS_AVAILABLE                    ("1")
#define FUNC_FEEDER_BELT_STATUS_KEY_STATUS_NO_AVAIL                     ("2")
#define FUNC_FEEDER_BELT_STATUS_KEY_STATUS_EXCEPTION                    ("3")

#define FUNC_COMMAND_DISPATCH_ACTION_ERSET                              0
#define FUNC_COMMAND_DISPATCH_ACTION_ERSET                              0

//sort方法
#define FUNC_COMMAND_DISPATCH_ACTION_ERSET                              0
#define FUNC_COMMAND_DISPATCH_ACTION_STANDBY                            1
#define FUNC_COMMAND_DISPATCH_ACTION_REGISTER                           2
#define FUNC_COMMAND_DISPATCH_ACTION_UNREGISTER                         3

#define FUNC_CONTROL_DISPATCH_TARGET_CONTAINER                          0
#define FUNC_CONTROL_DISPATCH_TARGET_STAGING                            1
#define FUNC_CONTROL_DISPATCH_TARGET_FEEDER                             2
#define FUNC_CONTROL_DISPATCH_TARGET_UNREGISTER_POINT                   3

#define FUNC_DIRECTION_DISPATCH_DIRECTION_HIGHEST                       0
#define FUNC_DIRECTION_DISPATCH_DIRECTION_MINIMUM                       1
#define FUNC_DIRECTION_DISPATCH_DIRECTION_MIDDLE                        2
#define FUNC_DIRECTION_DISPATCH_DIRECTION_BACKWARD                      3
#define FUNC_DIRECTION_DISPATCH_DIRECTION_FORWARD                       4
#define FUNC_DIRECTION_DISPATCH_DIRECTION_STOP                          5
#define FUNC_DIRECTION_DISPATCH_DIRECTION_SPECIFIED_POS                 6

#define FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_RESET                       0
#define FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_STANDBY                     1
#define FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_ONLINE                      2
#define FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_OFFLINE                     3

#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_Y_MOVE                      0
#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_Y_ZERO_CALIBRATION          1
#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_BELT_ROTATE                 2
#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_BELT_ZERO_CALIBRATION       3
#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_DUMP_TRUCK_ROTATE           4
#define FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_DUMP_TRUCK__ZERO_CALIBRATION       5


//分播车方法
#define FUNC_KEY_COMMAND_DISPATCH                                       ("autorebinsort.command-dispatch")
#define FUNC_COMMAND_DISPATCH_KEY_SORT_NO                               ("sort-no")
#define FUNC_COMMAND_DISPATCH_KEY_ACTION                                ("action")

#define FUNC_KEY_CONTROL_DISPATCH                                       ("autorebinsort.control-dispatch")
#define FUNC_CONTROl_DISPATCH_KEY_SORT_NO                               ("sort-no")
#define FUNC_CONTROL_DISPATCH_KEY_TARGET_ID                             ("target-id")
#define FUNC_CONTROL_DISPATCH_KEY_TARGET                                ("target")

#define FUNC_KEY_CONTROL_DEST_DISPATCH                                  ("autorebinsort.control-dest-dispatch")
#define FUNC_CONTROL_DEST_DISPATCH_KEY_SORT_NO                          ("sort-no")
#define FUNC_CONTROL_DEST_DISPATCH_KEY_DEST_LOCATION                    ("dest-location")
#define FUNC_CONTROL_DEST_DISPATCH_KEY_X                                ("x")
#define FUNC_CONTROL_DEST_DISPATCH_KEY_Y                                ("y")
#define FUNC_CONTROL_DEST_DISPATCH_KEY_Z                                ("z")

#define FUNC_KEY_CONTROL_WALK_DISPATCH                                  ("autorebinsort.control-walk-dispatch")
#define FUNC_CONTROL_WALK_KEY_SORT_NO                                   ("sort-no")
#define FUNC_CONTROL_WALK_KEY_DISTANCE                                  ("distance")

#define FUNC_KEY_BELT_DIRECTION_DISPATCH                                ("autorebinsort.belt-direction-dispatch")
#define FUNC_BELT_DIRECTION_DISPATCH_KEY_SORT_NO                        ("sort-no")
#define FUNC_BELT_DIRECTION_DISPATCH_KEY_DIRECTION                      ("direction")
#define FUNC_BELT_DIRECTION_DISPATCH_KEY_BELT_NO                        ("belt-no")

//载台命令下发
#define FUNC_KEY_PLATFORM_COMMAND_DISPATCH                              ("autorebinsortgroup.platform-command-dispatch")
#define FUNC_PLATFORM_COMMAND_DISPATCH_PLATFORM_NO                      ("platform-no")
#define FUNC_PLATFORM_COMMAND_DISPATCH_ACTION                           ("action")

//载台控制下发
#define FUNC_KEY_PLATFORM_CONTROL_DISPATCH                              ("autorebinsortgroup.platform-control-dispatch")
#define FUNC_PLATFORM_CONTROL_DISPATCH_PLATFORM_NO                      ("platform-no")
#define FUNC_PLATFORM_CONTROL_DISPATCH_ACTION                           ("action")
#define FUNC_PLATFORM_CONTROL_DISPATCH_DIRECTION                        ("direction")
#define FUNC_PLATFORM_CONTROL_DISPATCH_SPEED                            ("speed")
#define FUNC_PLATFORM_CONTROL_DISPATCH_LIMIT                            ("limit")





//分播车组方法
#define FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_ERSET                    0
#define FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_STANDBY                  1
#define FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_REGISTER                 2
#define FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_UNREGISTER               3

#define FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_FEEDER                   0
#define FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_CONTAINER                1
#define FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_GRAYSCALE_CAMERA         2
#define FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_INCREMENTAL_WALK         3

//命令下发
#define FUNC_KEY_SORTGROUP_COMMAND_DISPATCH                             ("autorebinsortgroup.command-dispatch")
#define FUNC_COMMAND_DISPATCH_KEY_SORTGROUP_NO                          ("sort-group-no")
#define FUNC_COMMAND_DISPATCH_KEY_SORTGROUP_ACTION                      ("action")

//行走控制下发
#define FUNC_KEY_SORTGROUP_CONTROL_WALK_DISPATCH                        ("autorebinsortgroup.control-walk-dispatch")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_SORTGROUP_NO                     ("sort-group-no")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_TARGET_ID                        ("target-id")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_TARGET                           ("target")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_LEVEL_SPEED                      ("level-speed")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_TURN_SPEED                       ("turn-speed")
#define FUNC_CONTROL_WALK_DISPATCH_KEY_LIMIT                            ("limit")

//车厢命令下发
#define FUNC_SORTGROUP_SORT_COMMAND_KEY_ACTION_CALIBRATION              0
#define FUNC_KEY_SORTGROUP_SORT_COMMAND_DISPATCH                        ("autorebinsortgroup.sort-command-dispatch")
#define FUNC_SORTGROUP_SORT_COMMAND_DISPATCH_SORT_NO                    ("sort-no")
#define FUNC_SORTGROUP_SORT_COMMAND_DISPATCH_ACTION                     ("action")


//feeder方法
#define FUNC_CONTROL_ROTATE_DIRECTION_KEY_BELT_FORWARD                  0
#define FUNC_CONTROL_ROTATE_DIRECTION_KEY_BELT_BACKWARD                 1
#define FUNC_BELT_DIRECTION_DISPATCH_DIRECTION_STOP                     2

#define FUNC_KEY_FEEDER_CONTROL_DISIPATCH                               ("autorebinfeeder.control-dispatch")
#define FUNC_CONTROL_DISPATHCH_KEY_FEEDER_NO                            ("feeder-no")
#define FUNC_CONTROL_DISPATHCH_KEY_BELTS_ARRAY                          ("belts-array")
#define FUNC_CONTROL_DISPATHCH_KEY_BELT_NO                              ("belt-no")
#define FUNC_CONTROL_DISPATHCH_KEY_SPEED                                ("speed")

#define FUNC_KEY_FEEDER_CONTROL_ROTATE_DISIPATCH                        ("autorebinfeeder.control-rotate-dispatch")
#define FUNC_CONTROL_ROTATE_DISPATHCH_KEY_FEEDER_NO                     ("feeder-no")
#define FUNC_CONTROL_ROTATE_DISPATHCH_KEY_DIR                           ("direction")
#define FUNC_CONTROL_ROTATE_DISPATHCH_KEY_SPEED                         ("speed")
#define FUNC_CONTROL_ROTATE_DISPATHCH_KEY_LIMIT                         ("limit")



//switcher方法
#define FUNC_EXECUTE_SWITCH_ACTION_OPEN                                 0
#define FUNC_EXECUTE_SWITCH_ACTION_CLOSE                                1

#define FUNC_KEY_EXECUTE_SWITCH                                         ("autorebinswitcher.execute-switch")
#define FUNC_EXECUTE_SWITCH_KEY_SWITCHER_NO                             ("switcher-no")
#define FUNC_EXECUTE_SWITCH_KEY_ACTION                                  ("action")

#define FUNC_KEY_ZERO_SET_DISPATCH                                      ("autorebinswitcher.zero-set-dispatch")
#define FUNC_ZERO_SET_DISPATCH_KEY_ORBITAL_TRANSFER_NO                  ("orbital-transfer-no")


//主控板方法
#define FUNC_MCU_CONTROL_DISPATCH_ACTION_REBOOT                         0
#define FUNC_MCU_CONTROL_DISPATCH_ACTION_RESET                          1
#define FUNC_MCU_KEY_CONTROL_DISPATCH                                   ("autorebinhardware.control-dispatch")
#define FUNC_MCU_CONTROL_DISPATCH_KEY_ACTION                            ("action")


//事件
//上报扫码信息
#define EVT_KEY_BARCODE_REPORT                                          ("autorebin.barcode-report")
#define EVT_BARCODE_REPORT_KEY_FEEDER_NO                                ("feeder-no")
#define EVT_BARCODE_REPORT_KEY_SCANER_NO                                ("scaner-no")
#define EVT_BARCODE_REPORT_KEY_TASK_NO                                  ("task-no")
#define EVT_BARCODE_REPORT_KEY_BARCODE_GROUP                            ("barcode-group")
#define EVT_BARCODE_REPORT_KEY_LENGTH                                   ("length")
#define EVT_BARCODE_REPORT_KEY_WIDTH                                    ("width")
#define EVT_BARCODE_REPORT_KEY_HEIGHT                                   ("height")
#define EVT_BARCODE_REPORT_KEY_WEIGHT                                   ("weight")
#define EVT_BARCODE_REPORT_KEY_QTY                                      ("qty")

//任务状态上报
#define EVT_TASK_STATUS_REPORT_TASK_TYPE_NORMAL_SORTING                 0
#define EVT_TASK_STATUS_REPORT_TASK_TYPE_DISCLOSE_SORTING               1
#define EVT_TASK_STATUS_REPORT_TASK_TYPE_SIMULATE_SORTING               2
#define EVT_TASK_STATUS_REPORT_TASK_STATUS_START                        0
#define EVT_TASK_STATUS_REPORT_TASK_STATUS_FINISH                       1

#define EVT_KEY_TASK_STATUS_REPORT                                      ("autorebin.task-status-report")
#define EVT_TASK_STATUS_REPORT_KEY_TASK_NO                              ("task-no")
#define EVT_TASK_STATUS_REPORT_KEY_TASK_TYPE                            ("task-type")
#define EVT_TASK_STATUS_REPORT_KEY_TASK_STATUS                          ("task-status")
#define EVT_TASK_STATUS_REPORT_KEY_GRID_NO                              ("grid-no")
#define EVT_TASK_STATUS_REPORT_KEY_SORT_NO                              ("sort-no")
#define EVT_TASK_STATUS_REPORT_KEY_ERROR_REASON                         ("error-reason")

//格口容器动作上报
#define EVT_GRID_CONTAINER_ACTION_REPORT_STATUS_UNBIND                  0
#define EVT_GRID_CONTAINER_ACTION_REPORT_STATUS_BIND                    1

#define EVT_KEY_GRID_CONTAINER_ACTION_REPORT                            ("autorebin.grid-container-action-report")
#define EVT_GRID_CONTAINER_ACTION_REPORT_KEY_GRID_CONTAINER_RELATION    ("grid-container-relation-array")
#define EVT_GRID_CONTAINER_ACTION_REPORT_GRID_NO                        ("grid-no")
#define EVT_GRID_CONTAINER_ACTION_REPORT_CONTAINER_NO                   ("container-no")
#define EVT_GRID_CONTAINER_ACTION_REPORT_STATUS                         ("status")


#define EVT_GRID_GROUP_ACTION_REPORT_STATUS_UNBIND                      0
#define EVT_GRID_GROUP_ACTION_REPORT_STATUS_BIND                        1

#define EVT_KEY_GRID_GROUP_CONTAINER_ACTION_REPORT                      ("autorebin.grid-group-container-action-report")
#define EVT_GRID_GROUP_ACTION_REPORT_KEY_GRID_GROUP_RELATION            ("grid-group-container-relation-array")
#define EVT_GRID_GROUP_ACTION_REPORT_GRID_GROUP_NO                      ("grid-group-no")
#define EVT_GRID_GROUP_ACTION_REPORT_SHELF_NO                           ("shelf-no")
#define EVT_GRID_GROUP_ACTION_REPORT_STATUS                             ("status")


//格口满箱上报
#define EVT_GRID_STATUS_REPORT_STATUS_FULL                              0
#define EVT_GRID_STATUS_REPORT_STATUS_EMPTY                             1

#define EVT_KEY_GRID_STATUS_REPORT                                      ("autorebin.grid-status-report")
#define EVT_GRID_STATUS_REPORT_KEY_GRID_STATUS_ARRAY                    ("grid-status-array")
#define EVT_GRID_STATUS_REPORT_GRID_NO                                  ("grid-no")
#define EVT_GRID_STATUS_REPORT_STATUS                                   ("status")

#define EVT_KEY_GRID_GRATING_SENSOR_REPORT                              ("autorebin.grid-grating-sensor-report")
#define EVT_GRID_GRATING_SENSOR_REPORT_KEY_GRID_SENSOR_ARRAY            ("grid-sensor-array")
#define EVT_GRID_GRATING_SENSOR_REPORT_GRID_NO                          ("grid-no")


//格口封箱动作上报
#define EVT_GRID_SEAL_ACTION_REPORT_STATUS_IDLE                         0
#define EVT_GRID_SEAL_ACTION_REPORT_STATUS_SEAL                         1
#define EVT_GRID_SEAL_ACTION_REPORT_STATUS_CONTAIN                      2

#define EVT_KEY_GRID_SEAL_ACTION_REPORT                                 ("autorebin.grid-seal-action-report")
#define EVT_GRID_SEAL_ACTION_REPORT_GRID_SEAL_ARRAY                     ("grid-seal-array")
#define EVT_GRID_SEAL_ACTION_REPORT_GRID_NO                             ("grid-no")
#define EVT_GRID_SEAL_ACTION_REPORT_STATUS                              ("status")


//货架段与货架动作上报
#define EVT_SEGMENT_SHELF_ACTION_REPORT_ACTION_UNBIND                   0
#define EVT_SEGMENT_SHELF_ACTION_REPORT_ACTION_BIND                     1

#define EVT_KEY_SEGMENT_SHELF_ACTION_REPORT                             ("autorebin.segment-shelf-action-report")
#define EVT_SEGMENT_SHELF_REPORT_SEGMENT_SHELF_RELATION_ARRAY           ("segment-shelf-relation-array")
#define EVT_SEGMENT_SHELF_ACTION_REPORT_SEGMENT_NO                      ("segment-no")
#define EVT_SEGMENT_SHELF_ACTION_REPORT_SHELF_NO                        ("shelf-no")
#define EVT_SEGMENT_SHELF_ACTION_REPORT_ACTION                          ("action")


//货架锁状态上报
#define EVT_SHELF_LOCK_STATUS_REPORT_STATUS_LOCK                       0
#define EVT_SHELF_LOCK_STATUS_REPORT_STATUS_UNLOCK                     1

#define EVT_KEY_SEGMENT_SHELF_STATUS_REPORT                             ("autorebin.shelf-lock-status-report")
#define EVT_SHELF_LOCK_REPORT_SHELF_LOCK_STATUS_ARRAY                   ("shelf-lock-status-array")
#define EVT_SHELF_LOCK_STATUS_REPORT_SEGMENT_NO                         ("segment-no")
#define EVT_SHELF_LOCK_STATUS_REPORT_STATUS                             ("status")


//正常格口列表上报
#define EVT_KEY_GRID_LIST_NORMAL_REPORT                                 ("autorebin.grid-list-normal-report")
#define EVT_GRID_LIST_NORMAL_REPORT_KEY_GRID_NORMAL_ARRAY               ("grid-normal-array")
#define EVT_GRID_LIST_NORMAL_REPORT_GRID_NO                             ("grid-no")


//车辆状态上报
#define EVT_SORT_KEY_STATUS_REPORT                                      ("autorebinsort.status-report")
// #define EVT_SORT_STATUS_REPORT_BATTERY_SOC                              ("battery-soc")
// #define EVT_SORT_STATUS_REPORT_BATTERY_TEMPERATURE                      ("battery-temperature")

#define EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS_ON               1
#define EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS_OFF              0

#define EVT_SORT_STATUS_REPORT_BELT_MOTOR_STATUS_CODE                   ("belt-motor-status-code")
#define EVT_SORT_STATUS_REPORT_BELT_MOTOR_STATUS                        ("belt-motor-status")

#define EVT_SORT_STATUS_REPORT_BELT_MOTOR_SPEED                         ("belt-motor-speed")
#define EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS                  ("belt-zero-sensor-status")

#define EVT_SORT_STATUS_REPORT_FEEDEER_REMOVE_SENSOR_STATUS_UNTRIGGER   0
#define EVT_SORT_STATUS_REPORT_FEEDEER_REMOVE_SENSOR_STATUS_TRIGGER     1





//车组状态上报
#define EVT_SORT_STATUS_REPORT_STATUS_IN_SYSTEM                         0
#define EVT_SORT_STATUS_REPORT_STATUS_OUT_SYSTEM                        1
#define EVT_SORT_GROUP_STATUS_REPORT_STATUS_FAULT                       0
#define EVT_SORT_GROUP_STATUS_REPORT_STATUS_NORMAL                      1
#define EVT_SORT_STATUS_REPORT_CARRIAGE_ENABLE                          0
#define EVT_SORT_STATUS_REPORT_CARRIAGE_DISABLE                         1
#define EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE_BELT                       0
#define EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE_DUMP                       1
#define EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR                         0
#define EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL                      1
#define EVT_SORT_STATUS_REPORT_LOAD_STATUS_NO_LOAD                      0
#define EVT_SORT_STATUS_REPORT_LOAD_STATUS_WITH_LOAD                    1
#define EVT_SORT_STATUS_REPORT_CONVEYOR_SENSOR_STATUS_ON                1
#define EVT_SORT_STATUS_REPORT_CONVEYOR_ZERO_SENSOR_STATUS_OFF          0
#define EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_HEAD                       0
#define EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_CARRIAGE                   1
#define EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_TAIL                       2
#define EVT_SORT_STATUS_REPORT_CARRIAGE_HAS_TOWERDE                     0
#define EVT_SORT_STATUS_REPORT_CARRIAGE_NO_TOWERED                      1


#define EVT_SORT_GROUP_KEY_STATUS_REPORT                                ("autorebinsortgroup.status-report")
#define EVT_SORT_STATUS_REPORT_KEY_SORT_GROUP_STATUS_ARRAY              ("sort-group-status-array")
#define EVT_SORT_GROUP_STATUS_REPORT_SORT_GROUP_NO                      ("sort-group-no")
#define EVT_SORT_GROUP_STATUS_REPORT_SPEED                              ("speed")
#define EVT_SORT_GROUP_STATUS_REPORT_STATUS                             ("status")
#define EVT_SORT_GROUP_STATUS_REPORT_FAULT_STATUS                       ("fault-status")
#define EVT_SORT_GROUP_STATUS_REPORT_LOCAL_IP                           ("local-ip")
#define EVT_SORT_GROUP_STATUS_REPORT_HARDWARE_VERSION                   ("hardware-version")

#define EVT_SORT_STATUS_REPORT_KEY_SORT_STATUS_ARRAY                    ("sort-status-array")
#define EVT_SORT_STATUS_REPORT_SORT_NO                                  ("sort-no")
#define EVT_SORT_STATUS_REPORT_LOCATION_X                               ("location-x")
#define EVT_SORT_STATUS_REPORT_LOCATION_Y                               ("location-y")
#define EVT_SORT_STATUS_REPORT_LOCATION_Z                               ("location-z")

#define EVT_SORT_STATUS_REPORT_CATEGORY                                 ("category")
#define EVT_SORT_STATUS_REPORT_FAULT_STATUS                             ("fault-status")
#define EVT_SORT_STATUS_REPORT_STATUS                                   ("status")
#define EVT_SORT_STATUS_REPORT_SOFTWARE_VERSION                         ("software-version")

#define EVT_SORT_STATUS_REPORT_KEY_CARGO_PLATFORM_STATUS_ARRAY          ("cargo-platform-status-array")
#define EVT_SORT_STATUS_REPORT_PLATFORM_NO                              ("platform-no")
#define EVT_SORT_STATUS_REPORT_LOAD_STATUS                              ("load-status")
#define EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE                            ("conveyor-type")
#define EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_STATUS_CODE               ("conveyor-motor-status-code")
#define EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_STATUS                    ("conveyor-motor-status")
#define EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_SPEED                     ("conveyor-motor-speed")
#define EVT_SORT_STATUS_REPORT_CONVEYOR_ZERO_SENSOR_STATUS              ("conveyor-zero-sensor-status")

#define EVT_SORT_STATUS_REPORT_POWERED                                  ("powered")
#define EVT_SORT_STATUS_REPORT_HOIST_MOTOR_STATUS_CODE                  ("hoist-motor-status-code")
#define EVT_SORT_STATUS_REPORT_HOIST_MOTOR_STATUS                       ("hoist-motor-status")
#define EVT_SORT_STATUS_REPORT_HOIST_MOTOR_SPEED                        ("hoist-motor-speed")

#define EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_STATUS_CODE             ("walk-motor-status-code")
#define EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_STATUS                  ("walk-motor-status")
#define EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_SPEED                   ("walk-motor-speed")

#define EVT_SORT_STATUS_REPORT_KEY_SENSOR_STATUS_ARRAY                  ("sensor-status-array")
#define EVT_SORT_STATUS_REPORT_FEEDER_REMOVE_SENSOR_NO                  ("feeder-remove-sensor-no")
#define EVT_SORT_STATUS_REPORT_FEEDER_REMOVE_SENSOR_STATUS              ("feeder-remove-sensor-status")


//供包台状态上报
#define EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_ERR                       0
#define EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_NORMAL                    1

#define EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS_UNTRIGGER                0
#define EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS_TRIGGER                  1

#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_START                      0
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_STOP                       1
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_RESET                      2
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_REVERSE                    3
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_SLEEP                      4
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_EMERG                      5

#define EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP                       0
#define EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN                     1

#define EVT_FEEDER_KEY_STATUS_REPORT                                    ("autorebinfeeder.status-report")
#define EVT_FEEDER_STATUS_REPORT_KEY_FEEDER_NO                          ("feeder-no")
#define EVT_FEEDER_STATUS_REPORT_KEY_FEEDER_STATUS                      ("feeder-status")

#define EVT_FEEDER_STATUS_REPORT_KEY_BELTS_STATUS                       ("belts-status")
#define EVT_FEEDER_STATUS_REPORT_BELT_NO                                ("belt-no")
#define EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS                          ("sensor-status")
#define EVT_FEEDER_STATUS_REPORT_MOTOR_STATUS                           ("motor-status")
#define EVT_FEEDER_STATUS_REPORT_MOTOR_SPEED                            ("motor-speed")

#define EVT_FEEDER_STATUS_REPORT_KEY_OCR_STATUS                         ("ocr-status")

#define EVT_FEEDER_STATUS_REPORT_KEY_BUTTONS_STATUS                     ("buttons-status")
#define EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE                            ("button-type")
#define EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION                          ("button-action")

#define EVT_FEEDER_BELT_STATUS_REPORT                                    ("autorebin.feeder-belt-status")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_FEEDER_NO                      ("feeder-no")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_BLETS                          ("belts")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_BLET_LINE_NO                   ("belt-line-no")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_SEQUENCE                       ("sequence")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_STATUS                         ("status")
#define EVT_FEEDER_BELT_STATUS_REPORT_KEY_BELT_ONE                       ("1")
//变轨器状态上报
#define EVT_SWITCHER_STATUS_REPORT_FAULT_STATUS_ERR                     0
#define EVT_SWITCHER_STATUS_REPORT_FAULT_STATUS_NORMAL                  1

#define EVT_SWITCHER_STATUS_REPORT_SWITCH_STATUS_CLOSE                  0
#define EVT_SWITCHER_STATUS_REPORT_SWITCH_STATUS_OPEN                   1
#define EVT_SWITCHER_STATUS_REPORT_SWITCH_STATUS_UNKNOW                 2

#define EVT_SWITCHER_KEY_STATUS_REPORT                                  ("autorebinswitcher.status-report")
#define EVT_SWITCHER_STATUS_REPORT_KEY_ORBITAL_TRANSFER_STATUS_ARRAY    ("orbital-transfer-status-array")
#define EVT_SWITCHER_STATUS_REPORT_NO                                   ("no")
#define EVT_SWITCHER_STATUS_REPORT_FAULT_STATUS                         ("fault-status")
#define EVT_SWITCHER_STATUS_REPORT_SWITCH_STATUS                        ("switch-status")
#define EVT_SWITCHER_STATUS_REPORT_LOCATION                             ("location")


//安全门状态上报
#define EVT_SAFETYGATE_STATE_REPORT_SATETYGATE_FRONT                    0
#define EVT_SAFETYGATE_STATE_REPORT_SATETYGATE_TAIL                     1

#define EVT_KEY_SAFETYGATE_STATE_REPORT                                 ("autorebinsafetygate.status-report")
#define EVT_SAFETYGATE_STATE_REPORT_KEY_SAFETYGATE_NO                   ("gate-no")
#define EVT_SAFETYGATE_STATE_REPORT_KEY_BUTTON_STATUS                   ("button-status")
#define EVT_SAFETYGATE_STATE_REPORT_KEY_GATE_STATUS                     ("gate-status")

//电气柜状态上报
#define EVT_KEY_ELECTRICAL_CABINET_STATE_REPORT                         ("autorebinelectricalcabinet.status-report")
#define EVT_ELECTRICAL_CABINET_STATE_REPORT_KEY_CABINET_NO              ("cabinet-no")
#define EVT_ELECTRICAL_CABINET_STATE_REPORT_KEY_BUTTON_STATUS           ("button-status")

#define EVT_KEY_STATUS_REPORT_EMERGENCY_PRESSED                         1
#define EVT_KEY_STATUS_REPORT_EMERGENCY_RESTORED                        0

//上线上报
#define EVT_REGISTER_REPORT_STATUS_UNREGISTER                           0
#define EVT_REGISTER_REPORT_STATUS_REGISTER                             1
#define EVT_REGISTER_REPORT_MODE_MANUAL                                 0
#define EVT_REGISTER_REPORT_MODE_AUTO                                   1
#define EVT_REGISTER_REPORT_MODE_SEMI_AUTO                              2

#define EVT_KEY_REGISTER_REPORT                                         ("autorebin.register-report")
#define EVT_REGISTER_REPORT_KEY_STATUS                                  ("status")
#define EVT_REGISTER_REPORT_KEY_MODE                                    ("mode")

//系统状态上报
#define EVT_STATUS_REPORT_MODE_MANUAL                                   0
#define EVT_STATUS_REPORT_MODE_AUTO                                     1
#define EVT_STATUS_REPORT_MODE_SEMI_AUTO                                2
#define EVT_STATUS_REPORT_WORK_STATUS_WORKING                           0
#define EVT_STATUS_REPORT_WORK_STATUS_IDLE                              1
#define EVT_STATUS_REPORT_FAULT_STATUS_NORMAL                           0
#define EVT_STATUS_REPORT_FAULT_STATUS_ERR                              1
#define EVT_STATUS_REPORT_FAULT_STATUS_EXCEPTION                        2
#define EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_OPENED                     0
#define EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_CLOSED                     1
#define EVT_STATUS_REPORT_RUNNING_STATUS_STARTUP                        0
#define EVT_STATUS_REPORT_RUNNING_STATUS_RUNNING                        1
#define EVT_STATUS_REPORT_RUNNING_STATUS_PAUSE                          2
#define EVT_STATUS_REPORT_RUNNING_STATUS_STOP                           3
#define EVT_STATUS_REPORT_RUNNING_STATUS_EMERG_STOP                     4

#define EVT_KEY_STATUS_REPORT                                           ("autorebin.status-report")
#define EVT_STATUS_REPORT_KEY_MODE                                      ("mode")
#define EVT_STATUS_REPORT_KEY_WORK_STATUS                               ("work-status")
#define EVT_STATUS_REPORT_KEY_FAULT_STATUS                              ("fault-status")
#define EVT_STATUS_REPORT_KEY_SAFETY_DOOR_STATUS                        ("safety-door-status")
#define EVT_STATUS_REPORT_KEY_RUNNING_STATUS                            ("running-status")

//device异常上报
#define EVT_KEY_ERROR_REPORT                                            ("autorebin.error-report")
#define EVT_ERROR_REPORT_KEY_ERROR_CODE                                 ("error-code")
#define EVT_KEY_ERROR_RELEASE_REPORT                                    ("autorebin.error-release-report")
#define EVT_ERROR_RELEASE_REPORT_KEY_ERROR_CODE_ARRAY                   ("error-code-array")

//格口异常上报
#define EVT_KEY_GRID_ERROR_REPORT                                       ("autorebin.grid-error-report")
#define EVT_GRID_ERROR_REPORT_KEY_GRID_NO                               ("grid-no")
#define EVT_GRID_ERROR_REPORT_KEY_ERROR_CODE                            ("error-code")
#define EVT_KEY_GRID_ERROR_RELEASE_REPORT                               ("autorebin.grid-error-release-report")
#define EVT_GRID_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_GRID_ARRAY      ("error-release-grid-array")
#define EVT_GRID_ERROR_RELEASE_REPORT_GRID_NO                           ("grid-no")
#define EVT_GRID_ERROR_RELEASE_REPORT_ERROR_CODE                        ("error-code")


#define EVT_KEY_GRID_GROUP_ERROR_REPORT                                 ("autorebin.grid-group-error-report")
#define EVT_GRID_GROUP_ERROR_REPORT_KEY_GRID_GROUP_NO                   ("grid-group-no")
#define EVT_GRID_GROUP_ERROR_REPORT_KEY_ERROR_CODE                      ("error-code")
#define EVT_KEY_GRID_GROUP_ERROR_RELEASE_REPORT                         ("autorebin.grid-group-error-release-report")
#define EVT_GRID_GROUP_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_GRID_GROUP_ARRAY      ("error-release-grid-group-array")
#define EVT_GRID_GROUP_ERROR_RELEASE_REPORT_GRID_GROUP_NO               ("grid-group-no")
#define EVT_GRID_GROUP_ERROR_RELEASE_REPORT_ERROR_CODE                  ("error-code")

//移动货架异常上报
#define EVT_KEY_SEGMENT_ERROR_REPORT                                    ("autorebin.segment-error-report")
#define EVT_SEGMENT_ERROR_REPORT_KEY_SEGMENT_NO                         ("segment-no")
#define EVT_SEGMENT_ERROR_REPORT_KEY_ERROR_CODE                         ("error-code")
#define EVT_KEY_SEGMENT_ERROR_RELEASE_REPORT                            ("autorebin.segment-error-release-report")
#define EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_RELEASE_SEGMENT_ARRAY      ("segment-error-release-array")
#define EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_SEGMENT_NO                 ("segment-no")
#define EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_ERROR_CODE                 ("error-code")


//车辆异常上报
#define EVT_SORT_KEY_ERROR_REPORT                                       ("autorebinsort.error-report")
#define EVT_SORT_ERROR_REPORT_KEY_SORT_NO                               ("sort-no")
#define EVT_SORT_ERROR_REPORT_KEY_ERROR_CODE                            ("error-code")
#define EVT_SORT_KEY_ERROR_RELEASE_REPORT                               ("autorebinsort.error-release-report")
#define EVT_SORT_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_SORT_ARRAY      ("error-release-sort-array")
#define EVT_SORT_ERROR_RELEASE_REPORT_SORT_NO                           ("sort-no")
#define EVT_SORT_ERROR_RELEASE_REPORT_ERROR_CODE                        ("error-code")

//车组异常上报
#define EVT_SORTGROUP_KEY_ERROR_REPORT                                  ("autorebinsortgroup.error-report")
#define EVT_SORTGROUP_ERROR_REPORT_KEY_SORTGROUP_NO                     ("sort-group-no")
#define EVT_SORTGROUP_ERROR_REPORT_KEY_ERROR_CODE                       ("error-code")
#define EVT_SORTGROUP_KEY_ERROR_RELEASE_REPORT                          ("autorebinsortgroup.error-release-report")
#define EVT_SORTGROUP_ERROR_RELEASE_REPORT_ARRAY                        ("error-release-sort-group-array")
#define EVT_SORTGROUP_ERROR_RELEASE_REPORT_SORTGROUP_NO                 ("sort-group-no")
#define EVT_SORTGROUP_ERROR_RELEASE_REPORT_ERROR_CODE                   ("error-code")


//车厢异常上报
#define EVT_SORTGROUP_SORT_KEY_ERROR_REPORT                             ("autorebinsortgroup.sort-error-report")
#define EVT_SORTGROUP_SORT_ERROR_REPORT_KEY_SORTGROUP_NO                ("sort-no")
#define EVT_SORTGROUP_SORT_ERROR_REPORT_KEY_ERROR_CODE                  ("error-code")
#define EVT_SORTGROUP_SORT_KEY_ERROR_RELEASE_REPORT                     ("autorebinsortgroup.sort-error-release-report")
#define EVT_SORTGROUP_SORT_ERROR_RELEASE_REPORT_ARRAY                   ("sort-error-release-array")
#define EVT_SORTGROUP_SORT_ERROR_RELEASE_REPORT_SORTGROUP_NO            ("sort-no")
#define EVT_SORTGROUP_SORT_ERROR_RELEASE_REPORT_ERROR_CODE              ("error-code")


//载货台异常上报
#define EVT_PLATFORM_KEY_ERROR_REPORT                                   ("autorebinsortgroup.platform-error-report")
#define EVT_PLATFORM_ERROR_REPORT_KEY_PLATFORM_NO                       ("platform-no")
#define EVT_PLATFORM_ERROR_REPORT_KEY_ERROR_CODE                        ("error-code")
#define EVT_PLATFORM_ERROR_RELEASE_REPORT                               ("autorebinsortgroup.platform-error-release-report")
#define EVT_PLATFORM_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_PLATFORM    ("platform-error-release-array")
#define EVT_PLATFORM_ERROR_RELEASE_REPORT_PLATFORM_NO                   ("platform-no")
#define EVT_PLATFORM_ERROR_RELEASE_REPORT_ERROR_CODE                    ("error-code")

//供包台异常上报
#define EVT_FEEDER_KEY_ERROR_REPORT                                     ("autorebinfeeder.feeder-error-report")
#define EVT_FEEDER_ERROR_REPORT_KEY_FEEDER_NO                           ("feeder-no")
#define EVT_FEEDER_ERROR_REPORT_KEY_ERROR_CODE                          ("error-code")
#define EVT_FEEDER_KEY_ERROR_RELEASE_REPORT                             ("autorebinfeeder.feeder-error-release-report")
#define EVT_FEEDER_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_FEEDER        ("error-release-feeder-array")
#define EVT_FEEDER_ERROR_RELEASE_REPORT_FEEDER_NO                       ("feeder-no")
#define EVT_FEEDER_ERROR_RELEASE_REPORT_ERROR_CODE                      ("error-code")


//变轨器异常上报
#define EVT_KEY_SWITCHER_ERROR_REPORT                                    ("autorebinswitcher.switcher-error-report")
#define EVT_SWITCHER_ERROR_REPORT_KEY_SWITCHER_NO                        ("switcher-no")
#define EVT_SWITCHER_ERROR_REPORT_KEY_ERROR_CODE                         ("error-code")
#define EVT_KEY_SWITCHER_ERROR_RELEASE_REPORT                            ("autorebinswitcher.switcher-error-release-report")
#define EVT_SWITCHER_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_SWITCHER     ("error-release-switcher-array")
#define EVT_SWITCHER_ERROR_RELEASE_REPORT_SWITCHER_NO                    ("switcher-no")
#define EVT_SWITCHER_ERROR_RELEASE_REPORT_ERROR_CODE                     ("error-code")


//实时参数下发
#define EVT_PARAMETER_SET_FOLLOW_DISTANCE                               ("follow-distance")                 //跟车距离
#define EVT_PARAMETER_SET_MAX_WALK_DISTANCE                             ("max-walk-distance")               //最大行走距离
#define EVT_PARAMETER_SET_MAX_CALIBRATION_DISTANCE                      ("max-calibration-distance")        //位置校准最大申请距离
#define EVT_PARAMETER_SET_CALIBRATION_SPEED                             ("calibration-speed")               //位置校准时运行速度
#define EVT_PARAMETER_SET_VARIABLE_SPEED                                ("variable-speed")                  //运行速度可变
#define EVT_PARAMETER_SET_NORMAL_LINEAR_SPEED                           ("normal-linear-speed")             //正常运行时直线速度
#define EVT_PARAMETER_SET_NORMAL_CURVE_SPEED                            ("normal-curve-speed")              //正常运行时弧线段速度
#define EVT_PARAMETER_SET_NORMAL_APPROACH_SPECIAL_SPEED                 ("normal-approach-special-speed")   //正常运行时接近特殊位置速度
#define EVT_PARAMETER_SET_NORMAL_CHASE_SPEED                            ("normal-chase-speed")              //正常运行时刻追车速度
#define EVT_PARAMETER_SET_MAX_DYNAMIC_ADJUST_DISTANCE                   ("max-dynamic-adjust-distance")     //正常运行时刻速度动态调整最大距离
#define EVT_PARAMETER_SET_AUTO_RECOVERY                                 ("auto-recovery")                   //组件异常退出自动恢复
#define EVT_PARAMETER_SET_BELT_ZERO_DETECTION                           ("belt-zero-detection")             //载货台皮带零点自动检测
#define EVT_PARAMETER_SET_GRID_FULL_ALARM                               ("grid-full-alarm")                 //格口满箱蜂鸣
#define EVT_PARAMETER_SET_SYSTEM_STOP_ALARM                             ("system-stop-alarm")               //系统急停蜂鸣
#define EVT_PARAMETER_SET_MULTI_LOOP_DROP                               ("multi-loop-drop")                 //开启多圈投包
#define EVT_PARAMETER_SET_MULTI_LOOP_ATTEMPTS                           ("multi-loop-attempts")             //多圈投包尝试次数
#define EVT_PARAMETER_SET_GRID_FULL_FALLBACK                            ("grid-full-fallback")              //满箱自动兜底处理
#define EVT_PARAMETER_SET_GRID_SEAL_FALLBACK                            ("grid-seal-fallback")              //封箱自动兜底处理
#define EVT_PARAMETER_SET_SYSTEM_FALLBACK_STRATEGY                      ("system-fallback-strategy")        //系统兜底处理策略
#define EVT_PARAMETER_SET_VERTICAL_ERROR_LIMIT                          ("vertical-error-limit")            //垂直位置检测允许误差上限
#define EVT_PARAMETER_SET_TASK_TIMEOUT_FALLBACK                         ("task-timeout-fallback")           //申请任务超时自动兜底
#define EVT_PARAMETER_SET_AUTO_PACKAGE_OFFSET                           ("auto-package-offset")             //自动上包/下包的位置提前量

//train_agent参数
#define EVT_PARAMETER_SET_HEARTBEAT_INTERVAL                            ("heartbeat-interval")              //心跳周期
#define EVT_PARAMETER_SET_DATA_RESEND_TIMEOUT                           ("data-resend-timeout")             //数据重发超时时间
#define EVT_PARAMETER_SET_APPROACH_SENSOR_TOLERANCE                     ("approach-sensor-tolerance")       //接近传感器容错数量
#define EVT_PARAMETER_SET_PACKAGE_SENSOR_SWITCH                         ("package-sensor-switch")           //下包检测传感器检测开关




//枚举值定义
#define ENUM_0                  ("\"0\"")
#define ENUM_1                  ("\"1\"")
#define ENUM_2                  ("\"2\"")
#define ENUM_3                  ("\"3\"")
#define ENUM_4                  ("\"4\"")
#define ENUM_5                  ("\"5\"")
#define ENUM_6                  ("\"6\"")
#define ENUM_7                  ("\"7\"")

class cjs_cvt
{
public:
    static cjs_cvt *get_instance(void)
    {
        static cjs_cvt instance;
        return &instance;
    }

    void to_enum(const char *in, int &value);
    void to_int(const char *in, int &value);
    void to_string(const char *in, std::string &value);
    void to_float(const char *in, float &value);

    void to_hmi_lamp_state(const char *in, std::string &id, int &state);
    void to_buzzer_info(const char *in, std::string &id, int &state);
    void to_shelf_info(const char *in, std::string &shelf_no, int &action);
    void to_scanner_info(const char *in, std::vector<std::string> &ids, std::vector<int> &modes);
    void to_feeder_belt_info(const char *in, std::vector<std::string> &ids, std::vector<int> &speeds);
    void to_container_state(const char *in, std::list<led_info> &states);
    void to_container_seal_state(const char *in, std::list<container_seal_state_single> &states);
    void to_position_xyz(const char *in, int32_t &p_x, int32_t &p_y, int32_t &p_z);

    // void to_config_set(const char *in, std::list<config_set_cmd> &cmd);
    void from_config_query(JDThingTalkProtoFuncCallResFunc_t *response, const uint32_t &set_value);
    void from_belt_state_get(JDThingTalkProtoFuncCallResFunc_t *response, const feeder_dev_state_total &state);
    void from_config_set_response(JDThingTalkProtoFuncCallResFunc_t *response,int result);

private:

};

class evt_cvt
{
public:
    static evt_cvt *get_instance(void)
    {
        static evt_cvt instance;
        return &instance;
    }

    std::string format_conversion(int num);
    std::string get_platform_id(int num);
    void from_barcode(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::barcode_task_info &barcode);
    void from_normal_task_state(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::task_state &state);
    void from_hospice_task_state(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::task_state &state);
    void from_container_group_rfid(JDThingTalkProtoEvtPostEvt_t *event, const box_info_multiple &states);
    void from_container_rfid(JDThingTalkProtoEvtPostEvt_t *event, const box_info_multiple &states);
    void from_container_satr(JDThingTalkProtoEvtPostEvt_t *event, const slot_state &state);
    void from_container_raster_state(JDThingTalkProtoEvtPostEvt_t *event, const slot_state &state);
    void from_container_seal_state(JDThingTalkProtoEvtPostEvt_t *event, const container_seal_state_single &state);
    void from_container_list(JDThingTalkProtoEvtPostEvt_t *event, const std::vector<uint32_t> &con_list);
    void from_shelf_state(JDThingTalkProtoEvtPostEvt_t *event, const shelves_state &shelf_state);
    void from_sys_register(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state);
    void from_sys_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state);
    void from_safetygate_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state, int bit_position);
    void from_electricalcabinet_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state);
    void from_vehicle_state(JDThingTalkProtoEvtPostEvt_t *event, const std::unordered_map<int, device_manager::vehicle_total_state> &states, const e_sys_mode &mode);
    void from_feeder_state(JDThingTalkProtoEvtPostEvt_t *event, const feeder_dev_state_total &feeder_state, const std::map<key_id, key_evt_type> &button_state);
    // void from_switcher_state(JDThingTalkProtoEvtPostEvt_t *event, const std::unordered_map<int, switch_state_single> &states);
    bool is_bit_set(uint32_t value, int bit_position);

    void from_vehicle_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_vehicle_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_carriage_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_carriage_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_platform_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_platform_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_container_group_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_container_group_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_container_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_contianer_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_shelves_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_shelves_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_feeder_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_feeder_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_switcher_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);      //用plane表示switcher
    void from_switcher_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_device_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_device_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception);
    void from_feeder_belt_status(JDThingTalkProtoEvtPostEvt_t *event, const feeder_dev_state_total &feeder_state);

private:

    char* pack_string(char *in_string);
    char* pack_string(const char *in_string);
    char* pack_enum(int num);
    char* pack_int(int num);

    void from_manual_vehicle_state(cJSON &item, const vehicle::vehicle_running_state &st);
    void from_manual_vehicle_sensor_state(cJSON &array, const std::unordered_map<int, device_manager::vehicle_total_state> &states);
    
    bool is_feeder_normal(const feeder_dev_state_total &state);
    void from_feeder_belt_state(cJSON &array, const feeder_belt_sensor_state_multiple &sensor_state, const feeder_belt_motor_state_multiple &motor_state);
    void from_feeder_button_state(cJSON &array, const std::map<key_id, key_evt_type> &button_state);
};
