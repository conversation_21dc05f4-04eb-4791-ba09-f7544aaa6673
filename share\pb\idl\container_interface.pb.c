/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.5 */

#include "container_interface.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(box_info_single, box_info_single, AUTO)


PB_BIND(box_info_multiple, box_info_multiple, 4)


PB_BIND(container_seal_state_single, container_seal_state_single, AUTO)


PB_BIND(led_info, led_info, AUTO)


PB_BIND(container_seal_cmd, container_seal_cmd, AUTO)


PB_BIND(container_agent_state, container_agent_state, AUTO)


PB_BIND(led_info_multiple, led_info_multiple, 4)


PB_BIND(slot_state, slot_state, AUTO)


PB_BIND(shelves_cmd, shelves_cmd, AUTO)


PB_BIND(shelves_state, shelves_state, AUTO)







