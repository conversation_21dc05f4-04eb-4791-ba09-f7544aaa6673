/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_DEV_HMI_PB_H_INCLUDED
#define PB_DEV_HMI_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _key_id {
    key_id_KEY_RESERVE = 0,
    key_id_KEY_START = 1,
    key_id_KEY_STOP = 2,
    key_id_KEY_SLEEP = 3,
    key_id_KEY_RESET = 4,
    key_id_KEY_EMERG = 5,
    key_id_KEY_FEEDER_ROLLBACK = 6
} key_id;

/* for safety door down means open, up means close */
typedef enum _key_evt_type {
    key_evt_type_EVENT_RESERVE = 0,
    key_evt_type_EVENT_PRESSED = 1,
    key_evt_type_EVENT_TRIGGER = 2,
    key_evt_type_EVENT_RELEASE = 3
} key_evt_type;

typedef enum _key_evt_src {
    key_evt_src_RESERVE = 0,
    key_evt_src_SAFE_DOOR_FRONT = 1,
    key_evt_src_SAFE_DOOR_BACK = 2,
    key_evt_src_SAFE_DOOR_MID = 3,
    key_evt_src_CONTROL_CABINET = 4,
    key_evt_src_FEEDER_1 = 5,
    key_evt_src_FEEDER_2 = 6,
    key_evt_src_FEEDER_3 = 7,
    key_evt_src_FEEDER_4 = 8,
    key_evt_src_FEEDER_5 = 9,
    key_evt_src_FEEDER_6 = 10
} key_evt_src;

typedef enum _led_rgb_bits {
    led_rgb_bits_BUZZER = 0,
    led_rgb_bits_RED = 1,
    led_rgb_bits_GREEN = 2,
    led_rgb_bits_YELLOW = 3 /* 系统塔灯是红绿黄，而非RGB */
} led_rgb_bits;

typedef enum _led_cmd_type {
    led_cmd_type_ON = 0,
    led_cmd_type_OFF = 1,
    led_cmd_type_FLICKER = 2
} led_cmd_type;

/* Struct definitions */
typedef struct _key_event {
    key_evt_src dev_id;
    key_id key;
    key_evt_type evt_type;
} key_event;

typedef struct _led_cmd {
    key_evt_src id;
    uint32_t color;
    led_cmd_type cmd;
    uint32_t param;
} led_cmd;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _key_id_MIN key_id_KEY_RESERVE
#define _key_id_MAX key_id_KEY_FEEDER_ROLLBACK
#define _key_id_ARRAYSIZE ((key_id)(key_id_KEY_FEEDER_ROLLBACK+1))

#define _key_evt_type_MIN key_evt_type_EVENT_RESERVE
#define _key_evt_type_MAX key_evt_type_EVENT_RELEASE
#define _key_evt_type_ARRAYSIZE ((key_evt_type)(key_evt_type_EVENT_RELEASE+1))

#define _key_evt_src_MIN key_evt_src_RESERVE
#define _key_evt_src_MAX key_evt_src_FEEDER_6
#define _key_evt_src_ARRAYSIZE ((key_evt_src)(key_evt_src_FEEDER_6+1))

#define _led_rgb_bits_MIN led_rgb_bits_BUZZER
#define _led_rgb_bits_MAX led_rgb_bits_YELLOW
#define _led_rgb_bits_ARRAYSIZE ((led_rgb_bits)(led_rgb_bits_YELLOW+1))

#define _led_cmd_type_MIN led_cmd_type_ON
#define _led_cmd_type_MAX led_cmd_type_FLICKER
#define _led_cmd_type_ARRAYSIZE ((led_cmd_type)(led_cmd_type_FLICKER+1))

#define key_event_dev_id_ENUMTYPE key_evt_src
#define key_event_key_ENUMTYPE key_id
#define key_event_evt_type_ENUMTYPE key_evt_type

#define led_cmd_id_ENUMTYPE key_evt_src
#define led_cmd_cmd_ENUMTYPE led_cmd_type


/* Initializer values for message structs */
#define key_event_init_default                   {_key_evt_src_MIN, _key_id_MIN, _key_evt_type_MIN}
#define led_cmd_init_default                     {_key_evt_src_MIN, 0, _led_cmd_type_MIN, 0}
#define key_event_init_zero                      {_key_evt_src_MIN, _key_id_MIN, _key_evt_type_MIN}
#define led_cmd_init_zero                        {_key_evt_src_MIN, 0, _led_cmd_type_MIN, 0}

/* Field tags (for use in manual encoding/decoding) */
#define key_event_dev_id_tag                     1
#define key_event_key_tag                        2
#define key_event_evt_type_tag                   3
#define led_cmd_id_tag                           1
#define led_cmd_color_tag                        2
#define led_cmd_cmd_tag                          3
#define led_cmd_param_tag                        4

/* Struct field encoding specification for nanopb */
#define key_event_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    key,               2) \
X(a, STATIC,   SINGULAR, UENUM,    evt_type,          3)
#define key_event_CALLBACK NULL
#define key_event_DEFAULT NULL

#define led_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    id,                1) \
X(a, STATIC,   SINGULAR, UINT32,   color,             2) \
X(a, STATIC,   SINGULAR, UENUM,    cmd,               3) \
X(a, STATIC,   SINGULAR, UINT32,   param,             4)
#define led_cmd_CALLBACK NULL
#define led_cmd_DEFAULT NULL

extern const pb_msgdesc_t key_event_msg;
extern const pb_msgdesc_t led_cmd_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define key_event_fields &key_event_msg
#define led_cmd_fields &led_cmd_msg

/* Maximum encoded size of messages (where known) */
#define DEV_HMI_PB_H_MAX_SIZE                    led_cmd_size
#define key_event_size                           6
#define led_cmd_size                             16

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
