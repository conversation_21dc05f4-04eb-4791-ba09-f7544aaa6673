#include "lwshell.h"

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdbool.h>

#include "posix_error.h"
//#include "bsp_rtc.h"

/*����ʵ�ֺ���*/
int help(const char* args[], const lwshell_interface_t *interface);
//static int date(const char* args[], const lwshell_interface_t *interface);

extern int lwshell_get_all_user_cmds(cmd_desc_t ***cmds);

static const struct cmd_handler builtin_cmds[] =
{
    {"help", "print this information", &help},
    //{"date", "set date and time", &date},
};

const cmd_desc_t* lwshell_get_buitin_cmd(const char* cmd)
{
    int i;

    for(i=0; i<ARRAY_SIZE(builtin_cmds); i++)
    {
        if(strcmp(cmd, builtin_cmds[i].cmd) == 0)
        {
            return &builtin_cmds[i];
        }
    }

    return NULL;
}

/***************command routine******************/
#if 0
static int wlan_state(const char* args[], write_func_t output, uint32_t param)
{
    const net_info_t *info;
    char buf[32];
    int l;

    info = get_wlan_info(false);
    output(param, "wlan_state:\r\n", 0);

    l=sprintf(buf, "channel: %d\r\n", info->channel);
    output(param, buf, l);

    l=sprintf(buf, "rssi: -%ddbm\r\n", info->rssi);
    output(param, buf, l);
    
    return 0;
}
#endif

int shell_output(const lwshell_interface_t *p_intf, const char* text, int l)
{
    int len = l;

    if( (p_intf == NULL) || (p_intf->write == NULL) )
        return 0;

    if(len == 0)
        len = strlen(text);

    return (p_intf)->write((p_intf->dev), text, len);
}

int help(const char* args[], const lwshell_interface_t *intf)
{
    int i;
    cmd_desc_t **user_cmds;
    int user_cmd_num;

    shell_output(intf, "builtin commands:\r\n", 0);
    for(i=0; i<ARRAY_SIZE(builtin_cmds); i++)
    {
        shell_output(intf, "\t", 1);
        shell_output(intf, builtin_cmds[i].cmd, 0);
        shell_output(intf, ":", 1);
        shell_output(intf, builtin_cmds[i].comment, 0);
        shell_output(intf, "\r\n", 2);
    }

    user_cmd_num = lwshell_get_all_user_cmds(&user_cmds);
    if(NULL == user_cmds)
        return 0;

    for(i=0; i<user_cmd_num; i++)
    {
        if(NULL == user_cmds[i])
            continue;
        shell_output(intf, "\t", 1);
        shell_output(intf, user_cmds[i]->cmd, 0);
        shell_output(intf, ":", 1);
        shell_output(intf, user_cmds[i]->comment, 0);
        shell_output(intf, "\r\n", 2);
    }


    return 0;
}

#if 0
static int show_date_time(const lwshell_interface_t *interface)
{
    RTC_DateTime tm;
    //struct_Rtc tm;
    char    buf[32];
    int l;

    //bsp_RtcGetDateTime(&tm);
    RTC_ReadDateTime(&tm);
    l = sprintf(buf, "\t20%02d-%02d-%02d %02d:%02d:%02d\r\n",
            tm.Year, tm.Month, tm.Date, tm.Hours, tm.Minutes, tm.Seconds);

    shell_output(interface, buf, l);

    return 0;
}

static int set_date_time(const char*date, const char*time)
{
    RTC_DateTime tm;
    const char *tmp;
    char* tmp2;

    tmp = date;

    tm.Year = strtoul(tmp, &tmp2, 10)-2000;
    if(*tmp2 != '-')
        return -POSIX_EINVAL;
    tmp = tmp2+1;

    tm.Month= strtoul(tmp, &tmp2, 10);
    if(*tmp2 != '-')
        return -POSIX_EINVAL;
    tmp = tmp2+1;

    tm.Date= strtoul(tmp, &tmp2, 10);
    if(*tmp2 != 0)
        return -POSIX_EINVAL;

    tmp = time;

    tm.Hours= strtoul(tmp, &tmp2, 10);
    if(*tmp2 != ':')
        return -POSIX_EINVAL;
    tmp = tmp2+1;

    tm.Minutes= strtoul(tmp, &tmp2, 10);
    if(*tmp2 != ':')
        return -POSIX_EINVAL;
    tmp = tmp2+1;

    tm.Seconds= strtoul(tmp, &tmp2, 10);
    if(*tmp2 != 0)
        return -POSIX_EINVAL;

    //bsp_RtcSetDateTime(&tm);
    RTC_SetDateTime(&tm);
    return 0;
}

static int date(const char* args[], const lwshell_interface_t *interface)
{
  if(args[1] == NULL)
        return show_date_time(interface);
    else if( (args[1] != NULL) && (args[2] != NULL) )
        return set_date_time(args[1], args[2]);
    else
        return -POSIX_EINVAL;
}
#endif