
#include "share/lwshell/lwshell.h"
#include "share/lwshell/user_cmds.h"
#include <spdlog/spdlog.h>
#include "share/global_def.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

// #include "share/pb/idl/vehicle_info.pb.h"

#include "share/pb/idl/data_map.pb.h"
// #include "share/pb/idl/vehicle_state.pb.h"
#include "share/pb/idl/task.pb.h"
#include "share/pb/idl/exception.pb.h"

#include "share/pb/nanopb/pb_common.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "ipc_interface/feeder_interface.hpp"
#include "ipc_interface/plane_interface.hpp"
#include "ipc_interface/vehicle_interface.hpp"
#include "ipc_interface/sys_state_interface.hpp"

#include "share/lwshell/builtin_cmds.h"

static int feeder_scan(const char* args[], const struct lwshell_interface *intf)
{
	int feeder_id = 0;

	SPDLOG_DEBUG("thingagent shell: recv scan cmd");
	
	if(args[1] == NULL)
	{
		return -1;
	}
	
	feeder_id = atoi(args[1]);
	
	feeder_interface::get_instance()->issue_feeder_scan(feeder_id);

	return 0;
};

static int feeder_belt_speed(const char* args[], const struct lwshell_interface *intf)
{
	int feeder_id = 0;
	int dev_id = 0;
	int speed = 0;
	
	SPDLOG_DEBUG("thingagent shell: recv feeder belt speed");

	if(args[1] == NULL || args[2] == NULL || args[3] == NULL)
	{
		return -1;
	}

	feeder_id = atoi(args[1]);
	dev_id = atoi(args[2]);
	speed = atoi(args[3]);
	feeder_interface::get_instance()->issue_belt_speed(feeder_id, dev_id, speed);

	return 0;
};

// static int switch_open(const char* args[], const struct lwshell_interface *intf)
// {
// 	int dev_id = 0;

// 	SPDLOG_DEBUG("thingagent shell: recv switch open cmd");
	
// 	/**
// 	 * 带参数，则控制对应输入参数的变轨器动作；不带参数，则控制所有的变轨器动作
// 	 */
// 	if(args[1] == NULL)
// 	{
// 		plane_interface::get_instance()->issue_switch_open(1);
// 		plane_interface::get_instance()->issue_switch_open(2);
// 		plane_interface::get_instance()->issue_switch_open(3);
// 		plane_interface::get_instance()->issue_switch_open(4);
// 	}
// 	else
// 	{
// 		dev_id = atoi(args[1]);
// 		plane_interface::get_instance()->issue_switch_open(dev_id);
// 	}

// 	return 0;
// };

// static int switch_close(const char* args[], const struct lwshell_interface *intf)
// {
// 	int dev_id = 0;

// 	SPDLOG_DEBUG("thingagent shell: recv switch close cmd");
	
// 	/**
// 	 * 带参数，则控制对应输入参数的变轨器动作；不带参数，则控制所有的变轨器动作
// 	 */
// 	if(args[1] == NULL)
// 	{
// 		plane_interface::get_instance()->issue_switch_close(1);
// 		plane_interface::get_instance()->issue_switch_close(2);
// 		plane_interface::get_instance()->issue_switch_close(3);
// 		plane_interface::get_instance()->issue_switch_close(4);
// 	}
// 	else
// 	{
// 		dev_id = atoi(args[1]);
// 		plane_interface::get_instance()->issue_switch_close(dev_id);
// 	}

// 	return 0;
// };

// static int switch_set_zero(const char* args[], const struct lwshell_interface *intf)
// {
// 	int dev_id = 0;

// 	SPDLOG_DEBUG("thingagent shell: recv switch set zero cmd");
	
// 	if(args[1] == NULL)
// 	{
// 		return -1;
// 	}

// 	dev_id = atoi(args[1]);
// 	plane_interface::get_instance()->issue_set_switch_zero(dev_id);

// 	return 0;
// };

static int led_red_on(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv led red on cmd");

	plane_interface::get_instance()->issue_led_red_on();

	return 0;
};

static int led_green_on(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv led green on cmd");

	plane_interface::get_instance()->issue_led_green_on();

	return 0;
};

static int led_yellow_on(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv led yellow on cmd");

	plane_interface::get_instance()->issue_led_yellow_on();

	return 0;
};

static int led_off(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv led off cmd");

	plane_interface::get_instance()->issue_led_off();

	return 0;
};

static int buzzer_on(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv buzzer on cmd");

	plane_interface::get_instance()->issue_buzzer_on();

	return 0;
};

static int buzzer_off(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv buzzer off cmd");

	plane_interface::get_instance()->issue_buzzer_off();

	return 0;
};

static int get_button_state(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: get feeder state");
	key_event state;

	feeder_interface::get_instance()->get_button_state(state);

	return 0;
};

static int get_feeder_state(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: get feeder state");
	feeder_dev_state_total state;

	feeder_interface::get_instance()->get_feeder_state(state);

	return 0;
};

// static int get_switch_state(const char* args[], const struct lwshell_interface *intf)
// {
// 	SPDLOG_DEBUG("thingagent shell: get switch state");
// 	switch_state_multiple state;

// 	plane_interface::get_instance()->get_switch_state(state);

// 	return 0;
// };

// static int get_safety_door_state(const char* args[], const struct lwshell_interface *intf)
// {
// 	SPDLOG_DEBUG("thingagent shell: get safety door state");
// 	plane_event_multiple emerg_event;

// 	plane_interface::get_instance()->get_emerg_dev_state(emerg_event);

// 	return 0;
// };

static int vehicle_reboot(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;

	if(args[1] == NULL)
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} reboot cmd", vehicle_id);
	vehicle_interface::get_instance()->issue_vehicle_reset_cmd(vehicle_id);

	return 0;
};

static int vehicle_onlin(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;

	if(args[1] == NULL)
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} online cmd", vehicle_id);
	sys_state_interface::get_instance()->issue_vehicle_register_cmd(vehicle_id);

	return 0;
};

static int vehicle_offline(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;

	if(args[1] == NULL)
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} offline cmd", vehicle_id);
	sys_state_interface::get_instance()->issue_vehicle_unregister_cmd(vehicle_id);

	return 0;
};

static int vehicle_belt_left_moving(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;
	uint32_t carriage_id;

	if((args[1] == NULL) || (args[2] == NULL))
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	carriage_id = atoi(args[2]);
	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} belt:{} left cmd", vehicle_id,  carriage_id);
	vehicle_interface::get_instance()->issue_vehicle_belt_left_moving(vehicle_id, carriage_id);

	return 0;
};

static int vehicle_belt_right_moving(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;
	uint32_t carriage_id = 0;

	if((args[1] == NULL) || (args[2] == NULL))
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	carriage_id = atoi(args[2]);
	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} belt:{} right cmd", vehicle_id, carriage_id);
	vehicle_interface::get_instance()->issue_vehicle_belt_right_moving(vehicle_id, carriage_id);

	return 0;
};

static int vehicle_moveto(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;
	uint32_t slot = 0;
	uint32_t level_speed;
	uint32_t turn_speed;

	if(args[1] == NULL || args[2] == NULL)
	{
		return -1;
	}

	vehicle_id = atoi(args[1]);
	slot = atoi(args[2]);
	level_speed = atoi(args[3]);
	turn_speed = atoi(args[4]);

	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} move to slot {} cmd", vehicle_id, slot);
	sys_state_interface::get_instance()->issue_vehicle_to_slot(vehicle_id, slot, level_speed, turn_speed);

	return 0;
};

// static int vehicle_moveto_offline(const char* args[], const struct lwshell_interface *intf)
// {
// 	uint32_t vehicle_id = 0;

// 	if(args[1] == NULL)
// 	{
// 		return -1;
// 	}
	
// 	vehicle_id = atoi(args[1]);
// 	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} move to offline point cmd", vehicle_id);
// 	debug_interface::get_instance()->issue_vehicle_to_unregister_point(vehicle_id);

// 	return 0;
// };

static int vehicle_move_lenth(const char* args[], const struct lwshell_interface *intf)
{
	uint32_t vehicle_id = 0;
	uint32_t lenth = 0;
	uint32_t level_speed, turn_speed;

	if(args[1] == NULL || args[2] == NULL)
	{
		return -1;
	}
	
	vehicle_id = atoi(args[1]);
	lenth = atoi(args[2]);
	level_speed = atoi(args[3]);
	turn_speed = atoi(args[4]);

	SPDLOG_DEBUG("thingagent shell: recv vehicle:{} move to lenth:{} cmd", vehicle_id, lenth);
	sys_state_interface::get_instance()->issue_vehicle_move_forward(vehicle_id, lenth, level_speed, turn_speed);

	return 0;
};

static int sys_vehicle_moving(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv sys all vehicle moving cmd");
	sys_state_interface::get_instance()->issue_vehicle_start_moving();

	return 0;
};

static int sys_vehicle_stop_moving(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv sys all vehicle stop moving cmd");
	sys_state_interface::get_instance()->issue_vehicle_stop_moving();

	return 0;
};

static int sys_reboot(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv sys reoot cmd");
	sys_state_interface::get_instance()->issue_mcu_reboot();

	return 0;
};

static int sys_reset(const char* args[], const struct lwshell_interface *intf)
{
	SPDLOG_DEBUG("thingagent shell: recv sys reset cmd");
	sys_state_interface::get_instance()->issue_mcu_reset();

	return 0;
};

static const struct cmd_handler cmds[] = 
{
    {"help", "print this information", &help},

	{"scan", "ctrl feeder scan once. eg: scan 1", &feeder_scan},
	{"feederbeltspeed", "ctrl feeder belt speed. eg: feederbeltspeed 1 1 1500", &feeder_belt_speed},
	// {"switchopen", "ctrl which or all switch open. eg: switchopen 1 or switchopen ", &switch_open},
	// {"switchclose", "ctrl which or all switch close. eg: switchclose 1 or switchclose", &switch_close},	
	// {"switchsetzero", "calibrate which switch zero point eg: switchsetzero 1", &switch_set_zero},		
	{"ledredon", "ctrl red led on. eg: ledredon", &led_red_on},
	{"ledgreenon", "ctrl led green on. eg: ledgreenon", &led_green_on},
	{"ledyellowon", "ctrl led yellow on. eg: ledyellowon", &led_yellow_on},
	{"ledoff", "ctrl led off. eg: ledoff", &led_off},
	{"buzzeron", "ctrl buzzer on. eg: buzzeron", &buzzer_on},
	{"buzzeroff", "ctrl buzzer off. eg: buzzeroff", &buzzer_off},
	{"vhreboot", "reboot vehicle. eg: vhreboot 3050", &vehicle_reboot},
	{"vhonline", "online vehicle. eg: vhonline 3050", &vehicle_onlin},
	{"vhoffline", "online vehicle. eg: vhoffline 3050", &vehicle_offline},
	{"vhbeltlm", "vehicle belt left moving. eg: vhbeltlm 3050 1", &vehicle_belt_left_moving},
	{"vhbeltrm", "vehicle belt right moving. eg: vhbeltrm 3050 1", &vehicle_belt_right_moving},
	{"vhmoveto", "vehicle move to grid. eg: vhmoveto 3050 0 1 1", &vehicle_moveto},
	// {"vhmovetooffline", "vehicle move to offline point. eg: vhmovetooffline 3050", &vehicle_moveto_offline},
	{"vhmovelenth", "vehicle move lenth. eg: vhmovelenth 3050 100 100 100", &vehicle_move_lenth},
	{"sysvhmoving", "system all vehicle moving. eg: sysvhmoving", &sys_vehicle_moving},
	{"sysvhstopmoving", "system all vehicle stop moving. eg: sysvhstopmoving", &sys_vehicle_stop_moving},
	{"sysreboot", "system reboot. eg: sysreboot", &sys_reboot},
	{"sysreset", "system reset. eg: sysreset", &sys_reset},

	{"getbuttonstate", "get button state. eg: getbuttonstate", &get_button_state},
	{"getfeederstate", "get feeder state. eg: getfeederstate", &get_feeder_state},
	// {"getswitchstate", "get switch state. eg: getswitchstate", &get_switch_state},
	// {"getsafetydoor", "get safety door state. eg: getsafetydoor", &get_safety_door_state}

};

int diag_thingagent_init(void)
{
	for(uint32_t i=0; i<ARRAY_SIZE(cmds); i++)
	{
		lwshell_register_cmd(&cmds[i]);
	}

	return 0;
}

