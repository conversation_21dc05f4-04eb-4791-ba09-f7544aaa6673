# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include protocol/CMakeFiles/lib_protocol.dir/depend.make

# Include the progress variables for this target.
include protocol/CMakeFiles/lib_protocol.dir/progress.make

# Include the compile flags for this target's objects.
include protocol/CMakeFiles/lib_protocol.dir/flags.make

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: protocol/CMakeFiles/lib_protocol.dir/flags.make
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../protocol/train_protocol.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_protocol.dir/train_protocol.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_protocol.dir/train_protocol.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp > CMakeFiles/lib_protocol.dir/train_protocol.cpp.i

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_protocol.dir/train_protocol.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp -o CMakeFiles/lib_protocol.dir/train_protocol.cpp.s

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.requires:

.PHONY : protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.requires

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.provides: protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.requires
	$(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.provides.build
.PHONY : protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.provides

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.provides.build: protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o


# Object files for target lib_protocol
lib_protocol_OBJECTS = \
"CMakeFiles/lib_protocol.dir/train_protocol.cpp.o"

# External object files for target lib_protocol
lib_protocol_EXTERNAL_OBJECTS =

protocol/liblib_protocol.a: protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o
protocol/liblib_protocol.a: protocol/CMakeFiles/lib_protocol.dir/build.make
protocol/liblib_protocol.a: protocol/CMakeFiles/lib_protocol.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library liblib_protocol.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && $(CMAKE_COMMAND) -P CMakeFiles/lib_protocol.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lib_protocol.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
protocol/CMakeFiles/lib_protocol.dir/build: protocol/liblib_protocol.a

.PHONY : protocol/CMakeFiles/lib_protocol.dir/build

protocol/CMakeFiles/lib_protocol.dir/requires: protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o.requires

.PHONY : protocol/CMakeFiles/lib_protocol.dir/requires

protocol/CMakeFiles/lib_protocol.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol && $(CMAKE_COMMAND) -P CMakeFiles/lib_protocol.dir/cmake_clean.cmake
.PHONY : protocol/CMakeFiles/lib_protocol.dir/clean

protocol/CMakeFiles/lib_protocol.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol/CMakeFiles/lib_protocol.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : protocol/CMakeFiles/lib_protocol.dir/depend

