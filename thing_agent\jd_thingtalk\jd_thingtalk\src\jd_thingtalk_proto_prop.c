/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 属性(properties)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   属性(properties)消息主题 释放属性设置结构体 成员变量的内存空间
 *
 * @param[in] in_set: 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_set(JDThingTalkProtoPropSet_t *in_set)
{
    int32_t ii;
    if (in_set != NULL) {
        if (in_set->deviceId != NULL) {
            jd_thingtalk_pal_free(in_set->deviceId);
            in_set->deviceId = NULL; 
        }
        if (in_set->messageId != NULL) {
            jd_thingtalk_pal_free(in_set->messageId);
            in_set->messageId = NULL; 
        }
        if (in_set->properties != NULL) {
            if (in_set->prop_num != 0) {
                for (ii = 0; ii < in_set->prop_num; ii++) {
                    jd_thingtalk_proto_free_key_value(in_set->properties[ii]);
                }
            }
            jd_thingtalk_pal_free(in_set->properties);
            in_set->properties = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
        if (in_set->prop_json != NULL) {
            jd_thingtalk_pal_free(in_set->prop_json);
            in_set->prop_json = NULL;
        }
#endif
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   属性(properties)消息主题 释放属性设置响应结构体 成员变量的内存空间
 *
 * @param[in] in_set: 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_set_res(JDThingTalkProtoPropSetRes_t *in_res)
{
    int32_t ii;
    if (in_res != NULL) {
        if (in_res->deviceId != NULL) {
            jd_thingtalk_pal_free(in_res->deviceId);
            in_res->deviceId = NULL; 
        }
        if (in_res->messageId != NULL) {
            jd_thingtalk_pal_free(in_res->messageId);
            in_res->messageId = NULL; 
        }
        if (in_res->message != NULL) {
            jd_thingtalk_pal_free(in_res->message);
            in_res->message = NULL;
        }
        if (in_res->properties != NULL) {
            if (in_res->prop_num != 0) {
                for (ii = 0; ii < in_res->prop_num; ii++) {
                    jd_thingtalk_proto_free_key_value(in_res->properties[ii]);
                }
            }
            jd_thingtalk_pal_free(in_res->properties);
            in_res->properties = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
        if (in_res->prop_json != NULL) {
            jd_thingtalk_pal_free(in_res->prop_json);
            in_res->prop_json = NULL;
        }
#endif
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   属性(properties)消息主题 解析属性设置消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_set: 用于存解析结果的 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_prop_set(char *in_json, JDThingTalkProtoPropSet_t *out_set)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_set) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }

    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_set->deviceId == NULL) {
        out_set->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_set->deviceId, pV->valuestring);

    // 解析 timestamp
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
#ifndef JD_THINGTALK_TIMESTAMP_MS
    out_set->timestamp = (TIMESTMAP_T)pV->valueint;
#else
    out_set->timestamp = (TIMESTMAP_T)pV->valuedouble;
#endif

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_set->messageId == NULL) {
        out_set->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_set->messageId, pV->valuestring);

    // 解析 version
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_VERSION);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    out_set->version = pV->valueint;

    // 解析 properties
    pV = cJSON_GetObjectItem(payload, "properties");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
    out_set->prop_json = cJSON_PrintUnformatted(pV);
#endif

    // 遍历解析属性键值对
    int ii = 0;
    cJSON *pSub = NULL;
    out_set->properties = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
                            JD_THINGTALK_PROTO_PROP_MAX_NUM * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out_set->properties, 0, JD_THINGTALK_PROTO_PROP_MAX_NUM * sizeof(JDThingTalkProtoKeyValue_t *));
    pSub = pV->child;
    while (pSub != NULL)
    {
        out_set->properties[ii] = jd_thingtalk_proto_parse_key_value(pSub);
        ii++;
        pSub = pSub->next;
    }
    out_set->prop_num = ii;

    // 删除 payload
    cJSON_Delete(payload);
    
    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   属性(properties)消息主题 打包属性设置响应
 *
 * @param[in] in_res: 待打包的 属性设置响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_set_res(JDThingTalkProtoPropSetRes_t *in_res)
{
    if(NULL == in_res) {
        return NULL;
    }

    // log_info("in_res: %s, %s, %s, %d, %d, %s, %d", in_res->deviceId, in_res->timestamp, in_res->messageId, in_res->version, in_res->code, in_res->message, in_res->prop_num);

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_res->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_res->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_res->timestamp);

    // 添加 messageId
    if (in_res->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_res->messageId);
    } 
    else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 version
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_VERSION, in_res->version);

    // 添加 code
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_CODE, in_res->code);

    // 添加 message (可选)
    if (in_res->message != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG, in_res->message);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG, "");
    }

    // 添加 properties
    int32_t ii;
    cJSON *propArray = NULL;
    log_info("number of properties:%d", in_res->prop_num);
    if ((in_res->prop_num != 0) && (in_res->properties != NULL)) {
        propArray = cJSON_CreateObject();
        if(NULL == propArray){
            cJSON_Delete(root);
            goto RET;
        }
        for (ii = 0; ii < in_res->prop_num; ii++) {
            if (in_res->properties[ii] != NULL) {
                if ((in_res->properties[ii]->key != NULL) && (in_res->properties[ii]->value != NULL)) {
                    cJSON_AddItemToObject(propArray,
                                         in_res->properties[ii]->key,
                                         jd_thingtalk_proto_pack_key_value(in_res->properties[ii]));
                }
            }
        }
        cJSON_AddItemToObject(root, "properties", propArray);
    } else {
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
        if (in_res->prop_json != NULL) {
            cJSON_AddItemToObject(root, "properties", cJSON_Parse(in_res->prop_json));
        } else cJSON_AddStringToObject(root, "properties", "");
#else
        cJSON_AddStringToObject(root, "properties", "");
#endif
    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   属性(properties)消息主题 释放属性获取结构体 成员变量的内存空间
 *
 * @param[in] in_get: 属性获取结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_get(JDThingTalkProtoPropGet_t *in_get)
{
    int32_t ii;
    if (in_get != NULL) {
        if (in_get->deviceId != NULL) {
            jd_thingtalk_pal_free(in_get->deviceId);
            in_get->deviceId = NULL; 
        }
        if (in_get->messageId != NULL) {
            jd_thingtalk_pal_free(in_get->messageId);
            in_get->messageId = NULL; 
        }
        if (in_get->properties != NULL) {
            if (in_get->prop_num != 0) {
                for (ii = 0; ii < in_get->prop_num; ii++) {
                    jd_thingtalk_proto_free_key_value(in_get->properties[ii]);
                }
            }
            jd_thingtalk_pal_free(in_get->properties);
            in_get->properties = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
        if (in_get->prop_json != NULL) {
            jd_thingtalk_pal_free(in_get->prop_json);
            in_get->prop_json = NULL;
        }
#endif
    }
    return 0;
}

/**
 * @brief   属性(properties)消息主题 释放属性获取响应结构体 成员变量的内存空间
 *
 * @param[in] in_res: 属性获取响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_get_res(JDThingTalkProtoPropGetRes_t *in_res)
{
    return jd_thingtalk_proto_free_prop_set_res((JDThingTalkProtoPropSetRes_t *)in_res);
}

/**
 * @brief   属性(properties)消息主题 解析属性获取消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_get: 用于存解析结果的 属性获取结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_prop_get(char *in_json, JDThingTalkProtoPropGet_t *out_get)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_get) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }

    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_get->deviceId == NULL) {
        out_get->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_get->deviceId, pV->valuestring);

    // 解析 timestamp
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
#ifndef JD_THINGTALK_TIMESTAMP_MS
    out_get->timestamp = (TIMESTMAP_T)pV->valueint;
#else
    out_get->timestamp = (TIMESTMAP_T)pV->valuedouble;
#endif

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_get->messageId == NULL) {
        out_get->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_get->messageId, pV->valuestring);

    // 解析 properties
    pV = cJSON_GetObjectItem(payload, "properties");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }

#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
    out_get->prop_json = cJSON_PrintUnformatted(pV);
#endif

    int32_t iSize, iCnt;
    cJSON *pSub;
    iSize = cJSON_GetArraySize(pV);
    out_get->prop_num = iSize;
    if (iSize != 0) {
        out_get->properties = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(iSize * sizeof(JDThingTalkProtoKeyValue_t *));
        jd_thingtalk_pal_memset(out_get->properties, 0, iSize * sizeof(JDThingTalkProtoKeyValue_t *));
        for (iCnt = 0; iCnt < iSize; iCnt++) {
            pSub = cJSON_GetArrayItem(pV, iCnt);
            out_get->properties[iCnt] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
            jd_thingtalk_pal_memset(out_get->properties[iCnt], 0, sizeof(JDThingTalkProtoKeyValue_t));
            out_get->properties[iCnt]->key = (char *) jd_thingtalk_pal_malloc(
                                        (jd_thingtalk_pal_strlen(pSub->valuestring) + 1) * sizeof(char));
            jd_thingtalk_pal_strcpy(out_get->properties[iCnt]->key, pSub->valuestring);
        }
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   属性(properties)消息主题 打包属性获取响应
 *
 * @param[in] in_res: 待打包的 属性获取响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_get_res(JDThingTalkProtoPropGetRes_t *in_res)
{
    return jd_thingtalk_proto_pack_prop_set_res((JDThingTalkProtoPropSetRes_t *)in_res);
}

/**
 * @brief   属性(properties)消息主题 释放属性上报结构体 成员变量的内存空间
 *
 * @param[in] in_post: 属性上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_post(JDThingTalkProtoPropPost_t *in_post)
{
    return jd_thingtalk_proto_free_prop_set((JDThingTalkProtoPropSet_t *)in_post);
}

/**
 * @brief   属性(properties)消息主题 打包属性上报
 *
 * @param[in] in_post: 待打包的 属性上报 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_post(JDThingTalkProtoPropPost_t *in_post)
{
    if(NULL == in_post) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_post->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_post->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_post->timestamp);

    // 添加 messageId
    if (in_post->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_post->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 version
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_VERSION, in_post->version);

    // 添加 properties
    int32_t ii;
    cJSON *propArray = NULL;
    if ((in_post->prop_num != 0) && (in_post->properties != NULL)) {
        propArray = cJSON_CreateObject();
        for (ii = 0; ii < in_post->prop_num; ii++) {
            if (in_post->properties[ii] != NULL) {
                cJSON_AddItemToObject(propArray,
                                     in_post->properties[ii]->key,
                                     jd_thingtalk_proto_pack_key_value(in_post->properties[ii]));
            }
        }
        cJSON_AddItemToObject(root, "properties", propArray);
    } else {
#ifdef JD_THINGTALK_PROTO_PROPS_JSON_STR
        if (in_post->prop_json != NULL) {
            cJSON_AddItemToObject(root, "properties", cJSON_Parse(in_post->prop_json));
        } else cJSON_AddStringToObject(root, "properties", "");
#else
        cJSON_AddStringToObject(root, "properties", "");
#endif
    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

// end of file
