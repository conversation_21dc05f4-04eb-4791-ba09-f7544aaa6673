#include "plane_interface.hpp"
#include <string>

int plane_interface::init(zmq::context_t &ctx)
{
    // plane_cmd_switch_action = new zmq::socket_t {ctx, zmq::socket_type::req};
	// plane_cmd_switch_action -> connect(SERVICE_PLANE_SWITCH_ACTION);

    plane_hmi_cmd = new zmq::socket_t {ctx, zmq::socket_type::pub};
	plane_hmi_cmd -> connect(TOPIC_HMI_LEDCMD);

	// plane_state_switch = new zmq::socket_t {ctx, zmq::socket_type::sub};
	// plane_state_switch -> connect(TOPIC_PLANE_SWITCH_STATE);
	// plane_state_switch -> set(zmq::sockopt::subscribe, "");

    // plane_state_safety_door = new zmq::socket_t {ctx, zmq::socket_type::sub};
	// plane_state_safety_door -> connect(TOPIC_PLANE_DOOR_STATE);
	// plane_state_safety_door -> set(zmq::sockopt::subscribe, "");    

    return 0;
}

// int plane_interface::issue_switch_open(uint32_t dev_id)
// {
//     uint8_t req_msg[switch_action_multiple_size];
// 	pb_ostream_t stream_out;

//     SPDLOG_DEBUG("plane interface issue {} switcher open", dev_id);

//     switch_action_multiple cmd;
//     cmd.sequence = 11;
// 	cmd.action_count = 1;

//     if(dev_id > 64)
//         return -1;

// 	cmd.action[0].switch_id = dev_id;
// 	cmd.action[0].action = switch_action_type_SWITCH_ACTION_OPEN;

//     stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
//     if (!pb_encode(&stream_out, switch_action_multiple_fields, &cmd))
//     {
//         SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
//         return -1;
//     }
//     else
//         plane_cmd_switch_action -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

//     zmq::message_t msg;
//     plane_cmd_switch_action->recv(msg);

//     return 0;
// }

// int plane_interface::issue_switch_close(uint32_t dev_id)
// {
//     uint8_t req_msg[switch_action_multiple_size];
// 	pb_ostream_t stream_out;

//     SPDLOG_DEBUG("plane interface issue {} switcher close", dev_id);

//     switch_action_multiple cmd;
//     cmd.sequence = 11;
// 	cmd.action_count = 1;

//     if(dev_id > 64)
//         return -1;

// 	cmd.action[0].switch_id = dev_id;
// 	cmd.action[0].action = switch_action_type_SWITCH_ACTION_CLOSE;

//     stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
//     if (!pb_encode(&stream_out, switch_action_multiple_fields, &cmd))
//     {
//         SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
//         return -1;
//     }
//     else
//         plane_cmd_switch_action -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

//     zmq::message_t msg;
//     plane_cmd_switch_action->recv(msg);

//     return 0;
// }

// int plane_interface::issue_set_switch_zero(uint32_t dev_id)
// {
//     uint8_t req_msg[switch_action_multiple_size];
// 	pb_ostream_t stream_out;

//     SPDLOG_DEBUG("plane interface issue {} switcher set zero point", dev_id);

//     switch_action_multiple cmd;
//     cmd.sequence = 11;
// 	cmd.action_count = 1;

//     if(dev_id > 64)
//         return -1;

// 	cmd.action[0].switch_id = dev_id;
// 	cmd.action[0].action = switch_action_type_SWITCH_ACTION_SET_ZERO;

//     stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
//     if (!pb_encode(&stream_out, switch_action_multiple_fields, &cmd))
//     {
//         SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
//         return -1;
//     }
//     else
//         plane_cmd_switch_action -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

//     zmq::message_t msg;
//     plane_cmd_switch_action->recv(msg);

//     return 0;
// }

int plane_interface::issue_led_red_on(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue red led on");

    led_cmd cmd;
    cmd.color = 0xff & (0x01 << led_rgb_bits_RED);
    cmd.cmd = led_cmd_type_ON;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_led_green_on(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue green led on");

    led_cmd cmd;
    cmd.color = 0xff & (0x01 << led_rgb_bits_GREEN);
    cmd.cmd = led_cmd_type_ON;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_led_yellow_on(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue yellow led on");

    led_cmd cmd;
    cmd.color = 0xff & (0x01 << led_rgb_bits_YELLOW);
    cmd.cmd = led_cmd_type_ON;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_led_on(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue led on");

    led_cmd cmd;
    cmd.color = 0x0f;
    cmd.cmd = led_cmd_type_ON;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_led_off(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue led off");

    led_cmd cmd;
    cmd.color = 0x0f;
    cmd.cmd = led_cmd_type_OFF;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_buzzer_on(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue buzzer on");

    led_cmd cmd;
    cmd.color = 0xff & (0x01 << led_rgb_bits_BUZZER);
    cmd.cmd = led_cmd_type_ON;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int plane_interface::issue_buzzer_off(void)
{
    uint8_t req_msg[led_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("plane interface issue buzzer off");

    led_cmd cmd;
    cmd.color = 0xff & (0x01 << led_rgb_bits_BUZZER);
    cmd.cmd = led_cmd_type_OFF;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, led_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        plane_hmi_cmd -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
    
    return 0;
}


// int plane_interface::get_switch_state(switch_state_multiple &switch_state)
// {
//     zmq::message_t msg;
//     pb_istream_t stream_in;

//     if (plane_state_switch->recv(msg, zmq::recv_flags::none))
//     {
//         stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
//         if (!pb_decode(&stream_in, switch_state_multiple_fields, &switch_state))
//         {
//             SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
//         }
//         else
//         {
//             //SPDLOG_DEBUG("get {} switchers state, fsm state:{}", switch_state.switches_count, switch_state.state);
// #if 0
//             static int i = 0;
//             if ((i++ & 31) == 0)
//             {
//                 SPDLOG_DEBUG("get {} switchers state, fsm state:{}", switch_state.switches_count, switch_state.state);
//                 for(int i = 0; i < switch_state.switches_count; i++)
//                 {
//                     auto sw = switch_state.switches[i];
//                     SPDLOG_DEBUG("switcher {} state: [{}]-[{}]-[{}]", sw.switch_id, sw.position, sw.state, sw.encoder_value);         
//                 }
//             }
// #endif

//             return 1;
//         }
//     }

//     return 0;
// }

// int plane_interface::get_emerg_dev_state(plane_event_multiple &emerg_event)
// {
//     zmq::message_t msg;
//     pb_istream_t stream_in;

//     if (plane_state_safety_door->recv(msg, zmq::recv_flags::none))
//     {
//         stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
//         if (!pb_decode(&stream_in, plane_event_multiple_fields, &emerg_event))
//         {
//             SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
//         }
//         else
//         {
//             for (int i = 0; i < emerg_event.events_count; i++)
//             {
//                 if (emerg_event.events[i].evt_type == plane_evt_type_DOOR_OPENED || emerg_event.events[i].evt_type == plane_evt_type_KEY_DOWN)
//                     SPDLOG_DEBUG("plane dev {}, id: {} emerg triggered", emerg_event.events[i].event_id, emerg_event.events[i].dev_id);
//                 else if (emerg_event.events[i].evt_type == plane_evt_type_DOOR_CLOSEED || emerg_event.events[i].evt_type == plane_evt_type_KEY_UP)
//                     SPDLOG_DEBUG("plane dev {}, id: {} emerg released", emerg_event.events[i].event_id, emerg_event.events[i].dev_id);
//             }

//             return 1;
//         }
//     }

//     return 0;
// }
