syntax = "proto3";
option optimize_for = LITE_RUNTIME;

enum key_id
{
	KEY_RESERVE = 0;
	KEY_START = 1;
	KEY_STOP= 2;
	KEY_SLEEP = 3;
	KEY_RESET = 4;
	KEY_EMERG = 5;
	KEY_FEEDER_ROLLBACK = 6;
};

//for safety door down means open, up means close
enum key_evt_type
{
	EVENT_RESERVE = 0;
	EVENT_PRESSED = 1;
	EVENT_TRIGGER = 2;	
	EVENT_RELEASE = 3;
};

enum key_evt_src
{
	RESERVE = 0;
	SAFE_DOOR_FRONT = 1;
	SAFE_DOOR_BACK = 2;
	SAFE_DOOR_MID = 3;
	CONTROL_CABINET = 4;
	FEEDER_1 = 5;
	FEEDER_2 = 6;
	FEEDER_3 = 7;
	FEEDER_4 = 8;
	FEEDER_5 = 9;
	FEEDER_6 = 10;
};

message key_event
{
	key_evt_src dev_id = 1;
	key_id key = 2;
	key_evt_type evt_type = 3;
};




enum led_rgb_bits
{
	BUZZER = 0;

	RED = 1;
	GREEN = 2;
	YELLOW = 3;	//系统塔灯是红绿黄，而非RGB
};


enum led_cmd_type
{
	ON = 0;
	OFF = 1;
	FLICKER = 2;
};

message led_cmd
{
   	key_evt_src id = 1;
    uint32 color = 2;
	led_cmd_type cmd = 3;
    uint32 param = 4;
}
