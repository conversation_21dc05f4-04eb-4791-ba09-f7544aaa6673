# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named autoswap_agent

# Build rule for target.
autoswap_agent: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 autoswap_agent
.PHONY : autoswap_agent

# fast build rule for target.
autoswap_agent/fast:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/build
.PHONY : autoswap_agent/fast

#=============================================================================
# Target rules for targets named nanopb

# Build rule for target.
nanopb: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 nanopb
.PHONY : nanopb

# fast build rule for target.
nanopb/fast:
	$(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/build
.PHONY : nanopb/fast

#=============================================================================
# Target rules for targets named idl

# Build rule for target.
idl: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 idl
.PHONY : idl

# fast build rule for target.
idl/fast:
	$(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/build
.PHONY : idl/fast

#=============================================================================
# Target rules for targets named lib_threadpool

# Build rule for target.
lib_threadpool: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_threadpool
.PHONY : lib_threadpool

# fast build rule for target.
lib_threadpool/fast:
	$(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/build
.PHONY : lib_threadpool/fast

#=============================================================================
# Target rules for targets named lib_net

# Build rule for target.
lib_net: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_net
.PHONY : lib_net

# fast build rule for target.
lib_net/fast:
	$(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/build
.PHONY : lib_net/fast

#=============================================================================
# Target rules for targets named lib_protocol

# Build rule for target.
lib_protocol: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_protocol
.PHONY : lib_protocol

# fast build rule for target.
lib_protocol/fast:
	$(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/build
.PHONY : lib_protocol/fast

#=============================================================================
# Target rules for targets named lib_swap_manage

# Build rule for target.
lib_swap_manage: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_swap_manage
.PHONY : lib_swap_manage

# fast build rule for target.
lib_swap_manage/fast:
	$(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/build
.PHONY : lib_swap_manage/fast

#=============================================================================
# Target rules for targets named lib_msg

# Build rule for target.
lib_msg: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_msg
.PHONY : lib_msg

# fast build rule for target.
lib_msg/fast:
	$(MAKE) -f scheduler_msg/CMakeFiles/lib_msg.dir/build.make scheduler_msg/CMakeFiles/lib_msg.dir/build
.PHONY : lib_msg/fast

#=============================================================================
# Target rules for targets named lib_fsm_manager

# Build rule for target.
lib_fsm_manager: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lib_fsm_manager
.PHONY : lib_fsm_manager

# fast build rule for target.
lib_fsm_manager/fast:
	$(MAKE) -f fsm_manager/CMakeFiles/lib_fsm_manager.dir/build.make fsm_manager/CMakeFiles/lib_fsm_manager.dir/build
.PHONY : lib_fsm_manager/fast

exception/dev_except.o: exception/dev_except.cpp.o

.PHONY : exception/dev_except.o

# target to build an object file
exception/dev_except.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o
.PHONY : exception/dev_except.cpp.o

exception/dev_except.i: exception/dev_except.cpp.i

.PHONY : exception/dev_except.i

# target to preprocess a source file
exception/dev_except.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.i
.PHONY : exception/dev_except.cpp.i

exception/dev_except.s: exception/dev_except.cpp.s

.PHONY : exception/dev_except.s

# target to generate assembly for a file
exception/dev_except.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.s
.PHONY : exception/dev_except.cpp.s

fsm_manager/fsm_manager.o: fsm_manager/fsm_manager.cpp.o

.PHONY : fsm_manager/fsm_manager.o

# target to build an object file
fsm_manager/fsm_manager.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o
.PHONY : fsm_manager/fsm_manager.cpp.o

fsm_manager/fsm_manager.i: fsm_manager/fsm_manager.cpp.i

.PHONY : fsm_manager/fsm_manager.i

# target to preprocess a source file
fsm_manager/fsm_manager.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.i
.PHONY : fsm_manager/fsm_manager.cpp.i

fsm_manager/fsm_manager.s: fsm_manager/fsm_manager.cpp.s

.PHONY : fsm_manager/fsm_manager.s

# target to generate assembly for a file
fsm_manager/fsm_manager.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.s
.PHONY : fsm_manager/fsm_manager.cpp.s

main.o: main.cpp.o

.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i

.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s

.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/main.cpp.s
.PHONY : main.cpp.s

multi_swap_manager.o: multi_swap_manager.cpp.o

.PHONY : multi_swap_manager.o

# target to build an object file
multi_swap_manager.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o
.PHONY : multi_swap_manager.cpp.o

multi_swap_manager.i: multi_swap_manager.cpp.i

.PHONY : multi_swap_manager.i

# target to preprocess a source file
multi_swap_manager.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.i
.PHONY : multi_swap_manager.cpp.i

multi_swap_manager.s: multi_swap_manager.cpp.s

.PHONY : multi_swap_manager.s

# target to generate assembly for a file
multi_swap_manager.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.s
.PHONY : multi_swap_manager.cpp.s

net/epoll_poller.o: net/epoll_poller.cpp.o

.PHONY : net/epoll_poller.o

# target to build an object file
net/epoll_poller.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o
.PHONY : net/epoll_poller.cpp.o

net/epoll_poller.i: net/epoll_poller.cpp.i

.PHONY : net/epoll_poller.i

# target to preprocess a source file
net/epoll_poller.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.i
.PHONY : net/epoll_poller.cpp.i

net/epoll_poller.s: net/epoll_poller.cpp.s

.PHONY : net/epoll_poller.s

# target to generate assembly for a file
net/epoll_poller.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.s
.PHONY : net/epoll_poller.cpp.s

net/tcp_socket.o: net/tcp_socket.cpp.o

.PHONY : net/tcp_socket.o

# target to build an object file
net/tcp_socket.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o
.PHONY : net/tcp_socket.cpp.o

net/tcp_socket.i: net/tcp_socket.cpp.i

.PHONY : net/tcp_socket.i

# target to preprocess a source file
net/tcp_socket.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.i
.PHONY : net/tcp_socket.cpp.i

net/tcp_socket.s: net/tcp_socket.cpp.s

.PHONY : net/tcp_socket.s

# target to generate assembly for a file
net/tcp_socket.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.s
.PHONY : net/tcp_socket.cpp.s

net/udp_socket.o: net/udp_socket.cpp.o

.PHONY : net/udp_socket.o

# target to build an object file
net/udp_socket.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o
.PHONY : net/udp_socket.cpp.o

net/udp_socket.i: net/udp_socket.cpp.i

.PHONY : net/udp_socket.i

# target to preprocess a source file
net/udp_socket.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.i
.PHONY : net/udp_socket.cpp.i

net/udp_socket.s: net/udp_socket.cpp.s

.PHONY : net/udp_socket.s

# target to generate assembly for a file
net/udp_socket.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.s
.PHONY : net/udp_socket.cpp.s

protocol/train_protocol.o: protocol/train_protocol.cpp.o

.PHONY : protocol/train_protocol.o

# target to build an object file
protocol/train_protocol.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o
.PHONY : protocol/train_protocol.cpp.o

protocol/train_protocol.i: protocol/train_protocol.cpp.i

.PHONY : protocol/train_protocol.i

# target to preprocess a source file
protocol/train_protocol.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.i
.PHONY : protocol/train_protocol.cpp.i

protocol/train_protocol.s: protocol/train_protocol.cpp.s

.PHONY : protocol/train_protocol.s

# target to generate assembly for a file
protocol/train_protocol.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.s
.PHONY : protocol/train_protocol.cpp.s

readme.o: readme.c.o

.PHONY : readme.o

# target to build an object file
readme.c.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/readme.c.o
.PHONY : readme.c.o

readme.i: readme.c.i

.PHONY : readme.i

# target to preprocess a source file
readme.c.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/readme.c.i
.PHONY : readme.c.i

readme.s: readme.c.s

.PHONY : readme.s

# target to generate assembly for a file
readme.c.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/readme.c.s
.PHONY : readme.c.s

scheduler_msg/scheduler_msg.o: scheduler_msg/scheduler_msg.cpp.o

.PHONY : scheduler_msg/scheduler_msg.o

# target to build an object file
scheduler_msg/scheduler_msg.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o
.PHONY : scheduler_msg/scheduler_msg.cpp.o

scheduler_msg/scheduler_msg.i: scheduler_msg/scheduler_msg.cpp.i

.PHONY : scheduler_msg/scheduler_msg.i

# target to preprocess a source file
scheduler_msg/scheduler_msg.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.i
.PHONY : scheduler_msg/scheduler_msg.cpp.i

scheduler_msg/scheduler_msg.s: scheduler_msg/scheduler_msg.cpp.s

.PHONY : scheduler_msg/scheduler_msg.s

# target to generate assembly for a file
scheduler_msg/scheduler_msg.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.s
.PHONY : scheduler_msg/scheduler_msg.cpp.s

swap_agent_config.o: swap_agent_config.cpp.o

.PHONY : swap_agent_config.o

# target to build an object file
swap_agent_config.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o
.PHONY : swap_agent_config.cpp.o

swap_agent_config.i: swap_agent_config.cpp.i

.PHONY : swap_agent_config.i

# target to preprocess a source file
swap_agent_config.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.i
.PHONY : swap_agent_config.cpp.i

swap_agent_config.s: swap_agent_config.cpp.s

.PHONY : swap_agent_config.s

# target to generate assembly for a file
swap_agent_config.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.s
.PHONY : swap_agent_config.cpp.s

swap_manage/swap_list.o: swap_manage/swap_list.cpp.o

.PHONY : swap_manage/swap_list.o

# target to build an object file
swap_manage/swap_list.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o
.PHONY : swap_manage/swap_list.cpp.o

swap_manage/swap_list.i: swap_manage/swap_list.cpp.i

.PHONY : swap_manage/swap_list.i

# target to preprocess a source file
swap_manage/swap_list.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.i
.PHONY : swap_manage/swap_list.cpp.i

swap_manage/swap_list.s: swap_manage/swap_list.cpp.s

.PHONY : swap_manage/swap_list.s

# target to generate assembly for a file
swap_manage/swap_list.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.s
.PHONY : swap_manage/swap_list.cpp.s

swap_manage/swap_manage.o: swap_manage/swap_manage.cpp.o

.PHONY : swap_manage/swap_manage.o

# target to build an object file
swap_manage/swap_manage.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o
.PHONY : swap_manage/swap_manage.cpp.o

swap_manage/swap_manage.i: swap_manage/swap_manage.cpp.i

.PHONY : swap_manage/swap_manage.i

# target to preprocess a source file
swap_manage/swap_manage.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.i
.PHONY : swap_manage/swap_manage.cpp.i

swap_manage/swap_manage.s: swap_manage/swap_manage.cpp.s

.PHONY : swap_manage/swap_manage.s

# target to generate assembly for a file
swap_manage/swap_manage.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.s
.PHONY : swap_manage/swap_manage.cpp.s

threadpool/condition.o: threadpool/condition.cpp.o

.PHONY : threadpool/condition.o

# target to build an object file
threadpool/condition.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o
.PHONY : threadpool/condition.cpp.o

threadpool/condition.i: threadpool/condition.cpp.i

.PHONY : threadpool/condition.i

# target to preprocess a source file
threadpool/condition.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.i
.PHONY : threadpool/condition.cpp.i

threadpool/condition.s: threadpool/condition.cpp.s

.PHONY : threadpool/condition.s

# target to generate assembly for a file
threadpool/condition.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.s
.PHONY : threadpool/condition.cpp.s

threadpool/thp_mutex.o: threadpool/thp_mutex.cpp.o

.PHONY : threadpool/thp_mutex.o

# target to build an object file
threadpool/thp_mutex.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o
.PHONY : threadpool/thp_mutex.cpp.o

threadpool/thp_mutex.i: threadpool/thp_mutex.cpp.i

.PHONY : threadpool/thp_mutex.i

# target to preprocess a source file
threadpool/thp_mutex.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.i
.PHONY : threadpool/thp_mutex.cpp.i

threadpool/thp_mutex.s: threadpool/thp_mutex.cpp.s

.PHONY : threadpool/thp_mutex.s

# target to generate assembly for a file
threadpool/thp_mutex.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.s
.PHONY : threadpool/thp_mutex.cpp.s

threadpool/thread_pool.o: threadpool/thread_pool.cpp.o

.PHONY : threadpool/thread_pool.o

# target to build an object file
threadpool/thread_pool.cpp.o:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o
.PHONY : threadpool/thread_pool.cpp.o

threadpool/thread_pool.i: threadpool/thread_pool.cpp.i

.PHONY : threadpool/thread_pool.i

# target to preprocess a source file
threadpool/thread_pool.cpp.i:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.i
.PHONY : threadpool/thread_pool.cpp.i

threadpool/thread_pool.s: threadpool/thread_pool.cpp.s

.PHONY : threadpool/thread_pool.s

# target to generate assembly for a file
threadpool/thread_pool.cpp.s:
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.s
.PHONY : threadpool/thread_pool.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... autoswap_agent"
	@echo "... nanopb"
	@echo "... idl"
	@echo "... lib_threadpool"
	@echo "... lib_net"
	@echo "... lib_protocol"
	@echo "... lib_swap_manage"
	@echo "... lib_msg"
	@echo "... lib_fsm_manager"
	@echo "... exception/dev_except.o"
	@echo "... exception/dev_except.i"
	@echo "... exception/dev_except.s"
	@echo "... fsm_manager/fsm_manager.o"
	@echo "... fsm_manager/fsm_manager.i"
	@echo "... fsm_manager/fsm_manager.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... multi_swap_manager.o"
	@echo "... multi_swap_manager.i"
	@echo "... multi_swap_manager.s"
	@echo "... net/epoll_poller.o"
	@echo "... net/epoll_poller.i"
	@echo "... net/epoll_poller.s"
	@echo "... net/tcp_socket.o"
	@echo "... net/tcp_socket.i"
	@echo "... net/tcp_socket.s"
	@echo "... net/udp_socket.o"
	@echo "... net/udp_socket.i"
	@echo "... net/udp_socket.s"
	@echo "... protocol/train_protocol.o"
	@echo "... protocol/train_protocol.i"
	@echo "... protocol/train_protocol.s"
	@echo "... readme.o"
	@echo "... readme.i"
	@echo "... readme.s"
	@echo "... scheduler_msg/scheduler_msg.o"
	@echo "... scheduler_msg/scheduler_msg.i"
	@echo "... scheduler_msg/scheduler_msg.s"
	@echo "... swap_agent_config.o"
	@echo "... swap_agent_config.i"
	@echo "... swap_agent_config.s"
	@echo "... swap_manage/swap_list.o"
	@echo "... swap_manage/swap_list.i"
	@echo "... swap_manage/swap_list.s"
	@echo "... swap_manage/swap_manage.o"
	@echo "... swap_manage/swap_manage.i"
	@echo "... swap_manage/swap_manage.s"
	@echo "... threadpool/condition.o"
	@echo "... threadpool/condition.i"
	@echo "... threadpool/condition.s"
	@echo "... threadpool/thp_mutex.o"
	@echo "... threadpool/thp_mutex.i"
	@echo "... threadpool/thp_mutex.s"
	@echo "... threadpool/thread_pool.o"
	@echo "... threadpool/thread_pool.i"
	@echo "... threadpool/thread_pool.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

