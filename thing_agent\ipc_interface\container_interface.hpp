#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/container_interface.pb.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/sys_interface.pb.h"
// #include "share/pb/idl/plane_slot.pb.h"
#include "setting/setting.hpp"


class container_interface
{
public:

    int init(zmq::context_t &ctx);

    int get_rfid_state(box_info_multiple &containers_state);          //格口状态是否改变的判断在格口状态发送节点判断，此处阻塞接收，不再做缓存和是否改变的判断

    int get_shelf_lock_state(shelves_state &shelf_state);

    int issue_container_color_control(led_info &led_cmd, bool need_log = true);

    int get_seal_state(container_seal_state_single &seal_state);

    int issue_container_seal_control(const uint32_t &id);
    int issue_container_seal_cmd(container_seal_state_single &state);

    int get_satr_state(slot_state &box_state);

    int issue_shelf_lock(uint32_t shelf_no);
    int issue_shelf_unlock(uint32_t shelf_no);

    static container_interface *get_instance(void)
    {
        static container_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *container_state_recver;      //发送格口灯控
    zmq::socket_t *container_color_sender;      //格口上报状态
    zmq::socket_t *box_state_recver;            //箱满空状态接收
    zmq::socket_t *container_seal_state_recver; //封箱状态接收
    zmq::socket_t *container_seal_cmd_sender;   //封箱命令下发
    zmq::socket_t *container_thingtalk_seal_cmd;          //物控封箱命令下发
    zmq::socket_t *container_shelf_cmd;
    zmq::socket_t *shelf_lock_state_recv;            //接收货架锁状态

};
