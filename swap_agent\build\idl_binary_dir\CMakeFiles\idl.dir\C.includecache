#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/ack.pb.c
ack.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/ack.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/ack.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange.pb.c
auto_exchange.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange.pb.h
pb.h
-
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_info.pb.c
auto_exchange_info.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_info.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_info.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_map.pb.c
auto_exchange_map.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_map.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/auto_exchange_map.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/container_interface.pb.c
container_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/container_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/container_interface.pb.h
pb.h
-
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_map.pb.c
data_map.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_map.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_map.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_request.pb.c
data_request.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_request.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/data_request.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/dev_hmi.pb.c
dev_hmi.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/dev_hmi.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/dev_hmi.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/exception.pb.c
exception.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/exception.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/exception.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/feeder_interface.pb.c
feeder_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/feeder_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/feeder_interface.pb.h
pb.h
-
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/scheduler_interface.pb.c
scheduler_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/scheduler_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/scheduler_interface.pb.h
pb.h
-
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.c
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/task.pb.c
task.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/task.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/task.pb.h
pb.h
-

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_info.pb.c
train_info.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_info.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_info.pb.h
pb.h
-
train_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_interface.pb.c
train_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/train_interface.pb.h
pb.h
-
sys_interface.pb.h
/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/sys_interface.pb.h

/home/<USER>/myfile/project/auto_replace_box/swap_agent/../share/pb/nanopb/pb.h
stdint.h
-
stddef.h
-
stdbool.h
-
string.h
-
limits.h
-
stdlib.h
-
avr/pgmspace.h
-

