#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/ack.pb.h"
// #include "share/pb/idl/plane_switch_action.pb.h"
// #include "share/pb/idl/plane_switch_state.pb.h"
// #include "share/pb/idl/plane_slot.pb.h"
// #include "share/pb/idl/plane_safe_door.pb.h"
#include "share/pb/idl/dev_hmi.pb.h"

class plane_interface
{
public:

    int init(zmq::context_t &ctx);

    // int issue_switch_open(uint32_t dev_id);

    // int issue_switch_close(uint32_t dev_id);

    // int issue_set_switch_zero(uint32_t dev_id);

    int issue_led_red_on();

    int issue_led_green_on();

    int issue_led_yellow_on();

    int issue_led_on(); //控制塔灯所有灯亮

    int issue_led_off();

    int issue_buzzer_on();

    int issue_buzzer_off();


    // int get_switch_state(switch_state_multiple &switch_state);
    
    // int get_emerg_dev_state(plane_event_multiple &emerg_event);

    static plane_interface *get_instance(void)
    {
        static plane_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *plane_cmd_switch_action;
    zmq::socket_t *plane_hmi_cmd;

    zmq::socket_t *plane_state_switch;
    zmq::socket_t *plane_state_safety_door;
};
