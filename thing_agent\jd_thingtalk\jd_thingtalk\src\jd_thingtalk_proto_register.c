/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 自动注册(register)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   自动注册(register) 释放请求结构体 成员变量的内存空间
 *
 * @param[in] in_req: 自动请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_reg_req(JDThingTalkProtoRegReq_t *in_req)
{
    int32_t ii, jj;
    if (in_req != NULL) {
        if (in_req->deviceId != NULL) {
            jd_thingtalk_pal_free(in_req->deviceId);
            in_req->deviceId = NULL;
        }
        if (in_req->messageId != NULL) {
            jd_thingtalk_pal_free(in_req->messageId);
            in_req->messageId = NULL;
        }
        if (in_req->devices != NULL) {
            if (in_req->dev_num > 0) {
                for (ii = 0; ii < in_req->dev_num; ii++) {
                    if (in_req->devices[ii] != NULL) {
                        if (in_req->devices[ii]->hardwareId != NULL) {
                            jd_thingtalk_pal_free(in_req->devices[ii]->hardwareId);
                            in_req->devices[ii]->hardwareId = NULL;
                        }
                        if (in_req->devices[ii]->vendor != NULL) {
                            jd_thingtalk_pal_free(in_req->devices[ii]->vendor);
                            in_req->devices[ii]->vendor = NULL;
                        }
                        if (in_req->devices[ii]->protocol != NULL) {
                            jd_thingtalk_pal_free(in_req->devices[ii]->protocol);
                            in_req->devices[ii]->protocol = NULL;
                        }
                        if (in_req->devices[ii]->product != NULL) {
                            jd_thingtalk_pal_free(in_req->devices[ii]->product);
                            in_req->devices[ii]->product = NULL;
                        }
                        if (in_req->devices[ii]->model != NULL) {
                            jd_thingtalk_pal_free(in_req->devices[ii]->model);
                            in_req->devices[ii]->model = NULL;
                        }
                        if (in_req->devices[ii]->extensions != NULL) {
                            if (in_req->devices[ii]->ext_num > 0) {
                                for (jj = 0; jj < in_req->devices[ii]->ext_num; jj++) {
                                    if (in_req->devices[ii]->extensions[jj]->key != NULL) {
                                        jd_thingtalk_pal_free(in_req->devices[ii]->extensions[jj]->key);
                                        in_req->devices[ii]->extensions[jj]->key = NULL;
                                    }
                                    if (in_req->devices[ii]->extensions[jj]->value != NULL) {
                                        jd_thingtalk_pal_free(in_req->devices[ii]->extensions[jj]->value);
                                        in_req->devices[ii]->extensions[jj]->value = NULL;
                                    }
                                    if (in_req->devices[ii]->extensions[jj]->desc != NULL) {
                                        jd_thingtalk_pal_free(in_req->devices[ii]->extensions[jj]->desc);
                                        in_req->devices[ii]->extensions[jj]->desc = NULL;
                                    }
                                }
                            }
                            jd_thingtalk_pal_free(in_req->devices[ii]->extensions);
                            in_req->devices[ii]->extensions = NULL;
                        }
                    }
                    jd_thingtalk_pal_free(in_req->devices[ii]);
                    in_req->devices[ii] = NULL; 
                }
            }
            jd_thingtalk_pal_free(in_req->devices);
            in_req->devices = NULL;
        }
        // TODO 
    }

    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   自动注册(register) 打包自动请求结构体
 *
 * @param[in] in_req: 自动请求结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
char *jd_thingtalk_proto_pack_reg_req(JDThingTalkProtoRegReq_t *in_req)
{
    if(NULL == in_req) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_req->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_req->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_req->timestamp);

    // 添加 messageId
    if (in_req->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_req->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 devices
    int32_t ii, jj;
    cJSON *devArray = NULL;
    cJSON *devObj = NULL;
    cJSON *extArray = NULL;
    cJSON *extObj = NULL;
    if (in_req->dev_num != 0) {
        devArray = cJSON_CreateArray();
        for (ii = 0; ii < in_req->dev_num; ii++) {
            devObj = cJSON_CreateObject();
            if (devObj == NULL) {
                cJSON_Delete(devArray);
                cJSON_Delete(root);
                goto RET;
            }
            // 添加 设备 hardware id
            if (in_req->devices[ii]->hardwareId != NULL) {
                cJSON_AddStringToObject(devObj, "hardware-id", in_req->devices[ii]->hardwareId);
            } else {
                cJSON_AddStringToObject(devObj, "hardware-id", "");
            }

            // 添加 设备 vendor
            if (in_req->devices[ii]->vendor != NULL) {
                cJSON_AddStringToObject(devObj, "vendor", in_req->devices[ii]->vendor);
            } else {
                cJSON_AddStringToObject(devObj, "vendor", "");
            }

            // 添加 设备 protocol
            if (in_req->devices[ii]->protocol != NULL) {
                cJSON_AddStringToObject(devObj, "protocol", in_req->devices[ii]->protocol);
            } else {
                cJSON_AddStringToObject(devObj, "protocol", "");
            }

            // 添加 设备 product
            if (in_req->devices[ii]->product != NULL) {
                cJSON_AddStringToObject(devObj, "product", in_req->devices[ii]->product);
            } else {
                cJSON_AddStringToObject(devObj, "product", "");
            }

            // 添加 设备 model
            if (in_req->devices[ii]->model != NULL) {
                cJSON_AddStringToObject(devObj, "model", in_req->devices[ii]->model);
            } else {
                cJSON_AddStringToObject(devObj, "model", "");
            }

            // 添加 设备 extensions
            if (in_req->devices[ii]->ext_num != 0) {
                if (in_req->devices[ii]->extensions == NULL) {
                    continue;
                }
                extArray = cJSON_CreateArray();
                if (extArray == NULL) {
                    cJSON_Delete(devObj);
                    cJSON_Delete(devArray);
                    cJSON_Delete(root);
                    goto RET;
                }

                for (jj = 0; jj < in_req->devices[ii]->ext_num; jj++) {
                    extObj = cJSON_CreateObject();
                    if (extObj == NULL) {
                        cJSON_Delete(extArray);
                        cJSON_Delete(devObj);
                        cJSON_Delete(devArray);
                        cJSON_Delete(root);
                        goto RET;
                    }
                    if (in_req->devices[ii]->extensions[jj] != NULL) {
                        // 添加 设备 extensions 中的 key
                        if (in_req->devices[ii]->extensions[jj]->key != NULL) {
                            cJSON_AddStringToObject(extObj, "key", in_req->devices[ii]->extensions[jj]->key);
                        } else {
                            cJSON_AddStringToObject(extObj, "key", "");
                        }

                        // 添加 设备 extensions 中的 value
                        if (in_req->devices[ii]->extensions[jj]->value != NULL) {
                            cJSON_AddStringToObject(extObj, "value", in_req->devices[ii]->extensions[jj]->value);
                        } else {
                            cJSON_AddStringToObject(extObj, "value", "");
                        }

                        // 添加 设备 extensions 中的 desc
                        if (in_req->devices[ii]->extensions[jj]->desc != NULL) {
                            cJSON_AddStringToObject(extObj, "desc", in_req->devices[ii]->extensions[jj]->desc);
                        } else {
                            cJSON_AddStringToObject(extObj, "desc", "");
                        }

                        // 添加元素到 extensions 数组
                        cJSON_AddItemToArray(extArray, extObj);
                    }
                }

                // 添加 extensions 数组到 devices
                cJSON_AddItemToObject(devObj, "extensions", extArray);
            }

            // 添加元素到devices数组
            cJSON_AddItemToArray(devArray, devObj);
        }
        cJSON_AddItemToObject(root, "devices", devArray);
    } else {
        cJSON_AddStringToObject(root, "devices", "");
    }

    // 添加 certType
    cJSON_AddStringToObject(root, "cerType", in_req->cerType);

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   自动注册(register) 释放请求响应结构体 成员变量的内存空间
 *
 * @param[in] in_res: 自动请求响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_reg_req_res(JDThingTalkProtoRegReqRes_t *in_res)
{
    int32_t ii, jj;
    if (in_res != NULL) {
        if (in_res->deviceId != NULL) {
            jd_thingtalk_pal_free(in_res->deviceId);
            in_res->deviceId = NULL;
        }
        if (in_res->messageId != NULL) {
            jd_thingtalk_pal_free(in_res->messageId);
            in_res->messageId = NULL;
        }
        if (in_res->message != NULL) {
            jd_thingtalk_pal_free(in_res->message);
            in_res->message = NULL;
        }
        if (in_res->devices != NULL) {
            if (in_res->dev_num > 0) {
                for (ii = 0; ii < in_res->dev_num; ii++) {
                    if (in_res->devices[ii] != NULL) {
                        if (in_res->devices[ii]->deviceId != NULL) {
                            jd_thingtalk_pal_free(in_res->devices[ii]->deviceId);
                            in_res->devices[ii]->deviceId = NULL;
                        }
                        if (in_res->devices[ii]->hardwareId != NULL) {
                            jd_thingtalk_pal_free(in_res->devices[ii]->hardwareId);
                            in_res->devices[ii]->hardwareId = NULL;
                        }
                        if (in_res->devices[ii]->config != NULL) {
                            if (in_res->devices[ii]->cfg_num > 0) {
                                for (jj = 0; jj < in_res->devices[ii]->cfg_num; jj++) {
                                    if (in_res->devices[ii]->config[jj] != NULL) {
                                        if (in_res->devices[ii]->config[jj]->name != NULL) {
                                            jd_thingtalk_pal_free(in_res->devices[ii]->config[jj]->name);
                                            in_res->devices[ii]->config[jj]->name = NULL;
                                        }
                                        if (in_res->devices[ii]->config[jj]->type != NULL) {
                                            jd_thingtalk_pal_free(in_res->devices[ii]->config[jj]->type);
                                            in_res->devices[ii]->config[jj]->type = NULL;
                                        }
                                        if (in_res->devices[ii]->config[jj]->content != NULL) {
                                            jd_thingtalk_pal_free(in_res->devices[ii]->config[jj]->content);
                                            in_res->devices[ii]->config[jj]->content = NULL;
                                        }
                                        jd_thingtalk_pal_free(in_res->devices[ii]->config[jj]);
                                        in_res->devices[ii]->config[jj] = NULL;
                                    }
                                }
                            }
                            jd_thingtalk_pal_free(in_res->devices[ii]->config);
                            in_res->devices[ii]->config = NULL;
                        }
                        jd_thingtalk_pal_free(in_res->devices[ii]);
                        in_res->devices[ii] = NULL;
                    }
                }
            }
            jd_thingtalk_pal_free(in_res->devices);
            in_res->devices = NULL;
        }
        // TODO
    }
    return JD_THINGTALK_RET_SUCCESS;
}


/**
 * @brief   自动注册(register) 解析自动注册请求效应消息体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res: 自动请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_parse_reg_req_res(char *in_json, JDThingTalkProtoRegReqRes_t *out_res)
{
    int ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_res) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }

    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->deviceId == NULL) {
        out_res->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->deviceId, pV->valuestring);

    // 解析 timestamp
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
#ifndef JD_THINGTALK_TIMESTAMP_MS
    out_res->timestamp = (TIMESTMAP_T)pV->valueint;
#else
    out_res->timestamp = (TIMESTMAP_T)pV->valuedouble;
#endif

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->messageId == NULL) {
        out_res->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->messageId, pV->valuestring);

    // 解析 code
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_CODE);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    out_res->code = pV->valueint;

    // 解析 message (可选)
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG);
    if (NULL != pV) {
        if (out_res->message == NULL) {
            out_res->message = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
        }
        jd_thingtalk_pal_strcpy(out_res->message, pV->valuestring);
    }

    // 解析 devices 数组
    pV = cJSON_GetObjectItem(payload, "devices");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    int32_t iSize, iCnt, ii;
    cJSON *pSub, *pDev, *pCfg;
    iSize = cJSON_GetArraySize(pV);
    out_res->dev_num = iSize;
    if (iSize != 0) {
        out_res->devices = (JDThingTalkProtoRegReqResDev_t **) jd_thingtalk_pal_malloc(iSize * sizeof(JDThingTalkProtoRegReqResDev_t *));
        jd_thingtalk_pal_memset(out_res->devices, 0, iSize * sizeof(JDThingTalkProtoRegReqResDev_t *));
        for (iCnt = 0; iCnt < iSize; iCnt++) {
            pSub = cJSON_GetArrayItem(pV, iCnt);
            out_res->devices[iCnt] = (JDThingTalkProtoRegReqResDev_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoRegReqResDev_t));
            jd_thingtalk_pal_memset(out_res->devices[iCnt], 0, sizeof(JDThingTalkProtoRegReqResDev_t));

            // 解析 devices 数组元素的 deviceId
            pDev = cJSON_GetObjectItem(pSub, "deviceId");
            if (pDev == NULL) {
                continue;
            }
            if (out_res->devices[iCnt]->deviceId == NULL) {
                out_res->devices[iCnt]->deviceId = (char *) jd_thingtalk_pal_malloc(
                        (jd_thingtalk_pal_strlen(pDev->valuestring) + 1) * sizeof(char));
            }
            jd_thingtalk_pal_strcpy(out_res->devices[iCnt]->deviceId, pDev->valuestring);

            // 解析 devices 数组元素的 hardwareId
            pDev = cJSON_GetObjectItem(pSub, "hardware-id");
            if (pDev == NULL) {
                continue;
            }
            if (out_res->devices[iCnt]->hardwareId == NULL) {
                out_res->devices[iCnt]->hardwareId = (char *) jd_thingtalk_pal_malloc(
                        (jd_thingtalk_pal_strlen(pDev->valuestring) + 1) * sizeof(char));
            }
            jd_thingtalk_pal_strcpy(out_res->devices[iCnt]->hardwareId, pDev->valuestring);

            // 解析 devices 数组元素的 config 数组
            pDev = cJSON_GetObjectItem(pSub, "config");
            if (pDev == NULL) {
                continue;
            }
            out_res->devices[iCnt]->cfg_num = cJSON_GetArraySize(pDev);
            if (out_res->devices[iCnt]->cfg_num == 0) {
                continue;
            }
            out_res->devices[iCnt]->config = (JDThingTalkProtoRegReqResDevCfg_t **) jd_thingtalk_pal_malloc(
                out_res->devices[iCnt]->cfg_num * sizeof(JDThingTalkProtoRegReqResDevCfg_t *));
            jd_thingtalk_pal_memset(out_res->devices[iCnt]->config, 0,
                    out_res->devices[iCnt]->cfg_num * sizeof(JDThingTalkProtoRegReqResDevCfg_t *));
            for (ii = 0; ii < out_res->devices[iCnt]->cfg_num; ii++) {
                pCfg = cJSON_GetArrayItem(pDev, ii);
                if (pCfg == NULL) {
                    continue;
                }
                out_res->devices[iCnt]->config[ii] = (JDThingTalkProtoRegReqResDevCfg_t *) jd_thingtalk_pal_malloc(
                        sizeof(JDThingTalkProtoRegReqResDevCfg_t));
                if (out_res->devices[iCnt]->config[ii] == NULL) {
                    continue;
                }
                jd_thingtalk_pal_memset(out_res->devices[iCnt]->config[ii], 0, sizeof(JDThingTalkProtoRegReqResDevCfg_t));

                // 解析 config数组元素的 name
                pSub = cJSON_GetObjectItem(pCfg, "name");
                if (pSub != NULL) {
                    if (out_res->devices[iCnt]->config[ii]->name == NULL) {
                        out_res->devices[iCnt]->config[ii]->name = (char *) jd_thingtalk_pal_malloc(
                                (jd_thingtalk_pal_strlen(pSub->valuestring) + 1) * sizeof(char));
                    }
                    jd_thingtalk_pal_strcpy(out_res->devices[iCnt]->config[ii]->name, pSub->valuestring);
                }

                // 解析 config数组元素的 type
                pSub = cJSON_GetObjectItem(pCfg, "type");
                if (pSub != NULL) {
                    if (out_res->devices[iCnt]->config[ii]->type == NULL) {
                        out_res->devices[iCnt]->config[ii]->type = (char *) jd_thingtalk_pal_malloc(
                                (jd_thingtalk_pal_strlen(pSub->valuestring) + 1) * sizeof(char));
                    }
                    jd_thingtalk_pal_strcpy(out_res->devices[iCnt]->config[ii]->type, pSub->valuestring);
                }

                // 解析 config数组元素的 content
                pSub = cJSON_GetObjectItem(pCfg, "content");
                if (pSub != NULL) {
                    if (out_res->devices[iCnt]->config[ii]->content == NULL) {
                        out_res->devices[iCnt]->config[ii]->content = (char *) jd_thingtalk_pal_malloc(
                                (jd_thingtalk_pal_strlen(pSub->valuestring) + 1) * sizeof(char));
                    }
                    jd_thingtalk_pal_strcpy(out_res->devices[iCnt]->config[ii]->content, pSub->valuestring);
                }
            }
        }
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

// end of file
