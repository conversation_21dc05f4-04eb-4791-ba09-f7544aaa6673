# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/threadpool/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 threadpool/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 threadpool/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 threadpool/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 threadpool/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
threadpool/CMakeFiles/lib_threadpool.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 threadpool/CMakeFiles/lib_threadpool.dir/rule
.PHONY : threadpool/CMakeFiles/lib_threadpool.dir/rule

# Convenience name for target.
lib_threadpool: threadpool/CMakeFiles/lib_threadpool.dir/rule

.PHONY : lib_threadpool

# fast build rule for target.
lib_threadpool/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/build
.PHONY : lib_threadpool/fast

condition.o: condition.cpp.o

.PHONY : condition.o

# target to build an object file
condition.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/condition.cpp.o
.PHONY : condition.cpp.o

condition.i: condition.cpp.i

.PHONY : condition.i

# target to preprocess a source file
condition.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/condition.cpp.i
.PHONY : condition.cpp.i

condition.s: condition.cpp.s

.PHONY : condition.s

# target to generate assembly for a file
condition.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/condition.cpp.s
.PHONY : condition.cpp.s

thp_mutex.o: thp_mutex.cpp.o

.PHONY : thp_mutex.o

# target to build an object file
thp_mutex.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thp_mutex.cpp.o
.PHONY : thp_mutex.cpp.o

thp_mutex.i: thp_mutex.cpp.i

.PHONY : thp_mutex.i

# target to preprocess a source file
thp_mutex.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thp_mutex.cpp.i
.PHONY : thp_mutex.cpp.i

thp_mutex.s: thp_mutex.cpp.s

.PHONY : thp_mutex.s

# target to generate assembly for a file
thp_mutex.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thp_mutex.cpp.s
.PHONY : thp_mutex.cpp.s

thread_pool.o: thread_pool.cpp.o

.PHONY : thread_pool.o

# target to build an object file
thread_pool.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thread_pool.cpp.o
.PHONY : thread_pool.cpp.o

thread_pool.i: thread_pool.cpp.i

.PHONY : thread_pool.i

# target to preprocess a source file
thread_pool.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thread_pool.cpp.i
.PHONY : thread_pool.cpp.i

thread_pool.s: thread_pool.cpp.s

.PHONY : thread_pool.s

# target to generate assembly for a file
thread_pool.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f threadpool/CMakeFiles/lib_threadpool.dir/build.make threadpool/CMakeFiles/lib_threadpool.dir/thread_pool.cpp.s
.PHONY : thread_pool.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... lib_threadpool"
	@echo "... condition.o"
	@echo "... condition.i"
	@echo "... condition.s"
	@echo "... thp_mutex.o"
	@echo "... thp_mutex.i"
	@echo "... thp_mutex.s"
	@echo "... thread_pool.o"
	@echo "... thread_pool.i"
	@echo "... thread_pool.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

