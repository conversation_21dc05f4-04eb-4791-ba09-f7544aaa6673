#pragma once

#ifndef __TEST_CASE_HPP__
#define __TEST_CASE_HPP__


#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <thread>
#include <mutex>
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/train_info.pb.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"




using namespace std;


class testcase
{

public:

    explicit testcase(zmq::context_t &context);

    ~testcase();




    void testcase_init();
    void testcase_run();


    void dev_manager_train_task_send(void);
    void dev_manager_train_state_subscriber(void);
    void dev_manager_train_agent_state_subscriber(void);
    void dev_manager_carriage_task_state_subscriber(void);
    void dev_manager_platform_task_state_subscriber(void);
    void dev_manager_sorting_task_state_msg_pub(void);
    void dev_manager_sorting_task_state_msg_sub(void);
    void dev_manager_train_excep_subscriber(void);
    void dev_manager_train_excep_publisher(void);
    void dev_manager_train_sys_state_publisher(void);
    void dev_manager_train_sys_state_subscriber(void);
    void train_sys_state_send(sys_mode_state &sys_state_temp);
    void dev_manager_train_schedule_state_subscriber(void);
    void dev_manager_train_schedule_state_publisher(void);
    void train_ext_state_send(train_ext_state_multi &train_ext_state);

    bool train_task_send(train_task &task_cmd);

    void train_state_info(train_state *train_state);
    void dev_manager_train_agent_info_request(void);
    void dev_manager_coreserver_train_info_request(void);

    int get_train_basic_info_form_coreserver(train_basic_info_mutilp &train_basice_info);
    int get_train_basic_info_form_train_agent(train_basic_info_mutilp &train_basice_info);
    int get_train_config_para_form_train_agent(train_config_para &train_config_cfg);

private:
    zmq::socket_t train_task_requester;  		///< 车辆任务下发socket REQ 类型
    zmq::socket_t train_state_subscriber;       ///< 车辆状态监听socket SUB 类型
    zmq::socket_t train_agent_state_subscriber; ///< 车辆代理监听socket SUB 类型
    zmq::socket_t carriage_task_state_subscriber;  ///< 车辆任务监听socket SUB 类型
    zmq::socket_t platform_task_state_subscriber;  ///< 车辆任务监听socket SUB 类型
    zmq::socket_t sorting_task_state_msg_pub;
    zmq::socket_t sorting_task_state_msg_sub;
    zmq::socket_t train_excep_subscriber;       ///< 车辆异常监听socket SUB 类型
    zmq::socket_t train_excep_publisher; 
    zmq::socket_t train_sys_state_publisher;    ///< 车辆系统状态发布socket PUB 类型
    zmq::socket_t train_sys_state_subscriber;
    zmq::socket_t train_ext_state_publisher;
    zmq::socket_t coreserver_train_info_request;   ///<coreserver节点汇总的train_info信息REQ类型
    zmq::socket_t train_agent_train_info_request;   ///<车代理节点汇总的train_info信息REQ类型

    zmq::socket_t vehicle_extstate_recver;      ///< 调度系统车辆状态监听socket SUB 类型

    std::mutex task_send_mutex;
    


};




#endif