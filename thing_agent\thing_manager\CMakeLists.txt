aux_source_directory(device_manager device_manager)
aux_source_directory(sys_manager sys_manager)
aux_source_directory(task_manager task_manager)
aux_source_directory(wcs_manager wcs_manager)
aux_source_directory(container_manager container_manager)
aux_source_directory(. SRCS)

include_directories("device_manager" "sys_manager" "task_manager" "wcs_manager" "container_manager")

add_library(thing_manager ${SRCS} ${device_manager} ${sys_manager} ${task_manager} ${wcs_manager} ${container_manager})
