syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";

enum exception_src 
{
    TRAIN = 0;				//分播车头
	CARRIAGE = 1;			//分播车厢
	PLATFORM = 2;			//载货台
    FEEDER = 3;				//供包台: 皮带，扫码头等
    SECURITY = 4;			//安全组件
    CONTAINER = 5;			//格口
	CAMERA = 6;				//灰度相机
	CONT_SHELVES = 7;		//移动货架
    CORESERVICE = 8;		//核心组件
	TRAIN_AGENT = 9;		//车组代理
	SCHEDULER = 10;			//调度: 位置匹配错误等
	FEEDER_AGENT = 11;		//供包台代理
	CONTAINER_AGENT = 12;	//格口代理
	THING_AGENT = 13;		//物控代理
	AUTO_EXCHANGE_DEV = 14; //自动取换箱设备
	AUTO_EXCHANGE_AGENT = 15; //自动取换箱代理
};

enum exception_level
{
    RESERVE = 0;
	NORMAL = 1;
	WARNNING = 2;
    ERROR = 3;
    FATAL = 4;
};

enum exception_state
{
	STATE_OCCURED = 0;
	STATE_RESET = 1;
};

enum exception_handle
{
	HANDLE_SELF_RECOVERY = 0;
	HANDLE_HIGH_LEVEL = 1;
};

message except_info
{
    exception_src src = 1;
    exception_level level = 2;
    uint32 code = 3;
    uint32 sub_code = 4;
    string description = 5 [(nanopb).max_size=128];
	uint32 dev = 6;
	uint32 sub_dev = 7;
	exception_state state = 8;
	exception_handle handle = 9;
};

message event_info
{
	exception_src src = 1;
	uint32 dev = 2;
	uint32 sub_dev = 3;
	uint32 code = 4;
	uint32 sub_code = 5;
};

message event_exception
{
	oneof evt_except
	{
		event_info evt = 1;
		except_info except = 2;
	}
};
