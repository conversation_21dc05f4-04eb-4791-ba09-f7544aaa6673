#include "jd_thingtalk_log.h"

#ifdef __LINUX_PAL__
#include <stddef.h>
#include <time.h>
#include <sys/time.h>
#include <stdio.h>
#endif

// 获取时间格式
int jd_thingtalk_log_time_format(char *time_str)
{
#ifdef __LINUX_PAL__
    struct timeval now;
    gettimeofday(&now, NULL);
    struct tm *p = localtime(&now.tv_sec);
    sprintf(time_str, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            p->tm_year+1900, p->tm_mon+1, p->tm_mday,
            p->tm_hour, p->tm_min, p->tm_sec, (int32_t)(now.tv_usec/1000));
#endif
    return 0;
}