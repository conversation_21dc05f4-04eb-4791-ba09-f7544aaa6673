﻿

/**@file  udp_server_socket.cpp
* @brief       基于UDP的socket操作软件二次封装
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持TCP服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* </table>
*
**********************************************************************************
*/



#include "udp_socket.hpp"

#include <spdlog/spdlog.h>


#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>




/**@brief  udp_server_socket class构造函数
* @param[in]  NULL
* @return     NULL
*/
udp_server_socket::udp_server_socket() 
:m_server_sockfd(-1)
,m_default_processor(100)
,m_default_listener(100)
{
	return ;
}


/**@brief  udp_server_socket class析构函数，调用时关闭tcp服务器的文件描述符
* @param[in]  NULL
* @return     NULL
*/
udp_server_socket::~udp_server_socket() 
{
    if (m_server_sockfd >= 0) 
	{
        close(m_server_sockfd);  
    }
}


/**@brief     TCP 通信中 Server 构造
* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
bool udp_server_socket::udp_server_socket_init(void) 
{
    m_server_sockfd = socket(PF_INET, SOCK_DGRAM, 0);

    if (m_server_sockfd < 0) 
	{
		SPDLOG_ERROR("m_server_sockfd init error :{}", m_server_sockfd);

        return false ;
    }
	
	return true;
	
}


/**@brief     TCP 通信中 Server 地址及端口绑定
* @param[in]  NULL
* @return     函数执行结果
* - false     server绑定失败
* - true      server绑定成功
*/
bool udp_server_socket::udp_server_socket_bind(void) 
{

	if (bind(m_server_sockfd, (struct sockaddr *)(&m_serv_addr), sizeof(m_serv_addr)) < 0) 
	{

		SPDLOG_ERROR("socket bink fail :{}", m_server_sockfd);
        return false;
    }
	
    return true;
}



/**@brief     向UDP Server分配IP地址及端口号
* @param[in]  const std::string &ip --- 服务器IP地址
* @param[in]  int port --- 服务器端口号
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_server_socket::udp_server_socket_server_cfg(const std::string &ip, int port) 
{
	//获取IP地址
	auto IP = ip.data();

    bzero(&m_serv_addr, sizeof(m_serv_addr));
	
    if (inet_pton(AF_INET, IP, &m_serv_addr.sin_addr.s_addr) < 0) 
	{
		SPDLOG_ERROR("ip address converse fail :{}", ip);

		return false;
    }

	//端口号检查
	if(!is_UDP_SOCKET_PORT_VALID(port))
	{

		SPDLOG_ERROR("port is not valid :{}", port);
	}
	
    m_serv_addr.sin_family = AF_INET;
    m_serv_addr.sin_port = htons(port);
	m_serv_addr.sin_addr.s_addr = htonl(INADDR_ANY);

    return true;
}


/**@brief     设置TCP 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启地址 reuse
* @ref          false 禁止地址 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_server_socket::udp_server_socket_set_reuseaddr(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, SOL_SOCKET, SO_REUSEADDR, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{

		SPDLOG_ERROR("udp set reuse addr err :{}", m_server_sockfd);


		return false;

	}
	
    return true;
}



/**@brief     设置TCP 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启端口 reuse
* @ref          false 禁止端口 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_server_socket::udp_server_socket_set_reuseport(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, SOL_SOCKET, SO_REUSEPORT, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
		SPDLOG_ERROR("udp set reuse port err :{}", m_server_sockfd);

		return false;

	}
	
    return true;
}







/**@brief     获取TCP 服务器端口号
* @param[in]  NULl
* @return     TCP 服务器端口号
*/
unsigned int udp_server_socket::udp_server_socket_get_port() const 
{

	auto port = m_serv_addr.sin_port;
	return static_cast<unsigned int>(port);

}





/**@brief     close 套接字
* @param[in]  int fd
* @return     函数执行结果
* - false     失败
* - true      成功
*/
bool udp_server_socket::udp_server_socket_close(int fd )
{
    return close(fd);
}



/**@brief     设置描述符为非阻塞形式
* @param[in]  int sock_fd  ---  待操作描述符
* @return      函数执行结果
* - false     失败
* - true      成功
*/
bool udp_server_socket::udp_server_socket_set_nonblocking(int sock_fd)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(sock_fd, F_GETFL);

	if(curr_option<0)
	{
		SPDLOG_ERROR("read current option fail :{}", m_server_sockfd);

        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(sock_fd,F_SETFL,new_option)<0)
	{
		
		SPDLOG_ERROR("rewrite current option fail :{}", m_server_sockfd);

        return false;
	}
	

    return true;
}

/**@brief     设置描述符为非阻塞形式
* @param[in]  NULL
* @return     函数执行结果
* - false     失败
* - true      成功
*/
bool udp_server_socket::udp_server_socket_set_nonblocking(void)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(m_server_sockfd, F_GETFL);

	if(curr_option<0)
	{
		SPDLOG_ERROR("read current option fail :{}", m_server_sockfd);

        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(m_server_sockfd,F_SETFL,new_option)<0)
	{
		
		SPDLOG_ERROR("rewrite current option fail :{}", m_server_sockfd);

        return false;
	}
	

    return true;
}



bool udp_server_socket::udp_server_socket_recv_msg(int fd, int *msg_len, uint8_t *msg_data, struct sockaddr_in *msg_src)
{
    int  ret;
    int  new_fd;
    struct sockaddr_in client_addr;
    socklen_t cli_len=sizeof(client_addr);
	bool result = false;

    new_fd = fd;

	ret = recvfrom(new_fd, msg_data, UDP_SOCKET_MAX_LEN, 0, (struct sockaddr *)&client_addr, &cli_len);
	
	if( ret > 0 )
	{
		*msg_src = client_addr;
		*msg_len = ret;
		result = true;
	}
	else
	{
		*msg_len = 0;
		result = false;
	}

	
#if 0
    if (ret > 0)
    {
        printf("socket %d 接收到来自:%s:%d的消息成功, 共 %d 个字节的数据, ",
             new_fd, inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port), ret);

		printf("接收到的数据为：\n");
		for(int i = 0; i < ret; i++)
			printf("%02x ", msg_data[i]);
	  	printf("\n");
	}
#endif

	return result;
}



bool udp_server_socket::udp_server_socket_recv_msg(int *msg_len, uint8_t *msg_data, struct sockaddr_in *msg_src)
{
    int  ret;
    struct sockaddr_in client_addr;
    socklen_t cli_len = sizeof(client_addr);
	bool result = false;

	ret = recvfrom(m_server_sockfd, msg_data, UDP_SOCKET_MAX_LEN, 0, (struct sockaddr *)&client_addr, &cli_len);
	
	if( ret > 0 )
	{	
		msg_data[ret] = '\0';
		*msg_src = client_addr;
		*msg_len = ret;
		result = true;
	}
	else
	{
		*msg_len = 0;
		result = false;
	}

#if 1
    if (ret > 0)
    {
        printf("socket %d 接收到来自:%s:%d的消息成功, 共 %d 个字节的数据, ",
             m_server_sockfd, inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port), ret);

		printf("接收到的数据为：\n");
		for(int i = 0; i < ret; i++)
			printf("%02x ", msg_data[i]);
	  	printf("\n");
	}
#endif
	
	return result;

}



bool udp_server_socket::udp_server_socket_send_msg(int msg_len, char *msg_data, struct sockaddr_in msg_des)
{

    int  ret;
    struct sockaddr_in client_addr;

	client_addr = msg_des;
    socklen_t cli_len=sizeof(client_addr);
	bool result = false;


	ret = sendto(m_server_sockfd, msg_data, msg_len, 0, (struct sockaddr *)&client_addr, cli_len);
	
	if( ret>0 )
	{	
		result = true;
	}
	else
	{
		result = false;
	}

	
	return result;


}



void udp_server_socket::udp_server_socket_close(void)
{
	if (m_server_sockfd >= 0) 
	{
        close(m_server_sockfd);  
    }

}





udp_client_socket::udp_client_socket() 
:m_client_sockfd(-1)
{
	return ;
}


/**@brief  udp_server_socket class析构函数，调用时关闭tcp服务器的文件描述符
* @param[in]  NULL
* @return     NULL
*/
udp_client_socket::~udp_client_socket() 
{
    if (m_client_sockfd >= 0) 
	{
        close(m_client_sockfd);  
    }
}


bool udp_client_socket::udp_client_socket_init(void) 
{
    m_client_sockfd = socket(PF_INET, SOCK_DGRAM, 0);

    if (m_client_sockfd < 0) 
	{
		SPDLOG_ERROR("m_client_sockfd init error :{}", m_client_sockfd);

        return false ;
    }
	
	return true;
	
}

bool udp_client_socket::udp_client_socket_send_msg(int msg_len, char *msg_data, struct sockaddr_in msg_des)
{

    int  ret;
    struct sockaddr_in server_addr;

	server_addr = msg_des;
    socklen_t ser_len=sizeof(server_addr);
	bool result = false;


	//ret = sendto(m_client_sockfd, msg_data, msg_len, 0, (struct sockaddr *)&server_addr, ser_len);

	ret = sendto(m_client_sockfd, msg_data, msg_len, MSG_NOSIGNAL|MSG_DONTWAIT, (struct sockaddr *)&server_addr, ser_len);
	
	if( ret>0 )
	{	
		SPDLOG_INFO("m_client_sockfd send OK :{} :{} ", m_client_sockfd , msg_len);

		result = true;
	}
	else
	{
		SPDLOG_INFO("m_client_sockfd send fail :{} :{} ", m_client_sockfd , errno);
		result = false;
	}

	SPDLOG_INFO("m_client_sockfd send");

	return result;


}


bool udp_client_socket::udp_client_socket_send_msg(std::string msg_string, struct sockaddr_in msg_des)
{

    int  ret;
    struct sockaddr_in server_addr;

	server_addr = msg_des;
    socklen_t ser_len=sizeof(server_addr);
	bool result = false;


	//ret = sendto(m_client_sockfd, msg_data, msg_len, 0, (struct sockaddr *)&server_addr, ser_len);

	ret = sendto(m_client_sockfd, msg_string.c_str(), msg_string.size(), MSG_NOSIGNAL|MSG_DONTWAIT, (struct sockaddr *)&server_addr, ser_len);
	
	if( ret>0 )
	{	
		SPDLOG_INFO("m_client_sockfd send OK :{} :{} ", m_client_sockfd , msg_string.size());

		result = true;
	}
	else
	{
		SPDLOG_INFO("m_client_sockfd send fail :{} :{} ", m_client_sockfd , errno);
		result = false;
	}

	SPDLOG_INFO("m_client_sockfd send");

	return result;


}



bool udp_client_socket::udp_client_socket_recv_msg(int *msg_len, char *msg_data, struct sockaddr_in *msg_src)
{

    int  ret;
    struct sockaddr_in server_addr;
    socklen_t server_len=sizeof(server_addr);
	bool result = false;

	ret = recvfrom(m_client_sockfd, msg_data, UDP_SOCKET_MAX_LEN, 0, (struct sockaddr *)&server_addr, &server_len);
	
	if( ret>0 )
	{	
		*msg_src = server_addr;
		*msg_len = ret;
		result = true;
	}
	else
	{
		*msg_len = 0;
		result = false;
	}

	
	return result;

#if 0 
    if (ret > 0)
    {
        printf("socket %d 接收到来自:%s:%d的消息成功:'%s'，共%d个字节的数据\n",
             new_fd, inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port), recvbuf, ret);
#endif

}


bool udp_client_socket::udp_client_socket_set_nonblocking(void)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(m_client_sockfd, F_GETFL);

	if(curr_option<0)
	{
		SPDLOG_ERROR("read current option fail :{}", m_client_sockfd);

        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(m_client_sockfd,F_SETFL,new_option)<0)
	{
		
		SPDLOG_ERROR("rewrite current option fail :{}", m_client_sockfd);

        return false;
	}
	

    return true;
}


void udp_client_socket::udp_client_socket_close(void)
{
	if (m_client_sockfd >= 0) 
	{
        close(m_client_sockfd);  
    }

}
