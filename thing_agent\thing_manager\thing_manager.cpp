#include "thing_manager.hpp"

//主体方法部分
int thing_manager::func_switch_mode(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int mode, sub_mode = -1;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_switch_mode key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_MODE_SWITCH_KEY_MODE))
            cjs_cvt::get_instance()->to_enum(in[i]->value, mode);
        else if (!strcmp(in[i]->key, FUNC_MODE_SWITCH_KEY_SUB_MODE))
            cjs_cvt::get_instance()->to_enum(in[i]->value, sub_mode);
        else
            SPDLOG_ERROR("invalid value");
    }

    SPDLOG_DEBUG("mode:{}, sub_mode:{}", mode, sub_mode);
    if (mode == FUNC_MODE_SWITCH_MODE_MANUAL)
    {
        if (sub_mode == FUNC_MODE_SWITCH_SUB_MODE_DEMONSTRATE)
            SPDLOG_ERROR("recv wrong mode");
        else
        {
            evt_report_timer.vehicle_state_report_timer.start();
            evt_report_timer.feeder_state_report_timer.start();
            // evt_report_timer.switcher_state_report_timer.start();
            sys_manager::get_instance()->set_manual_mode();
        }
    }
    else if (mode == FUNC_MODE_SWITCH_MODE_AUTO)
    {
        sys_manager::get_instance()->set_auto_mode();
    }
    else if (mode == FUNC_MODE_SWITCH_MODE_SEMI_AUTO)
    {
        SPDLOG_DEBUG("send semi auto");     //半自动  目前未实现
    }

    return 1;
}

int thing_manager::func_update_container_led(int in_num, JDThingTalkProtoKeyValue_t **in, std::vector<int> &devs)
{
    std::list<led_info> container_states;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_update_container_led key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_GRID_LAMP_STATUS_INFO))
            cjs_cvt::get_instance()->to_container_state(in[i]->value, container_states);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (devs.size() == container_states.size())
    {
        for (auto &con : container_states)
            device_manager::get_instance()->issue_lamp_state(con);
    }
    else
    {
        for (auto &con : container_states)
        {
            if (find(devs.begin(), devs.end(), con.id) != devs.end())
                device_manager::get_instance()->issue_lamp_state(con);
            else
                SPDLOG_WARN("not issue to container {} color {} because of avoiding repeat", con.id, con.color);
        }
    }

    return 1;
}
int thing_manager::func_update_container_state(int in_num, JDThingTalkProtoKeyValue_t **in, std::vector<int> &devs)
{
    std::list<container_seal_state_single> container_seal_states;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_update_container_seal state key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_GRID_LAMP_STATUS_INFO))
            cjs_cvt::get_instance()->to_container_seal_state(in[i]->value, container_seal_states);
            
        else
            SPDLOG_ERROR("invalid value");
    }

    if (devs.size() == container_seal_states.size())
    {
        for (auto &con : container_seal_states)
        {
            device_manager::get_instance()->issue_thingtalk_container_state(con);
            SPDLOG_DEBUG(" issue thingtalk seal state to container {} seal state {}", con.container_id, con.seal_state);
        }
        SPDLOG_DEBUG(" issue thingtalk seal state size equal");
    }
    else
    {
        for (auto &con : container_seal_states)
        {
            if (find(devs.begin(), devs.end(), con.container_id) != devs.end())
            {
                device_manager::get_instance()->issue_thingtalk_container_state(con);
                SPDLOG_DEBUG(" issue thingtalk seal state to container {} seal state {}", con.container_id, con.seal_state);
            }
                
            else
                SPDLOG_WARN("not issue to container {} seal {} because of avoiding repeat", con.container_id, con.seal_state);

        }
        SPDLOG_DEBUG(" issue thingtalk seal state size not equal");
    }

    return 1;
}
int thing_manager::func_issue_sorting_task(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int task_type;
    float vehicle_belt_speed;
    std::string grid_no, task_no, feeder_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_sorting_task key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_PALLET_TO_GRID_NOTIFY_KEY_GRID_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, grid_no);
        else if (!strcmp(in[i]->key, FUNC_PALLET_TO_GRID_NOTIFY_KEY_TASK_TYPE))
            cjs_cvt::get_instance()->to_enum(in[i]->value, task_type);
        else if (!strcmp(in[i]->key, FUNC_PALLET_TO_GRID_NOTIFY_KEY_TASK_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, task_no);
        else if (!strcmp(in[i]->key, FUNC_PALLET_TO_GRID_NOTIFY_KEY_FEEDER_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, feeder_no);
        else if (!strcmp(in[i]->key, FUNC_PALLET_TO_GRID_NOTIFY_KEY_ROTATE_SPEED))
            cjs_cvt::get_instance()->to_float(in[i]->value, vehicle_belt_speed);
        else
            SPDLOG_ERROR("invalid value");
    }

    SPDLOG_INFO("thingtalk task info: get issue task:{}, execute time:{}", task_no, task_timer.execute_time());

    if (task_manager::get_instance()->get_task_state(task_no) == task_manager::REPORTED || task_type != 0)        //上报过码值，还未下发供包台的任务，或兜底或模拟任务
        task_manager::get_instance()->issue_task(grid_no, task_no, task_type, vehicle_belt_speed);
    else
    {
        SPDLOG_DEBUG("get finished task {}, task state:{}", task_no, task_manager::get_instance()->get_task_state(task_no));      //发生供包台反转或超长包时才又可能出现下发已经完成的任务
    }

    return 1;
}

int thing_manager::func_issue_vehicle_demo(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int mode;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_vehicle_demo key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_SORTS_CONTINUATION_RUN_KEY_SORTS_MODE))
            cjs_cvt::get_instance()->to_enum(in[i]->value, mode);
        else
            SPDLOG_ERROR("invalid value");
    }

    SPDLOG_DEBUG("vehicle test mode:{}", mode);
    if (mode == FUNC_SORTS_CONTINUATION_RUN_SORTS_MODE_START)
        sys_manager::get_instance()->issue_vehicle_start_demo();
    else
        sys_manager::get_instance()->issue_vehicle_stop_demo();

    return 1;
}

int thing_manager::func_update_hmi_lamp(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string lamp_no;
    int state;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_update_hmi_lamp key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_PILOT_LAMP_STATUS_INFO))
            cjs_cvt::get_instance()->to_hmi_lamp_state(in[i]->value, lamp_no, state);
        else
            SPDLOG_ERROR("invalid value");
    }

    SPDLOG_DEBUG("hmi lamp no: {} state:{}", lamp_no, state);
    if (state == FUNC_PILOT_LAMP_STATUS_NOTIFY_STATUS_OFF)
        device_manager::get_instance()->issue_hmi_lamp_off();
    else
        device_manager::get_instance()->issue_hmi_lamp_on();

    return 1;
}

int thing_manager::func_issue_buzzer_control(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string buzzer_no;
    int state;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_buzzer_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_BUZZER_CONTROL_KEY_BUZZER_INFO))
            cjs_cvt::get_instance()->to_buzzer_info(in[i]->value, buzzer_no, state);
        else
            SPDLOG_ERROR("invalid value");
    }

    SPDLOG_DEBUG("buzzer no: {} state:{}", buzzer_no, state);
    if (state == FUNC_BUZZER_CONTROL_STATUS_OFF)
        device_manager::get_instance()->issue_buzzer_off();
    else
        device_manager::get_instance()->issue_buzzer_on();

    return 1;
}


int thing_manager::func_issue_container_lamp_test(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int state;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_container_test key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_GRID_IN_ORDER_CONTROL_KEY_STATUS))
            cjs_cvt::get_instance()->to_enum(in[i]->value, state);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (state == FUNC_GRID_IN_ORDER_CONTROL_STATUS_ON)
        device_manager::get_instance()->issue_container_lamp_test_start();
    else
        device_manager::get_instance()->issue_container_lamp_test_stop();

    return 1;
}

int thing_manager::func_issue_scanner_control(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::vector<std::string> ids;
    std::vector<int> modes;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_auto_scanner_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_AUTO_CODE_SCAN_KEY_CODE_SCANNER_INFO))
            cjs_cvt::get_instance()->to_scanner_info(in[i]->value, ids, modes);
        else
            SPDLOG_ERROR("invalid value");
    }

    for (unsigned int i = 0; i < ids.size(); i++)
    {
        uint32_t fd_id = atoi(ids[i].c_str());
        if (modes[i] == FUNC_AUTO_CODE_SCAN_MODE_ON)
            device_manager::get_instance()->issue_feeder_scan(fd_id);
    }

    return 1;
}

//sort方法部分
int thing_manager::func_sort_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int action;
    std::string sort_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_sort_command_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_COMMAND_DISPATCH_KEY_SORT_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
        else if (!strcmp(in[i]->key, FUNC_COMMAND_DISPATCH_KEY_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t vh_id = atoi(sort_no.c_str());

    if (action == FUNC_COMMAND_DISPATCH_ACTION_ERSET)
        device_manager::get_instance()->reset_vehicle(vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_ACTION_STANDBY)
        SPDLOG_WARN("recv wrong vehicle {} action: standby", vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_ACTION_REGISTER)
        device_manager::get_instance()->register_vehicle(vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_ACTION_UNREGISTER)
    {
        //下线先发一次设备最新信息
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_SORT_GROUP_KEY_STATUS_REPORT, 1);

        device_manager::get_instance()->unregister_vehicle(vh_id);
        std::unordered_map<int, device_manager::vehicle_total_state> states;
        device_manager::get_instance()->get_vehicle_state(states, vh_id);

        sys_mode_state sys_state;
        sys_manager::get_instance()->get_sys_state(sys_state);
        evt_cvt::get_instance()->from_vehicle_state(event, states, sys_state.mode);

        SPDLOG_DEBUG("send vehicle {} unregister event", vh_id);
        thing_interface::get_instance()->send_event(*event);
    }
    else
    {
        SPDLOG_ERROR("wrong action");
    }

    return 1;
}

// int thing_manager::func_sort_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
// {
//     int target;
//     std::string sort_no, target_id;

//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_sort_control_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_CONTROl_DISPATCH_KEY_SORT_NO))
//             cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
//         else if (!strcmp(in[i]->key, FUNC_CONTROL_DISPATCH_KEY_TARGET_ID))
//             cjs_cvt::get_instance()->to_string(in[i]->value, target_id);
//         else if (!strcmp(in[i]->key, FUNC_CONTROL_DISPATCH_KEY_TARGET))
//             cjs_cvt::get_instance()->to_enum(in[i]->value, target);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     uint32_t vh_id = atoi(sort_no.c_str());

//     if (target == FUNC_CONTROL_DISPATCH_TARGET_CONTAINER)
//     {
//         uint32_t slot_id = atoi(target_id.c_str());
//         device_manager::get_instance()->issue_vehicle_to_slot(vh_id, slot_id);
//     }
//     else if (target == FUNC_CONTROL_DISPATCH_TARGET_STAGING)
//         SPDLOG_ERROR("vehicle not support to stage");
//     else if (target == FUNC_CONTROL_DISPATCH_TARGET_FEEDER)
//         device_manager::get_instance()->issue_vehicle_to_feeder(vh_id);
//     else if (target == FUNC_CONTROL_DISPATCH_TARGET_UNREGISTER_POINT)
//         device_manager::get_instance()->issue_vehicle_to_unregister_point(vh_id);
//     else
//         SPDLOG_ERROR("wrong target");

//     return 1;
// }

// int thing_manager::func_sort_control_dest_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
// {
//     std::string sort_no;
//     int32_t x, y, z;

//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_sort_control_dest_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_CONTROL_DEST_DISPATCH_KEY_SORT_NO))
//             cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
//         else if (!strcmp(in[i]->key, FUNC_CONTROL_DEST_DISPATCH_KEY_DEST_LOCATION))
//             cjs_cvt::get_instance()->to_position_xyz(in[i]->value, x, y, z);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     uint32_t vh_id = atoi(sort_no.c_str());

//     device_manager::get_instance()->issue_vehicle_target_position(vh_id, x, y, z);

//     return 1;
// }

// int thing_manager::func_sort_control_walk_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
// {
//     std::string sort_no;
//     int distance;

//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_sort_control_walk_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_KEY_SORT_NO))
//             cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
//         else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_KEY_DISTANCE))
//             cjs_cvt::get_instance()->to_int(in[i]->value, distance);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     uint32_t vh_id = atoi(sort_no.c_str());
//     uint32_t length = distance;
//     device_manager::get_instance()->issue_vehicle_move_forward(vh_id, length);

//     return 1;
// }

int thing_manager::func_sort_belt_control(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string sort_no;
    int dir;
    std::string carriage_id;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_vehicle_belt_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_BELT_DIRECTION_DISPATCH_KEY_SORT_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
        else if (!strcmp(in[i]->key, FUNC_BELT_DIRECTION_DISPATCH_KEY_BELT_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, carriage_id);
        else if (!strcmp(in[i]->key, FUNC_BELT_DIRECTION_DISPATCH_KEY_DIRECTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, dir);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t vh_id = atoi(sort_no.c_str());
    uint32_t carr_id = atoi(carriage_id.c_str());
    if (dir == FUNC_DIRECTION_DISPATCH_DIRECTION_BACKWARD)
        device_manager::get_instance()->issue_vehicle_belt_backward_control(vh_id, carr_id);
    else if (dir == FUNC_DIRECTION_DISPATCH_DIRECTION_FORWARD)
        device_manager::get_instance()->issue_vehicle_belt_forward_control(vh_id, carr_id);
    else if (dir == FUNC_DIRECTION_DISPATCH_DIRECTION_STOP)
        device_manager::get_instance()->issue_vehicle_belt_stop(vh_id, carr_id);

    return 1;
}

int thing_manager::func_sort_platform_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string dev_id;
    int action_type;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_vehicle_belt_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_PLATFORM_COMMAND_DISPATCH_PLATFORM_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, dev_id);
        else if (!strcmp(in[i]->key, FUNC_PLATFORM_COMMAND_DISPATCH_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action_type);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (action_type == FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_RESET)
        device_manager::get_instance()->issue_platform_command_reset_control(dev_id);
    else if(action_type == FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_STANDBY)
        SPDLOG_INFO("recv wrong vehicle {} action: standby", dev_id);
    else if(action_type == FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_ONLINE)
        device_manager::get_instance()->issue_platform_command_enable_control(dev_id);
    else if(action_type == FUNC_PLATFORM_COMMAND_DISPATCH_TYPE_OFFLINE)
        device_manager::get_instance()->issue_platform_command_disable_control(dev_id);

    return 1;

}

int thing_manager::func_sort_platform_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string dev_id;
    int action_type;
    int dir;
    int speed;
    int distance;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_vehicle_belt_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_PLATFORM_CONTROL_DISPATCH_PLATFORM_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, dev_id);
        else if (!strcmp(in[i]->key, FUNC_PLATFORM_CONTROL_DISPATCH_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action_type);
        else if (!strcmp(in[i]->key, FUNC_PLATFORM_CONTROL_DISPATCH_DIRECTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, dir);
        else if (!strcmp(in[i]->key, FUNC_PLATFORM_CONTROL_DISPATCH_SPEED))
            cjs_cvt::get_instance()->to_int(in[i]->value, speed);
        else if (!strcmp(in[i]->key, FUNC_PLATFORM_CONTROL_DISPATCH_LIMIT))
            cjs_cvt::get_instance()->to_int(in[i]->value, distance);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_Y_MOVE)
        device_manager::get_instance()->issue_carriage_control_y_move(dev_id, distance, dir, speed);
    else if(action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_Y_ZERO_CALIBRATION)
        device_manager::get_instance()->issue_carriage_control_y_zero_calibration(dev_id);
    else if(action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_BELT_ROTATE)
        device_manager::get_instance()->issue_carriage_control_belt_rotate(dev_id, distance, speed, dir);
    else if(action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_BELT_ZERO_CALIBRATION)
        device_manager::get_instance()->issue_carriage_control_belt_zeor_calibration(dev_id);
    else if(action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_DUMP_TRUCK_ROTATE)
        device_manager::get_instance()->issue_carriage_control_belt_zeor_calibration(dev_id);
    else if(action_type == FUNC_PLATFORM_CONTROL_DISPATCH_TYPE_DUMP_TRUCK__ZERO_CALIBRATION)
        device_manager::get_instance()->issue_carriage_control_belt_zeor_calibration(dev_id);

    return 1;

}


int thing_manager::func_sortgroup_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int action;
    std::string sort_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_sortgroup_command_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_COMMAND_DISPATCH_KEY_SORTGROUP_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
        else if (!strcmp(in[i]->key, FUNC_COMMAND_DISPATCH_KEY_SORTGROUP_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t vh_id = atoi(sort_no.c_str());

    if (action == FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_ERSET)
        device_manager::get_instance()->reset_vehicle(vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_STANDBY)
        SPDLOG_WARN("recv wrong vehicle {} action: standby", vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_REGISTER)
        device_manager::get_instance()->register_vehicle(vh_id);
    else if (action == FUNC_COMMAND_DISPATCH_SORTGROUP_ACTION_UNREGISTER)
    {
        //下线先发一次设备最新信息
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_SORT_GROUP_KEY_STATUS_REPORT, 1);

        device_manager::get_instance()->unregister_vehicle(vh_id);
        std::unordered_map<int, device_manager::vehicle_total_state> states;
        device_manager::get_instance()->get_vehicle_state(states, vh_id);

        sys_mode_state sys_state;
        sys_manager::get_instance()->get_sys_state(sys_state);
        evt_cvt::get_instance()->from_vehicle_state(event, states, sys_state.mode);

        SPDLOG_DEBUG("send vehicle {} unregister event", vh_id);
        thing_interface::get_instance()->send_event(*event);
    }
    else
    {
        SPDLOG_ERROR("wrong action");
    }

    return 1;

}

int thing_manager::func_sortgroup_control_walk_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int target, level_speed, turn_speed, limit;
    std::string sort_group_no, target_id;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_sortgroup_control_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_SORTGROUP_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, sort_group_no);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_TARGET_ID))
            cjs_cvt::get_instance()->to_string(in[i]->value, target_id);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_TARGET))
            cjs_cvt::get_instance()->to_enum(in[i]->value, target);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_LEVEL_SPEED))
            cjs_cvt::get_instance()->to_int(in[i]->value, level_speed);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_TURN_SPEED))
            cjs_cvt::get_instance()->to_int(in[i]->value, turn_speed);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_DISPATCH_KEY_LIMIT))
            cjs_cvt::get_instance()->to_int(in[i]->value, limit);
        else
            SPDLOG_ERROR("invalid value");
    }


    uint32_t vh_id = atoi(sort_group_no.c_str());
    uint32_t slot_id = atoi(target_id.c_str());
    uint32_t length = limit;

    if (target == FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_FEEDER)
        device_manager::get_instance()->issue_vehicle_to_feeder(vh_id, level_speed, turn_speed, slot_id);
    else if (target == FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_CONTAINER)
        device_manager::get_instance()->issue_vehicle_to_slot(vh_id, slot_id, level_speed, turn_speed);
    else if (target == FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_GRAYSCALE_CAMERA)
        device_manager::get_instance()->issue_vehicle_to_grayscale_camera(vh_id, slot_id, level_speed, turn_speed);
    else if(target == FUNC_CONTROL_DISPATCH_SORTGROUP_TARGET_INCREMENTAL_WALK)
        device_manager::get_instance()->issue_vehicle_move_forward(vh_id, length, level_speed, turn_speed);
    else
        SPDLOG_ERROR("wrong target");

    return 1;

}



int thing_manager::func_sortgroup_sort_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int action;
    std::string sort_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_sortgroup_sort_command_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_SORTGROUP_SORT_COMMAND_DISPATCH_SORT_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, sort_no);
        else if (!strcmp(in[i]->key, FUNC_SORTGROUP_SORT_COMMAND_DISPATCH_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (action == FUNC_SORTGROUP_SORT_COMMAND_KEY_ACTION_CALIBRATION)
        device_manager::get_instance()->issue_carriage_control_y_zero_calibration(sort_no);
    else
        SPDLOG_ERROR("wrong target");

    return 1;

}



int thing_manager::func_issue_shelf_lock_control(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::string shelf_no;
    int action;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_buzzer_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_SHELF_LOCK_CONTROL_KEY_SHELF_LOCK_INFO))
            cjs_cvt::get_instance()->to_shelf_info(in[i]->value, shelf_no, action);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t shelf_id = atoi(shelf_no.c_str());
    SPDLOG_DEBUG("shelf_no: {}-{}, action:{}", shelf_no, shelf_id, action);

    if (action == FUNC_SHELF_CONTROL_ACTION_LOCK)
        device_manager::get_instance()->issue_shelf_lock(shelf_id);
    else
        device_manager::get_instance()->issue_shelf_unlock(shelf_id);

    return 1;

}


int thing_manager::func_feeder_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    std::vector<std::string> belt_ids;
    std::vector<int> speeds;
    std::string feeder_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_feeder_belt_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_CONTROL_DISPATHCH_KEY_BELTS_ARRAY))
            cjs_cvt::get_instance()->to_feeder_belt_info(in[i]->value, belt_ids, speeds);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_DISPATHCH_KEY_FEEDER_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, feeder_no);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t fd_no = atoi(feeder_no.c_str());

    for (unsigned int i = 0; i < belt_ids.size(); i++)
    {
        uint32_t bt_id = atoi(belt_ids[i].c_str());
        device_manager::get_instance()->issue_feeder_belt_cmd(fd_no, bt_id, speeds[i]);
        SPDLOG_DEBUG("issue feeder belt control: {}-{}-{}", fd_no, bt_id, speeds[i]);
    }

    return 1;
}


int thing_manager::func_feeder_command_rotate_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int dir, speed, limit;
    std::string feeder_no;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_sortgroup_control_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_CONTROL_ROTATE_DISPATHCH_KEY_FEEDER_NO))
            cjs_cvt::get_instance()->to_string(in[i]->value, feeder_no);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_ROTATE_DISPATHCH_KEY_DIR))
            cjs_cvt::get_instance()->to_enum(in[i]->value, dir);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_ROTATE_DISPATHCH_KEY_SPEED))
            cjs_cvt::get_instance()->to_int(in[i]->value, speed);
        else if (!strcmp(in[i]->key, FUNC_CONTROL_ROTATE_DISPATHCH_KEY_LIMIT))
            cjs_cvt::get_instance()->to_int(in[i]->value, limit);
        else
            SPDLOG_ERROR("invalid value");
    }

    uint32_t feeder_id = atoi(feeder_no.c_str());

    if (dir == FUNC_CONTROL_ROTATE_DIRECTION_KEY_BELT_FORWARD)
        device_manager::get_instance()->issue_control_feeder_rotate_belt_forward(feeder_id, speed, limit);
    else if (dir == FUNC_CONTROL_ROTATE_DIRECTION_KEY_BELT_BACKWARD)
        device_manager::get_instance()->issue_control_feeder_rotate_belt_backward(feeder_id, speed, limit);
    else if (dir == FUNC_BELT_DIRECTION_DISPATCH_DIRECTION_STOP)
        device_manager::get_instance()->issue_control_feeder_rotate_belt_stop(feeder_id, speed, limit);
    else
        SPDLOG_ERROR("wrong target");

    return 1;

}


// int thing_manager::func_switcher_execute_switch(int in_num, JDThingTalkProtoKeyValue_t **in)
// {
//     std::string switcher_no;
//     int action;

//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_switcher_execute_switch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_EXECUTE_SWITCH_KEY_SWITCHER_NO))
//             cjs_cvt::get_instance()->to_string(in[i]->value, switcher_no);
//         else if (!strcmp(in[i]->key, FUNC_EXECUTE_SWITCH_KEY_ACTION))
//             cjs_cvt::get_instance()->to_enum(in[i]->value, action);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     int32_t sw_id = atoi(switcher_no.c_str());
//     if (action == FUNC_EXECUTE_SWITCH_ACTION_OPEN)
//         device_manager::get_instance()->issue_switcher_open(sw_id);
//     else if (action == FUNC_EXECUTE_SWITCH_ACTION_CLOSE)
//         device_manager::get_instance()->issue_switcher_close(sw_id);

//     return 1;
// }

// int thing_manager::func_switcher_set_zero(int in_num, JDThingTalkProtoKeyValue_t **in)
// {
//     std::string switcher_no;
//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_switcher_set_zero key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_ZERO_SET_DISPATCH_KEY_ORBITAL_TRANSFER_NO))
//             cjs_cvt::get_instance()->to_string(in[i]->value, switcher_no);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     int32_t sw_id = atoi(switcher_no.c_str());
//     device_manager::get_instance()->issue_switcher_zero_set(sw_id);

//     return 1;
// }

int thing_manager::func_mcu_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int action;

    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_mcu_control_dispatch key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_MCU_CONTROL_DISPATCH_KEY_ACTION))
            cjs_cvt::get_instance()->to_enum(in[i]->value, action);
        else
            SPDLOG_ERROR("invalid value");
    }

    if (action == FUNC_MCU_CONTROL_DISPATCH_ACTION_REBOOT)
        sys_manager::get_instance()->issue_mcu_reboot();
    else if (action == FUNC_MCU_CONTROL_DISPATCH_ACTION_RESET)
        sys_manager::get_instance()->issue_program_restart();
    else
        SPDLOG_ERROR("wrong target");

    return 1;
}
int thing_manager::func_work_status_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{
    int status;
    for (int i = 0; i < in_num; i++)
    {
        SPDLOG_DEBUG("func_issue_work_status_control key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

        if (!strcmp(in[i]->key, FUNC_WORK_STATUS_SET_VALUE))
            cjs_cvt::get_instance()->to_enum(in[i]->value, status);
        else
            SPDLOG_ERROR("invalid value");
    }

    if ((status == WORK_STATUS_SET_START)||(status == WORK_STATUS_SET_STOP))
        device_manager::get_instance()->issue_work_status_set(status);
    else
        SPDLOG_ERROR("wrong target");

    return 1;
}
// int thing_manager::func_config_set_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
// {

//     std::list<config_set_cmd> config_set;
//     for (int i = 0; i < in_num; i++)
//     {
//         SPDLOG_DEBUG("func_issue_config_set key:{}, value:{}", std::string(in[i]->key), std::string(in[i]->value));

//         if (!strcmp(in[i]->key, FUNC_ONFIG_SET_KEY_CONFIGS))
//             cjs_cvt::get_instance()->to_config_set(in[i]->value, config_set);
//         else
//             SPDLOG_ERROR("invalid value");
//     }

//     for (auto &con : config_set)
//     { 
//         if(con.set_type == s_type_excp_HANDLE)
//         {
//             if(con.set_value < 3)   //简单校验
//             {
//                // device_manager::get_instance()->issue_exception_handle_set(con.set_value);
//                 setting::get_instance()->save_thing_agent_connect_setting(con.set_value);
//                 setting::get_instance()->load_thing_agent_connect_setting();
//             }
                
//         }     
//         else
//             SPDLOG_WARN("not issue to config set  because of type invalid:{}", con.set_type);
//     }
//     return 1;
// }

int thing_manager::func_config_query_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{

    return 1;
}
int thing_manager::func_feeder_belt_status_get_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in)
{

    return 1;
}

//主体属性
int thing_manager::train_agent_properties_set(char *cfg_type, char *cfg_value)
{
    SPDLOG_INFO("cfg_type:{}-{}", cfg_type, cfg_value);

    return 1;
}


int thing_manager::evt_get_barcode_thread()
{
    SPDLOG_DEBUG("evt_get_barcode_thread run");

    while (true)
    {
        //如果获取到noread码值，则不再上报给物控，直接下发兜底任务
        task_manager::barcode_task_info barcode;
        if (task_manager::get_instance()->get_barcode_to_request(barcode))
        {
            SPDLOG_DEBUG("get barcode");
            SPDLOG_DEBUG("barcode size:{}", barcode.code_group.size());
            for (auto &code: barcode.code_group)
                SPDLOG_DEBUG("barcode: {}", code);
            SPDLOG_DEBUG("feeder:{}", barcode.feeder_no);
            SPDLOG_DEBUG("scaner:{}", barcode.scaner_no);
            SPDLOG_DEBUG("task_no:{}", barcode.task_no);

            sys_mode_state sys_state;
            sys_manager::get_instance()->get_sys_state(sys_state);

            if ((!barcode.code_group.front().compare(0, strlen(INVALID_BARCODE1), INVALID_BARCODE1, 0, strlen(INVALID_BARCODE1))       //noread 直接发兜底格口
                || !barcode.code_group.front().compare(0, strlen(INVALID_BARCODE2), INVALID_BARCODE2, 0, strlen(INVALID_BARCODE2)))
                && setting::get_instance()->get_setting().connect_to_thing)
            {
                std::lock_guard<std::mutex> lock(hospice_task_state_lock);
                hospice_task_list.emplace_back(barcode.task_no, NOT_REPORT);

                task_manager::get_instance()->issue_task(std::to_string(device_manager::get_instance()->get_hospice_con_id()), barcode.task_no, FUNC_PALLET_TO_GRID_NOTIFY_TASK_TYPE_NORMAL, DEFAULT_BELT_SPEED);
            }
            else if (sys_state.mode == e_sys_mode_MANNUAL)        //手动模式任务下发
            {
                std::vector<uint32_t> con_list;
                device_manager::get_instance()->get_container_list(con_list);
                int n = rand()%(con_list.size());

                std::lock_guard<std::mutex> lock(hospice_task_state_lock);
                hospice_task_list.emplace_back(barcode.task_no, NOT_REPORT);
                task_manager::get_instance()->issue_task(std::to_string(con_list[n]), barcode.task_no, FUNC_PALLET_TO_GRID_NOTIFY_TASK_TYPE_NORMAL, DEFAULT_BELT_SPEED);
            }
            else
            {
                JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                thing_interface::get_instance()->event_malloc(&event, EVT_KEY_BARCODE_REPORT, 9);

                evt_cvt::get_instance()->from_barcode(event, barcode);

                task_timer.start();
                SPDLOG_INFO("send barcode event, task_id:{} feeder:{} scaner:{}", barcode.task_no,barcode.feeder_no,barcode.scaner_no);

                thing_interface::get_instance()->send_event(*event);
                task_manager::get_instance()->set_task_state(barcode.task_no, task_manager::REPORTED);
            }
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    return 1;
}

thing_manager::report_service_type thing_manager::get_report_task_state(task_manager::task_state state)
{
    if (state.report_state == task_manager::REPORT_SUSPEND)
        return REPORT_SUSPEND;

    report_service_type type = NORMAL_REPORT;

    std::lock_guard<std::mutex> lock(hospice_task_state_lock);
    if (state.status == task_manager::FINISH && state.type == task_manager::DISCLOSED_SORTING)        //超长件或物控下发的兜底分播在此处获取状态时加入
    {
        hospice_task_list.emplace_back(state.task_no, REPORT_HOSPICE_FINISH);
    }

    if (!hospice_task_list.empty())
    {
        for (auto task = hospice_task_list.begin(); task != hospice_task_list.end();)
        {
            if (state.task_no == (*task).task_id)
            {
                SPDLOG_DEBUG("{} is hospice task state, reason: {}", state.task_no, state.exp_info);
                if (state.status == task_manager::FINISH && (*task).report_type == REPORT_HOSPICE_FINISH)
                    type = REPORT_HOSPICE_FINISH;
                else if (state.status != task_manager::FINISH && (*task).report_type == REPORT_HOSPICE_FINISH)
                    type = NOT_REPORT;
                else if ((*task).report_type == NOT_REPORT)
                    type = NOT_REPORT;
                else
                    type = NORMAL_REPORT;

                if (state.status == task_manager::FINISH)
                    task = hospice_task_list.erase(task);
                break;
            }
            else
                ++task;

            if (task == hospice_task_list.end())
                type = NORMAL_REPORT;
        }
    }
    else
        type = NORMAL_REPORT;

    return type;
}

int thing_manager::evt_get_task_state_thread()
{
    SPDLOG_DEBUG("evt_get_task_state_thread run");

    while (true)
    {
        task_manager::task_state state;
        int ret = task_manager::get_instance()->get_task_state_to_report(state);
        report_service_type report_state;
        if (ret)
        {
            report_state = get_report_task_state(state);
            SPDLOG_DEBUG("report task type:{}", report_state);
        }
        state.report_state = static_cast<task_manager::report_server_state>(report_state);
        if (ret && setting::get_instance()->get_setting().production_mode == 1)     //wcs特殊判断和处理
        {
            auto st_temp = state;
            if (st_temp.grid_no == std::to_string(device_manager::get_instance()->get_hospice_con_id()))
                st_temp.report_state = task_manager::NORMAL_REPORT;

            if (st_temp.report_state == task_manager::REPORT_SUSPEND && st_temp.status == task_manager::START)
                st_temp.status = task_manager::FINISH;

            wcs_evt_interface *wcs_event = new wcs_evt_task_state(st_temp);
            wcs_manager::get_instance()->evt_report(wcs_event->data());
            delete wcs_event;
        }

        if (state.report_state == task_manager::REPORT_SUSPEND)       //wcs专供
            continue;

        if (ret && report_state == NORMAL_REPORT)
        {
            JDThingTalkProtoEvtPostEvt_t *event = nullptr;
            thing_interface::get_instance()->event_malloc(&event, EVT_KEY_TASK_STATUS_REPORT, 5);

            evt_cvt::get_instance()->from_normal_task_state(event, state);

            SPDLOG_DEBUG("send task state report event");
            thing_interface::get_instance()->send_event(*event);
        }
        else if (ret && report_state == REPORT_HOSPICE_FINISH)         //任务超时或超长包进兜底或物控下发了兜底分播，直接上报完成
        {
            JDThingTalkProtoEvtPostEvt_t *event = nullptr;
            thing_interface::get_instance()->event_malloc(&event, EVT_KEY_TASK_STATUS_REPORT, 6);

            evt_cvt::get_instance()->from_hospice_task_state(event, state);

            SPDLOG_DEBUG("send task state report event");
            thing_interface::get_instance()->send_event(*event);
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    return 1;
}

int thing_manager::evt_get_container_thread()
{
    SPDLOG_DEBUG("evt_get_container_thread run");

    evt_report_normal_grids_event();

    if (!setting::get_instance()->get_setting().need_report_query_result)
        return 0;

    if (setting::get_instance()->get_setting().mobile_shelf_version)
    {
        while (true)
        {
            box_info_multiple container_states;
            if (device_manager::get_instance()->get_container_rfid(container_states, false))
            {
                JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_GROUP_CONTAINER_ACTION_REPORT, 1);

                evt_cvt::get_instance()->from_container_group_rfid(event, container_states);

                SPDLOG_DEBUG("send container action report event");
                thing_interface::get_instance()->send_event(*event);
            }
            else
                std::this_thread::sleep_for(std::chrono::milliseconds(20));
        }
    }
    else
    {
        while (true)
        {
            box_info_multiple container_states;
            if (device_manager::get_instance()->get_container_rfid(container_states, false))
            {
                JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_CONTAINER_ACTION_REPORT, 1);

                evt_cvt::get_instance()->from_container_rfid(event, container_states);

                SPDLOG_DEBUG("send container action report event");
                thing_interface::get_instance()->send_event(*event);
            }
            else
                std::this_thread::sleep_for(std::chrono::milliseconds(20));
        }
    }

    return 0;
}

int thing_manager::evt_get_box_state_thread()       //容器满箱或空箱上报
{
    SPDLOG_DEBUG("evt_get_box_state_thread run");

    while (true)
    {
        std::vector<slot_state> slot_states;
        if (device_manager::get_instance()->get_container_satr(slot_states, false))
        {
            for (auto &state: slot_states)
            {
                if (state.st == state_FULL || state.st == state_NORMAL)
                {
                    if (setting::get_instance()->get_setting().production_mode == 1)
                        wcs_evt_container_state(state);
                    if (setting::get_instance()->get_setting().container_reprort_mode == 1)
                        container_manager::get_instance()->slot_evt_report(state);
                    if(state.st == state_FULL)
                    {
                        slot_event_report slot_evt;
                        slot_evt.state = state;
                        slot_evt.report_timer.start();
                        slot_events_report_list.emplace_back(slot_evt);
                    }else if(state.st == state_NORMAL)
                    {
                        for (auto evt = slot_events_report_list.begin(); evt != slot_events_report_list.end();)
                        {
                            if (evt->state.id ==  state.id)
                            {
                                evt = slot_events_report_list.erase(evt);
                               // break;
                            }
                            else
                                ++evt;
                        }
                    }                 
                    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_STATUS_REPORT, 1);

                    evt_cvt::get_instance()->from_container_satr(event, state);

                    SPDLOG_DEBUG("send container empty/full report event");
                    thing_interface::get_instance()->send_event(*event);
                    
                }
                else if (state.st == state_RASTER_TRIGGERED)
                {
                    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_GRATING_SENSOR_REPORT, 1);

                    evt_cvt::get_instance()->from_container_raster_state(event, state);

                    SPDLOG_DEBUG("send container raster triggered report event");
                    thing_interface::get_instance()->send_event(*event);
                }
            }
        }
        if (!slot_events_report_list.empty())   //2024.5.29 根据北美项目WCS要求 增加满箱 上报延时，延时时间设置为可配，正常为0，北美8S延时
        {
            for (auto evt = slot_events_report_list.begin(); evt != slot_events_report_list.end();)
            {
                if (evt->report_timer.execute_time() >= (setting::get_instance()->get_setting().time_issue_slot_delay))
                {
                    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
                    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_STATUS_REPORT, 1);

                    evt_cvt::get_instance()->from_container_satr(event, evt->state);

                    SPDLOG_DEBUG("send container empty/full report event :{} :{}",evt->state.id,evt->state.st);
                    thing_interface::get_instance()->send_event(*event);
                    evt = slot_events_report_list.erase(evt);
                }
                else
                    ++evt;
            }

          //  SPDLOG_WARN("recv ack, but event is not in waiting ack event list, message_id:{}", message_id);
        }

            
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    return 0;
}


int thing_manager::evt_get_shelf_lock_state_thread()
{
    SPDLOG_DEBUG("evt_get_shelf_lock_state_thread run");

    shelves_state shelf_state;

    while(true)
    {
        container_interface::get_instance()->get_shelf_lock_state(shelf_state);

        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SEGMENT_SHELF_STATUS_REPORT, 1);

        evt_cvt::get_instance()->from_shelf_state(event, shelf_state);
        
        SPDLOG_DEBUG("send shelf lock state lock/unlock report event");
        thing_interface::get_instance()->send_event(*event);

        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

}


int thing_manager::evt_report_normal_grids_event()
{
    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_LIST_NORMAL_REPORT, 1);

    std::vector<uint32_t> con_list;
    device_manager::get_instance()->get_container_list(con_list);
    evt_cvt::get_instance()->from_container_list(event, con_list);

    SPDLOG_DEBUG("send normal grids list event");
    thing_interface::get_instance()->send_event(*event);

    return 0;
}

int thing_manager::evt_report_sys_register_event()
{
    sys_mode_state sys_state;
    sys_manager::get_instance()->get_sys_state(sys_state);

    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_REGISTER_REPORT, 2);

    evt_cvt::get_instance()->from_sys_register(event, sys_state);

    SPDLOG_DEBUG("send sys register event");
    thing_interface::get_instance()->send_event(*event);

    return 0;
}

int thing_manager::evt_report_sys_state_event(const sys_mode_state &state)
{
    if (setting::get_instance()->get_setting().production_mode == 1)
    {
        wcs_evt_interface *wcs_event = new wcs_evt_sys_state(state);
        wcs_manager::get_instance()->evt_report(wcs_event->data());
        delete wcs_event;
    }

    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_STATUS_REPORT, 5);

    evt_cvt::get_instance()->from_sys_state(event, state);

    SPDLOG_DEBUG("send sys state event: {}-{}", state.state, state.err);
    thing_interface::get_instance()->send_event(*event);

    return 0;
}

int thing_manager::wcs_get_function_thread()
{
    SPDLOG_DEBUG("wcs function recv thread run");
    while (true)
    {
        auto wcs_func = wcs_manager::get_instance()->func_accept();
        if (wcs_func)
            wcs_func->issue();          //todo 接口，增加func_print()
        else
            SPDLOG_DEBUG("invalid func");
    }

    return 0;
}

int thing_manager::evt_get_event_exception_thread()
{
    SPDLOG_DEBUG("evt_get_event_exception_thread run");

    while (true)
    {
        except_info exception;
        sys_manager::get_instance()->get_exception(exception);

        if (exception.src == exception_src_TRAIN)
        {
            evt_get_vehicle_exception(exception);
        }
        else if (exception.src == exception_src_CARRIAGE)
        {
            evt_get_vehicle_exception(exception);
        }
        else if (exception.src == exception_src_PLATFORM)
        {
            evt_get_vehicle_exception(exception);
        }
        else if (exception.src == exception_src_CONTAINER)
        {
            if (setting::get_instance()->get_setting().mobile_shelf_version)
                evt_get_container_group_exception(exception);
            else
                evt_get_container_exception(exception);
        }
        else if (exception.src == exception_src_CONT_SHELVES)
        {
            evt_get_shelves_exception(exception);
        }
        else if (exception.src == exception_src_FEEDER)
        {
            evt_get_feeder_exception(exception);
        }
        else
        {
            evt_get_device_exception(exception);
        }
    }

    return 0;
}

int thing_manager::evt_get_device_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_ERROR_REPORT, 1);

        evt_cvt::get_instance()->from_device_exception_occur(event, exception);

        SPDLOG_DEBUG("send device exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_device_exception_reset(event, exception);

        SPDLOG_DEBUG("send device exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_vehicle_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_SORTGROUP_KEY_ERROR_REPORT, 2);
        evt_cvt::get_instance()->from_vehicle_exception_occur(event, exception);
    
        SPDLOG_DEBUG("send vehicle exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_SORT_KEY_ERROR_RELEASE_REPORT, 1);
        evt_cvt::get_instance()->from_vehicle_exception_reset(event, exception);

        SPDLOG_DEBUG("send vehicle exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_carriage_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_SORTGROUP_SORT_KEY_ERROR_REPORT, 2);
        evt_cvt::get_instance()->from_carriage_exception_occur(event, exception);

        SPDLOG_DEBUG("send carriage exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_SORTGROUP_SORT_KEY_ERROR_RELEASE_REPORT, 1);
        evt_cvt::get_instance()->from_carriage_exception_reset(event, exception);

        SPDLOG_DEBUG("send carriage exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}


int thing_manager::evt_get_platform_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_PLATFORM_KEY_ERROR_REPORT, 2);
        evt_cvt::get_instance()->from_platform_exception_occur(event, exception);

        SPDLOG_DEBUG("send platform exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;

        thing_interface::get_instance()->event_malloc(&event, EVT_PLATFORM_ERROR_RELEASE_REPORT, 1);
        evt_cvt::get_instance()->from_platform_exception_reset(event, exception);

        SPDLOG_DEBUG("send platform exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}


int thing_manager::evt_get_container_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_ERROR_REPORT, 2);

        evt_cvt::get_instance()->from_container_exception_occur(event, exception);

        SPDLOG_DEBUG("send container exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_contianer_exception_reset(event, exception);

        SPDLOG_DEBUG("send container exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_container_group_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_GROUP_ERROR_REPORT, 2);

        evt_cvt::get_instance()->from_container_group_exception_occur(event, exception);

        SPDLOG_DEBUG("send container group exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_GROUP_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_container_group_exception_reset(event, exception);

        SPDLOG_DEBUG("send container group exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_shelves_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SEGMENT_ERROR_REPORT, 2);

        evt_cvt::get_instance()->from_shelves_exception_occur(event, exception);

        SPDLOG_DEBUG("send shelves exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SEGMENT_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_shelves_exception_reset(event, exception);

        SPDLOG_DEBUG("send shelves exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_feeder_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_FEEDER_KEY_ERROR_REPORT, 2);

        evt_cvt::get_instance()->from_feeder_exception_occur(event, exception);

        SPDLOG_DEBUG("send feeder exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_FEEDER_KEY_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_feeder_exception_reset(event, exception);

        SPDLOG_DEBUG("send feeder exception release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_get_switcher_exception(except_info &exception)
{
    if (exception.state == exception_state_STATE_OCCURED)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SWITCHER_ERROR_REPORT, 2);

        evt_cvt::get_instance()->from_switcher_exception_occur(event, exception);

        SPDLOG_DEBUG("send switcher exception report event");
        thing_interface::get_instance()->send_event(*event);
    }
    else        //异常解除为结构体数组
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SWITCHER_ERROR_RELEASE_REPORT, 1);

        evt_cvt::get_instance()->from_switcher_exception_reset(event, exception);
        
        SPDLOG_DEBUG("send switcher exception release release event");
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::evt_report_vehicle_state_event(const e_sys_mode &mode)
{
    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
    // if (mode == e_sys_mode_AUTO)
    //     thing_interface::get_instance()->event_malloc(&event, EVT_SORT_KEY_STATUS_REPORT, 1);
    // else
    //     thing_interface::get_instance()->event_malloc(&event, EVT_SORT_KEY_STATUS_REPORT, 2);

    thing_interface::get_instance()->event_malloc(&event, EVT_SORT_GROUP_KEY_STATUS_REPORT, 1);
    std::unordered_map<int, device_manager::vehicle_total_state> states;
    device_manager::get_instance()->get_vehicle_state(states);

    if(0 == states.size())
        return 0;
        
    evt_cvt::get_instance()->from_vehicle_state(event, states, mode);

    SPDLOG_DEBUG("send vehicle state event");
    thing_interface::get_instance()->send_event(*event);

    return 0;
}

int thing_manager::evt_report_feeder_state_event()
{
    std::vector<int> ids;
    device_manager::get_instance()->get_feeder_ids(ids);

    for (auto &id: ids)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_FEEDER_KEY_STATUS_REPORT, 5);

        feeder_dev_state_total fd_st;
        device_manager::get_instance()->get_feeder_state(id, fd_st);
        std::map<key_id, key_evt_type> bt_st;
        device_manager::get_instance()->get_button_state(id, bt_st);
        evt_cvt::get_instance()->from_feeder_state(event, fd_st, bt_st);

        SPDLOG_DEBUG("send feeder {} state event", fd_st.dev_id);
        thing_interface::get_instance()->send_event(*event); 

        SPDLOG_INFO("get feeder state:{}, exc:{}", fd_st.ready_state, fd_st.excp_handle);      
    }

    return 0;
}

int thing_manager::evt_report_feeder_belt_status_event()
{
    std::vector<int> ids;
    device_manager::get_instance()->get_feeder_ids(ids);

    for (auto &id: ids)
    {
        if(device_manager::get_instance()->get_feeder_belt_state_changed(id))
       // ||(evt_report_timer.report_feeder_belt_timer[id].execute_time() > evt_report_timer.time_report_feeder_belt_state)))
        {
            JDThingTalkProtoEvtPostEvt_t *event = nullptr;
            thing_interface::get_instance()->event_malloc(&event, EVT_FEEDER_BELT_STATUS_REPORT, 2);

            feeder_dev_state_total fd_st;
            device_manager::get_instance()->get_feeder_state(id, fd_st);

            evt_cvt::get_instance()->from_feeder_belt_status(event, fd_st);
            thing_interface::get_instance()->send_event(*event);
            device_manager::get_instance()->set_feeder_belt_state_changed(id,false);
 //           SPDLOG_INFO("get feeder state : {} seq:{}  exc:{}",fd_st.ready_state,fd_st.sequence,fd_st.err_code);
            evt_report_timer.report_feeder_belt_timer[id].start();
  //          SPDLOG_DEBUG("send feeder {} belts state event", fd_st.dev_id);
        }

    }

    return 0;
}

int thing_manager::evt_report_safetygate_state_event(const sys_mode_state &state)
{
    JDThingTalkProtoEvtPostEvt_t *event = nullptr;

    for(int i = 0; i < 2; i++)
    {
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_SAFETYGATE_STATE_REPORT, 3);
        evt_cvt::get_instance()->from_safetygate_state(event, state, i);
    
        thing_interface::get_instance()->send_event(*event);
        SPDLOG_DEBUG("send safetygate state event: {}-{}", state.dev_st.safty_door_open, state.dev_st.safty_door_state);
    }

    return 0;
}

int thing_manager::evt_report_electricalcabinet_state_event(const sys_mode_state &state)
{
    JDThingTalkProtoEvtPostEvt_t *event = nullptr;
    thing_interface::get_instance()->event_malloc(&event, EVT_KEY_ELECTRICAL_CABINET_STATE_REPORT, 2);

    evt_cvt::get_instance()->from_electricalcabinet_state(event, state);
    thing_interface::get_instance()->send_event(*event);
    SPDLOG_DEBUG("send electricalcabinet state event: {}-{}", state.dev_st.emerg_pressed, state.dev_st.emerg_button_state);
    

    return 0;
}

//定时动作
int thing_manager::evt_state_event_thread()
{
    SPDLOG_DEBUG("evt_state_event_thread run");
    
    evt_report_sys_register_event();
    sys_mode_state sys_st;
    sys_manager::get_instance()->get_sys_state(sys_st);
    evt_report_sys_state_event(sys_st);

    while (true)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
        sys_mode_state sys_state;

        sys_manager::get_instance()->get_sys_state(sys_state);
        if (sys_state.state == e_wkstate_SYS_INIT)
            continue;
        evt_report_feeder_state_event();
        evt_report_feeder_belt_status_event();
        evt_report_safetygate_state_event(sys_state);
        evt_report_electricalcabinet_state_event(sys_state);
        break;
    }

    while (true)
    {
        sys_mode_state sys_state;
        sys_manager::get_instance()->get_sys_state(sys_state);

        if(sys_manager::get_instance()->is_sys_state_changed() || evt_report_timer.sys_state_report_timer.execute_time() > evt_report_timer.time_report_sys_state)
        {
            evt_report_safetygate_state_event(sys_state);
            evt_report_electricalcabinet_state_event(sys_state);
        }

        // if(sys_manager::get_instance()->is_sys_state_changed() || sys_state.dev_st.emerg_pressed)
        //     evt_report_electricalcabinet_state_event(sys_state);

        evt_report_feeder_belt_status_event();

        if (sys_manager::get_instance()->is_sys_state_changed()         //系统改变时立即报一次
            || evt_report_timer.sys_state_report_timer.execute_time() > evt_report_timer.time_report_sys_state)
        {
            evt_report_sys_state_event(sys_state);
            evt_report_timer.sys_state_report_timer.start();
        }

        if (sys_state.mode == e_sys_mode_AUTO)
        {
            if (evt_report_timer.vehicle_state_report_timer.execute_time() > evt_report_timer.time_report_vehicle_state_auto)
            {
                SPDLOG_INFO("time_report_vehicle_state_auto");
                evt_report_vehicle_state_event(sys_state.mode);
                evt_report_timer.vehicle_state_report_timer.start();
            }
        }
        else
        {
            if (evt_report_timer.vehicle_state_report_timer.execute_time() > evt_report_timer.time_report_vehicle_state_manual)
            {
                evt_report_vehicle_state_event(sys_state.mode);
                evt_report_timer.vehicle_state_report_timer.start();
            }

            if (evt_report_timer.feeder_state_report_timer.execute_time() > evt_report_timer.time_report_feeder_state)
            {
                evt_report_feeder_state_event();
                evt_report_timer.feeder_state_report_timer.start();
            }

        }

        std::this_thread::sleep_for(std::chrono::milliseconds(30));
    }

    return 0;
}

int thing_manager::evt_report_container_seal_state(const container_seal_state_single &st)
{
    if (setting::get_instance()->get_setting().production_mode == 1)        //分拣场景则上报wcs
        wcs_evt_container_state(st);

    if (setting::get_instance()->get_setting().container_reprort_mode == 1)        //信封瀑布模式  发布格口状态
        container_manager::get_instance()->seal_evt_report(st);
        
    if (setting::get_instance()->get_setting().need_report_seal_state == true)
    {
        JDThingTalkProtoEvtPostEvt_t *event = nullptr;
        thing_interface::get_instance()->event_malloc(&event, EVT_KEY_GRID_SEAL_ACTION_REPORT, 1);

        evt_cvt::get_instance()->from_container_seal_state(event, st);

        SPDLOG_DEBUG("send container {} seal state event {}", st.container_id, st.seal_state);
        thing_interface::get_instance()->send_event(*event);
    }

    return 0;
}

int thing_manager::init(zmq::context_t &context)
{
    SPDLOG_DEBUG("thing_manager init");

    device_manager::get_instance()->init(context);

    task_manager::get_instance()->init(context);

    sys_manager::get_instance()->init(context);

    if (setting::get_instance()->get_setting().production_mode == 1)
        wcs_manager::get_instance()->init(context);

    if (setting::get_instance()->get_setting().container_reprort_mode == 1)
        container_manager::get_instance()->init(context);
    set_module_callback_function();

    evt_report_timer.init();

    return 0;
}

int thing_manager::func_issue(JDThingTalkProtoFuncCallFunc_t &function, std::vector<int> devs)
{
    int ret = 0;
    SPDLOG_INFO("&&&&&&1234");

    if (!strcmp(function.key, FUNC_KEY_MODE_SWITCH))
        ret = thing_manager::get_instance()->func_switch_mode(function.in_num, function.in);
    else if (!strcmp(function.key, FUNC_KEY_GRID_LAMP_STATUS_NOTIFY))
        ret = thing_manager::get_instance()->func_update_container_led(function.in_num, function.in, devs);
    else if (!strcmp(function.key, FUNC_KEY_GRID_SEAL_STATUS_NOTIFY))
        ret = thing_manager::get_instance()->func_update_container_state(function.in_num, function.in, devs);
    else if (!strcmp(function.key, FUNC_KEY_PALLET_TO_GRID_NOTIFY))
        ret = thing_manager::get_instance()->func_issue_sorting_task(function.in_num, function.in);
    else if (!strcmp(function.key, FUNC_KEY_COMMAND_DISPATCH))
        ret = thing_manager::get_instance()->func_sort_command_dispatch(function.in_num, function.in);
    else if (!strcmp(function.key, FUNC_MCU_KEY_CONTROL_DISPATCH))
        ret = thing_manager::get_instance()->func_mcu_control_dispatch(function.in_num, function.in);
    else if (!strcmp(function.key, FUNC_WORK_STATUS_SET))
        ret = thing_manager::get_instance()->func_work_status_dispatch(function.in_num, function.in);
    // else if (!strcmp(function.key, FUNC_CONFIG_SET))
    //     ret = thing_manager::get_instance()->func_config_set_dispatch(function.in_num, function.in);
    else
        SPDLOG_WARN("auto mode wrong function key: {}", std::string(function.key));

    if (ret)
    {
        SPDLOG_DEBUG("issue function {} to dev done", function.key);
        return ret;
    }

    sys_mode_state sys_st;
    sys_manager::get_instance()->get_sys_state(sys_st);
    // if (sys_st.state != e_wkstate_SYS_MANUAL)
    // {
    //     SPDLOG_DEBUG("issue function {} to dev failed", function.key);
    //     return ret;
    // }
    // else        //演示模式下才接收的方法
    // {
        if (!strcmp(function.key, FUNC_KEY_SORTS_CONTINUATION_RUN))
            ret = thing_manager::get_instance()->func_issue_vehicle_demo(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_PILOT_LAMP_STATUS_NOTIFY))
            ret = thing_manager::get_instance()->func_update_hmi_lamp(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_BUZZER_CONTROL))
            ret = thing_manager::get_instance()->func_issue_buzzer_control(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_GRID_IN_ORDER_CONTROL))
            ret = thing_manager::get_instance()->func_issue_container_lamp_test(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_AUTO_CODE_SCAN))
            ret = thing_manager::get_instance()->func_issue_scanner_control(function.in_num, function.in);
        // else if (!strcmp(function.key, FUNC_KEY_CONTROL_DISPATCH))
            // ret = thing_manager::get_instance()->func_sort_control_dispatch(function.in_num, function.in);
        // else if (!strcmp(function.key, FUNC_KEY_CONTROL_DEST_DISPATCH))
        //     ret = thing_manager::get_instance()->func_sort_control_dest_dispatch(function.in_num, function.in);
        // else if (!strcmp(function.key, FUNC_KEY_CONTROL_WALK_DISPATCH))
        //     ret = thing_manager::get_instance()->func_sort_control_walk_dispatch(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_BELT_DIRECTION_DISPATCH))
            ret = thing_manager::get_instance()->func_sort_belt_control(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_PLATFORM_COMMAND_DISPATCH))
            ret = thing_manager::get_instance()->func_sort_platform_command_dispatch(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_PLATFORM_CONTROL_DISPATCH))
            ret = thing_manager::get_instance()->func_sort_platform_control_dispatch(function.in_num, function.in);

        else if(!strcmp(function.key, FUNC_KEY_SORTGROUP_COMMAND_DISPATCH))
            ret = thing_manager::get_instance()->func_sortgroup_command_dispatch(function.in_num, function.in);
        else if(!strcmp(function.key, FUNC_KEY_SORTGROUP_CONTROL_WALK_DISPATCH))
            ret = thing_manager::get_instance()->func_sortgroup_control_walk_dispatch(function.in_num, function.in);

        else if(!strcmp(function.key, FUNC_KEY_SORTGROUP_SORT_COMMAND_DISPATCH))
            ret = thing_manager::get_instance()->func_sortgroup_sort_command_dispatch(function.in_num, function.in);

        else if (!strcmp(function.key, FUNC_SHELF_LOCK_CONTROL))
            ret = thing_manager::get_instance()->func_issue_shelf_lock_control(function.in_num, function.in);

        else if (!strcmp(function.key, FUNC_KEY_FEEDER_CONTROL_DISIPATCH))
            ret = thing_manager::get_instance()->func_feeder_command_dispatch(function.in_num, function.in);
        else if (!strcmp(function.key, FUNC_KEY_FEEDER_CONTROL_ROTATE_DISIPATCH))
            ret = thing_manager::get_instance()->func_feeder_command_rotate_dispatch(function.in_num, function.in);

        // else if (!strcmp(function.key, FUNC_KEY_EXECUTE_SWITCH))
        //     ret = thing_manager::get_instance()->func_switcher_execute_switch(function.in_num, function.in);
        // else if (!strcmp(function.key, FUNC_KEY_ZERO_SET_DISPATCH))
        //     ret = thing_manager::get_instance()->func_switcher_set_zero(function.in_num, function.in);
        // else
        //     SPDLOG_ERROR("wrong function key: {}", std::string(function.key));

        if (ret)
            SPDLOG_DEBUG("issue function {} to dev done", function.key);
        else
            SPDLOG_DEBUG("issue function {} to dev failed", function.key);
    // }

    return ret;
}

int thing_manager::properties_issue(JDThingTalkProtoKeyValue_t **properties)
{
    int ret = 0;

    SPDLOG_INFO("properties_set key:{}, value:{}", properties[0]->key, properties[0]->value);

    if (!strcmp(properties[0]->key, EVT_PARAMETER_SET_PACKAGE_SENSOR_SWITCH))
        ret = thing_manager::get_instance()->train_agent_properties_set(properties[0]->key, properties[0]->value);
    

    else
        SPDLOG_WARN("properties set wrong key: {}", std::string(properties[0]->key));

    if (ret)
    {
        SPDLOG_DEBUG("issue properties {} to dev done", properties[0]->key);
        return ret;
    }

                     

               

        

// EVT_PARAMETER_SET_CALIBRATION_SPEED               

// EVT_PARAMETER_SET_VARIABLE_SPEED                  

// EVT_PARAMETER_SET_NORMAL_LINEAR_SPEED             

// EVT_PARAMETER_SET_NORMAL_CURVE_SPEED              

// EVT_PARAMETER_SET_NORMAL_APPROACH_SPECIAL_SPEED   

// EVT_PARAMETER_SET_NORMAL_CHASE_SPEED              

// EVT_PARAMETER_SET_MAX_DYNAMIC_ADJUST_DISTANCE     

// EVT_PARAMETER_SET_AUTO_RECOVERY                   

// EVT_PARAMETER_SET_BELT_ZERO_DETECTION             

// EVT_PARAMETER_SET_GRID_FULL_ALARM                 

// EVT_PARAMETER_SET_SYSTEM_STOP_ALARM               

// EVT_PARAMETER_SET_MULTI_LOOP_DROP                 

// EVT_PARAMETER_SET_MULTI_LOOP_ATTEMPTS             

// EVT_PARAMETER_SET_GRID_FULL_FALLBACK              

// EVT_PARAMETER_SET_GRID_SEAL_FALLBACK              

// EVT_PARAMETER_SET_SYSTEM_FALLBACK_STRATEGY        

// EVT_PARAMETER_SET_VERTICAL_ERROR_LIMIT            

// EVT_PARAMETER_SET_TASK_TIMEOUT_FALLBACK           

// EVT_PARAMETER_SET_AUTO_PACKAGE_OFFSET             



// agent参数

// EVT_PARAMETER_SET_HEARTBEAT_INTERVAL              

// EVT_PARAMETER_SET_DATA_RESEND_TIMEOUT             

// EVT_PARAMETER_SET_APPROACH_SENSOR_TOLERANCE       

// EVT_PARAMETER_SET_PACKAGE_SENSOR_SWITCH           




}

int thing_manager::wcs_evt_container_state(const slot_state &st)
{
    wcs_evt_interface *wcs_event = new wcs_container_seal_state(st);
    wcs_manager::get_instance()->evt_report(wcs_event->data());
    delete wcs_event;

    return 0;
}

int thing_manager::wcs_evt_container_state(const container_seal_state_single &st)
{
    wcs_evt_interface *wcs_event = new wcs_container_seal_state(st);
    wcs_manager::get_instance()->evt_report(wcs_event->data());
    delete wcs_event;

    return 0;
}

void thing_manager::set_module_callback_function()
{
    //using func_type = int (thing_manager::*)(const container_seal_state_single &st);
    //auto seal_state_function = std::bind((func_type)&thing_manager::wcs_evt_container_state, this, std::placeholders::_1);
    auto seal_state_function = std::bind(&thing_manager::evt_report_container_seal_state, this, std::placeholders::_1);
    device_manager::get_instance()->set_report_seal_state_function(seal_state_function);
}

void thing_manager::device_event_thread()
{
    while (true)
        if (thing_interface::get_instance()->is_connect_done())
        {
            evt_state_event = new std::thread(&thing_manager::evt_state_event_thread, this);

            if (setting::get_instance()->get_setting().production_mode == 0)
                evt_barcode = new std::thread(&thing_manager::evt_get_barcode_thread, this);

            evt_task_state = new std::thread(&thing_manager::evt_get_task_state_thread, this);

            evt_container = new std::thread(&thing_manager::evt_get_container_thread, this);

            evt_box_state = new std::thread(&thing_manager::evt_get_box_state_thread, this);

            evt_shelf_lock_state = new std::thread(&thing_manager::evt_get_shelf_lock_state_thread, this);

            evt_exception = new std::thread(&thing_manager::evt_get_event_exception_thread, this);

            if (setting::get_instance()->get_setting().production_mode == 1)
                wcs_function = new std::thread(&thing_manager::wcs_get_function_thread, this);

            device_manager::get_instance()->run();

            sys_manager::get_instance()->run();

            break;
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(30));

    while (true)
        std::this_thread::sleep_for(std::chrono::milliseconds(30));

}

int thing_manager::run()
{
    event_thread = new std::thread(&thing_manager::device_event_thread, this);

    return 0;
}
