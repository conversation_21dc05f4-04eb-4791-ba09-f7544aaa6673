#include "swap_agent_config.hpp"
#include "multi_swap_manager.hpp"


using namespace std;


static spdlog::level::level_enum train_agent_log_level_convers(string &str_in)
{
	if( !strcmp(str_in.c_str(), LOG_LEVEL_TRACE) )
	{
		return spdlog::level::trace;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_DEBUG) )
	{
		return spdlog::level::debug;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_INFO) )
	{
		return spdlog::level::info;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_WARN) )
	{
		return spdlog::level::warn;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_ERR) )
	{
		return spdlog::level::err;
	}
	else if( !strcmp(str_in.c_str(), LOG_LEVEL_OFF) )
	{
		return spdlog::level::off;
	}
	else
	{
		return spdlog::level::off;
	}
}

typedef struct _log_cfg_locol
{
	string log_level;
}log_cfg_locol;


void from_json(const nlohmann::json& j, server_info_t & v) 
{
	j.at("server_addr").get_to(v.server_addr);
	j.at("server_port").get_to(v.server_port);
}

void from_json(const nlohmann::json& j, log_cfg_locol& v) 
{
	j.at("log_level").get_to(v.log_level);
}

bool train_agent_get_config(log_cfg *log_config, dev_agent_cfg *dev_cfg)
{
	std::string file_name;

	std::string home_path = getenv("HOME");
	file_name = home_path + "/auto_sort_high_efficient/cfg_file/autoswap.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;

    nlohmann::json j;
    ifstream jfile(file_name.c_str());


#if 1
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());

		dev_cfg->server_info.server_addr = "0.0.0.0";
		dev_cfg->server_info.server_port = 9000;
		dev_cfg->server_info.client_port = 9001;
		log_config->log_level = spdlog::level::debug;
		
		return false;
	}
#endif


    log_cfg_locol log = j.at("log");
	std::cout << log.log_level << std::endl;

	server_info_t cfg = j.at("server");
	log_config->log_level = train_agent_log_level_convers(log.log_level);

	dev_cfg->server_info.server_addr = j["server"]["server_addr"];
	dev_cfg->server_info.server_port = j["server"]["server_port"];
	dev_cfg->server_info.client_port = j["server"]["client_port"];

	// dev_cfg->server_info.server_addr = cfg.server_addr;
	// dev_cfg->server_info.server_port = cfg.server_port;
	// std::cout << "server_addr: " << dev_cfg->server_info.server_addr << std::endl; 
	// std::cout << "server_port: " << dev_cfg->server_info.server_port << std::endl; 

	dev_cfg->component_count = j["config"]["component_count"];

    dev_cfg->heartbeat_timeout = j["config"]["heartbeat_timeout"];	//单位：秒
	dev_cfg->heartbeat_cycle = j["config"]["heartbeat_cycle"];
	dev_cfg->resend_timeout = j["config"]["resend_timeout"];

	dev_cfg->motor_speed_x = j["config"]["motor_speed_x"];
	dev_cfg->motor_acc_x = j["config"]["motor_acc_x"];
	dev_cfg->motor_dec_x = j["config"]["motor_dec_x"];
	dev_cfg->motor_speed_y = j["config"]["motor_speed_y"];
	dev_cfg->motor_acc_y = j["config"]["motor_acc_y"];
	dev_cfg->motor_dec_y = j["config"]["motor_dec_y"];
	dev_cfg->motor_no_load_speed_z = j["config"]["motor_no_load_speed_z"];
	dev_cfg->motor_load_speed_z = j["config"]["motor_load_speed_z"];
	dev_cfg->motor_load_acc_z = j["config"]["motor_load_acc_z"];
	dev_cfg->motor_unload_acc_z = j["config"]["motor_unload_acc_z"];
	dev_cfg->reset_force_send_flag = j["config"]["reset_force_send_flag"];



	return true;
}


