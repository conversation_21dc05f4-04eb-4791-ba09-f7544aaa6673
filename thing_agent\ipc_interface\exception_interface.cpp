#include "exception_interface.hpp"

int exception_interface::init(zmq::context_t &ctx)
{
	exception_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};

	exception_recver -> connect(TOPIC_EXCEPTION_SCHED);
    // exception_recver -> connect(TOPIC_EXCEPTION_PLANE);
    exception_recver -> connect(TOPIC_EXCEPTION_TRAIN);
    exception_recver -> connect(TOPIC_EXCEPTION_CONTAINER);
    exception_recver -> connect(TOPIC_EXCEPTION_CORE_SERVICE);
    // exception_recver -> connect(TOPIC_EXCEPTION_DB);
    exception_recver -> connect(TOPIC_EXCEPTION_FEEDER);
    exception_recver -> connect(TOPIC_EXCEPTION_THING);
    // exception_recver -> connect(TOPIC_EXCEPTION_FEEDER_BASICK_SORT);
    exception_recver -> set(zmq::sockopt::subscribe, "");

    exception_sender = new zmq::socket_t {ctx, zmq::socket_type::pub};
	exception_sender -> bind(TOPIC_EXCEPTION_THING);

	return 0;
}

int exception_interface::report_exception(const event_exception &except)
{
	uint8_t pub_msg[event_exception_size];
	pb_ostream_t stream_out;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, event_exception_fields, &except))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}

	auto ret = exception_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
	if(ret.has_value())
		return ret.value();
	else return -1;
}

//异常return 1，供包台反转return 2，其他return 0
int exception_interface::get_event_exception(event_exception &exception_event)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    event_exception evt_exp;

    exception_recver->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, event_exception_fields, &evt_exp))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
        if (evt_exp.which_evt_except == event_exception_except_tag)
        {
            exception_event.evt_except.except = evt_exp.evt_except.except;
            exception_event.which_evt_except = event_exception_except_tag;

            if (evt_exp.evt_except.except.state == exception_state_STATE_OCCURED)
            {
                SPDLOG_INFO("recv exception occur: [{}][{}][{}]-{}:{}-{}; {}]",
                            evt_exp.evt_except.except.src,
                            evt_exp.evt_except.except.dev,
                            evt_exp.evt_except.except.sub_dev,
                            evt_exp.evt_except.except.code,
                            evt_exp.evt_except.except.sub_code,
                            evt_exp.evt_except.except.level,
                            evt_exp.evt_except.except.description);
            }
            else if (evt_exp.evt_except.except.state == exception_state_STATE_RESET)
            {
                SPDLOG_INFO("recv exception reset: [{}][{}][{}]-{}:{}-{}; {}]",
                            evt_exp.evt_except.except.src,
                            evt_exp.evt_except.except.dev,
                            evt_exp.evt_except.except.sub_dev,
                            evt_exp.evt_except.except.code,
                            evt_exp.evt_except.except.sub_code,
                            evt_exp.evt_except.except.level,
                            evt_exp.evt_except.except.description);
            }

            return 1;
        }
        else if (evt_exp.which_evt_except == event_exception_evt_tag)
        {
            exception_event.evt_except.evt = evt_exp.evt_except.evt;
            exception_event.which_evt_except = event_exception_evt_tag;

            SPDLOG_INFO("recv event: [{}-{}-{}-{}-{}]",
                evt_exp.evt_except.evt.src,
                evt_exp.evt_except.evt.dev,
                evt_exp.evt_except.evt.sub_dev,
                evt_exp.evt_except.evt.code,
                evt_exp.evt_except.evt.sub_code);
            
            return 1;
        }
        else
        {
            SPDLOG_DEBUG("error exception_event");
            return 0;
        }
    }

    return 0;
}
