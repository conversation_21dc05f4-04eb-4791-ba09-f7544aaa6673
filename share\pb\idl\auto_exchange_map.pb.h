/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_AUTO_EXCHANGE_MAP_PB_H_INCLUDED
#define PB_AUTO_EXCHANGE_MAP_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _auto_exchange_map_calib_polarity_type {
    auto_exchange_map_calib_polarity_type_TYPE_RESERVE = 0, /* 无定义 */
    auto_exchange_map_calib_polarity_type_TYPE_POSTIVE = 1, /* 正向 */
    auto_exchange_map_calib_polarity_type_TYPE_NEGATIVE = 2, /* 负向 */
    auto_exchange_map_calib_polarity_type_TYPE_LEFT = 3,
    auto_exchange_map_calib_polarity_type_TYPE_RIGHT = 4
} auto_exchange_map_calib_polarity_type;

typedef enum _auto_exchange_map_calib_point_type {
    auto_exchange_map_calib_point_type_CALIB_POINT_ORDINARY = 0,
    auto_exchange_map_calib_point_type_CALIB_POINT_ORIGIN = 1
} auto_exchange_map_calib_point_type;

/* Struct definitions */
typedef struct _auto_exchange_map_pack_station_dev_info {
    uint32_t id;
    float dev_pos_x;
    float dev_pos_y;
    float dev_height;
    bool valid_flag;
    int32_t curr_active_device;
} auto_exchange_map_pack_station_dev_info;

typedef struct _auto_exchange_map_pack_station_map {
    uint32_t dev_cnt;
    pb_size_t pack_station_info_count;
    auto_exchange_map_pack_station_dev_info pack_station_info[60];
} auto_exchange_map_pack_station_map;

typedef struct _auto_exchange_map_auto_exchange_map_para {
    uint32_t id;
    uint32_t dev_width;
    uint32_t dev_height;
    uint32_t grab_dis;
    uint32_t unload_dis;
    uint32_t unload_negative_dis;
    uint32_t bind_slot_sect_cnt;
    uint32_t bind_slot_min;
    uint32_t bind_slot_max;
    int32_t zero_inner_pos;
    int32_t zero_global_pos;
    uint32_t calib_polarity;
    int32_t calib_len;
    uint32_t bind_pack_staion_no;
    bool has_bind_pack_station_info;
    auto_exchange_map_pack_station_dev_info bind_pack_station_info;
} auto_exchange_map_auto_exchange_map_para;

typedef struct _auto_exchange_map {
    float total_length;
    float total_height;
    int32_t dev_total_cnt;
    pb_size_t auto_exch_map_count;
    auto_exchange_map_auto_exchange_map_para auto_exch_map[32];
    int32_t pack_station_total_cnt;
    pb_size_t pack_stat_map_count;
    auto_exchange_map_pack_station_dev_info pack_stat_map[32];
} auto_exchange_map;

typedef struct _auto_exchange_conv_para {
    int32_t x_axis_z1_offset;
    int32_t y_axis_z1_offset;
    int32_t x_axis_z2_offset;
    int32_t y_axis_z2_offset;
    int32_t x_axis_z3_offset;
    int32_t y_axis_z3_offset;
} auto_exchange_conv_para;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _auto_exchange_map_calib_polarity_type_MIN auto_exchange_map_calib_polarity_type_TYPE_RESERVE
#define _auto_exchange_map_calib_polarity_type_MAX auto_exchange_map_calib_polarity_type_TYPE_RIGHT
#define _auto_exchange_map_calib_polarity_type_ARRAYSIZE ((auto_exchange_map_calib_polarity_type)(auto_exchange_map_calib_polarity_type_TYPE_RIGHT+1))

#define _auto_exchange_map_calib_point_type_MIN auto_exchange_map_calib_point_type_CALIB_POINT_ORDINARY
#define _auto_exchange_map_calib_point_type_MAX auto_exchange_map_calib_point_type_CALIB_POINT_ORIGIN
#define _auto_exchange_map_calib_point_type_ARRAYSIZE ((auto_exchange_map_calib_point_type)(auto_exchange_map_calib_point_type_CALIB_POINT_ORIGIN+1))







/* Initializer values for message structs */
#define auto_exchange_map_init_default           {0, 0, 0, 0, {auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default, auto_exchange_map_auto_exchange_map_para_init_default}, 0, 0, {auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default}}
#define auto_exchange_map_pack_station_dev_info_init_default {0, 0, 0, 0, 0, 0}
#define auto_exchange_map_pack_station_map_init_default {0, 0, {auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default, auto_exchange_map_pack_station_dev_info_init_default}}
#define auto_exchange_map_auto_exchange_map_para_init_default {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, auto_exchange_map_pack_station_dev_info_init_default}
#define auto_exchange_conv_para_init_default     {0, 0, 0, 0, 0, 0}
#define auto_exchange_map_init_zero              {0, 0, 0, 0, {auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero, auto_exchange_map_auto_exchange_map_para_init_zero}, 0, 0, {auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero}}
#define auto_exchange_map_pack_station_dev_info_init_zero {0, 0, 0, 0, 0, 0}
#define auto_exchange_map_pack_station_map_init_zero {0, 0, {auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero, auto_exchange_map_pack_station_dev_info_init_zero}}
#define auto_exchange_map_auto_exchange_map_para_init_zero {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, auto_exchange_map_pack_station_dev_info_init_zero}
#define auto_exchange_conv_para_init_zero        {0, 0, 0, 0, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define auto_exchange_map_pack_station_dev_info_id_tag 1
#define auto_exchange_map_pack_station_dev_info_dev_pos_x_tag 2
#define auto_exchange_map_pack_station_dev_info_dev_pos_y_tag 3
#define auto_exchange_map_pack_station_dev_info_dev_height_tag 4
#define auto_exchange_map_pack_station_dev_info_valid_flag_tag 5
#define auto_exchange_map_pack_station_dev_info_curr_active_device_tag 6
#define auto_exchange_map_pack_station_map_dev_cnt_tag 1
#define auto_exchange_map_pack_station_map_pack_station_info_tag 2
#define auto_exchange_map_auto_exchange_map_para_id_tag 1
#define auto_exchange_map_auto_exchange_map_para_dev_width_tag 2
#define auto_exchange_map_auto_exchange_map_para_dev_height_tag 3
#define auto_exchange_map_auto_exchange_map_para_grab_dis_tag 4
#define auto_exchange_map_auto_exchange_map_para_unload_dis_tag 5
#define auto_exchange_map_auto_exchange_map_para_unload_negative_dis_tag 6
#define auto_exchange_map_auto_exchange_map_para_bind_slot_sect_cnt_tag 7
#define auto_exchange_map_auto_exchange_map_para_bind_slot_min_tag 8
#define auto_exchange_map_auto_exchange_map_para_bind_slot_max_tag 9
#define auto_exchange_map_auto_exchange_map_para_zero_inner_pos_tag 10
#define auto_exchange_map_auto_exchange_map_para_zero_global_pos_tag 11
#define auto_exchange_map_auto_exchange_map_para_calib_polarity_tag 12
#define auto_exchange_map_auto_exchange_map_para_calib_len_tag 13
#define auto_exchange_map_auto_exchange_map_para_bind_pack_staion_no_tag 14
#define auto_exchange_map_auto_exchange_map_para_bind_pack_station_info_tag 15
#define auto_exchange_map_total_length_tag       1
#define auto_exchange_map_total_height_tag       2
#define auto_exchange_map_dev_total_cnt_tag      3
#define auto_exchange_map_auto_exch_map_tag      4
#define auto_exchange_map_pack_station_total_cnt_tag 5
#define auto_exchange_map_pack_stat_map_tag      6
#define auto_exchange_conv_para_x_axis_z1_offset_tag 1
#define auto_exchange_conv_para_y_axis_z1_offset_tag 2
#define auto_exchange_conv_para_x_axis_z2_offset_tag 3
#define auto_exchange_conv_para_y_axis_z2_offset_tag 4
#define auto_exchange_conv_para_x_axis_z3_offset_tag 5
#define auto_exchange_conv_para_y_axis_z3_offset_tag 6

/* Struct field encoding specification for nanopb */
#define auto_exchange_map_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    total_length,      1) \
X(a, STATIC,   SINGULAR, FLOAT,    total_height,      2) \
X(a, STATIC,   SINGULAR, INT32,    dev_total_cnt,     3) \
X(a, STATIC,   REPEATED, MESSAGE,  auto_exch_map,     4) \
X(a, STATIC,   SINGULAR, INT32,    pack_station_total_cnt,   5) \
X(a, STATIC,   REPEATED, MESSAGE,  pack_stat_map,     6)
#define auto_exchange_map_CALLBACK NULL
#define auto_exchange_map_DEFAULT NULL
#define auto_exchange_map_auto_exch_map_MSGTYPE auto_exchange_map_auto_exchange_map_para
#define auto_exchange_map_pack_stat_map_MSGTYPE auto_exchange_map_pack_station_dev_info

#define auto_exchange_map_pack_station_dev_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, FLOAT,    dev_pos_x,         2) \
X(a, STATIC,   SINGULAR, FLOAT,    dev_pos_y,         3) \
X(a, STATIC,   SINGULAR, FLOAT,    dev_height,        4) \
X(a, STATIC,   SINGULAR, BOOL,     valid_flag,        5) \
X(a, STATIC,   SINGULAR, INT32,    curr_active_device,   6)
#define auto_exchange_map_pack_station_dev_info_CALLBACK NULL
#define auto_exchange_map_pack_station_dev_info_DEFAULT NULL

#define auto_exchange_map_pack_station_map_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_cnt,           1) \
X(a, STATIC,   REPEATED, MESSAGE,  pack_station_info,   2)
#define auto_exchange_map_pack_station_map_CALLBACK NULL
#define auto_exchange_map_pack_station_map_DEFAULT NULL
#define auto_exchange_map_pack_station_map_pack_station_info_MSGTYPE auto_exchange_map_pack_station_dev_info

#define auto_exchange_map_auto_exchange_map_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UINT32,   dev_width,         2) \
X(a, STATIC,   SINGULAR, UINT32,   dev_height,        3) \
X(a, STATIC,   SINGULAR, UINT32,   grab_dis,          4) \
X(a, STATIC,   SINGULAR, UINT32,   unload_dis,        5) \
X(a, STATIC,   SINGULAR, UINT32,   unload_negative_dis,   6) \
X(a, STATIC,   SINGULAR, UINT32,   bind_slot_sect_cnt,   7) \
X(a, STATIC,   SINGULAR, UINT32,   bind_slot_min,     8) \
X(a, STATIC,   SINGULAR, UINT32,   bind_slot_max,     9) \
X(a, STATIC,   SINGULAR, INT32,    zero_inner_pos,   10) \
X(a, STATIC,   SINGULAR, INT32,    zero_global_pos,  11) \
X(a, STATIC,   SINGULAR, UINT32,   calib_polarity,   12) \
X(a, STATIC,   SINGULAR, INT32,    calib_len,        13) \
X(a, STATIC,   SINGULAR, UINT32,   bind_pack_staion_no,  14) \
X(a, STATIC,   OPTIONAL, MESSAGE,  bind_pack_station_info,  15)
#define auto_exchange_map_auto_exchange_map_para_CALLBACK NULL
#define auto_exchange_map_auto_exchange_map_para_DEFAULT NULL
#define auto_exchange_map_auto_exchange_map_para_bind_pack_station_info_MSGTYPE auto_exchange_map_pack_station_dev_info

#define auto_exchange_conv_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    x_axis_z1_offset,   1) \
X(a, STATIC,   SINGULAR, INT32,    y_axis_z1_offset,   2) \
X(a, STATIC,   SINGULAR, INT32,    x_axis_z2_offset,   3) \
X(a, STATIC,   SINGULAR, INT32,    y_axis_z2_offset,   4) \
X(a, STATIC,   SINGULAR, INT32,    x_axis_z3_offset,   5) \
X(a, STATIC,   SINGULAR, INT32,    y_axis_z3_offset,   6)
#define auto_exchange_conv_para_CALLBACK NULL
#define auto_exchange_conv_para_DEFAULT NULL

extern const pb_msgdesc_t auto_exchange_map_msg;
extern const pb_msgdesc_t auto_exchange_map_pack_station_dev_info_msg;
extern const pb_msgdesc_t auto_exchange_map_pack_station_map_msg;
extern const pb_msgdesc_t auto_exchange_map_auto_exchange_map_para_msg;
extern const pb_msgdesc_t auto_exchange_conv_para_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define auto_exchange_map_fields &auto_exchange_map_msg
#define auto_exchange_map_pack_station_dev_info_fields &auto_exchange_map_pack_station_dev_info_msg
#define auto_exchange_map_pack_station_map_fields &auto_exchange_map_pack_station_map_msg
#define auto_exchange_map_auto_exchange_map_para_fields &auto_exchange_map_auto_exchange_map_para_msg
#define auto_exchange_conv_para_fields &auto_exchange_conv_para_msg

/* Maximum encoded size of messages (where known) */
#define AUTO_EXCHANGE_MAP_PB_H_MAX_SIZE          auto_exchange_map_size
#define auto_exchange_conv_para_size             66
#define auto_exchange_map_auto_exchange_map_para_size 135
#define auto_exchange_map_pack_station_dev_info_size 34
#define auto_exchange_map_pack_station_map_size  2166
#define auto_exchange_map_size                   5600

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
