# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include scheduler_msg/CMakeFiles/lib_msg.dir/depend.make

# Include the progress variables for this target.
include scheduler_msg/CMakeFiles/lib_msg.dir/progress.make

# Include the compile flags for this target's objects.
include scheduler_msg/CMakeFiles/lib_msg.dir/flags.make

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o: scheduler_msg/CMakeFiles/lib_msg.dir/flags.make
scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o: ../scheduler_msg/scheduler_msg.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_msg.dir/scheduler_msg.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp > CMakeFiles/lib_msg.dir/scheduler_msg.cpp.i

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_msg.dir/scheduler_msg.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp -o CMakeFiles/lib_msg.dir/scheduler_msg.cpp.s

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.requires:

.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.requires

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.provides: scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.requires
	$(MAKE) -f scheduler_msg/CMakeFiles/lib_msg.dir/build.make scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.provides.build
.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.provides

scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.provides.build: scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o


# Object files for target lib_msg
lib_msg_OBJECTS = \
"CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o"

# External object files for target lib_msg
lib_msg_EXTERNAL_OBJECTS =

scheduler_msg/liblib_msg.a: scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o
scheduler_msg/liblib_msg.a: scheduler_msg/CMakeFiles/lib_msg.dir/build.make
scheduler_msg/liblib_msg.a: scheduler_msg/CMakeFiles/lib_msg.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library liblib_msg.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && $(CMAKE_COMMAND) -P CMakeFiles/lib_msg.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lib_msg.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
scheduler_msg/CMakeFiles/lib_msg.dir/build: scheduler_msg/liblib_msg.a

.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/build

scheduler_msg/CMakeFiles/lib_msg.dir/requires: scheduler_msg/CMakeFiles/lib_msg.dir/scheduler_msg.cpp.o.requires

.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/requires

scheduler_msg/CMakeFiles/lib_msg.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg && $(CMAKE_COMMAND) -P CMakeFiles/lib_msg.dir/cmake_clean.cmake
.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/clean

scheduler_msg/CMakeFiles/lib_msg.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/scheduler_msg/CMakeFiles/lib_msg.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : scheduler_msg/CMakeFiles/lib_msg.dir/depend

