#include "feeder_interface.hpp"

int feeder_interface::init(zmq::context_t &ctx)
{
    feeder_cmd_total = new zmq::socket_t {ctx, zmq::socket_type::req};
	feeder_cmd_total -> connect(SERVICE_FEEDER_CMD);

    feeder_state_total = new zmq::socket_t {ctx, zmq::socket_type::sub};
	feeder_state_total -> connect(TOPIC_FEEDER_STATE);
	feeder_state_total -> set(zmq::sockopt::subscribe, "");

    feeder_state_button = new zmq::socket_t {ctx, zmq::socket_type::sub};
	feeder_state_button -> connect(TOPIC_FEEDER_KEYEVT);
	feeder_state_button -> set(zmq::sockopt::subscribe, "");    

    return 0;
}

int feeder_interface::issue_feeder_scan(uint32_t feeder_id)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder {} scan cmd", feeder_id);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.para = action_para_ROLL_ALL;
    cmd.act = action_SCANNING;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}


int feeder_interface::issue_work_status_set(uint32_t status)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder cmd :{}", status);

    feeder_cmd cmd;
    cmd.para = action_para_ROLL_ALL;
    cmd.feeder_id = 0;
    if(status == 1)
        cmd.act = action_START;
    else if(status == 0)
        cmd.act = action_STOP;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}
// int feeder_interface::issue_exception_handle_set(uint32_t value)
// {
//     uint8_t req_msg[feeder_cmd_size];
// 	pb_ostream_t stream_out;

//     SPDLOG_DEBUG("issue exception handle set :{}", value);

//     feeder_cmd cmd;
//     cmd.para = action_para_ROLL_ALL;
//     cmd.feeder_id = 0;
//     cmd.act = action_SET;
//     cmd.set_type = s_type_excp_HANDLE;
//     cmd.set_value = value;

//     stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
//     if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
//     {
//         SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
//         return -1;
//     }
//     else
//         feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

//     zmq::message_t msg;
//     feeder_cmd_total->recv(msg);

//     return 0;
// }

int feeder_interface::issue_belt_forward_rotation(uint32_t feeder_id, uint32_t dev_id)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder {} belt {} forward rotation", feeder_id, dev_id);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.para = (action_para)dev_id;
    cmd.act = action_ROLL_FORWARD;
    cmd.belt_speed = 1500;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}

int feeder_interface::issue_belt_inverse_rotation(uint32_t feeder_id, uint32_t dev_id)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder {} belt {} backward rotation", feeder_id, dev_id);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.para = (action_para)dev_id;
    cmd.act = action_ROLL_BACKWARD;
    cmd.belt_speed = -1500;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}

int feeder_interface::issue_belt_speed(uint32_t feeder_id, uint32_t dev_id, int32_t speed)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder {} belt {} speed:{}", feeder_id, dev_id, speed);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.para = (action_para)dev_id;
    cmd.belt_speed = speed;
    
    cmd.act = action_ROLL_FORWARD;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}

int feeder_interface::get_button_state(key_event &button_info)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (feeder_state_button->recv(msg, zmq::recv_flags::none))
    {
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, key_event_fields, &button_info))
        {
            SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
            return -1;
        }
        else
        {
            SPDLOG_DEBUG("get feeder {} button {} state: [{}]", button_info.dev_id, button_info.key, button_info.evt_type);
            return 1;
        }
    }

    return 0;
}

int feeder_interface::get_feeder_state(feeder_dev_state_total &feeder_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (feeder_state_total->recv(msg, zmq::recv_flags::none))
    {
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, feeder_dev_state_total_fields, &feeder_state))
        {
            SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
            return -1;
        }
        else
        {
#if 0
            static int i = 0;
            if ((i++ & 7) == 0)
            {
                SPDLOG_DEBUG("get feeder {} state, fsm state:{}", feeder_state.feeder_id, feeder_state.state);
                for(int i = 0; i < feeder_state.belt_motor.motor_count; i++)
                {
                    auto bt = feeder_state.belt_motor.motor[i];
                    SPDLOG_DEBUG("feeder belt motor {} state: [{}]-[{}]-[{}]", bt.dev_id, bt.speed, bt.state, bt.state_code);
                }

                for(int i = 0; i < feeder_state.belt_sensor.sensor_count; i++)
                {
                    auto bs = feeder_state.belt_sensor.sensor[i];
                    SPDLOG_DEBUG("feeder belt sensor {} state: [{}]", bs.dev_id, bs.state);
                }

            }
#endif
         //   SPDLOG_INFO("get feeder id :{} state: {} seq:{} exc:{} errcode:{}",feeder_state.feeder_id,feeder_state.ready_state,feeder_state.sequence,feeder_state.excp_handle,feeder_state.err_code);
            return 1;
        }
    }

    return 0;
}


int feeder_interface::issue_control_feeder_rotate_belt_forward(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder all belt rotate forward feeder_id {}, speed {}, limit {}", feeder_id, speed, limit);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.act = action_ROLL_FORWARD;
    cmd.para = action_para_ROLL_ALL;
    cmd.belt_speed = speed;
    cmd.manual_length = limit;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;

}

int feeder_interface::issue_control_feeder_rotate_belt_backward(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder all belt rotate backward feeder_id {}, speed {}, limit {}", feeder_id, speed, limit);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.act = action_ROLL_BACKWARD;
    cmd.para = action_para_ROLL_ALL;
    cmd.belt_speed = speed;
    cmd.manual_length = limit;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;
}

int feeder_interface::issue_control_feeder_rotate_belt_stop(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    uint8_t req_msg[feeder_cmd_size];
	pb_ostream_t stream_out;

    SPDLOG_DEBUG("issue feeder all belt rotate stop feeder_id {}, speed {}, limit {}", feeder_id, speed, limit);

    feeder_cmd cmd;
    cmd.feeder_id = feeder_id;
    cmd.act = action_STOP;
    cmd.para = action_para_ROLL_ALL;
    cmd.belt_speed = speed;
    cmd.manual_length = limit;

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, feeder_cmd_fields, &cmd))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        feeder_cmd_total -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    feeder_cmd_total->recv(msg);

    return 0;

}