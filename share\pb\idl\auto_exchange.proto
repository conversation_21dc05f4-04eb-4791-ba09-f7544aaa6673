syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";
import "sys_interface.proto";

//// 设备相关数据流设计

//状态相关
enum dev_pos_calib_state
{
	STATE_NORMAL = 0;
    STATE_ERROR = 1;
    STATE_INIT = 3;
}

enum auto_exchange_device_state
{
    DEV_RESERVE = 0;
	DEV_INIT = 1;
    DEV_NORMAL = 2;
    DEV_ERROR = 3;
	DEV_FATAL = 4;
	DEV_EMERG_STOP = 5;
	DEV_UNKNOWN = 6;
}

enum auto_exchange_work_state
{
    WORK_STATE_RESERVE = 0;
    WORK_STATE_INIT = 1;
    WORK_STATE_CHECK = 2;
	WORK_STATE_IDLE = 3;
	WORK_STATE_WORK = 4;
	WORK_STATE_CALIB = 5;
	WORK_STATE_ERROR = 6;
	WORK_STATE_FATAL = 7;
}



enum auto_exchange_task_type
{
	TASK_NULL = 0;
    MOVE_X_AXIS = 1;
    MOVE_Y_AXIS = 2;
	MOVE_XY_AXIS = 3;
	MOVE_Z1_AXIS = 4;
	MOVE_Z2_AXIS = 5;
	MOVE_Z3_AXIS = 6;
	HOOK_GRAB = 7;
	HOOK_UNLOAD = 8;
	TASK_INTE_GRAB_MOVE	= 9;
	TASK_INTE_UNLOAD_MOVE = 10;
	TASK_INTE_GRAB_UNLOAD = 11;
}

enum cmd_type
{
	CMD_NULL = 0;
    CMD_EMERG = 1;
    CMD_RESET = 2;
    CMD_REBOOT = 3;
	CMD_START = 4;
	CMD_STOP = 5;
	CMD_CALIB = 6;
}


enum auto_exchange_dev_task_state
{
	IDLE = 0;
	INIT = 1;
    START = 2;
    RUNNING = 3;
    SUCCEED_OVER = 4;
    ERROR = 5;
};


message dev_axis_work_state 
{
    int32 move_target = 1;
	int32 curr_pos = 2;
	int32 ins_speed = 3;
	int32 curr_speed = 4;
	int32 contrl_speed = 5;
	int32 dev_state = 6;
}

message dev_motor_work_state 
{
    int32 curr_speed = 1;
	int32 curr_pos = 2;
	int32 error_no = 3;
}

message dev_hook_work_state 
{
    int32 target_pos = 1;
	int32 curr_pos = 2;
	int32 motion_state = 3;
	int32 motor_err_no = 4;
}



message auto_exchange_dev_state 
{
	//设备基础信息	
	uint32 dev_id = 1;									//设备ID
	dev_pos_calib_state pos_state = 2;					//定位状态	
	auto_exchange_device_state curr_state = 3;
	auto_exchange_work_state work_state = 4;			//设备工作状态
	int32 dev_error_level = 5;							//异常等级
	uint32 dev_error_no = 6;							//车头故障码
	uint32 dev_task_type = 7;
	uint32 dev_task_state = 8;
	uint32 dev_sub_task_state = 9;
	
	uint32 motion_positon = 10;							//车辆坐标
	bool motion_positon_valid_flag = 11;				//车辆位置有效标志
	int32 motion_velocity = 12;							//车辆速度
	
	bool slot_1_good_state = 13;
	bool slot_2_good_state = 14;
	
	dev_axis_work_state x_axis_work_state = 15;
	dev_motor_work_state x1_motor_state = 16;
	dev_motor_work_state x2_motor_state = 17;
	
	dev_axis_work_state y_axis_work_state = 18;
	dev_motor_work_state y1_motor_state = 19;
	dev_motor_work_state y2_motor_state = 20;
	
	dev_hook_work_state z1_hook_state = 21;
	dev_hook_work_state z2_hook_state = 22;
	dev_hook_work_state z3_hook_state = 23;
	
	int32 current_mileage = 24;
	int32 encoder_mileage = 25;
	
}

message auto_exchange_agent_state 
{
	//设备基础信息	
	uint32 auto_exchange_dev_cnt = 1;					//车厢数量	
	uint32 auto_exchange_curr_valid_dev_cnt = 2;
	component_state agent_work_state = 3;			//车头运行状态
}




//命令相关

message auto_exchange_task_move
{
	auto_exchange_task_type type = 1;
	uint32 target = 2;
	uint32 speed_limit = 3;
	uint32 acc_limit = 4;
	uint32 ctrl_object = 5;
	int32  move_distance = 6;
}



message auto_exchange_task_grab
{
	auto_exchange_task_type type = 1;
	int32 ctrl_object = 2;
	int32 acc_limit_loaded = 3;
	int32 acc_limit_unloaded = 4;
	int32 speed_limit_loaded = 5;
	int32 speed_limit_unloaded = 6;
}



message auto_exchange_task_grab_move
{
	auto_exchange_task_type type = 1;
	int32 cmd_type = 2;
	int32 ctrl_object = 3;
	int32 x_target_pos = 4;
	int32 y_target_pos = 5;
	int32 x_acc = 6;
	int32 x_speed = 7;
	int32 y_acc = 8;
	int32 y_speed = 9;
	int32 z_acc_loaded = 10;
	int32 z_acc_unloaded = 11;
	int32 z_speed_loaded = 12;
	int32 z_speed_unloaded = 13;
}

message auto_exchange_task_grab_integration
{
	auto_exchange_task_type type = 1;
	int32 cmd_type = 2;
	int32 ctrl_object = 3;
	int32 x_grab_pos = 4;
	int32 y_grab_pos = 5;
	int32 x_unload_pos = 6;
	int32 y_unload_pos = 7;
	int32 z_stroke_length = 8;
	int32 x_acc = 9;
	int32 x_speed = 10;
	int32 y_acc = 11;
	int32 y_speed = 12;
	int32 z_acc_loaded = 13;
	int32 z_acc_unloaded = 14;
	int32 z_speed_loaded = 15;
	int32 z_speed_unloaded = 16;
}



message auto_exchange_dev_cmd
{
	cmd_type cmd = 1;
	uint32 para = 2;
}



message auto_exchange_mileage_info
{
	int32 current_mileage = 1;	// 行驶里程参考
    int32 encoder_mileage = 2;	// 行驶里程
}

message auto_exchange_task
{
	uint32 sequence = 1;
    uint32 dev_id = 2;
	uint32 sub_dev_id = 3;
	oneof task
	{
    	auto_exchange_task_move move = 4;			//小车移动
    	auto_exchange_task_grab grab = 5;	
		auto_exchange_task_grab_move grab_move = 6;
		auto_exchange_task_grab_integration grab_inte = 7;
		auto_exchange_dev_cmd cmd= 8;
		uint32 hb_time_sync = 9;
		auto_exchange_mileage_info mil_info = 10;

	};
};

message auto_exchange_task_state
{
    uint32 dev_id = 1;
	uint32 sub_dev_id = 2;
    auto_exchange_task_type type = 3;
    auto_exchange_dev_task_state state = 4;
}




//// 业务相关数据流设计 - 物控

message workstation_ext_state
{
	uint32 workstation_id = 1;
	uint32 fault_state = 2;
	bool online_state = 3 ;
	bool dev_idle_state = 4;
}

message auto_exchange_ext_state_single
{
	uint32 train_id = 1;
	bool online_state = 2;
	uint32 speed = 3;
	uint32 fault_state = 4;
	string ip_addr = 5 [(nanopb).max_size=16];
	string firmware_version = 6 [(nanopb).max_size=16];
	string software_version = 7 [(nanopb).max_size=16];
	int32 pos_x = 8;
	int32 pox_y = 9;
	repeated workstation_ext_state workstation_state = 10 [(nanopb).max_count = 16];

	string motor_state_no = 11 [(nanopb).max_size=16];
	auto_exchange_work_state motor_state = 12;					//行走电机状态
	uint32 motor_speed = 13;								//行走电速度
	int32  curr_mileage = 14;
	int32  encoder_mileage = 15;
	
	
}


message auto_exchange_ext_state_multi
{	
	repeated auto_exchange_ext_state_single auto_exchanges = 1 [(nanopb).max_count = 10];	
}


enum auto_exchange_ext_task_type
{
	TASK_RESERVE = 0;
	TASK_MOVE = 1;
	TASK_GRAB = 2;
	TASK_UNLOAD = 3;
	TASK_INTE = 4;
};

enum auto_exchange_ext_task_cmd_type
{
	TASK_CMD_RESERVE = 0;
	TASK_CMD_CANCEL = 1;
	TASK_CMD_RESUME = 2;
};

enum auto_exchange_ext_task_state
{
	TASK_STATE_RESERVE = 0;
	TASK_STATE_IDLE = 1;
	TASK_STATE_READY = 2;
	TASK_STATE_GRAB_MOVING = 4;
	TASK_STATE_GRABBING = 5;
	TASK_STATE_GRAB_FINISH = 6;
	TASK_STATE_UNLOAD_MOVING = 7;
	TASK_STATE_UNLOADING = 8;
	TASK_STATE_UNLOAD_FINISH = 9;
	TASK_STATE_FINISHED_MANUALLY = 10;	//分到了收容口
	TASK_STATE_SUSPEND = 11;
};


message auto_exchange_ext_task_state_msg
{
	bytes task_id = 1 [(nanopb).max_size = 80, (nanopb).fixed_length = true];
	auto_exchange_ext_task_state state = 2;
	uint32 dev_id = 3;
	uint32 sub_dev_id = 4;
	uint32 container = 5;								//格口号
	uint32 suspend_info = 6;
}

message auto_exchange_ext_task_msg
{
	uint32 sequence = 1;
	bytes task_id = 2 [(nanopb).max_size = 80, (nanopb).fixed_length = true];
	auto_exchange_ext_task_type task_type = 3;
	uint32 container = 4;
	uint32 unload_no = 5;
}


message auto_exchange_ext_manual_task_msg
{
	uint32 sequence = 1;
	bytes task_id = 2 [(nanopb).max_size = 80, (nanopb).fixed_length = true];
	auto_exchange_ext_task_state state = 3;
	auto_exchange_ext_task_cmd_type cmd = 4;
	uint32 dev_id = 5;
	uint32 sub_dev_id = 6;
	uint32 container = 7;
	uint32 unload_no = 8;
}