# libzmq.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-0.1
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libzmq.so.5'

# Names of this library.
library_names='libzmq.so.5.1.5 libzmq.so.5 libzmq.so'

# The name of the static archive.
old_library='libzmq.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' -lrt -lpthread -ldl'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libzmq.
current=6
age=1
revision=5

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/opt/zeromq/lib'
