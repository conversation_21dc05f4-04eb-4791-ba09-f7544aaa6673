Determining if the pthread_create exist failed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_0f386/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_0f386.dir/build.make CMakeFiles/cmTC_0f386.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_0f386.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_0f386.dir/CheckSymbolExists.c.o
/usr/bin/cc     -o CMakeFiles/cmTC_0f386.dir/CheckSymbolExists.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c
Linking C executable cmTC_0f386
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0f386.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_0f386.dir/CheckSymbolExists.c.o  -o cmTC_0f386 
CMakeFiles/cmTC_0f386.dir/CheckSymbolExists.c.o: In function `main':
CheckSymbolExists.c:(.text+0x16): undefined reference to `pthread_create'
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_0f386.dir/build.make:97: recipe for target 'cmTC_0f386' failed
make[1]: *** [cmTC_0f386] Error 1
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
Makefile:126: recipe for target 'cmTC_0f386/fast' failed
make: *** [cmTC_0f386/fast] Error 2

File /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <pthread.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef pthread_create
  return ((int*)(&pthread_create))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_c00ba/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_c00ba.dir/build.make CMakeFiles/cmTC_c00ba.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_c00ba.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_c00ba.dir/CheckFunctionExists.c.o
/usr/bin/cc    -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_c00ba.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.5/Modules/CheckFunctionExists.c
Linking C executable cmTC_c00ba
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c00ba.dir/link.txt --verbose=1
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_c00ba.dir/CheckFunctionExists.c.o  -o cmTC_c00ba -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_c00ba.dir/build.make:97: recipe for target 'cmTC_c00ba' failed
make[1]: *** [cmTC_c00ba] Error 1
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
Makefile:126: recipe for target 'cmTC_c00ba/fast' failed
make: *** [cmTC_c00ba/fast] Error 2


