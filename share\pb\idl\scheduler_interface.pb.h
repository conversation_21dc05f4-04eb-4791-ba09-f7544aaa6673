/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_SCHEDULER_INTERFACE_PB_H_INCLUDED
#define PB_SCHEDULER_INTERFACE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _scheduler_state {
    component_state state;
    bool maintenance_state;
} scheduler_state;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define scheduler_state_init_default             {_component_state_MIN, 0}
#define scheduler_state_init_zero                {_component_state_MIN, 0}

/* Field tags (for use in manual encoding/decoding) */
#define scheduler_state_state_tag                1
#define scheduler_state_maintenance_state_tag    2

/* Struct field encoding specification for nanopb */
#define scheduler_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    state,             1) \
X(a, STATIC,   SINGULAR, BOOL,     maintenance_state,   2)
#define scheduler_state_CALLBACK NULL
#define scheduler_state_DEFAULT NULL

extern const pb_msgdesc_t scheduler_state_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define scheduler_state_fields &scheduler_state_msg

/* Maximum encoded size of messages (where known) */
#define SCHEDULER_INTERFACE_PB_H_MAX_SIZE        scheduler_state_size
#define scheduler_state_size                     4

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
