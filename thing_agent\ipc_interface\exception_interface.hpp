#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/exception.pb.h"

class exception_interface
{
public:

    int init(zmq::context_t &ctx);

    int get_event_exception(event_exception &exception_event);

    int report_exception(const event_exception &except);

    static exception_interface *get_instance(void)
    {
        static exception_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *exception_recver;            //只接收上报的异常发生/恢复
    zmq::socket_t *exception_sender;

};
