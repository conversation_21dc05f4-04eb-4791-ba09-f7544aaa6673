#ifndef __SWAP_AGENT_DEBUG_H__
#define __SWAP_AGENT_DEBUG_H__

#include <iostream>


#define     TRAIN_NET_LOG
#define     MULTI_DEV_DEBUG
#define     PROTOCOL_DEBUG	
#define     SWAP_MANAGE_DEBUG
#define     TRAIN_ZMQ_LOG
#define     SCHEDULER_MSG_DEBUG
#define     TRAIN_ZMQ_LOG
#define     TRAIN_MUL_DEV_LOG

#define  OUTPUT_ERR				(std::cout << "error>>: " << __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl)
#define  OUTPUT_LOG				(std::cout << "log>>: " << __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl)







#endif