#pragma once

#include <list>
#include <thread>

#include "share/pb/idl/exception.pb.h"

class exception_list
{
	public:
		/**/
		int except_occur(const except_info &excep)
		{
			const std::lock_guard<std::mutex> lck(lock);

			//遍历pending list，如果该异常已经存在则直接退出。
			for (auto const &e : pending_list)
			{
				/*已存在,则不再重复插入*/
				if ((e.src == excep.src) && (e.dev == excep.dev) && (e.sub_dev == excep.sub_dev) && (e.code == excep.code) && (e.sub_code == excep.sub_code))
				{
					return 0;
				}
			}

			SPDLOG_INFO("exception occur: [{}.{}.{}]: {}-{}-({})", excep.src, excep.dev, excep.sub_dev, excep.code, excep.sub_code, std::string(excep.description));
			pending_list.push_back(excep);

			return 1;
		}

		/**/
		int except_reset(const except_info &excep)
		{
			const std::lock_guard<std::mutex> lck(lock);
			int found = 0;

			auto f = [&](except_info e) {
				if ((e.src == excep.src) && (e.dev == excep.dev) && (e.sub_dev == excep.sub_dev) && (e.code == excep.code) && (e.sub_code == excep.sub_code))
				{
					++found;
					SPDLOG_INFO("exception reset: [{}.{}.{}]: {}-{}-({})", e.src, e.dev, e.sub_dev, e.code, e.sub_code, std::string(e.description));
					return true;
				}
				else
					return false;
			};

			pending_list.remove_if(f);

			return found;
		}

	private:
		std::mutex lock;
		std::list<except_info> pending_list;
};

