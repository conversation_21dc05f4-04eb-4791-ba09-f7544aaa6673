/**
 * @file main.cpp
 * @brief camera_berxel 类图像数据读取模拟测试程序
 * @details 模拟测试 Berxel 相机的初始化、数据读取、图像处理等功能
 *          不依赖实际硬件设备，使用模拟数据进行接口测试
 * <AUTHOR> Program
 * @date 2025-08-25
 * @version v1.0.0
 */

#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>
#include <atomic>
#include <memory>
#include <iomanip>
#include <fstream>
#include <sstream>

// 模拟相机驱动的数据结构和接口
// 避免包含外部依赖库的头文件

// 模拟 camera_berxel_data 结构
typedef struct __camera_berxel_data {
    uint32_t id;
    bool result;
    uint32_t area;
} camera_berxel_data;

// 模拟 camera_berxel 类
class camera_berxel {
public:
    static camera_berxel* get_instance() {
        static camera_berxel instance;
        return &instance;
    }
    
    bool camera_berxel_init() {
        std::cout << "[模拟] 初始化相机设备..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        m_initialized = true;
        return true;
    }
    
    void camera_berxel_run() {
        std::cout << "[模拟] 启动相机数据采集线程..." << std::endl;
        m_running = true;
    }
    
    camera_berxel_data camera_berxel_camera_dev_data_pop() {
        camera_berxel_data data;
        data.id = ++m_frame_counter;
        data.result = (m_frame_counter % 10 != 0); // 90% 成功率
        data.area = 1000 + (m_frame_counter % 500);
        
        // 模拟数据读取延迟
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        return data;
    }
    
    uint8_t camera_berxel_camera_dev_2_get_gray_mask() {
        return 128; // 模拟灰度掩码值
    }
    
    uint32_t camera_berxel_camera_dev_2_get_area_mask() {
        return 0xFF00FF00; // 模拟区域掩码值
    }
    
    int camera_berxel_camera_dev_get_trig_cycle() {
        return 33; // 模拟 30fps 的触发周期
    }
    
    bool is_initialized() const { return m_initialized; }
    bool is_running() const { return m_running; }
    uint32_t get_frame_count() const { return m_frame_counter; }

private:
    camera_berxel() : m_initialized(false), m_running(false), m_frame_counter(0) {}
    
    bool m_initialized;
    bool m_running;
    uint32_t m_frame_counter;
};

// 全局控制变量
std::atomic<bool> g_running(true);

// 函数声明
void save_camera_test_report(int successful_reads, int failed_reads, int total_time_ms);

/**
 * @brief 信号处理函数
 * @param sig 信号值
 */
void signal_handler(int sig) {
    std::cout << "\n收到信号 " << sig << "，正在退出程序..." << std::endl;
    g_running = false;
}

/**
 * @brief 打印测试菜单
 */
void print_menu() {
    std::cout << "\n========== Camera Berxel 测试程序 ==========" << std::endl;
    std::cout << "1. 初始化相机" << std::endl;
    std::cout << "2. 启动相机数据采集" << std::endl;
    std::cout << "3. 读取相机数据" << std::endl;
    std::cout << "4. 获取灰度掩码" << std::endl;
    std::cout << "5. 获取区域掩码" << std::endl;
    std::cout << "6. 获取触发周期" << std::endl;
    std::cout << "7. 连续数据监控模式" << std::endl;
    std::cout << "8. 性能测试" << std::endl;
    std::cout << "9. 显示相机状态" << std::endl;
    std::cout << "0. 退出程序" << std::endl;
    std::cout << "===========================================" << std::endl;
    std::cout << "请选择操作: ";
}

/**
 * @brief 测试相机完整初始化流程
 * @param camera 相机实例指针
 * @return 初始化是否成功
 */
bool test_camera_init(camera_berxel* camera) {
    std::cout << "\n========== 相机完整初始化测试 ==========" << std::endl;
    
    // 1. 相机设备初始化
    std::cout << "[步骤1] 正在初始化相机设备..." << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    bool init_result = camera->camera_berxel_init();
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    if (!init_result) {
        std::cout << "[失败] 相机设备初始化失败，耗时: " << duration.count() << "ms" << std::endl;
        return false;
    }
    std::cout << "[成功] 相机设备初始化成功，耗时: " << duration.count() << "ms" << std::endl;
    
    // 2. 参数配置验证
    std::cout << "\n[步骤2] 正在验证相机参数配置..." << std::endl;
    try {
        // 获取触发周期配置
        int trigger_cycle = camera->camera_berxel_camera_dev_get_trig_cycle();
        std::cout << "  - 触发周期: " << trigger_cycle << "ms" << std::endl;
        
        // 获取灰度掩码配置
        uint8_t gray_mask = camera->camera_berxel_camera_dev_2_get_gray_mask();
        std::cout << "  - 灰度掩码: " << static_cast<int>(gray_mask) << std::endl;
        
        // 获取区域掩码配置
        uint32_t area_mask = camera->camera_berxel_camera_dev_2_get_area_mask();
        std::cout << "  - 区域掩码: 0x" << std::hex << area_mask << std::dec << std::endl;
        
        std::cout << "[成功] 相机参数配置验证完成" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "[警告] 参数配置验证异常: " << e.what() << std::endl;
    }
    
    // 3. 启动数据采集
    std::cout << "\n[步骤3] 正在启动相机数据采集..." << std::endl;
    try {
        camera->camera_berxel_run();
        std::cout << "[成功] 相机数据采集线程已启动" << std::endl;
        
        // 等待数据采集稳定
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
    } catch (const std::exception& e) {
        std::cout << "[失败] 启动数据采集异常: " << e.what() << std::endl;
        return false;
    }
    
    // 4. 图像数据读取测试
    std::cout << "\n[步骤4] 正在测试图像数据读取..." << std::endl;
    int successful_reads = 0;
    int failed_reads = 0;
    const int test_frames = 5;
    
    for (int i = 0; i < test_frames; i++) {
        try {
            auto read_start = std::chrono::high_resolution_clock::now();
            camera_berxel_data frame_data = camera->camera_berxel_camera_dev_data_pop();
            auto read_end = std::chrono::high_resolution_clock::now();
            auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);
            
            std::cout << "  帧 " << (i+1) << ": ID=" << frame_data.id 
                      << ", 结果=" << (frame_data.result ? "成功" : "失败")
                      << ", 区域=" << frame_data.area 
                      << ", 读取耗时=" << read_duration.count() << "μs" << std::endl;
            
            if (frame_data.result) {
                successful_reads++;
            } else {
                failed_reads++;
            }
            
            // 帧间延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
        } catch (const std::exception& e) {
            std::cout << "  帧 " << (i+1) << ": [异常] " << e.what() << std::endl;
            failed_reads++;
        }
    }
    
    // 5. 数据处理结果分析
    std::cout << "\n[步骤5] 图像数据处理结果分析:" << std::endl;
    std::cout << "  - 总测试帧数: " << test_frames << std::endl;
    std::cout << "  - 成功读取帧数: " << successful_reads << std::endl;
    std::cout << "  - 失败读取帧数: " << failed_reads << std::endl;
    std::cout << "  - 成功率: " << std::fixed << std::setprecision(1) 
              << (successful_reads * 100.0 / test_frames) << "%" << std::endl;
    
    // 6. 相机状态检查
    std::cout << "\n[步骤6] 相机状态最终检查:" << std::endl;
    std::cout << "  - 设备初始化状态: " << (camera->is_initialized() ? "✓ 已初始化" : "✗ 未初始化") << std::endl;
    std::cout << "  - 数据采集状态: " << (camera->is_running() ? "✓ 运行中" : "✗ 未运行") << std::endl;
    std::cout << "  - 已处理帧数: " << camera->get_frame_count() << std::endl;
    
    // 7. 性能评估
    auto total_end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(total_end_time - start_time);
    
    std::cout << "\n========== 初始化测试总结 ==========" << std::endl;
    std::cout << "总耗时: " << total_duration.count() << "ms" << std::endl;
    
    bool overall_success = init_result && (successful_reads > 0);
    if (overall_success) {
        std::cout << "✓ 相机初始化和数据读取测试 - 成功完成" << std::endl;
        
        // 保存测试报告
        save_camera_test_report(successful_reads, failed_reads, total_duration.count());
        
    } else {
        std::cout << "✗ 相机初始化和数据读取测试 - 存在问题" << std::endl;
    }
    std::cout << "======================================" << std::endl;
    
    return overall_success;
}

/**
 * @brief 测试相机数据读取
 * @param camera 相机实例指针
 */
void test_camera_data_read(camera_berxel* camera) {
    std::cout << "\n[测试] 正在读取相机数据..." << std::endl;
    
    try {
        camera_berxel_data data = camera->camera_berxel_camera_dev_data_pop();
        
        std::cout << "[数据] ID: " << data.id 
                  << ", 结果: " << (data.result ? "成功" : "失败")
                  << ", 区域: " << data.area << std::endl;
    } catch (const std::exception& e) {
        std::cout << "[异常] 读取数据时发生异常: " << e.what() << std::endl;
    }
}

/**
 * @brief 测试灰度掩码获取
 * @param camera 相机实例指针
 */
void test_gray_mask(camera_berxel* camera) {
    std::cout << "\n[测试] 正在获取灰度掩码..." << std::endl;
    
    uint8_t gray_mask = camera->camera_berxel_camera_dev_2_get_gray_mask();
    std::cout << "[结果] 灰度掩码值: " << static_cast<int>(gray_mask) << std::endl;
}

/**
 * @brief 测试区域掩码获取
 * @param camera 相机实例指针
 */
void test_area_mask(camera_berxel* camera) {
    std::cout << "\n[测试] 正在获取区域掩码..." << std::endl;
    
    uint32_t area_mask = camera->camera_berxel_camera_dev_2_get_area_mask();
    std::cout << "[结果] 区域掩码值: " << area_mask << std::endl;
}

/**
 * @brief 测试触发周期获取
 * @param camera 相机实例指针
 */
void test_trigger_cycle(camera_berxel* camera) {
    std::cout << "\n[测试] 正在获取触发周期..." << std::endl;
    
    int cycle = camera->camera_berxel_camera_dev_get_trig_cycle();
    std::cout << "[结果] 触发周期: " << cycle << "ms" << std::endl;
}

/**
 * @brief 连续数据监控模式
 * @param camera 相机实例指针
 */
void continuous_monitoring(camera_berxel* camera) {
    std::cout << "\n[监控] 进入连续数据监控模式 (按 Ctrl+C 退出)" << std::endl;
    
    int count = 0;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    while (g_running) {
        try {
            camera_berxel_data data = camera->camera_berxel_camera_dev_data_pop();
            count++;
            
            auto current_time = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time);
            
            std::cout << "[" << std::setw(6) << count << "] "
                      << "时间: " << elapsed.count() << "s, "
                      << "ID: " << data.id << ", "
                      << "结果: " << (data.result ? "✓" : "✗") << ", "
                      << "区域: " << data.area << std::endl;
                      
            // 每秒最多处理10次数据
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
        } catch (const std::exception& e) {
            std::cout << "[异常] " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
    
    std::cout << "\n[监控] 总共处理了 " << count << " 条数据" << std::endl;
}

/**
 * @brief 性能测试
 * @param camera 相机实例指针
 */
void performance_test(camera_berxel* camera) {
    std::cout << "\n[性能测试] 开始性能测试..." << std::endl;
    
    const int test_count = 1000;
    int success_count = 0;
    int error_count = 0;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < test_count && g_running; i++) {
        try {
            // 测试数据读取性能
            camera_berxel_data data = camera->camera_berxel_camera_dev_data_pop();
            if (data.result) {
                success_count++;
            } else {
                error_count++;
            }
            
            // 测试其他接口性能
            (void)camera->camera_berxel_camera_dev_2_get_gray_mask();
            (void)camera->camera_berxel_camera_dev_2_get_area_mask();
            (void)camera->camera_berxel_camera_dev_get_trig_cycle();
            
            if (i % 100 == 0) {
                std::cout << "[进度] " << i << "/" << test_count << std::endl;
            }
            
        } catch (const std::exception& e) {
            error_count++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "\n[性能测试结果]" << std::endl;
    std::cout << "总测试次数: " << test_count << std::endl;
    std::cout << "成功次数: " << success_count << std::endl;
    std::cout << "错误次数: " << error_count << std::endl;
    std::cout << "总耗时: " << duration.count() << "ms" << std::endl;
    std::cout << "平均耗时: " << (duration.count() / (double)test_count) << "ms/次" << std::endl;
    std::cout << "成功率: " << (success_count * 100.0 / test_count) << "%" << std::endl;
}

/**
 * @brief 显示相机状态
 * @param camera 相机实例指针
 */
void show_camera_status(camera_berxel* camera) {
    std::cout << "\n========== 相机状态信息 ==========" << std::endl;
    
    try {
        int trigger_cycle = camera->camera_berxel_camera_dev_get_trig_cycle();
        uint8_t gray_mask = camera->camera_berxel_camera_dev_2_get_gray_mask();
        uint32_t area_mask = camera->camera_berxel_camera_dev_2_get_area_mask();
        
        std::cout << "触发周期: " << trigger_cycle << "ms" << std::endl;
        std::cout << "灰度掩码: " << static_cast<int>(gray_mask) << std::endl;
        std::cout << "区域掩码: " << area_mask << std::endl;
        
        // 检查模拟设备状态
        std::cout << "设备1状态: " << (camera->is_initialized() ? "已连接" : "未连接") << std::endl;
        std::cout << "设备2状态: " << (camera->is_running() ? "运行中" : "未运行") << std::endl;
        std::cout << "已处理帧数: " << camera->get_frame_count() << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "[错误] 获取状态信息失败: " << e.what() << std::endl;
    }
    
    std::cout << "=================================" << std::endl;
}

/**
 * @brief 保存测试日志
 * @param message 日志消息
 */
void save_test_log(const std::string& message) {
    std::ofstream log_file("camera_test_log.txt", std::ios::app);
    if (log_file.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        log_file << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] " 
                 << message << std::endl;
        log_file.close();
    }
}

/**
 * @brief 保存相机测试报告
 * @param successful_reads 成功读取帧数
 * @param failed_reads 失败读取帧数
 * @param total_time_ms 总耗时(毫秒)
 */
void save_camera_test_report(int successful_reads, int failed_reads, int total_time_ms) {
    std::ofstream report_file("camera_init_test_report.txt", std::ios::app);
    if (report_file.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        report_file << "\n========== 相机初始化测试报告 ==========" << std::endl;
        report_file << "测试时间: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
        report_file << "成功读取帧数: " << successful_reads << std::endl;
        report_file << "失败读取帧数: " << failed_reads << std::endl;
        report_file << "总测试帧数: " << (successful_reads + failed_reads) << std::endl;
        report_file << "成功率: " << std::fixed << std::setprecision(1) 
                   << (successful_reads * 100.0 / (successful_reads + failed_reads)) << "%" << std::endl;
        report_file << "总耗时: " << total_time_ms << "ms" << std::endl;
        report_file << "平均帧处理时间: " << std::fixed << std::setprecision(2)
                   << (total_time_ms / (double)(successful_reads + failed_reads)) << "ms/帧" << std::endl;
        report_file << "========================================" << std::endl;
        report_file.close();
        
        std::cout << "✓ 测试报告已保存到: camera_init_test_report.txt" << std::endl;
    }
}

/**
 * @brief 主函数
 */
int main() {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    std::cout << "Camera Berxel 测试程序启动" << std::endl;
    save_test_log("测试程序启动");
    
    // 获取相机实例
    camera_berxel* camera = camera_berxel::get_instance();
    if (!camera) {
        std::cerr << "[错误] 无法获取相机实例" << std::endl;
        return -1;
    }
    
    bool camera_initialized = false;
    int choice;
    
    while (g_running) {
        print_menu();
        
        if (!(std::cin >> choice)) {
            std::cin.clear();
            std::cin.ignore(10000, '\n');
            std::cout << "[错误] 无效输入，请输入数字" << std::endl;
            continue;
        }
        
        switch (choice) {
            case 1:
                camera_initialized = test_camera_init(camera);
                save_test_log("执行相机初始化测试，结果: " + std::string(camera_initialized ? "成功" : "失败"));
                break;
                
            case 2:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                std::cout << "\n[测试] 启动相机数据采集..." << std::endl;
                camera->camera_berxel_run();
                std::cout << "[成功] 相机数据采集已启动" << std::endl;
                save_test_log("启动相机数据采集");
                break;
                
            case 3:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                test_camera_data_read(camera);
                break;
                
            case 4:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                test_gray_mask(camera);
                break;
                
            case 5:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                test_area_mask(camera);
                break;
                
            case 6:
                test_trigger_cycle(camera);
                break;
                
            case 7:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                continuous_monitoring(camera);
                break;
                
            case 8:
                if (!camera_initialized) {
                    std::cout << "[警告] 请先初始化相机" << std::endl;
                    break;
                }
                performance_test(camera);
                break;
                
            case 9:
                show_camera_status(camera);
                break;
                
            case 0:
                std::cout << "\n正在退出程序..." << std::endl;
                g_running = false;
                break;
                
            default:
                std::cout << "[错误] 无效选择，请重新输入" << std::endl;
                break;
        }
        
        if (choice != 0 && g_running) {
            std::cout << "\n按回车键继续...";
            std::cin.ignore();
            std::cin.get();
        }
    }
    
    save_test_log("测试程序退出");
    std::cout << "测试程序已退出" << std::endl;
    
    return 0;
}