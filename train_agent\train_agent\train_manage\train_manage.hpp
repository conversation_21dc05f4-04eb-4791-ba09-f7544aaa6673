#ifndef __TRAIN_MANAGE_HPP__
#define __TRAIN_MANAGE_HPP__

#include "share/pb/idl/train_interface.pb.h"
#include "multi_train_manager.hpp"
#include "train_list.hpp"
#include "../protocol/train_protocol.hpp"

using namespace std;

/**@enum TRAIN_MSG_TYPE
* @brief 定义网络数传的车辆协议数据所对应的车辆行为
*/
typedef enum
{
	TRAIN_MSG_UNDEFINED = 0,    ///<  无定义
	TRAIN_MSG_REG     = 1,      ///<  车辆注册
	TRAIN_MSG_STATE	= 2,        ///<  车辆状态上报
    TRAIN_MSG_EXCEPTIONAL = 3,  ///<  车辆异常上报
	TRAIN_MSG_CMD_ACK  = 4,     ///<  车辆命令回复
}TRAIN_MSG_TYPE;


// 行为定义 UDP 协议
#define TRAIN_PROTOCOL_OPERATION_REGISTER_UDP									(TRAIN_PROTOCOL_SESSION_REGISTER)
#define TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP										(TRAIN_PROTOCOL_SESSION_CMD_OPT)
#define TRAIN_PROTOCOL_SESSION_HEART_BEAT_UDP									(TRAIN_PROTOCOL_SESSION_HEART_BEAT)
#define TRAIN_PROTOCOL_SESSION_EXCEPTION_UDP									(TRAIN_PROTOCOL_SESSION_EXCEPTION)


//小车指令具体参数
#define TRAIN_PROTOCOL_CMD_WALK_NOLOCATE                            ( 0x10 )
#define TRAIN_PROTOCOL_CMD_WALK_LOCATE                              ( 0x11 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS                         ( 0x20 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS_ACK                     ( 0x21 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION          ( 0x30 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION_ACK      ( 0x31 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE               ( 0x40 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE_ACK           ( 0x41 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO                 ( 0x50 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO_ACK             ( 0x51 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_PACK                            ( 0x60 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_PACK_ACK                        ( 0x61 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK                      	( 0x70 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK_ACK                      ( 0x71 )
#define TRAIN_PROTOCOL_CMD_EXCEPTION                                ( 0x80 ) //0x01：小车急停；0x02：异常复位；0x03：重启指令
#define TRAIN_PROTOCOL_CMD_QUERY_PARA                               ( 0x90 )
#define TRAIN_PROTOCOL_CMD_QUERY_PARA_ACK                           ( 0x91 )
#define TRAIN_PROTOCOL_CMD_REAL_TIME_PARA_SEND						( 0xA0 )
#define TRAIN_PROTOCOL_CMD_VERSION_INFO_ACK							( 0xA1 )
#define TRAIN_PROTOCOL_CMD_MILEAGE_INFO								( 0xB0 )
#define TRAIN_PROTOCOL_CMD_MILEAGE_INFO_ACK							( 0xB1 )

#define TRAIN_PROTOCOL_CMD_WALK_LEN                                 ( 7 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS_LEN                     ( 8 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION_LEN      ( 2 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION_ACK_LEN  ( 6 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE_LEN           ( 10 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE_ACK_LEN       ( 6 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO_LEN             ( 2 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO_ACK_LEN         ( 6 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_PACK_LEN                        ( 2 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_PACK_ACK_LEN                    ( 6 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK_LEN	                    ( 10 )
#define TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK_ACK_LEN                  ( 6 )
#define TRAIN_PROTOCOL_CMD_EXCEPTION_LEN                            ( 2 )
#define TRAIN_PROTOCOL_CMD_QUERY_PARA_ACK_LEN                       ( 24 )





/**@brief     根据网络数据包，解析车辆传输的数据并判断车辆目标行为类型
* @param[in]  uint8_t *buf --- 输入的车辆网络数据包
* @param[in]  uint16_t buf_len --- 输入的车辆网络数据包长度
* @param[in]  uint8_t *data_buf --- 解码得到的data字段数据缓冲区
* @param[out] int *data_cnt  --- 解码得到的data字段长度
* @param[out] uint32_t *id  --- 解码得到的车辆ID 
* @param[out] uint32_t *sequeue  --- 解码得到的通信序列号
* @return     车辆消息类型，详见头文件定义
*/
extern TRAIN_MSG_TYPE train_manage_msg_type(uint8_t *buf, uint16_t buf_len, uint8_t *data_buf, int *data_cnt, uint8_t *id, uint32_t *sequeue);


/**@brief     车辆注册消息ACK生成
* @param[in]  int dev_id --- 车辆ID
* @param[in]  uint32_t seque  --- 有效通信序列号
* @param[in]  train_agent_cfg *cfg  ---待ack的配置参数
* @param[out] uint8_t *send  --- 输出的注册ACK数据缓冲区
* @param[out] uint16_t *send_len  --- 输出的注册ACK数据缓冲区长度
* @return     NULL 
*/
extern void train_manage_train_register_ack(int dev_id, uint32_t seque, uint8_t *send, uint16_t *send_len, train_agent_cfg *cfg);


/**@brief     获取车辆状态信息
* @param[out] train_state *train_dev_state --- 车辆状态信息
* @param[out] train_state_net *dev_net_state ---网络数据包
* @param[in]  uint8_t *data_buf	--- data字段数据缓冲区
* @param[in]  uint8_t dev_id	 --- 车头ID
* @param[out] uint8_t *carriage_id	 --- 异常车厢ID
* @param[out] uint8_t *excep_level	 --- 异常等级
* @param[out] uint32_t *excep_code	 --- 异常码
*/
extern void train_manage_get_dev_state(train_state *train_dev_state, train_state_net *dev_net_state, uint8_t *data_buf, uint8_t dev_id, uint8_t *carriage_id, uint8_t *excep_level, uint32_t *excep_code);


/**@brief     判断待执行任务的类型
* @param[in]  train_task *v_task --- 接收到的任务数据结构体
* @param[out] uint8_t *data_out --- 根据任务数据生成的网络字节流
* @param[out] uint16_t *data_len --- 生成的网络字节流长度
* @param[in]  train_agent_cfg *cfg  --- 配置参数
* @return     操作结果，用来判断数据有效性
* - true      操作成功，数据有效
* - false     操作失败，数据无效
*/
extern int train_manage_release_task(train_task *v_task, uint8_t *data_out, uint16_t *data_len, train_agent_cfg *cfg, sys_mode_state *m_dev_curr_state, train_position_info *pos_info_temp);
extern uint8_t train_manage_release_inte_task(train_task *v_task, uint8_t *data_out, uint16_t *data_len, train_agent_cfg *cfg);
extern void train_query_para_recv(train_run_para *para, uint8_t *data_buf);
void run_para_display(train_run_para *cfg);

void byte_conversion_uint32(uint8_t *buff_ptr, uint32_t data);
void byte_conversion_uint16(uint8_t *buff_ptr, uint16_t data);
void cfg_display(train_state_net *dev_net_state);

void hb_log_decode(uint8_t *buffer, uint8_t *data_buf, string *net_msg_decode);



#endif