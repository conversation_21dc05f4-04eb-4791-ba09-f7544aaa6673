#include "multi_swap_manager.hpp"
#include "swap_agent_config.hpp"
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/async.h>
#include "swap_manage/swap_manage.hpp"



using namespace std;


int init_log(void)
{
	std::string home_path = getenv("HOME");
	std::string str_log_path = home_path + "/auto_sort_high_efficient/logs/swap_agent/swap_agent";

	std::shared_ptr<spdlog::logger> dev_logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>("swap_agent", str_log_path, 128*1024*1024, 60);
	
	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();

	dev_logger->set_level(spdlog::level::info);
	
	dev_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出
    spdlog::set_default_logger(dev_logger);
	spdlog::flush_every(std::chrono::seconds(3));
	
	spdlog::set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%s: %!: %#] [%l] %v");

	return 0;	
}

void display_dev_cfg(dev_agent_cfg &dev_cfg)
{
	SPDLOG_INFO("server_addr: {}", dev_cfg.server_info.server_addr);
	SPDLOG_INFO("server_port: {}", dev_cfg.server_info.server_port);
	SPDLOG_INFO("client_port: {}", dev_cfg.server_info.client_port);

	SPDLOG_INFO("heartbeat_timeout: {}", dev_cfg.heartbeat_timeout);
	SPDLOG_INFO("heartbeat_cycle: {}", dev_cfg.heartbeat_cycle);
	SPDLOG_INFO("resend_timeout: {}", dev_cfg.resend_timeout);
	SPDLOG_INFO("motor_speed_x: {}", dev_cfg.motor_speed_x);
	SPDLOG_INFO("motor_acc_x: {}", dev_cfg.motor_acc_x);
	SPDLOG_INFO("motor_dec_x: {}", dev_cfg.motor_dec_x);
	SPDLOG_INFO("motor_speed_y: {}", dev_cfg.motor_speed_y);
	SPDLOG_INFO("motor_acc_y: {}", dev_cfg.motor_acc_y);
	SPDLOG_INFO("motor_dec_y: {}", dev_cfg.motor_dec_y);
	SPDLOG_INFO("motor_no_load_speed_z: {}", dev_cfg.motor_no_load_speed_z);
	SPDLOG_INFO("motor_load_speed_z: {}", dev_cfg.motor_load_speed_z);
	SPDLOG_INFO("motor_load_acc_z: {}", dev_cfg.motor_load_acc_z);
	SPDLOG_INFO("motor_unload_acc_z: {}", dev_cfg.motor_unload_acc_z);
	SPDLOG_INFO("reset_force_send_flag: {}", dev_cfg.reset_force_send_flag);

	for(int i = 0; i < dev_cfg.swap_map.auto_exch_map_count; i++)
	{
		SPDLOG_INFO("find_zero_dir[1正向, 2负向]: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].calib_polarity);
		SPDLOG_INFO("max_travel_x: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].dev_width);
		SPDLOG_INFO("max_travel_y: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].dev_height);
		SPDLOG_INFO("max_travel_z1: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].grab_dis);
		SPDLOG_INFO("max_travel_z3: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].unload_dis);
		SPDLOG_INFO("max_travel_negative_z3: {}-{}", dev_cfg.swap_map.auto_exch_map[i].id, dev_cfg.swap_map.auto_exch_map[i].unload_negative_dis);
	}
}

int main()
{
    zmq::context_t context(1);

    log_cfg log_cfg_temp;
    dev_agent_cfg dev_cfg;

    init_log();
	SPDLOG_INFO("initialize loging ok");

	train_agent_get_config(&log_cfg_temp, &dev_cfg);

	fsm_manager::get_instance()->fsm_manager_init(context);
	SPDLOG_INFO("dev fsm_manager_init msg(zmq) init ok");
	fsm_manager::get_instance()->fsm_manager_run();

	scheduler_manager::get_instance()->scheduler_manager_init(context);
	SPDLOG_INFO("dev scheduler_manager_init msg(zmq) init ok");
	scheduler_manager::get_instance()->scheduler_manager_run();

	multi_swap_manager::get_instance()->multi_swap_manager_init(context, &dev_cfg);
	SPDLOG_INFO("dev multi_swap_manager_init msg(zmq) init ok");
	multi_swap_manager::get_instance()->multi_swap_manager_run();
	

	display_dev_cfg(dev_cfg);
	
	while (1)
	{
		std::this_thread::sleep_for(std::chrono::seconds(1));
	}
    

}