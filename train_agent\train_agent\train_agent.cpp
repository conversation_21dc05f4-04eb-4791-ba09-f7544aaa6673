#include "multi_train_manager.hpp"
#include "train_agent_config.hpp"
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/async.h>
#include "train_manage/train_manage.hpp"



using namespace std;


int init_log(void)
{
	std::string home_path = getenv("HOME");
	std::string str_log_path = home_path + "/auto_sort_high_efficient/logs/train_agent/train_agent";

	std::shared_ptr<spdlog::logger> dev_logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>("train_agent", str_log_path, 128*1024*1024, 60);
	
	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();

	dev_logger->set_level(spdlog::level::info);
	
	dev_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出
    spdlog::set_default_logger(dev_logger);
	spdlog::flush_every(std::chrono::seconds(3));
	
	spdlog::set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%s: %!: %#] [%l] %v");

	return 0;	
}

void display_dev_cfg(train_agent_cfg &dev_cfg)
{
	SPDLOG_INFO("server_addr: {}", dev_cfg.server_info.server_addr);
	SPDLOG_INFO("server_port: {}", dev_cfg.server_info.server_port);
	SPDLOG_INFO("client_port: {}", dev_cfg.server_info.client_port);
	SPDLOG_INFO("heartbeat_timeout: {}", dev_cfg.heartbeat_timeout);
	SPDLOG_INFO("heartbeat_cycle: {}", dev_cfg.heartbeat_cycle);
	SPDLOG_INFO("resend_timeout: {}", dev_cfg.resend_timeout);
	SPDLOG_INFO("map_total_length: {}", dev_cfg.map_total_length);
	SPDLOG_INFO("map_dev_calib_point_cnt: {}", dev_cfg.map_dev_calib_point_cnt);
	SPDLOG_INFO("proximity_sensors_tolerant_num: {}", dev_cfg.proximity_sensors_tolerant_num);
	SPDLOG_INFO("walk_motor_speed: {}", dev_cfg.walk_motor_speed);
	SPDLOG_INFO("walk_motor_acc: {}", dev_cfg.walk_motor_acc);
	SPDLOG_INFO("walk_motor_dec: {}", dev_cfg.walk_motor_dec);
	SPDLOG_INFO("carriage_motor_speed: {}", dev_cfg.carriage_motor_speed);
	SPDLOG_INFO("carriage_motor_acc: {}", dev_cfg.carriage_motor_acc);
	SPDLOG_INFO("carriage_motor_dec: {}", dev_cfg.carriage_motor_dec);
	SPDLOG_INFO("belt_motor_speed: {}", dev_cfg.belt_motor_speed);
	SPDLOG_INFO("belt_zero_speed: {}", dev_cfg.belt_zero_speed);
	SPDLOG_INFO("belt_motor_acc: {}", dev_cfg.belt_motor_acc);
	SPDLOG_INFO("belt_rotation_distance: {}", dev_cfg.belt_rotation_distance);
	SPDLOG_INFO("carriage_max_travel: {}", dev_cfg.carriage_max_travel);
	SPDLOG_INFO("unpack_exceed_threshold: {}", dev_cfg.unpack_exceed_threshold);
	SPDLOG_INFO("unpack_sensor_switch: {}", dev_cfg.unpack_sensor_switch);
	SPDLOG_INFO("m_dev_reset_force_send_flag: {}", dev_cfg.m_dev_reset_force_send_flag);

	for(int i = 0; i < dev_cfg.train_cfg_info.train_info_count; i++)
	{
		for(int j = 0; j < dev_cfg.train_cfg_info.train_info[i].carriage_info_count; j++)
			SPDLOG_INFO("train_id:{}, carriage_num:{}, carriage_id:{}", dev_cfg.train_cfg_info.train_info[i].train_id, dev_cfg.train_cfg_info.train_info[i].carriage_info_count, dev_cfg.train_cfg_info.train_info[i].carriage_info[j].carriage_id);
	}

    for(int i = 0; i < dev_cfg.map_dev_calib_point_cnt; i++)
		SPDLOG_INFO("map_calib_points_info: id:{} - position:{} ", dev_cfg.map_calib_points[i].id, dev_cfg.map_calib_points[i].position);

}

int main()
{
    zmq::context_t context(1);
    multi_train_manager manager(context);

    log_cfg log_cfg_temp;
    train_agent_cfg dev_cfg;

    init_log();
	SPDLOG_INFO("initialize loging ok");
	std::this_thread::sleep_for(std::chrono::seconds(1));

    train_agent_get_config(&log_cfg_temp, &dev_cfg);
    manager.multi_train_manager_init(&dev_cfg);

	std::this_thread::sleep_for(std::chrono::seconds(1));

    display_dev_cfg(dev_cfg);
	
    manager.multi_train_manager_run();

	
	while (1)
	{
		this_thread::sleep_for(chrono::seconds(1));
	}
    

}