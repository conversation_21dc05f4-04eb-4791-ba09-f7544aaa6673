syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";
import "train_interface.proto";

enum enable_state
{
	DEV_ENABLE_STATE_RESERVE = 0;	
	DEV_ENABLE_STATE_ENABLE = 1;	
	DEV_ENABLE_STATE_DISABLE = 2;	
};


message platform_basic_info
{
	uint32 platform_id = 1;
	enable_state state = 2;
	platform_type type = 3;
}


message carriage_basic_info
{
	uint32 carriage_id = 1;
	enable_state state = 2;
	carriage_type type = 3;
	string sw_ver = 4 [(nanopb).max_size=16];
	uint32 platform_cnt = 5;
	repeated platform_basic_info platform_info = 6 [(nanopb).max_count = 5];
}


message train_basic_info
{
	uint32 train_id = 1;
	enable_state state = 2;
	string ip = 3 [(nanopb).max_size=16];
	string sw_ver = 4 [(nanopb).max_size=16];
	string hw_ver = 5 [(nanopb).max_size=16];
	
	uint32 carriage_cnt = 6;
	repeated carriage_basic_info carriage_info = 7 [(nanopb).max_count = 16];
}



message train_basic_info_mutilp
{
	repeated train_basic_info train_info = 1 [(nanopb).max_count = 30];
}


message train_config_para
{
	uint32 x_speed = 1;
	uint32 x_acc = 2;
	uint32 x_d_acc = 3;
	uint32 y_speed = 4;
	uint32 y_acc = 5;
	uint32 y_d_acc = 6;
	uint32 z_speed = 7;
	uint32 z_cali_speed = 8;
	uint32 z_acc = 9;
	uint32 z_limit = 10;
}



