# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include CMakeFiles/autoswap_agent.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/autoswap_agent.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/autoswap_agent.dir/flags.make

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o: ../multi_swap_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.cpp

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.cpp > CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.i

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.cpp -o CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.s

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.requires

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.provides: CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.provides

CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o


CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o: ../swap_agent_config.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.cpp

CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.cpp > CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.i

CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.cpp -o CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.s

CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.requires

CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.provides: CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.provides

CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o


CMakeFiles/autoswap_agent.dir/readme.c.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/readme.c.o: ../readme.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/autoswap_agent.dir/readme.c.o"
	/usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/autoswap_agent.dir/readme.c.o   -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/readme.c

CMakeFiles/autoswap_agent.dir/readme.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/autoswap_agent.dir/readme.c.i"
	/usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/readme.c > CMakeFiles/autoswap_agent.dir/readme.c.i

CMakeFiles/autoswap_agent.dir/readme.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/autoswap_agent.dir/readme.c.s"
	/usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/readme.c -o CMakeFiles/autoswap_agent.dir/readme.c.s

CMakeFiles/autoswap_agent.dir/readme.c.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/readme.c.o.requires

CMakeFiles/autoswap_agent.dir/readme.c.o.provides: CMakeFiles/autoswap_agent.dir/readme.c.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/readme.c.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/readme.c.o.provides

CMakeFiles/autoswap_agent.dir/readme.c.o.provides.build: CMakeFiles/autoswap_agent.dir/readme.c.o


CMakeFiles/autoswap_agent.dir/main.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/main.cpp.o: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/autoswap_agent.dir/main.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/main.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/main.cpp

CMakeFiles/autoswap_agent.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/main.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/main.cpp > CMakeFiles/autoswap_agent.dir/main.cpp.i

CMakeFiles/autoswap_agent.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/main.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/main.cpp -o CMakeFiles/autoswap_agent.dir/main.cpp.s

CMakeFiles/autoswap_agent.dir/main.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/main.cpp.o.requires

CMakeFiles/autoswap_agent.dir/main.cpp.o.provides: CMakeFiles/autoswap_agent.dir/main.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/main.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/main.cpp.o.provides

CMakeFiles/autoswap_agent.dir/main.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/main.cpp.o


CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o: ../threadpool/thp_mutex.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.cpp

CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.cpp > CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.i

CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.cpp -o CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.s

CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.requires

CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.provides: CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.provides

CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o


CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o: ../threadpool/condition.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.cpp

CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.cpp > CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.i

CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.cpp -o CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.s

CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.requires

CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.provides: CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.provides

CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o


CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o: ../threadpool/thread_pool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.cpp

CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.cpp > CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.i

CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.cpp -o CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.s

CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.requires

CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.provides: CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.provides

CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o


CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o: ../scheduler_msg/scheduler_msg.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp

CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp > CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.i

CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp -o CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.s

CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.requires

CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.provides: CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.provides

CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o


CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o: ../net/tcp_socket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp

CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp > CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.i

CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp -o CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.s

CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.requires

CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.provides: CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.provides

CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o


CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o: ../net/epoll_poller.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp

CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp > CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.i

CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp -o CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.s

CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.requires

CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.provides: CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.provides

CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o


CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o: ../net/udp_socket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp

CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp > CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.i

CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp -o CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.s

CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.requires

CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.provides: CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.provides

CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o


CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o: ../protocol/train_protocol.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp

CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp > CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.i

CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp -o CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.s

CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.requires

CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.provides: CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.provides

CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o


CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o: ../swap_manage/swap_list.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp

CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp > CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.i

CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp -o CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.s

CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.requires

CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.provides: CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.provides

CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o


CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o: ../swap_manage/swap_manage.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp

CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp > CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.i

CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp -o CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.s

CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.requires

CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.provides: CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.provides

CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o


CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o: ../fsm_manager/fsm_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp

CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp > CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.i

CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp -o CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.s

CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.requires

CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.provides: CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.provides

CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o


CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o: CMakeFiles/autoswap_agent.dir/flags.make
CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o: ../exception/dev_except.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.cpp

CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.cpp > CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.i

CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.cpp -o CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.s

CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.requires:

.PHONY : CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.requires

CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.provides: CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.requires
	$(MAKE) -f CMakeFiles/autoswap_agent.dir/build.make CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.provides.build
.PHONY : CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.provides

CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.provides.build: CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o


# Object files for target autoswap_agent
autoswap_agent_OBJECTS = \
"CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o" \
"CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o" \
"CMakeFiles/autoswap_agent.dir/readme.c.o" \
"CMakeFiles/autoswap_agent.dir/main.cpp.o" \
"CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o" \
"CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o" \
"CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o" \
"CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o" \
"CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o" \
"CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o" \
"CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o" \
"CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o" \
"CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o" \
"CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o" \
"CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o" \
"CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o"

# External object files for target autoswap_agent
autoswap_agent_EXTERNAL_OBJECTS =

autoswap_agent: CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/readme.c.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/main.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o
autoswap_agent: CMakeFiles/autoswap_agent.dir/build.make
autoswap_agent: nanopb_binary_dir/libnanopb.a
autoswap_agent: idl_binary_dir/libidl.a
autoswap_agent: net/liblib_net.a
autoswap_agent: threadpool/liblib_threadpool.a
autoswap_agent: scheduler_msg/liblib_msg.a
autoswap_agent: protocol/liblib_protocol.a
autoswap_agent: swap_manage/liblib_swap_manage.a
autoswap_agent: fsm_manager/liblib_fsm_manager.a
autoswap_agent: CMakeFiles/autoswap_agent.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Linking CXX executable autoswap_agent"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/autoswap_agent.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/autoswap_agent.dir/build: autoswap_agent

.PHONY : CMakeFiles/autoswap_agent.dir/build

CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/readme.c.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/main.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o.requires
CMakeFiles/autoswap_agent.dir/requires: CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o.requires

.PHONY : CMakeFiles/autoswap_agent.dir/requires

CMakeFiles/autoswap_agent.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/autoswap_agent.dir/cmake_clean.cmake
.PHONY : CMakeFiles/autoswap_agent.dir/clean

CMakeFiles/autoswap_agent.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles/autoswap_agent.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/autoswap_agent.dir/depend

