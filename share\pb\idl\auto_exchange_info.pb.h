/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_AUTO_EXCHANGE_INFO_PB_H_INCLUDED
#define PB_AUTO_EXCHANGE_INFO_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _auto_exchange_enable_state {
    auto_exchange_enable_state_DEV_ENABLE_STATE_RESERVE = 0,
    auto_exchange_enable_state_DEV_ENABLE_STATE_ENABLE = 1,
    auto_exchange_enable_state_DEV_ENABLE_STATE_DISABLE = 2
} auto_exchange_enable_state;

/* Struct definitions */
typedef struct _workstation_basic_info {
    uint32_t dev_id;
    auto_exchange_enable_state state;
} workstation_basic_info;

typedef struct _auto_exchange_basic_info {
    uint32_t dev_id;
    auto_exchange_enable_state state;
    char ip[16];
    char sw_ver[16];
    char hw_ver[16];
    pb_size_t workstation_info_count;
    workstation_basic_info workstation_info[4];
} auto_exchange_basic_info;

typedef struct _auto_exchange_basic_info_mutilp {
    pb_size_t dev_info_count;
    auto_exchange_basic_info dev_info[30];
} auto_exchange_basic_info_mutilp;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _auto_exchange_enable_state_MIN auto_exchange_enable_state_DEV_ENABLE_STATE_RESERVE
#define _auto_exchange_enable_state_MAX auto_exchange_enable_state_DEV_ENABLE_STATE_DISABLE
#define _auto_exchange_enable_state_ARRAYSIZE ((auto_exchange_enable_state)(auto_exchange_enable_state_DEV_ENABLE_STATE_DISABLE+1))

#define workstation_basic_info_state_ENUMTYPE auto_exchange_enable_state

#define auto_exchange_basic_info_state_ENUMTYPE auto_exchange_enable_state



/* Initializer values for message structs */
#define workstation_basic_info_init_default      {0, _auto_exchange_enable_state_MIN}
#define auto_exchange_basic_info_init_default    {0, _auto_exchange_enable_state_MIN, "", "", "", 0, {workstation_basic_info_init_default, workstation_basic_info_init_default, workstation_basic_info_init_default, workstation_basic_info_init_default}}
#define auto_exchange_basic_info_mutilp_init_default {0, {auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default, auto_exchange_basic_info_init_default}}
#define workstation_basic_info_init_zero         {0, _auto_exchange_enable_state_MIN}
#define auto_exchange_basic_info_init_zero       {0, _auto_exchange_enable_state_MIN, "", "", "", 0, {workstation_basic_info_init_zero, workstation_basic_info_init_zero, workstation_basic_info_init_zero, workstation_basic_info_init_zero}}
#define auto_exchange_basic_info_mutilp_init_zero {0, {auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero, auto_exchange_basic_info_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define workstation_basic_info_dev_id_tag        1
#define workstation_basic_info_state_tag         2
#define auto_exchange_basic_info_dev_id_tag      1
#define auto_exchange_basic_info_state_tag       2
#define auto_exchange_basic_info_ip_tag          3
#define auto_exchange_basic_info_sw_ver_tag      4
#define auto_exchange_basic_info_hw_ver_tag      5
#define auto_exchange_basic_info_workstation_info_tag 6
#define auto_exchange_basic_info_mutilp_dev_info_tag 1

/* Struct field encoding specification for nanopb */
#define workstation_basic_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2)
#define workstation_basic_info_CALLBACK NULL
#define workstation_basic_info_DEFAULT NULL

#define auto_exchange_basic_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, STRING,   ip,                3) \
X(a, STATIC,   SINGULAR, STRING,   sw_ver,            4) \
X(a, STATIC,   SINGULAR, STRING,   hw_ver,            5) \
X(a, STATIC,   REPEATED, MESSAGE,  workstation_info,   6)
#define auto_exchange_basic_info_CALLBACK NULL
#define auto_exchange_basic_info_DEFAULT NULL
#define auto_exchange_basic_info_workstation_info_MSGTYPE workstation_basic_info

#define auto_exchange_basic_info_mutilp_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  dev_info,          1)
#define auto_exchange_basic_info_mutilp_CALLBACK NULL
#define auto_exchange_basic_info_mutilp_DEFAULT NULL
#define auto_exchange_basic_info_mutilp_dev_info_MSGTYPE auto_exchange_basic_info

extern const pb_msgdesc_t workstation_basic_info_msg;
extern const pb_msgdesc_t auto_exchange_basic_info_msg;
extern const pb_msgdesc_t auto_exchange_basic_info_mutilp_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define workstation_basic_info_fields &workstation_basic_info_msg
#define auto_exchange_basic_info_fields &auto_exchange_basic_info_msg
#define auto_exchange_basic_info_mutilp_fields &auto_exchange_basic_info_mutilp_msg

/* Maximum encoded size of messages (where known) */
#define AUTO_EXCHANGE_INFO_PB_H_MAX_SIZE         auto_exchange_basic_info_mutilp_size
#define auto_exchange_basic_info_mutilp_size     3030
#define auto_exchange_basic_info_size            99
#define workstation_basic_info_size              8

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
