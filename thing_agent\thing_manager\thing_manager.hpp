#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>
#include <stdlib.h>
#include <condition_variable>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "task_manager/task_manager.hpp"
#include "sys_manager/sys_manager.hpp"
#include "device_manager/device_manager.hpp"

#include "jd_thingtalk/cJSON/cJSON.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_protocol.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_proto_internal.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk.h"
#include "jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk_internal.h"

#include "setting/setting.hpp"
#include "thing_interface/thing_interface.hpp"
#include "wcs_manager/wcs_manager.hpp"
#include "container_manager/container_manager.hpp"


#define VEHICLE_DEVICE                          ("autorebinsort")
#define SURESORT_DEVICE                         ("autorebin")

#define INVALID_BARCODE1                        ("noread")
#define INVALID_BARCODE2                        ("NOREAD")

#define DEFAULT_BELT_SPEED                      0.6

class thing_manager
{
public:

    static thing_manager *get_instance(void)
    {
        static thing_manager instance;
        return &instance;
    }

    int init(zmq::context_t &ctx);

    int run();

    int func_issue(JDThingTalkProtoFuncCallFunc_t &function, std::vector<int> devs);
    int properties_issue(JDThingTalkProtoKeyValue_t **properties);

private:

    //主体属性
    int train_agent_properties_set(char *cfg_type, char *cfg_value);

    //主体方法
    int func_switch_mode(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_update_container_led(int in_num, JDThingTalkProtoKeyValue_t **in, std::vector<int> &devs);
    
    int func_update_container_state(int in_num, JDThingTalkProtoKeyValue_t **in, std::vector<int> &devs);

    int func_issue_sorting_task(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_issue_vehicle_demo(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_update_hmi_lamp(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_issue_buzzer_control(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_issue_container_lamp_test(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_issue_scanner_control(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_issue_shelf_lock_control(int in_num, JDThingTalkProtoKeyValue_t **in);

    //sort方法
    int func_sort_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    // int func_sort_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_sort_control_dest_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    // int func_sort_control_walk_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_sort_belt_control(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_sort_platform_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_sort_platform_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    int func_sortgroup_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_sortgroup_control_walk_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_sortgroup_sort_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    //feeder方法
    int func_feeder_command_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_feeder_command_rotate_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);

    //switcher方法
    // int func_switcher_execute_switch(int in_num, JDThingTalkProtoKeyValue_t **in);

    // int func_switcher_set_zero(int in_num, JDThingTalkProtoKeyValue_t **in);

    //主控板mcu方法
    int func_mcu_control_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_work_status_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_config_set_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_config_query_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
    int func_feeder_belt_status_get_dispatch(int in_num, JDThingTalkProtoKeyValue_t **in);
//事件：
    struct timer
    {
        std::chrono::high_resolution_clock::time_point start_time;   //定时查询用
        std::chrono::high_resolution_clock::time_point end_time;

        void start()
        {
            start_time = std::chrono::high_resolution_clock::now();
        }

        uint32_t execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

    struct report_timer
    {
        timer sys_state_report_timer;
        timer vehicle_state_report_timer;
        timer feeder_state_report_timer;
        timer switcher_state_report_timer;
        timer feeder_belt_report_timer;

        uint32_t time_report_vehicle_state_auto;
        uint32_t time_report_sys_state;
        uint32_t time_report_feeder_belt_state;
        uint32_t time_report_vehicle_state_manual;      //模式切换到手动后再开始计时
        uint32_t time_report_feeder_state;
        uint32_t time_report_switcher_state;

        std::map<int, timer>report_feeder_belt_timer;       
        void init()
        {
            time_report_sys_state = setting::get_instance()->get_setting().time_report_sys_state;
            time_report_vehicle_state_auto = setting::get_instance()->get_setting().time_report_vehicle_state_auto;
            time_report_vehicle_state_manual = setting::get_instance()->get_setting().time_report_vehicle_state_manual;
            time_report_feeder_state = setting::get_instance()->get_setting().time_report_feeder_state;
            time_report_switcher_state = setting::get_instance()->get_setting().time_report_switcher_state;
            time_report_feeder_belt_state = setting::get_instance()->get_setting().time_report_feeder_belt_state;


            sys_state_report_timer.start();
            vehicle_state_report_timer.start();

            //timer feeder_belt_report_timer;
           // std::map<int, timer>report_feeder_belt_timer;
           // int init_feeder_belt_report_timer()
            //{
            std::vector<int> ids;
            device_manager::get_instance()->get_feeder_ids(ids);

            for (auto &id: ids)
            {
                report_feeder_belt_timer.emplace(id,feeder_belt_report_timer);
                report_feeder_belt_timer[id].start();
            }
           // }
        }
    };

    std::thread *evt_barcode;          //条形码相关上报
    std::thread *evt_task_state;        //任务状态相关上报
    std::thread *evt_container;     //格口事件上报
    std::thread *evt_exception;     //异常相关上报
    std::thread *evt_box_state;     //满箱/空箱状态上报
    std::thread *evt_shelf_lock_state;  //货架锁状态上报
    std::thread *evt_state_event;   //需要定时执行的事件
    std::thread *evt_task_overtime;     //超时任务，目前关注商品上车后长时间未下发正式任务，需下发兜底分播
    std::thread *wcs_function;          //接收wcs下发方法线程

    std::thread *event_thread;        //需要等待和物控平台连接完毕线程才能启动

    int evt_get_barcode_thread();
    int evt_get_task_state_thread();

    int evt_get_container_thread();
    int evt_get_box_state_thread();
    int evt_get_shelf_lock_state_thread();
    int evt_report_normal_grids_event();

    int evt_report_sys_register_event();
    int evt_report_sys_state_event(const sys_mode_state &state);
    int evt_report_safetygate_state_event(const sys_mode_state &state);
    int evt_report_electricalcabinet_state_event(const sys_mode_state &state);
    int evt_report_vehicle_state_event(const e_sys_mode &mode);
    int evt_report_feeder_state_event();
    int evt_report_feeder_belt_status_event();
    int evt_report_switcher_state_event();


    int evt_get_event_exception_thread();
    int evt_get_vehicle_exception(except_info &exception);
    int evt_get_carriage_exception(except_info &exception);
    int evt_get_platform_exception(except_info &exception);
    int evt_get_container_exception(except_info &exception);
    int evt_get_container_group_exception(except_info &exception);
    int evt_get_shelves_exception(except_info &exception);
    int evt_get_feeder_exception(except_info &exception);
    int evt_get_switcher_exception(except_info &exception);
    int evt_get_device_exception(except_info &exception);

    int wcs_get_function_thread();

    int evt_state_event_thread();

    void device_event_thread();

    //格口满箱和封箱状态公用接口
    int evt_report_container_seal_state(const container_seal_state_single &st);
    int wcs_evt_container_state(const slot_state &st);
    int wcs_evt_container_state(const container_seal_state_single &st);
    void set_module_callback_function();

    report_timer evt_report_timer;
//上报类型，用于判断兜底格口的任务状态是否需要上报
//收到noread——不上报，超时下发任务——上报兜底完成
    enum report_service_type
    {
        NOT_REPORT = 0,     //不需要上报
        REPORT_HOSPICE_FINISH = 1,
        NORMAL_REPORT = 2,
        REPORT_SUSPEND = 3
    };

    struct hospice_task
    {
        std::string task_id;
        report_service_type report_type;

        hospice_task(std::string id, report_service_type type)
        {
            task_id = id;
            report_type = type;
        }

        bool operator==(const hospice_task tk) const
        {
            return (this->task_id == tk.task_id && this->report_type == tk.report_type);
    	}
    };

    std::mutex hospice_task_state_lock;
    std::list<hospice_task> hospice_task_list;         //收到noread码值后，不上报任务状态的任务号list
    report_service_type get_report_task_state(task_manager::task_state state);

    timer task_timer;       //关注上报物控商品码到下发任务的时间

    struct slot_event_report   //满箱 空箱上报延迟
    {
        slot_state state;
        timer report_timer;
    };
    std::list<slot_event_report> slot_events_report_list;           //用于未回ack的事件重新定时发送
};
