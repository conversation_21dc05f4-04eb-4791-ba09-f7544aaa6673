#include "test_case.hpp"
#include "share/global_def.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/task.pb.h"
#include "share/exception_code.hpp"
#include <mutex>  
#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <fstream>
#include <iostream>




testcase::testcase(zmq::context_t &context)
:train_task_requester(zmq::socket_t(context, ZMQ_REQ))
,train_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,carriage_task_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,platform_task_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,sorting_task_state_msg_pub(zmq::socket_t(context, ZMQ_PUB))
,sorting_task_state_msg_sub(zmq::socket_t(context, ZMQ_SUB))
,train_excep_subscriber(zmq::socket_t(context, ZMQ_SUB))
,train_excep_publisher(zmq::socket_t(context, ZMQ_PUB))
,train_sys_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,train_sys_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,train_ext_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,coreserver_train_info_request(zmq::socket_t(context, ZMQ_REQ))
,train_agent_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,train_agent_train_info_request(zmq::socket_t(context, ZMQ_REQ))
,vehicle_extstate_recver(zmq::socket_t(context, ZMQ_SUB))

{
    return;
}

testcase::~testcase()
{

}


bool testcase::train_task_send(train_task &task_cmd)
{
	std::lock_guard<std::mutex> lk(task_send_mutex);
    pb_ostream_t stream_out;
    pb_istream_t stream_in;
    uint8_t pub_msg[train_task_size];
    ack ack_recv;
    zmq::message_t zmq_reply;

    if( !train_task_requester.connected() )
	{
		SPDLOG_INFO("[ZMQ] train_task_send fail to connect");
	}

    	    
    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg)); 
	if(!pb_encode(&stream_out, train_task_fields, &task_cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return false;
	}

	zmq::send_result_t ret = train_task_requester.send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_INFO("pub_test_case_send {}:{} bytes successfully", ret.has_value(), ret.value());

    train_task_requester.recv(zmq_reply, zmq::recv_flags::none);
    if(0 == zmq_reply.size())
    {
        SPDLOG_ERROR("train_task_requester:{} recv error", task_cmd.dev_id);
        return false;
    }

    stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, ack_fields, &ack_recv))
	{
		SPDLOG_INFO("[ZMQ] train_task_requester ack pb_decode fail ");
		return false;
	}

    SPDLOG_INFO("train_task_requester sequence:{}-{}",  ack_recv.sequence, task_cmd.sequence);
	if(ack_recv.sequence != task_cmd.sequence)
	{
		return false;
	}

	return true;

}


void testcase::train_state_info(train_state *train_state)
{
    // SPDLOG_INFO("dev_manager_train_state_info:");
    SPDLOG_INFO("train_id:{}", train_state->train_id);
    // SPDLOG_INFO("carriage_cnt:{}", train_state->carriage_cnt);
    // SPDLOG_INFO("train_state:{}", train_state->train_state);
    // SPDLOG_INFO("train_error_no:{}", train_state->train_error_no);
    // SPDLOG_INFO("motion_positon:{}", train_state->motion_positon);
    // SPDLOG_INFO("motion_positon_valid_flag:{}", train_state->motion_positon_valid_flag);
    // SPDLOG_INFO("motion_velocity:{}", train_state->motion_velocity);
    // SPDLOG_INFO("calib_sensor_value:{}", train_state->calib_sensor_value);
    // SPDLOG_INFO("motor_state:{}", train_state->motor_state);
    // SPDLOG_INFO("motor_encoder_value:{}", train_state->motor_encoder_value);
    // SPDLOG_INFO("motor_error_no:{}", train_state->motor_error_no);
    // SPDLOG_INFO("dev_comm_ack_state:{}", train_state->dev_comm_ack_state);
    // SPDLOG_INFO("work_state:{}", train_state->work_state);
    // SPDLOG_INFO("carriage_st_count:{}", train_state->carriage_st_count);

    for(int i = 0; i < train_state->carriage_st_count; i++)
    {
        SPDLOG_INFO("carriage_id:{}", train_state->carriage_st[i].carriage_id);
        SPDLOG_INFO("y_axis_encoder_state:{}", train_state->carriage_st[i].y_axis_encoder_state);
        // SPDLOG_INFO("y_axis_encoder_value:{}", train_state->carriage_st[i].y_axis_encoder_value);
        // SPDLOG_INFO("y_axis_encoder_err_no:{}", train_state->carriage_st[i].y_axis_encoder_err_no);
        SPDLOG_INFO("load_platform_state:{}", train_state->carriage_st[i].load_platform_state);
        // SPDLOG_INFO("load_platform_belt_zero_state:{}", train_state->carriage_st[i].load_platform_belt_zero_state);
        // SPDLOG_INFO("load_platform_encoder_value:{}", train_state->carriage_st[i].load_platform_encoder_value);
        // SPDLOG_INFO("load_platform_err_no:{}", train_state->carriage_st[i].load_platform_err_no);
        // SPDLOG_INFO("carriage_load_state:{}", train_state->carriage_st[i].carriage_load_state);
        // SPDLOG_INFO("carriage_task:{}", train_state->carriage_st[i].carriage_task);
        // SPDLOG_INFO("carriage_task_state:{}", train_state->carriage_st[i].carriage_task_state);

    }
}



//车辆任务下发
void testcase::dev_manager_train_task_send(void)
{
    SPDLOG_INFO("dev_manager_train_task_send thread run...");
    std::this_thread::sleep_for(std::chrono::seconds(5));

    train_task task_cmd_move;
    train_task task_cmd_lifting;
    train_task task_cmd_shift;
    train_task task_cmd_dev_cmd;
    train_task task_cmd_inte_task;
    train_task task_move;
    train_task belt_roll_back;

    zmq::message_t msg;
    int retry_cnt = 0;
    bool result = false;
    

    task_cmd_move.sequence = 100;
    task_cmd_move.dev_id = 1;
    task_cmd_move.carriage_id = 0;
    task_cmd_move.which_task = train_task_move_tag;    //小车移动
    task_cmd_move.task.move.type = task_type_MOVE_SPEED;
    task_cmd_move.task.move.start = 0;
    task_cmd_move.task.move.target = 0;
    task_cmd_move.task.move.speed_limit = 0;

    
    task_cmd_lifting.sequence = 200;
    task_cmd_lifting.dev_id = 3;
    task_cmd_lifting.carriage_id = 1;
    task_cmd_lifting.which_task = train_task_lifting_tag;    //车厢升降
    task_cmd_lifting.task.lifting.type = task_type_Y_MOVE;
    task_cmd_lifting.task.lifting.target = 100;

    
    task_cmd_shift.sequence = 300;
    task_cmd_shift.dev_id = 1;
    task_cmd_shift.carriage_id = 2;
    task_cmd_shift.which_task = train_task_shift_tag;
    // task_cmd_shift.task.shift.type = task_type_CARRIAGE_SHIFT_IN;
    task_cmd_shift.task.shift.type = task_type_CARRIAGE_SHIFT_OUT;
    task_cmd_shift.task.shift.target_pos = -200;
    task_cmd_shift.task.shift.target_y_pos = 200;


    task_cmd_dev_cmd.sequence = 400;
    task_cmd_dev_cmd.dev_id = 1;
    task_cmd_dev_cmd.carriage_id = 4;
    task_cmd_dev_cmd.which_task = train_task_cmd_tag;
    task_cmd_dev_cmd.task.cmd = dev_cmd_tab_RESET;


    task_cmd_inte_task.sequence = 500;
    task_cmd_inte_task.dev_id = 3;
    task_cmd_inte_task.carriage_id = 5;
    task_cmd_inte_task.which_task = train_task_inte_task_tag;
    task_cmd_inte_task.task.inte_task.type = task_type_TASK_INTE;
    task_cmd_inte_task.task.inte_task.task_info_move_pos = 1234;
    task_cmd_inte_task.task.inte_task.task_info_move_speed_limit = 40;
    task_cmd_inte_task.task.inte_task.task_lifting_valid = true;
    task_cmd_inte_task.task.inte_task.task_info_y_pos = 80;
    task_cmd_inte_task.task.inte_task.has_task_info_shift = false;
    task_cmd_inte_task.task.inte_task.task_info_shift.type = task_type_CARRIAGE_SHIFT_OUT;
    task_cmd_inte_task.task.inte_task.task_info_shift.target_pos = 3000;
    task_cmd_inte_task.task.inte_task.task_info_shift.target_y_pos = 90;


    task_move.sequence = 600;
    task_move.dev_id = 4;
    task_move.carriage_id = 0;
    task_move.which_task = train_task_move_tag;    //小车移动
    task_move.task.move.type = task_type_MOVE_SPEED;
    task_move.task.move.start = 0;
    task_move.task.move.target = 2000;
    task_move.task.move.speed_limit = 300;

    belt_roll_back.sequence = 700;
    belt_roll_back.dev_id = 1;
    belt_roll_back.carriage_id = 3;
    belt_roll_back.which_task = train_task_roll_back_tag;
    belt_roll_back.task.roll_back.type = task_type_TASK_BELT_ROLL_BACK;
    belt_roll_back.task.roll_back.target_pos = 10900;
    belt_roll_back.task.roll_back.speed = 3000;
    belt_roll_back.task.roll_back.target_limit = 8000;



    // train_task_send(task_cmd_shift);
    // std::this_thread::sleep_for(std::chrono::microseconds(200));

    while(1)
    {
        train_task_send(belt_roll_back);
        
        // train_task_send(task_cmd_move);
        // task_cmd_move.sequence++;
        // if(task_cmd_move.task.move.target != 2000)
        //     task_cmd_move.task.move.target++;
        // else
        //     task_cmd_move.task.move.target = 0;
        // std::this_thread::sleep_for(std::chrono::microseconds(100));
        // std::this_thread::sleep_for(std::chrono::seconds(10));


        // train_task_send(task_cmd_lifting);
        // task_cmd_lifting.sequence++;
        // if(task_cmd_lifting.task.lifting.target != 900)
        //     task_cmd_lifting.task.lifting.target += 100;
        // else
        //     task_cmd_lifting.task.lifting.target = 100;
        // std::this_thread::sleep_for(std::chrono::microseconds(200));


        // train_task_send(task_cmd_shift);
        // task_cmd_shift.sequence++;
        // std::this_thread::sleep_for(std::chrono::microseconds(100));


        // train_task_send(task_cmd_lifting);
        // task_cmd_lifting.sequence++;  
        // std::this_thread::sleep_for(std::chrono::microseconds(200));


        // train_task_send(task_cmd_shift);
        // task_cmd_shift.sequence++;
        // std::this_thread::sleep_for(std::chrono::microseconds(100));


        // train_task_send(task_cmd_dev_cmd);
        // task_cmd_dev_cmd.sequence++;
        // std::this_thread::sleep_for(std::chrono::microseconds(200));


        // train_task_send(task_cmd_inte_task);
        // task_cmd_inte_task.sequence++;
        // std::this_thread::sleep_for(std::chrono::microseconds(100));
        

        // train_task_send(task_move);
        // task_move.sequence++;


        std::this_thread::sleep_for(std::chrono::seconds(60));
    }

  

}

void train_config_para_display(train_config_para train_config_cfg)
{
    SPDLOG_INFO("train_config_cfg.x_speed: {}", train_config_cfg.x_speed);
    SPDLOG_INFO("train_config_cfg.x_acc: {}", train_config_cfg.x_acc);
    SPDLOG_INFO("train_config_cfg.x_d_acc: {}", train_config_cfg.x_d_acc);
    SPDLOG_INFO("train_config_cfg.y_speed: {}", train_config_cfg.y_speed);
    SPDLOG_INFO("train_config_cfg.y_acc: {}", train_config_cfg.y_acc);
    SPDLOG_INFO("train_config_cfg.y_d_acc: {}", train_config_cfg.y_d_acc);
    SPDLOG_INFO("train_config_cfg.z_speed: {}", train_config_cfg.z_speed);
    SPDLOG_INFO("train_config_cfg.z_cali_speed: {}", train_config_cfg.z_cali_speed);
    SPDLOG_INFO("train_config_cfg.z_acc: {}", train_config_cfg.z_acc);
    SPDLOG_INFO("train_config_cfg.z_limit: {}", train_config_cfg.z_limit);

}

void testcase::dev_manager_train_agent_info_request(void)
{
    SPDLOG_INFO("dev_manager_train_agent_info_request thread run...");

    train_basic_info_mutilp train_basic_info;
    train_config_para train_config_cfg;

    while(1)
    { 
        // get_train_basic_info_form_train_agent(train_basic_info);
    
        // for(int i = 0; i < train_basic_info.train_info_count; i++)
        //     SPDLOG_INFO("train_agent_train_info: train_id:{}, ip:{}, sw:{}, hw:{}", train_basic_info.train_info[i].train_id, train_basic_info.train_info[i].ip, train_basic_info.train_info[i].sw_ver, train_basic_info.train_info[i].hw_ver);

        get_train_config_para_form_train_agent(train_config_cfg);
        train_config_para_display(train_config_cfg);

        std::this_thread::sleep_for(std::chrono::seconds(5));
    }

}


void testcase::dev_manager_coreserver_train_info_request(void)
{
    SPDLOG_INFO("dev_manager_coreserver_train_info_request thread run...");

    train_basic_info_mutilp train_basic_info;

    while(1)
    { 
        get_train_basic_info_form_coreserver(train_basic_info);

        for(int i = 0; i < train_basic_info.train_info_count; i++)
            SPDLOG_INFO("coreserver_train_info: train_id:{}, ip:{}, sw:{}, hw:{}", train_basic_info.train_info[i].train_id, train_basic_info.train_info[i].ip, train_basic_info.train_info[i].sw_ver, train_basic_info.train_info[i].hw_ver);


        std::this_thread::sleep_for(std::chrono::seconds(5));
    }

}

int testcase::get_train_basic_info_form_coreserver(train_basic_info_mutilp &train_info)
{
    uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	// 首先检查当前socket的连接状态
	if( !coreserver_train_info_request.connected() )
	{
		SPDLOG_ERROR("coreserver_train_info_request zmq unconected");
	}
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_TRAIN_LIST, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		coreserver_train_info_request.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	coreserver_train_info_request.recv(zmq_reply, zmq::recv_flags::none);		
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train list from coreserver.");
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, train_basic_info_mutilp_fields, &train_info))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train list"), "pb decode error: {}", stream_in.errmsg);
	}

    return 0;
}


int testcase::get_train_basic_info_form_train_agent(train_basic_info_mutilp &train_info)
{
    uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	// 首先检查当前socket的连接状态
	if( !train_agent_train_info_request.connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
	}
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_TRAIN_BASE_INFO, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		train_agent_train_info_request.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	train_agent_train_info_request.recv(zmq_reply, zmq::recv_flags::none);		
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train list from coreserver.");
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, train_basic_info_mutilp_fields, &train_info))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train list"), "pb decode error: {}", stream_in.errmsg);
	}

    return 0;
}

int testcase::get_train_config_para_form_train_agent(train_config_para &train_config_cfg)
{
    uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	// 首先检查当前socket的连接状态
	if( !train_agent_train_info_request.connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
	}
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_TRAIN_CFG_PARA, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_config_para"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		train_agent_train_info_request.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	train_agent_train_info_request.recv(zmq_reply, zmq::recv_flags::none);		
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train config para from coreserver.");
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, train_config_para_fields, &train_config_cfg))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train config para"), "pb decode error: {}", stream_in.errmsg);
	}

    return 0;

}


void testcase::dev_manager_train_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_train_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_state  train_state;
    
    
    while(1)
    {
        train_state_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, train_state_fields, &train_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        train_state_info(&train_state);
        // SPDLOG_INFO("train_id:{}-{}", train_state.train_id, train_state.carriage_cnt);

        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }


}

void testcase::dev_manager_train_agent_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_train_agent_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_agent_state  train_agent_state;
    
    
    while(1)
    {
        train_agent_state_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, train_agent_state_fields, &train_agent_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        SPDLOG_INFO("dev_manager_train_agent_state_info:");
        SPDLOG_INFO("train_id:{}", train_agent_state.train_id);
        SPDLOG_INFO("carriage_cnt:{}", train_agent_state.carriage_cnt);
        SPDLOG_INFO("train_state:{}", train_agent_state.train_state);
        SPDLOG_INFO("train_agent_work_state:{}", train_agent_state.train_agent_work_state);
        SPDLOG_INFO("work_state:{}", train_agent_state.work_state);
        SPDLOG_INFO("train_error_no:{}", train_agent_state.train_error_no);

        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }
}

void testcase::dev_manager_train_schedule_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_train_schedule_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_ext_state_multi  train_ext_state;
    
    while(1)
    {
        vehicle_extstate_recver.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, train_ext_state_multi_fields, &train_ext_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        SPDLOG_INFO("dev_manager_train_ext_state_info:");
        SPDLOG_INFO("trains_count:{}", train_ext_state.trains_count);

        for(int i = 0; i < train_ext_state.trains_count; i++)
        {
            SPDLOG_INFO("train_id:{}", train_ext_state.trains[i].train_id);
            SPDLOG_INFO("online_state:{}", train_ext_state.trains[i].online_state);
            SPDLOG_INFO("speed:{}", train_ext_state.trains[i].speed);
            SPDLOG_INFO("ip_addr:{}", train_ext_state.trains[i].ip_addr);
            SPDLOG_INFO("firmware_version:{}", train_ext_state.trains[i].firmware_version);
            SPDLOG_INFO("motor_state_no:{}", train_ext_state.trains[i].motor_state_no);
            SPDLOG_INFO("motor_state:{}", train_ext_state.trains[i].motor_state);
            SPDLOG_INFO("motor_speed:{}", train_ext_state.trains[i].motor_speed);

            for(int j = 0 ; j < train_ext_state.trains[i].carriages_count; j++)
            {
                SPDLOG_INFO("carriage_id:{}", train_ext_state.trains[i].carriages[j].carriage_id);
                SPDLOG_INFO("platform_id:{}", train_ext_state.trains[i].carriages[j].platforms[0].platform_id);
                SPDLOG_INFO("pos_3d.x:{}", train_ext_state.trains[i].carriages[j].pos_3d.x);
                SPDLOG_INFO("pos_3d.y:{}", train_ext_state.trains[i].carriages[j].pos_3d.y);
                SPDLOG_INFO("pos_3d.z:{}", train_ext_state.trains[i].carriages[j].pos_3d.z);
            }
            

    

        }

        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }
}

void testcase::train_ext_state_send(train_ext_state_multi &train_ext_state)
{
    uint8_t pub_msg[train_ext_state_multi_size];
	pb_ostream_t stream_out;
    zmq::send_result_t ret;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, train_ext_state_multi_fields, &train_ext_state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return;
	}


    ret = train_ext_state_publisher.send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_INFO("train_ext_state_publisher send {}:{} bytes successfully", ret.has_value(), ret.value());

}


void testcase::dev_manager_train_schedule_state_publisher(void)
{
    SPDLOG_INFO("dev_manager_train_schedule_state_publisher thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_ext_state_multi  train_ext_state;

    train_ext_state.trains_count = 1;
    for(int i = 0; i < train_ext_state.trains_count; i++)
    {
        train_ext_state.trains[i].train_id = 1;
        train_ext_state.trains[i].online_state = true;
        train_ext_state.trains[i].speed = 200;
        train_ext_state.trains[i].fault_state = 0;
        strcpy(train_ext_state.trains[i].ip_addr, "************");
        strcpy(train_ext_state.trains[i].firmware_version, "123");
        strcpy(train_ext_state.trains[i].motor_state_no, "456");
        train_ext_state.trains[i].motor_state = train_dev_state_DEV_NORMAL;
        train_ext_state.trains[i].motor_speed = 1000;
        train_ext_state.trains[i].carriages_count = 4;

        for(int j = 0; j < train_ext_state.trains[i].carriages_count; j++)
        {
            train_ext_state.trains[i].carriages[j].carriage_id = 0;
            train_ext_state.trains[i].carriages[j].carriage_id ++;
            train_ext_state.trains[i].carriages[j].pos = 300;
            train_ext_state.trains[i].carriages[j].has_pos_3d = true;
            train_ext_state.trains[i].carriages[j].pos_3d.x = 100;
            train_ext_state.trains[i].carriages[j].pos_3d.y = 200;
            train_ext_state.trains[i].carriages[j].pos_3d.z = 300;
            train_ext_state.trains[i].carriages[j].type = carriage_type_CARRIAGE_TYPE_HEADSTOCK;
            train_ext_state.trains[i].carriages[j].work_state = train_dev_state_DEV_NORMAL;
            train_ext_state.trains[i].carriages[j].online_state = true;
            strcpy( train_ext_state.trains[i].carriages[j].firmware_version, "456");
            train_ext_state.trains[i].carriages[j].power_flag = true;
            strcpy( train_ext_state.trains[i].carriages[j].lifting_state_no, "800");
            train_ext_state.trains[i].carriages[j].lifting_state = train_dev_state_DEV_NORMAL;
            train_ext_state.trains[i].carriages[j].lifting_speed = 2000;
            train_ext_state.trains[i].carriages[j].platforms_count = 1;

            for(int z = 0; z < train_ext_state.trains[i].carriages[j].platforms_count; z++)
            {
                train_ext_state.trains[i].carriages[j].platforms[z].platform_id = 1;
                train_ext_state.trains[i].carriages[j].platforms[z].location_z = 900;
                train_ext_state.trains[i].carriages[j].platforms[z].online_state = true;
                train_ext_state.trains[i].carriages[j].platforms[z].load_state = true;
                train_ext_state.trains[i].carriages[j].platforms[z].type = platform_type_PLATFORM_TYPE_BELT;
                strcpy(train_ext_state.trains[i].carriages[j].platforms[z].servo_state_no, "0");
                train_ext_state.trains[i].carriages[j].platforms[z].servo_state = train_dev_state_DEV_NORMAL;
                train_ext_state.trains[i].carriages[j].platforms[z].servo_speed = 400;
                train_ext_state.trains[i].carriages[j].platforms[z].platform_zero_flag = true;

                train_ext_state.trains[i].carriages[j].platforms[z].platform_id++;

            }
        } 
    }


    while(1)
    {
        train_ext_state_send(train_ext_state);

        std::this_thread::sleep_for(std::chrono::seconds(5));
    }


}

void testcase::dev_manager_carriage_task_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_carriage_task_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_task_state  train_task_state;
    
   
    while(1)
    {
        carriage_task_state_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, train_task_state_fields, &train_task_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        SPDLOG_INFO("dev_manager_carriage_task_state_info:");
        SPDLOG_INFO("train_id:{}", train_task_state.dev_id);
        SPDLOG_INFO("carriage_id:{}", train_task_state.carriage_id);
        SPDLOG_INFO("task_type:{}, task_state:{}", train_task_state.type, train_task_state.state);

        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }
}

void testcase::dev_manager_platform_task_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_platform_task_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    train_task_state  train_task_state;
    
   
    while(1)
    {
        platform_task_state_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, train_task_state_fields, &train_task_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        SPDLOG_INFO("dev_manager_platform_task_state_info:");
        SPDLOG_INFO("train_id:{}", train_task_state.dev_id);
        SPDLOG_INFO("carriage_id:{}", train_task_state.carriage_id);
        SPDLOG_INFO("task_type:{}, task_state:{}", train_task_state.type, train_task_state.state);

        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }
}

void testcase::dev_manager_sorting_task_state_msg_pub(void)
{
    SPDLOG_INFO("dev_manager_sorting_task_state_msg_pub thread run...");


    pb_ostream_t stream_out;
    sorting_task_state_msg task_info;
    uint8_t pub_msg[sorting_task_state_msg_size];
    zmq::send_result_t ret;


    strcpy((char *)task_info.task_id, "123");
    strcpy((char *)task_info.gd_codes, "96162");
    task_info.state = sorting_task_state_FINISHED;
    task_info.train_id = 3;
    task_info.platform_id = 301;


    while(1)
    {
        stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
        if(!pb_encode(&stream_out, sorting_task_state_msg_fields, &task_info))
        {
            SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
            return;
        }

        ret = sorting_task_state_msg_pub.send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
        SPDLOG_INFO("dev_manager_train_excep_publisher publish {}:{} bytes successfully", ret.has_value(), ret.value());

        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
}

void testcase::dev_manager_sorting_task_state_msg_sub(void)
{
    SPDLOG_INFO("dev_manager_sorting_task_state_msg_sub run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    sorting_task_state_msg task_info;

    while(1)
    {
        sorting_task_state_msg_sub.recv(msg, zmq::recv_flags::none);
        // sorting_task_state_msg_sub.recv(&msg, ZMQ_NULL);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, sorting_task_state_msg_fields, &task_info))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        else
        {
            SPDLOG_INFO("get task_info codes:{}, state:{}, taskid:{}, train_id:{}, platform_id:{}, conid:{}", task_info.gd_codes, task_info.state,
                task_info.task_id, task_info.train_id, task_info.platform_id, task_info.container);
        }

    }



}



void testcase::dev_manager_train_excep_subscriber(void)
{
    SPDLOG_INFO("dev_manager_train_excep_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    event_exception exception_info;

    while(1)
    {
        train_excep_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, event_exception_fields, &exception_info))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }

        if(exception_info.which_evt_except == 1)
        {
            SPDLOG_INFO("dev_manager_train_exception_event_info:");
            SPDLOG_INFO("src:{}, dev:{}, sub_dev:{}, code:{}, sub_code:{}", exception_info.evt_except.evt.src, exception_info.evt_except.evt.dev,
             exception_info.evt_except.evt.sub_dev, exception_info.evt_except.evt.code, exception_info.evt_except.evt.sub_code);
        }
        else
        {   
            SPDLOG_INFO("dev_manager_train_exception_except_info:");
            SPDLOG_INFO("src:{}, level:{}", exception_info.evt_except.except.src, exception_info.evt_except.except.level);
            SPDLOG_INFO("code:{}, sub_code:{}", exception_info.evt_except.except.code, exception_info.evt_except.except.sub_code);
            SPDLOG_INFO("dev:{}, sub_dev:{}", exception_info.evt_except.except.dev, exception_info.evt_except.except.sub_dev);
            SPDLOG_INFO("state:{}, description:{}", exception_info.evt_except.except.state, std::string(exception_info.evt_except.except.description));
        }


        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }
}

void testcase::dev_manager_train_excep_publisher()
{
    std::this_thread::sleep_for(std::chrono::microseconds(2000));
    SPDLOG_INFO("dev_manager_train_excep_publisher thread run...");

    uint8_t pub_msg[event_exception_size];
    pb_ostream_t stream_out;
    event_exception exception_info;
    zmq::send_result_t ret;

    exception_info.which_evt_except = event_exception_except_tag;
    exception_info.evt_except.except.src = exception_src_TRAIN;
    exception_info.evt_except.except.level = exception_level_FATAL;

    exception_info.evt_except.except.code = TRAIN_MOVE_SERVO_FATAL;
    exception_info.evt_except.except.sub_code = 32;
    strcpy(exception_info.evt_except.except.description, "车头组行走伺服异常");
    exception_info.evt_except.except.dev = 1;
    exception_info.evt_except.except.sub_dev = 0;
    exception_info.evt_except.except.state = exception_state_STATE_OCCURED;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, event_exception_fields, &exception_info))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return;
	}


    ret = train_excep_publisher.send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_INFO("dev_manager_train_excep_publisher publish {}:{} bytes successfully", ret.has_value(), ret.value());

}


void testcase::train_sys_state_send(sys_mode_state &sys_state_temp)
{
    uint8_t pub_msg[sys_mode_state_size];
	pb_ostream_t stream_out;
    zmq::send_result_t ret;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, sys_mode_state_fields, &sys_state_temp))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return;
	}


    ret = train_sys_state_publisher.send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_INFO("train_sys_state_publisher send {}:{}:{}:{} bytes successfully", sys_state_temp.dev_st.emerg_pressed, sys_state_temp.dev_st.safty_door_open, ret.has_value(), ret.value());

}

void testcase::dev_manager_train_sys_state_publisher(void)
{
    SPDLOG_INFO("dev_manager_train_sys_state_publisher thread run...");

    sys_mode_state sys_state_temp;
    sys_mode_state sys_state;
    sys_mode_state mode_state;

    sys_state_temp.has_dev_st = 1;
    sys_state_temp.dev_st.safty_door_open = 0;
    sys_state_temp.dev_st.emerg_pressed = 1;
    sys_state_temp.mode = e_sys_mode_AUTO;
    sys_state_temp.state = e_wkstate_SYS_MANUAL;

    sys_state.has_dev_st = 1;
    sys_state.dev_st.safty_door_open = 1;
    sys_state.dev_st.emerg_pressed = 1;

    mode_state.mode = e_sys_mode_MANNUAL;



    while(1)
    {
        // train_sys_state_send(mode_state);

        train_sys_state_send(sys_state_temp);

        // std::this_thread::sleep_for(std::chrono::seconds(5));
        // train_sys_state_send(sys_state);
        std::this_thread::sleep_for(std::chrono::seconds(3));


    }
	
    
}


void testcase::dev_manager_train_sys_state_subscriber(void)
{
    SPDLOG_INFO("dev_manager_train_sys_state_subscriber thread run...");

    zmq::message_t msg;
    pb_istream_t stream_in;
    sys_mode_state sys_state;
    
    
    while(1)
    {
        train_sys_state_subscriber.recv(msg, zmq::recv_flags::none);
        stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
        if (!pb_decode(&stream_in, sys_mode_state_fields, &sys_state))
        {
            SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        }
        
        SPDLOG_INFO("sys_state, mode:{}, state:{}, err:{}", sys_state.mode, sys_state.state, sys_state.err);

        std::this_thread::sleep_for(std::chrono::microseconds(10));
    }

}


void testcase::testcase_init()
{
    int timeout = 1000;

	train_task_requester.connect(SERVICE_TRAIN_TASK);
    zmq_setsockopt (train_task_requester, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (train_task_requester, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

    coreserver_train_info_request.connect(SERVICE_DATA_ACCESS);
    zmq_setsockopt (train_task_requester, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (train_task_requester, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

    train_agent_train_info_request.connect(SERVICE_TARIN_BASE_INFO);
    zmq_setsockopt (train_task_requester, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (train_task_requester, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

	vehicle_extstate_recver.connect(TOPIC_TRAIN_EXTSTATE);
	vehicle_extstate_recver.set(zmq::sockopt::subscribe, "");

    train_state_subscriber.connect(TOPIC_TRAIN_STATE);
    train_state_subscriber.set(zmq::sockopt::subscribe, "");

    train_agent_state_subscriber.connect(TOPIC_TRAIN_AGENT_STATE);
    train_agent_state_subscriber.set(zmq::sockopt::subscribe, "");

	carriage_task_state_subscriber.connect(TOPIC_TRAIN_CARRIAGE_TASK);
    carriage_task_state_subscriber.set(zmq::sockopt::subscribe, "");

    platform_task_state_subscriber.connect(TOPIC_TRAIN_PLATFORM_TASK_STATE);
    platform_task_state_subscriber.set(zmq::sockopt::subscribe, "");

	train_excep_subscriber.connect(TOPIC_EXCEPTION_TRAIN);
    train_excep_subscriber.set(zmq::sockopt::subscribe, "");

    // train_excep_publisher.bind(TOPIC_EXCEPTION_TRAIN);

    train_sys_state_publisher.bind(TOPIC_SYS_STATE);

    // train_sys_state_subscriber.connect(TOPIC_SYS_STATE);
    // train_sys_state_subscriber.set(zmq::sockopt::subscribe, "");

    // train_ext_state_publisher.bind(TOPIC_TRAIN_EXTSTATE);

    // sorting_task_state_msg_pub.bind(TOPIC_TASK_STATE);

    sorting_task_state_msg_sub.connect(TOPIC_TASK_STATE);
    sorting_task_state_msg_sub.set(zmq::sockopt::subscribe, "");
}


void testcase::testcase_run()
{
    // new std::thread(&testcase::dev_manager_train_task_send, this);

    // new std::thread(&testcase::dev_manager_train_agent_info_request, this);
    // new std::thread(&testcase::dev_manager_coreserver_train_info_request, this);
    // new std::thread(&testcase::dev_manager_train_state_subscriber, this);
    // new std::thread(&testcase::dev_manager_train_agent_state_subscriber, this);
    // new std::thread(&testcase::dev_manager_carriage_task_state_subscriber, this);
    // new std::thread(&testcase::dev_manager_platform_task_state_subscriber, this);

    // new std::thread(&testcase::dev_manager_sorting_task_state_msg_pub, this);
    // new std::thread(&testcase::dev_manager_sorting_task_state_msg_sub, this);

    // new std::thread(&testcase::dev_manager_train_excep_subscriber, this);
    // new std::thread(&testcase::dev_manager_train_excep_publisher, this);
    // new std::thread(&testcase::dev_manager_train_schedule_state_subscriber, this);
    // new std::thread(&testcase::dev_manager_train_schedule_state_publisher, this);

    new std::thread(&testcase::dev_manager_train_sys_state_publisher, this);
    // new std::thread(&testcase::dev_manager_train_sys_state_subscriber, this);
}