/*
* @file  	   container_manager.hpp
* @brief      格口状态通过TCP发送
* @attention
* 主程序版本：
* @par 修改日志:
* -# 2024.5.28 针对天津信封瀑布分拣模式，增加格口状态通过TCP发送给另外一个分播墙
* </table>
*
**********************************************************************************
*/

#include "container_manager.hpp"

int container_manager::init(zmq::context_t &ctx)
{
    SPDLOG_DEBUG("container manager init ......");
    seal_evt_send = new zmq::socket_t {ctx, zmq::socket_type::pub};

    seal_evt_send -> set(zmq::sockopt::tcp_keepalive, 1);
    seal_evt_send -> set(zmq::sockopt::tcp_keepalive_idle, 30);
    seal_evt_send -> set(zmq::sockopt::tcp_keepalive_cnt, 5);
    seal_evt_send -> set(zmq::sockopt::tcp_keepalive_intvl, 6);

    seal_evt_send->bind(setting::get_instance()->get_setting().con_seal_ip);

    slot_evt_send = new zmq::socket_t {ctx, zmq::socket_type::pub};

    slot_evt_send -> set(zmq::sockopt::tcp_keepalive, 1);
    slot_evt_send -> set(zmq::sockopt::tcp_keepalive_idle, 30);
    slot_evt_send -> set(zmq::sockopt::tcp_keepalive_cnt, 5);
    slot_evt_send -> set(zmq::sockopt::tcp_keepalive_intvl, 6);

    slot_evt_send->bind(setting::get_instance()->get_setting().con_slot_ip);
    SPDLOG_DEBUG("container manager init done");
    return 0;
}

int container_manager::seal_evt_report(const container_seal_state_single &st)
{
    std::lock_guard<std::mutex> lk(seal_event_mtx);
    uint8_t pub_msg[container_seal_state_single_size];
	pb_ostream_t stream_out;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, container_seal_state_single_fields, &st))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
    

	seal_evt_send->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_DEBUG("send container seal state tcp {}-{}", st.container_id, st.seal_state);
    return 0;
}

int container_manager::slot_evt_report(const slot_state &st)
{
    std::lock_guard<std::mutex> lk(slot_event_mtx);
    uint8_t pub_msg[slot_state_size];
	pb_ostream_t stream_out;

    slot_state state;
    state.id = st.id;
    state.st = st.st;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, slot_state_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	slot_evt_send->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_DEBUG("send container slot state tcp {}-{}", state.id, state.st);
    return 0;
}
