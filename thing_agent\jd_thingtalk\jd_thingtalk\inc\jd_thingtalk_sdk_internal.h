/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 头文件，提供sdk内部使用的相关定义
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */
#ifndef __JD_THINGTALK_SDK_INTERNAL__
#define __JD_THINGTALK_SDK_INTERNAL__

#include "jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_mqtt.h"

#include "jd_thingtalk.h"
#include "jd_thingtalk_sdk.h"
#include "jd_thingtalk_protocol.h"

#ifdef __cplusplus
extern "C"{
#endif /* __cplusplus */

/**
 * @brief  sdk config 协议类型宏定义
 */
#define JD_THINGTALK_SDK_CFG_PROTO_MQTT_TCP   ("mqtt_tcp")
#define JD_THINGTALK_SDK_CFG_PROTO_MQTT_TLS   ("mqtt_tls")
#define JD_THINGTALK_SDK_CFG_PROTO_MQTT_ECC   ("mqtt_ecc")

/**
 * @brief  设备回调函数
 */

typedef struct {
    jd_thingtalk_sdk_callback_dev_connect    on_connect;
    jd_thingtalk_sdk_callback_dev_disconnect on_disconnect;
    jd_thingtalk_sdk_callback_dev_prop_set   on_prop_set;
    jd_thingtalk_sdk_callback_dev_prop_get   on_prop_get;
    jd_thingtalk_sdk_callback_dev_func_call  on_func_call;
    jd_thingtalk_sdk_callback_dev_reg_res    on_reg_res;
    jd_thingtalk_sdk_callback_dev_thmd_post_res   on_thmd_post_res;
    jd_thingtalk_sdk_callback_dev_ntp_req_res     on_ntp_req_res;

    jd_thingtalk_sdk_callback_agent_func_call     on_agent_func_call;

    jd_thingtalk_sdk_callback_dev_ota_func_call   on_ota_fun_call;

    jd_thingtalk_sdk_callback_sub_dev_prop_set    on_sub_prop_set;
    jd_thingtalk_sdk_callback_sub_dev_prop_get    on_sub_prop_get;
    jd_thingtalk_sdk_callback_sub_dev_func_call   on_sub_func_call;
} jd_thingtalk_sdk_dev_cb_t;

/**
 * @brief  认证信息
 */
typedef struct {
    char sessionkey[40];
} jd_thingtalk_sdk_auth_t;


/**
 * @brief  sdk 句柄的结构定义
 */
struct jd_thingtalk_sdk_t {
    jd_thingtalk_sdk_config_t *cfg;      // sdk 设置参数结构体指针
    jd_thingtalk_mqtt_t mqtt;                 // MQTT 句柄
    jd_thingtalk_mqtt_config_t mqtt_cfg;      // MQTT 设置参数结构体
    jd_thingtalk_sdk_dev_cb_t dev_cb;    // 设备回调处理函数
    jd_thingtalk_sdk_auth_t   auth;      // 预留 认证信息 
    // others
};

/**
 * @brief   sdk 消息发布函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_message_publish(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message);

/**
 * @brief   sdk 消息处理逻辑 连接回调函数
 *
 * @param[in] mqtt: mqtt_handler: MQTT 客户端句柄
 * @param[in] user_data: 用户数据
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_connect_callback(jd_thingtalk_mqtt_t mqtt, void *user_data);

/**
 * @brief   sdk 消息处理逻辑 连接断开回调函数
 *
 * @param[in] mqtt: mqtt_handler: MQTT 客户端句柄
 * @param[in] user_data: 用户数据
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_disconnect_callback(jd_thingtalk_mqtt_t mqtt, void *user_data);

/**
 * @brief   sdk 消息处理逻辑 消息回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_message_callback(jd_thingtalk_mqtt_t mqtt, jd_thingtalk_mqtt_msg_t *message, void *user_data);

/**
 * @brief   sdk 消息处理逻辑 订阅ACK回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] mid: MQTT 消息ID
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_subscribe_callback(jd_thingtalk_mqtt_t mqtt, int32_t mid, void *user_data);

/**
 * @brief   sdk 消息处理逻辑 发布ACK回调函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] mid: MQTT 消息ID
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_publish_callback(jd_thingtalk_mqtt_t mqtt, int32_t mid, void *user_data);

/**
 * @brief   属性设置主题 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_prop_set(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message, char *obj_name, char *service_key);

/**
 * @brief   属性获取主题 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_prop_get(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_mqtt_msg_t *message, char *obj_name, char *service_key);

/**
 * @brief   方法调用 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_func_call(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key);

/**
 * @brief   自动注册响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_reg_res(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key);

/**
 * @brief   物模型上报响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_thmd_post_res(struct jd_thingtalk_sdk_t *sdk, 
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key);

/**
 * @brief   设备NTP授时响应 消息处理函数
 *
 * @param[in] sdk: sdk 句柄
 * @param[in] message: MQTT 消息结构体指针
 * @param[in] obj_name: object name {"device", "edge", "group"}
 * @param[in] service_key: service key
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_msg_proc_ntp_req_res(struct jd_thingtalk_sdk_t *sdk,
         jd_thingtalk_mqtt_msg_t *message,
         char *obj_name,
         char *service_key);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* __JD_THINGTALK_SDK_INTERNAL__ */
