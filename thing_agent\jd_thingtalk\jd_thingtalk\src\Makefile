include ../../Makefile.rule

# Header and source files
HEADERS = $(wildcard *.h)
SOURCES = $(wildcard *.c) $(wildcard */*.c)
OBJS = $(patsubst %.c, %.o, $(SOURCES))

# Include path
INCLUDES += -I${PROJECT_ROOT_PATH}/jd_thingtalk/inc
INCLUDES += -I${PROJECT_ROOT_PATH}/pal/inc
INCLUDES += ${CJSON_INC_PATH}

# Export path for SDK library
EXPORT_SDK_LIB_PATH = ../lib/${PLATFORM}

ifeq (${ARCH}, x86)  
all:${OBJS} libso liba
else
all:${OBJS} liba 
endif


.SUFFIXES: .c .o
.c.o:
	${CC} ${CFLAGS} -c $(INCLUDES)  $*.c

liba:${OBJS}
	${AR} -crs lib${LIB_NAME_SDK}.a ${OBJS}
	mkdir -p ${EXPORT_SDK_LIB_PATH}
	${CP} lib${LIB_NAME_SDK}.a ${EXPORT_SDK_LIB_PATH}/lib${LIB_NAME_SDK}.a
	${MV} lib${LIB_NAME_SDK}.a ${TARGET_LIB}
	${MV} *.o ${TARGET_OBJ}

libso:${OBJS}
	${CC}  ${OBJS} -shared -fPIC -o lib${LIB_NAME_SDK}.so
	${MV} lib${LIB_NAME_SDK}.so ${TARGET_LIB}

clean:
	${RM} *.o *.so *.a

distclean:clean
	${RM} ${TARGET_LIB}/lib${LIB_NAME_SDK}.*
	${RM} ${EXPORT_SDK_LIB_PATH}

.PHONY:all clean distclean
