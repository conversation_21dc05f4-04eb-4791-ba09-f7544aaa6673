/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 方法(functions)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   方法(functions)消息主题 释放方法调用结构体 成员变量的内存空间
 *
 * @param[in] in_call: 方法调用结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_func_call(JDThingTalkProtoFuncCall_t *in_call)
{
    int32_t ii, jj;
    if (in_call != NULL) {
        if (in_call->deviceId != NULL) {
            jd_thingtalk_pal_free(in_call->deviceId);
            in_call->deviceId = NULL; 
        }
        if (in_call->messageId != NULL) {
            jd_thingtalk_pal_free(in_call->messageId);
            in_call->messageId = NULL; 
        }
        if (in_call->functions != NULL) {
            if (in_call->func_num != 0) {
                for (ii = 0; ii < in_call->func_num; ii++) {
                    if (in_call->functions[ii] != NULL) {
                        if (in_call->functions[ii]->key != NULL) {
                            jd_thingtalk_pal_free(in_call->functions[ii]->key);
                            in_call->functions[ii]->key = NULL;
                        }
                        if (in_call->functions[ii]->in != NULL) {
                            if (in_call->functions[ii]->in_num != 0) {
                                for (jj = 0; jj < in_call->functions[ii]->in_num; jj++) {
                                    jd_thingtalk_proto_free_key_value(in_call->functions[ii]->in[jj]);
                                }
                            }
                            jd_thingtalk_pal_free(in_call->functions[ii]->in);
                            in_call->functions[ii]->in = NULL;
                        }
                        jd_thingtalk_pal_free(in_call->functions[ii]);
                        in_call->functions[ii] = NULL;
                    }
                }
            }
            jd_thingtalk_pal_free(in_call->functions);
            in_call->functions = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
        if (in_call->func_json != NULL) {
            jd_thingtalk_pal_free(in_call->func_json);
            in_call->func_json = NULL;
        }
#endif
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   方法(functions)消息主题 释放方法调用响应结构体 成员变量的内存空间
 *
 * @param[in] in_call: 方法调用结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_func_call_res(JDThingTalkProtoFuncCallRes_t *in_res)
{
    int32_t ii, jj;
    if (in_res != NULL) {
        if (in_res->deviceId != NULL) {
            jd_thingtalk_pal_free(in_res->deviceId);
            in_res->deviceId = NULL; 
        }
        if (in_res->messageId != NULL) {
            jd_thingtalk_pal_free(in_res->messageId);
            in_res->messageId = NULL; 
        }
        if (in_res->message != NULL) {
            jd_thingtalk_pal_free(in_res->message);
            in_res->message = NULL;
        }
        if (in_res->functions != NULL) {
            if (in_res->func_num != 0) {
                for (ii = 0; ii < in_res->func_num; ii++) {
                    if (in_res->functions[ii] != NULL) {
                        if (in_res->functions[ii]->key != NULL) {
                            jd_thingtalk_pal_free(in_res->functions[ii]->key);
                            in_res->functions[ii]->key = NULL;
                        }
                        if (in_res->functions[ii]->out != NULL) {
                            if (in_res->functions[ii]->out_num != 0) {
                                for (jj = 0; jj < in_res->functions[ii]->out_num; jj++) {
                                    if (in_res->functions[ii]->out[jj] != NULL) {
                                        jd_thingtalk_proto_free_key_value(in_res->functions[ii]->out[jj]);
                                    }
                                }
                            }
                            jd_thingtalk_pal_free(in_res->functions[ii]->out);
                            in_res->functions[ii]->out = NULL;
                        }
                        if (in_res->functions[ii]->out_json != NULL) {
                            jd_thingtalk_pal_free(in_res->functions[ii]->out_json);
                            in_res->functions[ii]->out_json = NULL;
                        }
                        jd_thingtalk_pal_free(in_res->functions[ii]);
                        in_res->functions[ii] = NULL;
                    }
                }
            }
            jd_thingtalk_pal_free(in_res->functions);
            in_res->functions = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
        if (in_res->func_json != NULL) {
            jd_thingtalk_pal_free(in_res->func_json);
            in_res->func_json = NULL;
        }
#endif
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   方法(functions)消息主题 解析方法调用消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_call: 用于存解析结果的 方法调用的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_func_call(char *in_json, JDThingTalkProtoFuncCall_t *out_call)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_call) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }

    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_call->deviceId == NULL) {
        out_call->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_call->deviceId, pV->valuestring);

    // 解析 timestamp
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
#ifndef JD_THINGTALK_TIMESTAMP_MS
    out_call->timestamp = (TIMESTMAP_T)pV->valueint;
#else
    out_call->timestamp = (TIMESTMAP_T)pV->valuedouble;
#endif

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_call->messageId == NULL) {
        out_call->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_call->messageId, pV->valuestring);

    // 解析 functions
    pV = cJSON_GetObjectItem(payload, "functions");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }

#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
    out_call->func_json = cJSON_PrintUnformatted(pV);
#endif

    int32_t iSize, iCnt, ii;
    cJSON *pSub, *pKey, *pIn;
    iSize = cJSON_GetArraySize(pV);
    out_call->func_num = iSize;
    if (iSize != 0) {
        out_call->functions = (JDThingTalkProtoFuncCallFunc_t **) jd_thingtalk_pal_malloc(iSize * sizeof(JDThingTalkProtoFuncCallFunc_t *));
        jd_thingtalk_pal_memset(out_call->functions, 0, iSize * sizeof(JDThingTalkProtoFuncCallFunc_t *));
        for (iCnt = 0; iCnt < iSize; iCnt++) {
            pSub = cJSON_GetArrayItem(pV, iCnt);
            out_call->functions[iCnt] = (JDThingTalkProtoFuncCallFunc_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoFuncCallFunc_t));
            jd_thingtalk_pal_memset(out_call->functions[iCnt], 0, sizeof(JDThingTalkProtoFuncCallFunc_t));

            // 解析 functions 数组元素 key
            pKey = cJSON_GetObjectItem(pSub, "key");
            if (pKey == NULL) {
                continue;
            }
            out_call->functions[iCnt]->key = (char *) jd_thingtalk_pal_malloc(
                (jd_thingtalk_pal_strlen(pKey->valuestring) + 1) * sizeof(char));
            jd_thingtalk_pal_strcpy(out_call->functions[iCnt]->key, pKey->valuestring);

            // 解析 functions 数组元素 in
            pIn = cJSON_GetObjectItem(pSub, "in");
            if (pIn == NULL) {
                continue;
            }
            out_call->functions[iCnt]->in = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
                JD_THINGTALK_PROTO_FUNC_IN_MAX_NUM * sizeof(JDThingTalkProtoKeyValue_t *));
            jd_thingtalk_pal_memset(out_call->functions[iCnt]->in, 0, JD_THINGTALK_PROTO_FUNC_IN_MAX_NUM * sizeof(JDThingTalkProtoKeyValue_t *));
            pSub = pIn->child;
            ii = 0;
            while (pSub != NULL)
            {
                out_call->functions[iCnt]->in[ii] = jd_thingtalk_proto_parse_key_value(pSub);
                ii++;
                pSub = pSub->next;
            }
            out_call->functions[iCnt]->in_num = ii;
        }
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   方法(functions)消息主题 打包方法调用响应消息
 *
 * @param[in] in_res: 待打包的 方法调用响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_func_call_res(JDThingTalkProtoFuncCallRes_t *in_res)
{
    if(NULL == in_res) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_res->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_res->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_res->timestamp);

    // 添加 messageId
    if (in_res->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_res->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 code
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_CODE, in_res->code);

    // 添加 message
    if (in_res->message != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG, in_res->message);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG, "");
    }

    // 添加 functions
    int32_t ii, jj;
    cJSON *funArray = NULL;
    cJSON *funObj = NULL;
    cJSON *outArray = NULL;
    if ((in_res->func_num != 0) && (in_res->functions != NULL)) {
        funArray = cJSON_CreateArray();
        for (ii = 0; ii < in_res->func_num; ii++) {
            if (in_res->functions[ii] == NULL) {
                cJSON_AddItemToArray(funArray, NULL);
                continue;
            }
            funObj = cJSON_CreateObject();
            if (funObj == NULL) {
                cJSON_Delete(funArray);
                cJSON_Delete(root);
                goto RET;
            }

            // 添加 functions 数组元素 key
            cJSON_AddStringToObject(funObj, "key", in_res->functions[ii]->key);

            if (in_res->functions[ii]->out_num != 0) {
                if (in_res->functions[ii]->out == NULL) {
                    continue;
                }

                outArray = cJSON_CreateObject();
                if (outArray == NULL) {
                    cJSON_Delete(funObj);
                    cJSON_Delete(funArray);
                    cJSON_Delete(root);
                    goto RET;
                }

                // 添加 functions 数组元素 out 键值对
                for (jj = 0; jj < in_res->functions[ii]->out_num; jj++) {
                    if (in_res->functions[ii]->out[jj] != NULL) {
                        cJSON_AddItemToObject(outArray,
                                            in_res->functions[ii]->out[jj]->key,
                                            jd_thingtalk_proto_pack_key_value(in_res->functions[ii]->out[jj]));
                    }
                }

                // 添加 functions 数组元素 out
                cJSON_AddItemToObject(funObj, "out", outArray);
            }
            else {
                if (in_res->functions[ii]->out_json != NULL) {
                    cJSON_AddItemToObject(funObj, "out", cJSON_Parse(in_res->functions[ii]->out_json));
                }
            }

            // 添加元素到 functions 数组
            cJSON_AddItemToArray(funArray, funObj);
        }
        cJSON_AddItemToObject(root, "functions", funArray);
    } else {
#ifdef JD_THINGTALK_PROTO_FUNCS_JSON_STR
        if (in_res->func_json != NULL) {
            cJSON_AddItemToObject(root, "functions", cJSON_Parse(in_res->func_json));
        } else cJSON_AddStringToObject(root, "functions", "");
#else
        cJSON_AddStringToObject(root, "functions", "");
#endif

    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

// end of file
