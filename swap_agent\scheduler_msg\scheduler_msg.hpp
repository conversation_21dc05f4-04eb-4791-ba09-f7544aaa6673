#ifndef __SCHEDULER_MSG_SCHEDULER_MSG_HPP__
#define __SCHEDULER_MSG_SCHEDULER_MSG_HPP__

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include "threadpool/blocking_queue.hpp"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/auto_exchange_info.pb.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/auto_exchange_map.pb.h"
#include "share/pb/idl/auto_exchange.pb.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/ack.pb.h"
#include "swap_manage/swap_list.hpp"
#include "fsm_manager/fsm_manager.hpp"


#include <iostream>
#include "swap_agent_debug.h"

#define _IS_ALL_DEV_TASK(n)				(9999==n)

/**@enum __MSG_PUB_TYPE
* @brief 定义的需要PUB的消息类型，用来解析后续的数据是何种消息
*/
typedef enum
{
	SWAP_STATE_PUB = 0,
	SWAP_AGENT_STATE_PUB = 1,
	SWAP_LOAD_TASK_STATE_PUB = 2,
	SWAP_MOVE_TASK_STATE_PUB = 3,
	SWAP_EXCEP_PUB = 4,
	SWAP_TASK_OUT = 5,		//调度发过来的小车任务
	SWAP_RESET_PUB = 6,
	
}__MSG_PUB_TYPE;


/**@struct msg_queue
* @brief 定义的用来构成blocking queue的数据结构，使用枚举定义类型，用来指定后续data的解析
*/
typedef struct _msg_queue
{
	__MSG_PUB_TYPE type;	// 任务可执行对象
	int swap_id;
	int sub_id;
	uint8_t msg_data[auto_exchange_task_size];
}msg_queue;


class scheduler_manager
{

public:

	static scheduler_manager *get_instance(void)
	{
		static scheduler_manager instance;
		return &instance;
	}

    ~scheduler_manager();


    /**@brief	  scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  server创建成功
	*/
	bool scheduler_manager_init(zmq::context_t &ctx);

	
	/**@brief	  scheduler_manager 运行函数，创建线程并运行
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  server创建成功
	*/
	bool scheduler_manager_run(); 
	

	/**@brief	  blocking queue push函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue &data --- 待操作的数据
	* @return	  NULL
	*/
	void scheduler_manager_queue_push(msg_queue &data);


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue &data --- 待操作的数据
	* @return	  NULL
	*/
	void scheduler_manager_queue_pop(msg_queue &data);

	/**@brief	  车辆设备消息的发布，基于ZMQ 的PUB-SUB模式
	* @param[in]  auto_exchange_dev_state *dev_state --- 待操作的车辆状态数据结构体指针
	* @return	  操作结构
	* - true	  发布成功
	* - false	  发布失败
	*/
	bool scheduler_manager_swap_state_pub(auto_exchange_dev_state *dev_state);

	bool scheduler_manager_swap_agent_state_pub(auto_exchange_agent_state *agent_state);
	bool scheduler_manager_swap_grab_task_state_pub(auto_exchange_task_state *task_state);
	bool scheduler_manager_swap_move_task_state_pub(auto_exchange_task_state *task_state);

	/**@brief	  blocking queue push函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue *data --- 待操作的数据指针
	* @return	  NULL
	*/
	void scheduler_manager_task_msg_queue_push(msg_queue &data);



	/**@brief	  车辆设备异常消息的发布，基于ZMQ 的PUB-SUB模式
	* @param[in]  exception_info *info --- 待操作的车辆异常数据结构体指针
	* @return	  操作结构
	* - true	  发布成功
	* - false	  发布失败
	*/
	bool scheduler_manager_swap_exception_pub(event_exception *info); 

	
	/**@brief     从database获取当前的设备map信息
	* @param[out]  auto_exchange_map &swap_map --- 获取的map信息数据
	* @return     操作结构
	* - true      获取成功
	* - false     获取失败
	*/
	bool scheduler_manager_get_swap_map_info(auto_exchange_map &swap_map);


	/**@brief	  通过REQ-REP模式从coreserver获取数据
	* @param[out] auto_exchange_basic_info_mutilp *dev_list  --- 获取到的coreserver发布的设备列表
	* @return	  当前函数执行结果，用于判断 dev_list的有效性
	* - true	  成功
	* - false	  失败
	*/
	bool scheduler_manager_get_swap_info(auto_exchange_basic_info_mutilp &dev_list);



	/**@brief	  同scheduler通信的线程，基于REQ-REP模式，接收scheduler发送的车辆任务，并通过PUB-SUB模式发布消息
	* @param[in]  NULL
	* @return	  PB的执行结果(其实没用到)
	* - true	  成功
	* - false	  失败
	*/
	bool scheduler_manager_thread_task_reply(void);

	void scheduler_manager_thead_sendmsg();


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue *data --- 待操作的数据指针
	* @return	  NULL
	*/
	void scheduler_manager_task_msg_queue_pop(msg_queue *data);
	bool scheduler_manager_task_msg_queue_empty(void);
	int scheduler_manager_task_msg_queue_size(void);


private:

    zmq::socket_t *m_swap_state_publisher;          ///自动取换箱状态socket PUB 类型
    zmq::socket_t *m_swap_agent_state_publisher;  ///自动取换箱代理状态socket PUB 类型
	zmq::socket_t *m_swap_task_move_state_publisher;  ///<移动任务状态socket PUB 类型
	zmq::socket_t *m_swap_grab_task_state_publisher;  ///<取/卸货任务状态socket PUB 类型
	zmq::socket_t *m_swap_excep_publisher;  	///< 车辆异常socket PUB 类型

	zmq::socket_t *m_data_requester;             ///< coreserver通信socket REQ 类型
	zmq::socket_t *m_task_replayer;   			///< scheduler消息响应socket REP 类型

	blocking_queue<msg_queue> m_scheduler_msg_queue_ptr;
	
	blocking_queue<msg_queue> m_scheduler_task_msg;
	
	std::mutex m_train_basic_info_mtx;	           	// train_basic_info获取互斥锁

};







#endif  





