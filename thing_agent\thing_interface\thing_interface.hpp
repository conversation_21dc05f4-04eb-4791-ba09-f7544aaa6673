#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>
#include <stdio.h>
#include <memory>
#include <queue>
#include <condition_variable>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "../jd_thingtalk/cJSON/cJSON.h"
#include "../jd_thingtalk/jd_thingtalk/inc/jd_thingtalk.h"
#include "../jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_protocol.h"
#include "../jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_proto_internal.h"
#include "../jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk.h"
#include "../jd_thingtalk/jd_thingtalk/inc/jd_thingtalk_sdk_internal.h"

#include "../jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"
#include "../jd_thingtalk/pal/inc/jd_thingtalk_time.h"
#include "../jd_thingtalk/pal/inc/jd_thingtalk_string.h"
#include "../jd_thingtalk/pal/inc/jd_thingtalk_memory.h"
#include "../jd_thingtalk/pal/inc/jd_thingtalk_log.h"
#include "../jd_thingtalk/pal/inc/jd_thingtalk_thread.h"

#include "converter/converter.hpp"

class thing_interface
{
public:

    int init(jd_thingtalk_sdk_t *sdk);

    int run();

    bool is_connect_done()
    {
        return (suresort_device.is_connected && suresort_device.is_model_post_done);
    }

    struct device
    {
        struct jd_thingtalk_sdk_t *sdk;
        bool is_connected = false;
        bool is_model_post_done = false;
        uint16_t prop_version;
        std::string sdk_device_id;
        std::string thing_model_id;
        std::string version;
    };

    static int32_t suresort_device_callback_connect(struct jd_thingtalk_sdk_t *sdk);

    static int32_t suresort_device_callback_disconnect(struct jd_thingtalk_sdk_t *sdk);

    static int32_t suresort_device_callback_thingmodel_post_response(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoThingModelPostRes_t *in_res);

    static int32_t suresort_device_callback_property_set(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoPropSet_t *in_set);

    static int32_t suresort_device_callback_property_get(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoPropGet_t *in_get);

    static int32_t suresort_device_callback_function_call(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call);

    static int32_t suresort_device_callback_register_response(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoRegReqRes_t *in_res);

    static int32_t suresort_device_callback_ntp_response(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_time_stamp_t *set_time);

    static int32_t device_thing_model_post(struct jd_thingtalk_sdk_t *sdk);

    static thing_interface *get_instance(void)
    {
        static thing_interface instance;
        return &instance;
    }

    void event_malloc(JDThingTalkProtoEvtPostEvt_t **event, const char *event_key, int32_t param_num);       //为保持对称性，定义在此

    int send_event(JDThingTalkProtoEvtPostEvt_t &event);

    void function_sync_response_malloc(JDThingTalkProtoFuncCallResFunc_t **response, const char *response_key, int32_t out_num);
    int function_sync_response_add_to_list(JDThingTalkProtoFuncCallResFunc_t &response);
private:

    int func_construct(JDThingTalkProtoFuncCallFunc_t *func, const nlohmann::json &root);
    int func_deconstruct(JDThingTalkProtoFuncCallFunc_t &func);
    void zmq_func_accept();

//方法相关
    void function_response_ack(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call);
    int function_check_validity(JDThingTalkProtoFuncCall_t *in_call);
    int function_avoid_duplication(JDThingTalkProtoFuncCall_t *in_call, std::vector<int> &devs);
    int string_transfer(const char *in, std::string &value);

//资源释放
    int event_free(JDThingTalkProtoEvtPost_t *in_post);
    int function_free(JDThingTalkProtoFuncCallRes_t *in_res);
    int free_evt_post(JDThingTalkProtoEvtPostEvt_t *event);
    int free_fun_res(JDThingTalkProtoFuncCallResFunc_t *function);

//通用成员
    struct timer
    {
        std::chrono::high_resolution_clock::time_point start_time;
        std::chrono::high_resolution_clock::time_point end_time;

        void start()
        {
            start_time = std::chrono::high_resolution_clock::now();
        }

        int execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

    int64_t get_current_local_timestamp()
    {
        std::chrono::time_point<std::chrono::system_clock, std::chrono::milliseconds> tp = std::chrono::time_point_cast<std::chrono::milliseconds>(std::chrono::system_clock::now());
        auto tmp = std::chrono::duration_cast<std::chrono::milliseconds>(tp.time_since_epoch());
        return tmp.count();
    }

//防重相关定义
    struct latest_info
    {
        int64_t timestamp = 0;
        int dev;

        bool operator==(const latest_info d) const
        {
            return (this->dev == d.dev);
    	}
    };

    struct latest_function_info
    {
        std::string function_key;
        bool dev_unique;        //方法是否关联单一设备标志位
        latest_info info;       //方法只关联单一设备时使用
        std::list<latest_info> dev_info_list;       //方法只关联多个设备时使用
    };

    struct function_sync_info
    {
        std::string function_key;
        bool ack_nowait;        //方法是否关联单一设备标志位
    };

    std::list<latest_function_info> avoid_repetition_function_list;
    std::list<function_sync_info> function_sync_list;
    int creat_avoid_repetition_list();
    void function_info_init(std::string function_key, bool dev_unique);
    latest_function_info* get_function_info(std::string key);
    int get_function_call_dev(int in_num, JDThingTalkProtoKeyValue_t **in, std::list<int> &dev_list);
    latest_info *get_latest_info(std::list<latest_info> &list, int dev);

    void function_sync_init(std::string function_key, bool ack_nowait);
    int creat_synchronous_function_list();  //同步方法列表
    int function_check_async(JDThingTalkProtoFuncCall_t *in_call);
    int function_check_response_nowait(JDThingTalkProtoFuncCall_t *in_call);
    int function_sync_response_handle(struct jd_thingtalk_sdk_t *sdk,char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call);
    int function_id_free(JDThingTalkProtoFuncCallRes_t *in_res);
 //   void function_sync_response_malloc(JDThingTalkProtoFuncCallResFunc_t **response, const char *response_key, int32_t out_num);
//事件重发相关
    struct event_without_ack
    {
        JDThingTalkProtoEvtPost_t event_post;
        timer report_timer;
    };
//同步方法相关
    struct function_need_ack
    {
        bool ack_nowait;        //方法是否关联等待执行结果还是立即查询系统状态回复
        JDThingTalkProtoFuncCallRes_t out_res;
        timer report_timer;
        bool is_ack;
        int set_value;
    };
    std::list<event_without_ack> events_waiting_ack_list;           //用于未回ack的事件重新定时发送
    int remove_event_with_ack(std::string message_id);
    std::mutex events_without_ack_lock;
    std::list<function_need_ack> function_waiting_ack_list;           //用于同步方法 等待执行结果并上发
    std::mutex function_waiting_ack_lock;   
    std::thread *retry_event_report;
    std::thread *model_post;
    std::thread *function_sync_handle;
    void retry_event_report_thread(void);
    void thing_model_post_thread(void);
    void function_sync_ack_thread(void);
    device suresort_device;
};
