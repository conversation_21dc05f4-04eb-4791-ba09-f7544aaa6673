﻿#ifndef __FSM_MANAGER_HPP__
#define __FSM_MANAGER_HPP__

#include "../threadpool/blocking_queue.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/sys_interface.pb.h"


#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <mutex>
#include <thread>

using namespace std;


class fsm_manager
{
public:


	static fsm_manager *get_instance(void)
    {
        static fsm_manager instance;
        return &instance;
    }

	/**@brief  fsm_manager class析构函数
	 * @param[in]  NULL
	 * @return	  NULL
	 */
	~fsm_manager();

	void fsm_manager_init(zmq::context_t &ctx) ;

	void fsm_manager_run(void);

	void fsm_manager_main_thread(void);

	bool fsm_manager_get_fsm_state(void);

	sys_mode_state fsm_manager_get_sys_state(void);


private:

	zmq::socket_t *m_dev_fsm_subscriber;
		
	std::thread *m_dev_state_get_thread;

	std::mutex m_dev_sys_state_mutex;
	sys_mode_state m_dev_sys_state;


};



#endif