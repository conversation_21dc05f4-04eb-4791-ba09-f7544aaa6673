/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 事件(events)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"


/**
 * @brief   事件(events)消息主题 释放上下线的结构体 成员变量的内存空间
 *
 * @param[in] in_status: 上线下结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_evt_online_status(JDThingTalkProtoEvtOnlineStatus_t *in_status)
{
    if (in_status != NULL) {
        if (in_status->deviceId != NULL) {
            jd_thingtalk_pal_free(in_status->deviceId);
            in_status->deviceId = NULL; 
        }
        if (in_status->messageId != NULL) {
            jd_thingtalk_pal_free(in_status->messageId);
            in_status->messageId = NULL; 
        }
        if (in_status->thing_model.id != NULL) {
            jd_thingtalk_pal_free(in_status->thing_model.id);
            in_status->thing_model.id = NULL;
        }
        if (in_status->thing_model.version != NULL) {
            jd_thingtalk_pal_free(in_status->thing_model.version);
            in_status->thing_model.version = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   事件(events)消息主题 打包上下线消息
 *
 * @param[in] in_status: 带打包的上线下结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_evt_online_status(JDThingTalkProtoEvtOnlineStatus_t *in_status)
{
    if(NULL == in_status) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_status->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_status->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_status->timestamp);

    // 添加 messageId
    if (in_status->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_status->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 event
    if (in_status->is_online) {
        cJSON_AddStringToObject(root, "event", "connected");
    } else {
        cJSON_AddStringToObject(root, "event", "disconnected");
    }

    // 添加 thing-model
    cJSON *th_model = cJSON_CreateObject();
    if (th_model == NULL) {
        cJSON_Delete(root);
        goto RET;
    }
    if (in_status->thing_model.id != NULL) {
        cJSON_AddStringToObject(th_model, "id", in_status->thing_model.id);
    } else {
        cJSON_AddStringToObject(th_model, "id", "");
    }    

    if (in_status->thing_model.version != NULL) {
        cJSON_AddStringToObject(th_model, "thing-model-version", in_status->thing_model.version);
    } else {
        cJSON_AddStringToObject(th_model, "thing-model-version", "");
    }

    cJSON_AddItemToObject(root, "thing-model", th_model);

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   事件(events)消息主题 释放事件上报结构体 成员变量的内存空间
 *
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_evt_post(JDThingTalkProtoEvtPost_t *in_post)
{
    int ii, jj;
    if (in_post != NULL) {
        if (in_post->deviceId != NULL) {
            jd_thingtalk_pal_free(in_post->deviceId);
            in_post->deviceId = NULL; 
        }
        if (in_post->messageId != NULL) {
            jd_thingtalk_pal_free(in_post->messageId);
            in_post->messageId = NULL; 
        }
        if (in_post->events != NULL) {
            for (ii = 0; ii < in_post->evt_num; ii++) {
                if (in_post->events[ii] != NULL) {
                    if (in_post->events[ii]->key != NULL) {
                        jd_thingtalk_pal_free(in_post->events[ii]->key);
                        in_post->events[ii]->key = NULL;
                    }
                    for (jj = 0; jj < in_post->events[ii]->param_num; jj++) {
                        if (in_post->events[ii]->parameters[jj] != NULL) {
                            jd_thingtalk_proto_free_key_value(in_post->events[ii]->parameters[jj]);
                        }
                    }
#ifdef JD_THINGTALK_PROTO_EVENT_PARAMS_JSON_STR
                    if (in_post->events[ii]->param_json != NULL) {
                        jd_thingtalk_pal_free(in_post->events[ii]->param_json);
                        in_post->events[ii]->param_json = NULL;
                    }
#endif
                    in_post->events[ii] = NULL;
                }
            }
            jd_thingtalk_pal_free(in_post->events);
            in_post->events = NULL; 
        }
#ifdef JD_THINGTALK_PROTO_EVENTS_JSON_STR
        if (in_post->evt_json != NULL) {
            jd_thingtalk_pal_free(in_post->evt_json);
            in_post->evt_json = NULL;
        }
#endif
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   事件(events)消息主题 打包事件上报消息
 *
 * @param[in] in_post: 带打包的事件上报结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_evt_post(JDThingTalkProtoEvtPost_t *in_post)
{
    if(NULL == in_post) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_post->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_post->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_post->timestamp);

    // 添加 messageId
    if (in_post->messageId) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_post->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 events
    int32_t ii, jj;
    cJSON *evtArray = NULL;
    cJSON *evtObj = NULL;
    cJSON *paraArray = NULL;
    if (in_post->evt_num != 0) {
        evtArray = cJSON_CreateArray();
        for (ii = 0; ii < in_post->evt_num; ii++) {
            evtObj = cJSON_CreateObject();
            if (evtObj == NULL) {
                cJSON_Delete(evtArray);
                cJSON_Delete(root);
                goto RET;
            }

            // 添加 events 数组元素的 key
            cJSON_AddStringToObject(evtObj, "key", in_post->events[ii]->key);
            if (in_post->events[ii]->param_num != 0) {
                if (in_post->events[ii]->parameters == NULL) {
                    continue;
                }
                paraArray = cJSON_CreateObject();
                if (paraArray == NULL) {
                    cJSON_Delete(evtObj);
                    cJSON_Delete(evtArray);
                    cJSON_Delete(root);
                    goto RET;
                }

                // 添加 events 数组元素的 out 中的 键值对
                for (jj = 0; jj < in_post->events[ii]->param_num; jj++) {
                    if (in_post->events[ii]->parameters != NULL) {
                        cJSON_AddItemToObject(paraArray,
                                             in_post->events[ii]->parameters[jj]->key,
                                             jd_thingtalk_proto_pack_key_value(in_post->events[ii]->parameters[jj]));
                    }
                }

                // 添加 events 数组元素的 parameters
                cJSON_AddItemToObject(evtObj, "parameters", paraArray);
            }
#ifdef JD_THINGTALK_PROTO_EVENT_PARAMS_JSON_STR
            else {
                if (in_post->events[ii]->param_json != NULL) {
                    cJSON_AddItemToObject(evtObj, "parameters", cJSON_Parse(in_post->events[ii]->param_json));
                }
            }
#endif

            // 添加元素到 events 数组
            cJSON_AddItemToArray(evtArray, evtObj);
        }
        cJSON_AddItemToObject(root, "events", evtArray);
    } else {
#ifdef JD_THINGTALK_PROTO_EVENTS_JSON_STR
        if (in_post->evt_json != NULL) {
            cJSON_AddItemToObject(root, "events", cJSON_Parse(in_post->evt_json));
        } else cJSON_AddStringToObject(root, "events", "");
#else
        cJSON_AddStringToObject(root, "events", "");
#endif
    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

// end of file
