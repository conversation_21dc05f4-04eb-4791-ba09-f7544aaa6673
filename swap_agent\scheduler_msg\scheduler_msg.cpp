#include "scheduler_msg.hpp"
#include "share/global_def.h"
#include "swap_agent_debug.h"
#include "swap_manage/swap_list.hpp"
#include <arpa/inet.h>
#include <zmq.h>

#include <spdlog/logger.h>
#include <spdlog/spdlog.h> 
#include "share/exception_code.hpp"
#include "fsm_manager/fsm_manager.hpp"


/**@brief     scheduler_manager class析构函数
* @param[in]  NULL
* @return     NULL
*/
scheduler_manager::~scheduler_manager() 
{

}


#if 1
bool scheduler_manager::scheduler_manager_get_swap_info(auto_exchange_basic_info_mutilp &dev_list) 
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	// 首先检查当前socket的连接状态
	if( !socket.connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
		return false;
	}
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_AUTO_EXCHANGE_LIST, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	socket.recv(zmq_reply, zmq::recv_flags::none);	
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train list from coreserver.");
		return false;
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, auto_exchange_basic_info_mutilp_fields, &dev_list))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train list"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	return true;			
}

bool scheduler_manager::scheduler_manager_get_swap_map_info(auto_exchange_map &swap_map) 
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;

	zmq::message_t zmq_reply;
	pb_istream_t stream_in;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	if( !socket.connected() )
	{
		SPDLOG_ERROR("m_data_requester is unconnected");
		return false;
	}
	
	strncpy(request.key, DATA_KEY_AUTO_EXCHANGE_MAP, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("swap_map"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
		
	socket.recv(zmq_reply, zmq::recv_flags::none);
	
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get data from auto_exchange_map.");
		return false;
	}

	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, auto_exchange_map_fields, &swap_map))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("swap_map"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	return true;
}
#endif


#if 0
/**@brief     从database获取当前的设备map信息
* @param[out]  data_map *map --- 获取的map信息数据
* @return     操作结构
* - true      获取成功
* - false     获取失败
*/
bool scheduler_manager::scheduler_manager_get_data_map_info(data_map *map) 
{
	uint8_t req_msg[1024];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	std::lock_guard<std::mutex> opt_lock(m_train_basic_info_mtx);

	if( !m_data_requester.connected() )
	{
		SPDLOG_ERROR("m_data_requester is unconnected");
		return false;
	}
	

	strncpy(request.key, DATA_KEY_MAP, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("map"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		m_data_requester.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
		
	m_data_requester.recv(zmq_reply, zmq::recv_flags::none);
	
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get data from data_map.");
		return false;
	}

	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, data_map_fields, map))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("map"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	return true;
}


/**@brief	  通过REQ-REP模式从coreserver获取数据
* @param[out] train_basic_info_mutilp *dev_list  --- 获取到的coreserver发布的设备列表
* @return	  当前函数执行结果，用于判断 dev_list的有效性
* - true	  成功
* - false	  失败
*/
bool scheduler_manager::scheduler_manager_get_swap_info(train_basic_info_mutilp &dev_list) 
{
	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	std::lock_guard<std::mutex> opt_lock(m_train_basic_info_mtx);


	// 首先检查当前socket的连接状态
	if( !m_data_requester.connected() )
	{
		SPDLOG_ERROR("m_data_requester zmq unconected");
		return false;
	}
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_TRAIN_LIST, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
		m_data_requester.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	m_data_requester.recv(zmq_reply, zmq::recv_flags::none);		
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train list from coreserver.");
		return false;
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, train_basic_info_mutilp_fields, &dev_list))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train list"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	return true;
}
#endif


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询队列的空满状态
* @param[in]  msg_queue &data --- 待操作的数据
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool scheduler_manager::scheduler_manager_task_msg_queue_empty(void)
{
	return m_scheduler_task_msg.empty();
}

int scheduler_manager::scheduler_manager_task_msg_queue_size(void)
{
	return m_scheduler_task_msg.size();
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[in]  msg_queue *data --- 待操作的数据指针
* @return     NULL
*/
void scheduler_manager::scheduler_manager_task_msg_queue_pop(msg_queue *data)
{
	msg_queue temp;

	m_scheduler_task_msg.pop(temp);

	memcpy(data, &temp, sizeof(temp));
}


/**@brief     同scheduler通信的线程，基于REQ-REP模式，接收scheduler发送的车辆任务，并通过PUB-SUB模式发布消息
* @param[in]  NULL
* @return     PB的执行结果(其实没用到)
* - true      成功
* - false     失败
*/
bool scheduler_manager::scheduler_manager_thread_task_reply(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	auto_exchange_task task;
	ack task_rep;
	uint8_t rep_msg[64];
	pb_ostream_t stream_out;

	msg_queue temp;

	zmq::recv_result_t result;


	while(1)
	{
		// 查询当前的socket是否接收到消息，若无消息收到，直接执行下一步
		if(m_task_replayer->recv(msg, zmq::recv_flags::none))
		{
			// 收到REQ消息，首先解码，并构造REP消息

#ifdef 	TRAIN_ZMQ_LOG
			SPDLOG_INFO("receive task msg(pb) ");
#endif
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, auto_exchange_task_fields, &task))
			{

#ifdef SCHEDULER_MSG_DEBUG
				OUTPUT_ERR;
				std::cout << "log>>: msg pb decode failed\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
				SPDLOG_ERROR(" msg pb decode fail fail");
#endif
			}
			else
			{
				//成功反序列化解码成功，构造REP消息
				task_rep.sequence = task.sequence;

				// 将收到的任务消息释放出去
				temp.swap_id = task.dev_id;
				temp.sub_id = task.sub_dev_id;
				temp.type = SWAP_TASK_OUT;
				memcpy(temp.msg_data, (uint8_t *)(&task), sizeof(task) );
			
				SPDLOG_INFO("software log swap_id:{}, sub_id:{}, which_task:{}", temp.swap_id, temp.sub_id, task.which_task);

				scheduler_manager_task_msg_queue_push(temp);


				//构造REP消息并发送
				stream_out = pb_ostream_from_buffer(rep_msg, sizeof(rep_msg));
				if (!pb_encode(&stream_out, ack_fields, &task_rep))
				{		

#ifdef 	TRAIN_ZMQ_LOG
					SPDLOG_ERROR(" msg pb_encode fail fail");
#endif
					return false;
				}
				else
				{
					try
					{
						m_task_replayer->send(zmq::buffer(rep_msg, stream_out.bytes_written), zmq::send_flags::none);
					}
					catch(zmq::error_t &fe)
					{
						SPDLOG_ERROR("[ZMQ] m_task_replayer zmq send throw err :{}", fe.what());
					}
					catch(const std::exception& e)
					{
						SPDLOG_ERROR("[ZMQ] m_task_replayer zmq send system err :{}", e.what());
					}
 					catch (...)
    				{
        				SPDLOG_ERROR("[ZMQ] m_task_replayer unknown error ");
					}
					
#ifdef 	TRAIN_ZMQ_LOG
					SPDLOG_INFO("send ack msg to scheduler for task");
#endif
				}

			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(1));
	}
}


/**@brief     blocking queue push函数的二次封装，避免直接访问成员变量
* @param[in]  msg_queue &data --- 待操作的数据
* @return     NULL
*/
void scheduler_manager::scheduler_manager_queue_push(msg_queue &data)
{
    m_scheduler_msg_queue_ptr.push(data);
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[in]  msg_queue &data --- 待操作的数据
* @return     NULL
*/
void scheduler_manager::scheduler_manager_queue_pop(msg_queue &data)
{
    m_scheduler_msg_queue_ptr.pop(data);
}

/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[in]  msg_queue *data --- 待操作的数据指针
* @return     NULL
*/
void scheduler_manager::scheduler_manager_task_msg_queue_push(msg_queue &data)
{	
	m_scheduler_task_msg.push(data);
}


/**@brief     车辆设备消息的发布，基于ZMQ 的PUB-SUB模式
* @param[in]  auto_exchange_dev_state *dev_state --- 待操作的车辆状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_swap_state_pub(auto_exchange_dev_state *dev_state) 
{
	uint8_t state_msg[auto_exchange_dev_state_size];
	pb_ostream_t stream_out;
	zmq::send_result_t send_result_temp;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if( 0x00 != stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR("msg init fail");
#endif
		return false;
	}
	
	if (!pb_encode(&stream_out, auto_exchange_dev_state_fields, dev_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" pb_encode fail");
#endif
	
		return false;
	}
	else
	{
		try
		{
			send_result_temp = m_swap_state_publisher->send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
		}
		catch(zmq::error_t &fe)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_state_publisher zmq send throw err :{}", fe.what());
		}
		catch(const std::exception& e)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_state_publisher zmq send system err :{}", e.what());
		}
 		catch (...)
    	{
        	SPDLOG_ERROR("[ZMQ] m_swap_state_publisher unknown error ");
		}
		
	}

#ifdef 	TRAIN_ZMQ_LOG
	SPDLOG_INFO("msg m_swap_state_publisher send swap_id:{}, curr_state:{}, work_state:{}, has_value:{}, value:{} ", dev_state->dev_id, dev_state->curr_state, dev_state->work_state, send_result_temp.has_value(), send_result_temp.value() );
#endif

	return true;
}


bool scheduler_manager::scheduler_manager_swap_agent_state_pub(auto_exchange_agent_state *agent_state)
{
	uint8_t state_msg[auto_exchange_agent_state_size];
	pb_ostream_t stream_out;
	zmq::send_result_t send_result_temp;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if( 0x00 != stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR("msg init fail");
#endif
		return false;
	}
	
	if (!pb_encode(&stream_out, auto_exchange_agent_state_fields, agent_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" pb_encode fail");
#endif
	
		return false;
	}
	else
	{
		try
		{
			send_result_temp = m_swap_agent_state_publisher->send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
		}
		catch(zmq::error_t &fe)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_agent_state_publisher zmq send throw err :{}", fe.what());
		}
		catch(const std::exception& e)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_agent_state_publisher zmq send system err :{}", e.what());
		}
 		catch (...)
    	{
        	SPDLOG_ERROR("[ZMQ] m_swap_agent_state_publisher unknown error ");
		}
		
	}

#ifdef 	TRAIN_ZMQ_LOG
	SPDLOG_INFO("msg m_swap_agent_state_publisher send dev_cnt:{}, valid_dev_cnt:{}, state:{}, has_value:{}, value:{} ", agent_state->auto_exchange_dev_cnt, agent_state->auto_exchange_curr_valid_dev_cnt, agent_state->agent_work_state, send_result_temp.has_value(), send_result_temp.value() );
#endif

	return true;	
}

bool scheduler_manager::scheduler_manager_swap_grab_task_state_pub(auto_exchange_task_state *task_state)
{
	uint8_t state_msg[auto_exchange_task_state_size];
	pb_ostream_t stream_out;
	zmq::send_result_t send_result_temp;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if( 0x00 != stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR("msg init fail");
#endif
		return false;
	}
	
	if (!pb_encode(&stream_out, auto_exchange_task_state_fields, task_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" pb_encode fail");
#endif
	
		return false;
	}
	else
	{
		try
		{
			send_result_temp = m_swap_grab_task_state_publisher->send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
		}
		catch(zmq::error_t &fe)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_grab_task_state_publisher zmq send throw err :{}", fe.what());
		}
		catch(const std::exception& e)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_grab_task_state_publisher zmq send system err :{}", e.what());
		}
 		catch (...)
    	{
        	SPDLOG_ERROR("[ZMQ] m_swap_grab_task_state_publisher unknown error ");
		}
		
	}

#ifdef 	TRAIN_ZMQ_LOG
	SPDLOG_INFO("msg m_swap_grab_task_state_publisher send devid:{}, sub_id:{}, state:{}, type:{}, has_value:{}, value:{} ", task_state->dev_id, task_state->sub_dev_id, task_state->state, task_state->type, send_result_temp.has_value(), send_result_temp.value() );
#endif

	return true;
}

bool scheduler_manager::scheduler_manager_swap_move_task_state_pub(auto_exchange_task_state *task_state)
{
	uint8_t state_msg[auto_exchange_task_state_size];
	pb_ostream_t stream_out;
	zmq::send_result_t send_result_temp;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if( 0x00 != stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR("msg init fail");
#endif
		return false;
	}
	
	if (!pb_encode(&stream_out, auto_exchange_task_state_fields, task_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" pb_encode fail");
#endif
	
		return false;
	}
	else
	{
		try
		{
			send_result_temp = m_swap_task_move_state_publisher->send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
		}
		catch(zmq::error_t &fe)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_task_move_state_publisher zmq send throw err :{}", fe.what());
		}
		catch(const std::exception& e)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_task_move_state_publisher zmq send system err :{}", e.what());
		}
 		catch (...)
    	{
        	SPDLOG_ERROR("[ZMQ] m_swap_task_move_state_publisher unknown error ");
		}
		
	}

#ifdef 	TRAIN_ZMQ_LOG
	SPDLOG_INFO("msg m_swap_task_move_state_publisher send devid:{}, sub_id:{}, state:{}, type:{}, has_value:{}, value:{} ", task_state->dev_id, task_state->sub_dev_id, task_state->state, task_state->type, send_result_temp.has_value(), send_result_temp.value() );
#endif

	return true;
}


/**@brief     车辆设备异常消息的发布，基于ZMQ 的PUB-SUB模式
* @param[in]  exception_info *info --- 待操作的车辆异常数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_swap_exception_pub(event_exception *info) 
{

	if(event_exception_evt_tag == info->which_evt_except)
		SPDLOG_INFO("scheduler_manager_swap_exception_pub evt, src:{}, dev:{}, sub_dev:{}, code:{}, sub_code:{}", info->evt_except.evt.src, info->evt_except.evt.dev, info->evt_except.evt.sub_dev, info->evt_except.evt.code, info->evt_except.evt.sub_code);
	else if(event_exception_except_tag == info->which_evt_except)
		SPDLOG_INFO("scheduler_manager_swap_exception_pub except, src:{}, lev:{}, code:{}, sub_code:{}, dev:{}, sub_dev:{}, state:{}", info->evt_except.except.src, info->evt_except.except.level, info->evt_except.except.code, info->evt_except.except.sub_code, info->evt_except.except.dev, info->evt_except.except.sub_dev, info->evt_except.except.state);
	

	uint8_t excep_msg[event_exception_size];
	pb_ostream_t stream_out;

	stream_out = pb_ostream_from_buffer(excep_msg, sizeof(excep_msg));

	if( 0x00 != stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif
#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" msg init fail ");
#endif
		return false;
	}

	if (!pb_encode(&stream_out, event_exception_fields, info))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
#ifdef 	TRAIN_ZMQ_LOG
		SPDLOG_ERROR(" pb_encode fail");
#endif

		return false;
	}
	else
	{
		try
		{
			m_swap_excep_publisher->send(zmq::buffer(excep_msg, stream_out.bytes_written), zmq::send_flags::none);
		}
		catch(zmq::error_t &fe)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_excep_publisher zmq send throw err :{}", fe.what());
		}
		catch(const std::exception& e)
		{
			SPDLOG_ERROR("[ZMQ] m_swap_excep_publisher zmq send system err :{}", e.what());
		}
 		catch (...)
    	{
        	SPDLOG_ERROR("[ZMQ] m_swap_excep_publisher unknown error ");
		}
	
	}
	
#ifdef 	TRAIN_ZMQ_LOG
	if( event_exception_except_tag == info->which_evt_except )
	{
		SPDLOG_INFO("msg train exception send devid:{}",  info->evt_except.except.dev);
	}
	else
	{
		SPDLOG_INFO("msg train event send devid:{}",  info->evt_except.evt.dev);
	}
#endif
	
	return true;	
}



//异步发送消息
void scheduler_manager::scheduler_manager_thead_sendmsg()
{
	msg_queue temp;

	// 处理需要publish出去的消息 if (!m_scheduler_msg_queue_ptr.empty())
	while(true)
	{
		scheduler_manager_queue_pop(temp);

		int size = m_scheduler_msg_queue_ptr.size();

#ifdef TRAIN_ZMQ_LOG
		SPDLOG_INFO("need publish msg type:{} total:{}", temp.type, (size+1));
#endif

		switch(temp.type)
		{
			case SWAP_STATE_PUB:
				scheduler_manager_swap_state_pub((auto_exchange_dev_state *)(temp.msg_data));
				break;

			case SWAP_AGENT_STATE_PUB:
				scheduler_manager_swap_agent_state_pub((auto_exchange_agent_state *)(temp.msg_data));
				break;


			case SWAP_LOAD_TASK_STATE_PUB:
				scheduler_manager_swap_grab_task_state_pub((auto_exchange_task_state *)(temp.msg_data));
				break;

			case SWAP_MOVE_TASK_STATE_PUB:
				scheduler_manager_swap_move_task_state_pub((auto_exchange_task_state *)(temp.msg_data));
				break;

			case SWAP_RESET_PUB:
				scheduler_manager_swap_exception_pub((event_exception *)(temp.msg_data));
				std::this_thread::sleep_for(std::chrono::milliseconds(2));
				break;

			case SWAP_EXCEP_PUB:
				scheduler_manager_swap_exception_pub((event_exception *)(temp.msg_data));
				std::this_thread::sleep_for(std::chrono::milliseconds(2));
				break;
				
			default:
				break;
		}

		std::this_thread::sleep_for(std::chrono::microseconds(2));
	}
}





/**@brief     scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool scheduler_manager::scheduler_manager_init(zmq::context_t &ctx) 
{
	m_swap_state_publisher = new zmq::socket_t {ctx, zmq::socket_type::pub};
	m_swap_state_publisher -> bind(TOPIC_AUTO_EXCHANGE_STATE);

	m_swap_agent_state_publisher = new zmq::socket_t {ctx, zmq::socket_type::pub};
	m_swap_agent_state_publisher -> bind(TOPIC_AUTO_EXCHANGE_AGENT_STATE);

	m_swap_task_move_state_publisher = new zmq::socket_t {ctx, zmq::socket_type::pub};
	m_swap_task_move_state_publisher->bind(TOPIC_AUTO_EXCHANGE_TASK_STATE);

	m_swap_grab_task_state_publisher = new zmq::socket_t {ctx, zmq::socket_type::pub};
	m_swap_grab_task_state_publisher->bind(TOPIC_AUTO_EXCHANGE_TASK);


	m_swap_excep_publisher = new zmq::socket_t {ctx, zmq::socket_type::pub};
	m_swap_excep_publisher -> bind(TOPIC_EXCEPTION_AUTO_EXCHANGE);

	m_data_requester = new zmq::socket_t {ctx, zmq::socket_type::req};
	// m_data_requester -> connect(SERVICE_DATA_ACCESS);

	m_task_replayer = new zmq::socket_t {ctx, zmq::socket_type::rep};
	m_task_replayer -> bind(SERVICE_AUTO_EXCHANGE_TASK);


    return true;
}


/**@brief     scheduler_manager 运行函数，创建线程并运行
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool scheduler_manager::scheduler_manager_run() 
{
	new std::thread(&scheduler_manager::scheduler_manager_thead_sendmsg, this);
	new std::thread(&scheduler_manager::scheduler_manager_thread_task_reply, this);

    return true;
}