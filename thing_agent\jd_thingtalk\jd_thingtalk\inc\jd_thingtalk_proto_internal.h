/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 头文件，提供sdk内部使用的定义和接口
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#ifndef __JD_THINGTALK_PROTO_INTERNAL_H__
#define __JD_THINGTALK_PROTO_INTERNAL_H__
#include <stdbool.h>
#include <stddef.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"

#include "jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_time.h"
#include "jd_thingtalk/cJSON/cJSON.h"

#ifdef __cplusplus
extern "C"{
#endif /* __cplusplus */

/**
 * @brief   消息主题类型空间宏定义
 *
 */
#define JD_THINGTALK_PROTO_TOPIC_TYPE_PROP  ("properties")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_EVT   ("events")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_FUNC  ("functions")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_REG   ("register")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_AUTH  ("auth")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_HB    ("heartbeat")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_THMD  ("thing-model")
#define JD_THINGTALK_PROTO_TOPIC_TYPE_NTP   ("ntp")

/**
 * @brief   消息内容节点名字宏定义
 *
 */
#define JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID      ("deviceId")
#define JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP   ("timestamp")
#define JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID      ("messageId")
#define JD_THINGTALK_PAYLOAD_NODE_NAME_MSG         ("message")
#define JD_THINGTALK_PAYLOAD_NODE_NAME_CODE        ("code")
#define JD_THINGTALK_PAYLOAD_NODE_NAME_VERSION     ("version")

/**
 * @brief   消息主题结构体定义
 *
 */
typedef struct {
   char *retain;
   char *version;
   char *obj_name;
   char *deviceId;
   char *service_key;
   char *type_name;
   char *command;
   char *response;
} JDThingTalkProtoTopic_t;

/**
 * @brief   解析消息主题
 *
 * @param[in] topic_str: 消息主题字符串
 * @return 
 *    解析后的消息主题结构体指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
JDThingTalkProtoTopic_t *jd_thingtalk_proto_topic_parse(char *topic_str);

/**
 * @brief   消息主题结构提资源释放
 *
 * @param[in] topic: 待解析的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_topic_free(JDThingTalkProtoTopic_t *topic);

/**
 * @brief   产生消息主题字符串
 *
 * @param[in] type: 消息主题的类型
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] deviceId: 设备标识
 * @param[in] service_key: 服务关键字，如果消息主题类型不是服务类，该参数可填空
 * @return 
 *    消息主题字符串指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
char *jd_thingtalk_proto_topic(JD_THINGTALK_PROTO_TOPIC_T type, char *obj_name, char *deviceId, char *service_key);

/**
 * @brief   产生通配消息主题字符串
 *
 * @param[in] obj_name: 对象空间名字
 * @param[in] deviceId: 设备标识
 * @return 
 *    消息主题字符串指针
 * @see None.
 * @note 果消息主题类型不是服务类型，入参 service_key 填空
 */
char *jd_thingtalk_proto_wildcard_topic(char *obj_name, char *deviceId);

/**
 * @brief   键值对结构体内存空间释放
 *
 * @param[in] key_value: 键值对结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 释放所有的，不只是成员变量的
 */
int32_t jd_thingtalk_proto_free_key_value(JDThingTalkProtoKeyValue_t *key_value);

/**
 * @brief   键值对结构体解析
 *
 * @param[in] in_json: json结构体指针
 * @return 
 *    解析后的键值对结构体指针
 * @see None.
 * @note None.
 */
JDThingTalkProtoKeyValue_t *jd_thingtalk_proto_parse_key_value(cJSON *in_json);

/**
 * @brief   键值对结构体打包
 *
 * @param[in] key_value: 键值对结构体指针
 * @return 
 *    打包完成后的json指针
 * @see None.
 * @note None.
 */
cJSON *jd_thingtalk_proto_pack_key_value(JDThingTalkProtoKeyValue_t *key_value);

/**
 * @brief   物模型(thing-model)消息主题 释放物模型上报结构体 成员变量空间
 *
 * @param[in] in_post:物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_thing_model_post(JDThingTalkProtoThingModelPost_t *in_post);

/**
 * @brief   物模型(thing-model)消息主题 释放物模型上报响应结构体 成员变量空间
 *
 * @param[in] in_res:物模型上报响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_thing_model_post_res(JDThingTalkProtoThingModelPostRes_t *in_res);

/**
 * @brief   物模型(thing-model)消息主题 打包物模型上报结构体
 *
 * @param[in] in_post:物模型上报结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_thing_model_post(JDThingTalkProtoThingModelPost_t *in_post);

/**
 * @brief   物模型(thing-model)消息主题 解析物模型上报响应结构体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res:物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None
 */
int32_t jd_thingtalk_proto_parse_thing_model_post_res(char *in_json, JDThingTalkProtoThingModelPostRes_t *out_res);


/**
 * @brief   属性(properties)消息主题 释放属性设置结构体 成员变量的内存空间
 *
 * @param[in] in_set: 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_set(JDThingTalkProtoPropSet_t *in_set);

/**
 * @brief   属性(properties)消息主题 解析属性设置消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_set: 用于存解析结果的 属性设置结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_prop_set(char *in_json, JDThingTalkProtoPropSet_t *out_set);

/**
 * @brief   属性(properties)消息主题 打包属性设置响应
 *
 * @param[in] in_res: 待打包的 属性设置响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_set_res(JDThingTalkProtoPropSetRes_t *in_res);

/**
 * @brief   属性(properties)消息主题 释放属性获取结构体 成员变量的内存空间
 *
 * @param[in] in_get: 属性获取结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_prop_get(JDThingTalkProtoPropGet_t *in_get);

/**
 * @brief   属性(properties)消息主题 解析属性获取消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_get: 用于存解析结果的 属性获取结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_prop_get(char *in_json, JDThingTalkProtoPropGet_t *out_get);

/**
 * @brief   属性(properties)消息主题 打包属性获取响应
 *
 * @param[in] in_res: 待打包的 属性获取响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_get_res(JDThingTalkProtoPropGetRes_t *in_res);

/**
 * @brief   属性(properties)消息主题 打包属性上报
 *
 * @param[in] in_post: 待打包的 属性上报 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_prop_post(JDThingTalkProtoPropPost_t *in_post);

/**
 * @brief   方法(functions)消息主题 释放方法调用结构体 成员变量的内存空间
 *
 * @param[in] in_call: 方法调用结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_func_call(JDThingTalkProtoFuncCall_t *in_call);

/**
 * @brief   方法(functions)消息主题 解析方法调用消息
 *
 * @param[in] in_json: 输入的json串
 * @param[out] out_call: 用于存解析结果的 方法调用的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_func_call(char *in_json, JDThingTalkProtoFuncCall_t *out_call);

/**
 * @brief   方法(functions)消息主题 打包方法调用响应消息
 *
 * @param[in] in_res: 待打包的 方法调用响应 结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char   *jd_thingtalk_proto_pack_func_call_res(JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief   事件(events)消息主题 释放上下线的结构体 成员变量的内存空间
 *
 * @param[in] in_status: 上线下结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_evt_online_status(JDThingTalkProtoEvtOnlineStatus_t *in_status);

/**
 * @brief   事件(events)消息主题 打包上下线消息
 *
 * @param[in] in_status: 带打包的上线下结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_evt_online_status(JDThingTalkProtoEvtOnlineStatus_t *in_status);

/**
 * @brief   事件(events)消息主题 打包事件上报消息
 *
 * @param[in] in_post: 带打包的事件上报结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_evt_post(JDThingTalkProtoEvtPost_t *in_post);

/**
 * @brief   自动注册(register) 释放请求结构体 成员变量的内存空间
 *
 * @param[in] in_req: 自动请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_reg_req(JDThingTalkProtoRegReq_t *in_req);

/**
 * @brief   自动注册(register) 打包自动请求结构体
 *
 * @param[in] in_req: 自动请求结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_reg_req(JDThingTalkProtoRegReq_t *in_req);

/**
 * @brief   自动注册(register) 释放请求响应结构体 成员变量的内存空间
 *
 * @param[in] in_res: 自动请求响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_reg_req_res(JDThingTalkProtoRegReqRes_t *in_res);

/**
 * @brief   自动注册(register) 解析自动注册请求效应消息体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res: 自动请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_reg_req_res(char *in_json, JDThingTalkProtoRegReqRes_t *out_res);

/**
 * @brief   设备NTP授时 请求结构体定义
 */
typedef struct {
    char              *deviceId;
    char              *messageId;
    jd_thingtalk_time_stamp_t   devSendTime;
} JDThingTalkProtoNTPReq;

/**
 * @brief   设备NTP授时 请求响应结构体定义
 */
typedef struct {
    char              *deviceId;
    char              *messageId;
    int32_t           code;
    jd_thingtalk_time_stamp_t   devSendTime;
    jd_thingtalk_time_stamp_t   serRecvTime;
    jd_thingtalk_time_stamp_t   serSendTime;
} JDThingTalkProtoNTPReqRes;

/**
 * @brief   设备NTP授时 释放 请求信息结构体成员变量
 *
 * @param[in] in_req: NTP请求信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_ntp_req(JDThingTalkProtoNTPReq *in_req);

/**
 * @brief   设备NTP授时 打包NTP请求结构体
 *
 * @param[in] in_req: 请求NTP结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_ntp_req(JDThingTalkProtoNTPReq *in_req);

/**
 * @brief   设备NTP授时 释放 请求响应信息结构体成员变量
 *
 * @param[in] in_res: NTP请求响应信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_ntp_req_res(JDThingTalkProtoNTPReqRes *in_res);

/**
 * @brief   设备NTP授时 解析NTP请求响应消息体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res: NTP请求响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_ntp_req_res(char *in_json, JDThingTalkProtoNTPReqRes *out_res);

/**
 * @brief   ota 创建当前状态
 *
 * @param[in] evt_key: 事件的实例名的实例名
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_ota_create_current_state(char *evt_key,
                                                             char *task_id,
                                                             int32_t state,
                                                             int32_t e_code,
                                                             int8_t progress);

/**
 * @brief   ota 创建当前版本号事件
 *
 * @param[in] evt_key: 事件的实例名的实例名
 * @param[in] version: 当前升级包版本
 * @param[in] object_id: 子设备的设备标识，可为空，表示自身的
 * @param[in] type: 升级对象类型
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_ota_create_evet_version(char *evt_key, char *version, char *object_id, char *type);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __JD_THINGTALK_PROTO_INTERNAL_H__  */
