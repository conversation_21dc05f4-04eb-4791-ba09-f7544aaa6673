#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/pb/idl/ack.pb.h"
#include "share/global_def.h"
// #include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/container_interface.pb.h"
#include "share/pb/idl/data_map.pb.h"
// #include "share/pb/idl/plane_hmi.pb.h"
// #include "share/pb/idl/plane_safe_door.pb.h"
// #include "share/pb/idl/feeder_state.pb.h"
// #include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/train_info.pb.h"
// #include "converter/converter.hpp"


#define COLOR_LIGHT_OFF                             0
#define COLOR_RED                                   1
#define COLOR_GREEN                                 2
#define COLOR_YELLOW                                3
#define COLOR_BULE                                  4
#define COLOR_PURPLE                                5
#define COLOR_WHITE                                 6
#define COLOR_CYAN                                  7

#define FLASH_50HZ                                  1
#define NO_FLASH                                    0


class container
{
public:
    container() {};

    container(int id, data_map_container_type &type)
    {
        this->id = id;
        this->type = type;
        seal_state = container_seal_state_UNKNOWN;
        rfid_state.box_id = id;
        saturation_state.id = id;
    }

    void init_rfid_data(box_info_single &st)
    {
        rfid_state = st;
        strcpy(rfid_state.RFID, st.RFID);
        has_rfid_func = true;
    }

    void init_satr_data(slot_state &st)
    {
        saturation_state.st = st.st;
        has_saturation_func = true;
    }

    void set_bind(char *rfid)
    {
        rfid_state.box_st = box_state_BIND;
        strcpy(rfid_state.RFID, rfid);
    }

    void set_unbind()
    {
        strcpy(rfid_state.RFID, "0");
        rfid_state.box_st = box_state_UNBIND;
    }

    void set_full()
    {
        saturation_state.st = state_FULL;
    }

    void set_empty()
    {
        saturation_state.st = state_NORMAL;
    }

    void set_raster_trigger()
    {
        saturation_state.st = state_RASTER_TRIGGERED;
    }

    void set_seal_state(const container_seal_state &st)
    {
        seal_state = st;
    }

    const container_seal_state &get_seal_state()
    {
        return seal_state;
    }

    const int &get_id()
    {
        return id;
    }

    const slot_state &get_satr_state()
    {
        return saturation_state;
    }

    const box_info_single &get_rfid_state()
    {
        return rfid_state;
    }

    const data_map_container_type &get_type()
    {
        return type;
    }

    bool has_rfid()
    {
        return has_rfid_func;
    }

    bool has_satr()
    {
        return has_saturation_func;
    }

private:

    int id;
    bool has_rfid_func = false;
    bool has_saturation_func = false;
    container_seal_state seal_state;
    box_info_single rfid_state;
    slot_state saturation_state;
    data_map_container_type type;
};

class feeder
{
public:
    feeder() {};

    feeder(int id)
    {
        this->id= id;
        init_state();
        init_buttons();
    }

    void set_button_state(const key_id &id, key_evt_type &type)
    {
        buttons[id] = type;
    }

    void set_state(feeder_dev_state_total &st)
    {
        state = st;
    }

    void set_belt_status_changed(bool value)
    {
        belt_state_changed_thing = value;
    }

    const bool &get_belt_status_changed()
    {
        return belt_state_changed_thing;
    }
    void set_sys_state_key(key_id key)
    {
        sys_state_key = key;
    }

    const key_evt_type &get_button_state(key_id id)
    {
        return buttons[id];
    }

    const std::map<key_id, key_evt_type> &get_button_state()
    {
        return buttons;
    }

    const feeder_belt_motor_state_multiple &get_belt_motor()
    {
        return state.belt_motor;
    }

    const scanner_state &get_auto_scanner_state()
    {
        return state.auto_scanner;
    }

    const scanner_state &get_manual_scanner_state()
    {
        return state.manual_scanner;
    }

    const feeder_belt_sensor_state_multiple &get_belt_sensor_state()
    {
        return state.belt_sensor;
    }

    const feeder_dev_state_total &get_feeder_state()
    {
        return state;
    }

    const int &get_id()
    {
        return id;
    }

    const key_id &get_sys_state_key()
    {
        return sys_state_key;
    }

private:

    int init_buttons()
    {
        buttons.emplace(key_id_KEY_RESERVE, key_evt_type_EVENT_RELEASE);
        buttons.emplace(key_id_KEY_START, key_evt_type_EVENT_RELEASE);
        buttons.emplace(key_id_KEY_STOP, key_evt_type_EVENT_RELEASE);
        buttons.emplace(key_id_KEY_SLEEP, key_evt_type_EVENT_RELEASE);
        buttons.emplace(key_id_KEY_RESET, key_evt_type_EVENT_RELEASE);
        buttons.emplace(key_id_KEY_EMERG, key_evt_type_EVENT_RELEASE);

        return buttons.size();
    }

    int init_state()
    {
        state.auto_scanner.state = feeder_dev_state_DEV_STATE_UNKNOWN;
        state.belt_motor.motor_count = 0;
        state.belt_sensor.sensor_count = 0;
        state.manual_scanner.state = feeder_dev_state_DEV_STATE_UNKNOWN;

        state.state = component_state_C_INIT;

        return 0;
    }

    int id;
    std::map<key_id, key_evt_type> buttons;
    key_id sys_state_key = key_id_KEY_STOP;        //用于表示供包台按键改变系统状态的期望状态，按键事件和改变系统状态可能存在较大时间差，多供包台需要都停止才可切换系统状态
    feeder_dev_state_total state;
    bool belt_state_changed_thing = true;         //用于表示供包机皮带状态改变后立即上报物控
};

class plane
{
public:

    enum emerg_state
    {
        TRIGGERED = 0,
        RELEASED = 1
    };

    plane()
    {
        init();
    }

    // int add_switcher(int id)
    // {
    //     switch_state_single sw;
    //     sw.position = switch_position_def_SWITCH_STATE_UNKNOWN;
    //     sw.state = switch_state_def_STATE_UNKNOWN;
    //     sw.switch_id = id;
    //     switchers.emplace(id, sw);

    //     return switchers.size();
    // }

    // int add_switcher(switch_state_single &sw)
    // {
    //     switchers.emplace(sw.switch_id, sw);
    //     return switchers.size();
    // }

    // void set_sw_state(switch_state_single &state)
    // {
    //     switchers[state.switch_id] = state;
    // }

    // void set_emerg_dev_state(const plane_event &dev_state)
    // {
    //     bool is_existed = false;
    //     if (dev_state.event_id == plane_id_SAFETY_DOOR)
    //     {
    //         for (auto &dr: safety_doors)
    //         {
    //             if (dr.id == int(dev_state.dev_id))
    //             {
    //                 is_existed = true;
    //                 if (dev_state.evt_type == plane_evt_type_DOOR_OPENED)
    //                     dr.state = TRIGGERED;
    //                 else if (dev_state.evt_type == plane_evt_type_DOOR_CLOSEED)
    //                     dr.state = RELEASED;
    //             }
    //         }
    //         if (!is_existed)
    //         {
    //             if (dev_state.evt_type == plane_evt_type_DOOR_OPENED)
    //                 safety_doors.emplace_back(dev_state.dev_id, TRIGGERED);
    //             else if (dev_state.evt_type == plane_evt_type_DOOR_CLOSEED)
    //                 safety_doors.emplace_back(dev_state.dev_id, RELEASED);
    //         }
    //     }
    //     else if (dev_state.event_id == plane_id_GEC_EMERG || dev_state.event_id == plane_id_DOOR_EMERG
    //             || dev_state.event_id == plane_id_TRACK_EMERG || dev_state.event_id == plane_id_DOOR_OPEN_REQUEST)
    //     {
    //         for (auto &bt: emerg_buttons)
    //         {
    //             if (bt.id == int(dev_state.dev_id) && bt.type == dev_state.event_id)
    //             {
    //                 is_existed = true;
    //                 if (dev_state.evt_type == plane_evt_type_KEY_DOWN)
    //                     bt.state = TRIGGERED;
    //                 else if (dev_state.evt_type == plane_evt_type_KEY_UP)
    //                     bt.state = RELEASED;
    //             }
    //         }
    //         if (!is_existed)
    //         {
    //             if (dev_state.evt_type == plane_evt_type_KEY_DOWN)
    //                 emerg_buttons.emplace_back(dev_state.dev_id, dev_state.event_id, TRIGGERED);
    //             else if (dev_state.evt_type == plane_evt_type_KEY_UP)
    //                 emerg_buttons.emplace_back(dev_state.dev_id, dev_state.event_id, RELEASED);
    //         }
    //     }
    //     else
    //         SPDLOG_DEBUG("wrong emerg dev: {}", dev_state.event_id);
    // }

    // const switch_state_single &get_switcher_state(const int &id)
    // {
    //     return switchers[id];
    // }

    // const std::unordered_map<int, switch_state_single> &get_switchers_state()
    // {
    //     return switchers;
    // }

    // const emerg_state get_door_state()
    // {
    //     for(auto &sd: safety_doors)
    //     {
    //         if (sd.state == TRIGGERED)
    //             return TRIGGERED;
    //     }

    //     return RELEASED;
    // }

    // const emerg_state get_emgbt_state()         //分播架急停
    // {
    //     for(auto &bt: emerg_buttons)
    //     {
    //         if (bt.state == TRIGGERED)
    //             return TRIGGERED;
    //     }

    //     return RELEASED;
    // }

private:

    // struct safety_door
    // {
    //     int id;
    //     emerg_state state;

    //     safety_door(int _id, emerg_state _st): id(_id), state(_st) {};
    // };

    // struct plane_emerg_button
    // {
    //     int id;
    //     plane_id type;
    //     emerg_state state;

    //     plane_emerg_button(int _id, plane_id _tp, emerg_state _st): id(_id), type(_tp), state(_st) {};
    // };

    // std::unordered_map<int, switch_state_single> switchers;
    // std::vector<safety_door> safety_doors;
    // std::vector<plane_emerg_button> emerg_buttons;

    int init()
    {

        return 0;
    }
};

class vehicle
{
public:

    vehicle() {};


    typedef struct _train_basic_info
    {
        bool state;
        char ip[16];
        char sw_ver[16];
        char hw_ver[16];
    }train_basic_info;

    typedef struct _platform_st{
        uint32_t platform_id;
        int32_t z;
        bool state; //皮带启用/禁用
        bool with_load;
        platform_type conveyor_type;
        char platform_motor_status_code[16];
        train_dev_state platform_motor_status;
        uint32_t platform_motor_speed;
        bool platform_zero_sensor_status;
      
    }platform_st;

    typedef struct _carriage_st {
        uint32_t carriage_id;
        char carriage_motor_status_code[16];
        train_dev_state carriage_motor_state; /* Y轴电机状态 正常/异常/未知 */
        uint32_t motor_speed;
        position_xyz p_xyz;
        carriage_type car_type;
        train_dev_state carriage_state;
        bool state; //载货台启用/禁用
        int platform_count;
        platform_st platform_state[4];
    } carriage_st;

    struct vehicle_running_state
    {
        int train_count;
        int carriage_count;
        train_basic_info basic_info;
        uint32_t fault_state;
        uint32_t walk_motor_speed;
        train_dev_state walk_motor_state;
        char walk_motor_status_code[16];
        carriage_st carriage_state[20];
    };

    vehicle(uint32_t id)
    {
        this->id = id;
        // info.train_id = id;
        init();
    }

    // void set_online()
    // {
    //     info.state = enable_state_DEV_ENABLE_STATE_ENABLE;
    // }

    // void set_offline()
    // {
    //     info.state = enable_state_DEV_ENABLE_STATE_DISABLE;
    // }

    void set_state(const train_ext_state_single &st, int trains_count)
    {
        state.train_count = trains_count;
        state.carriage_count = st.carriages_count;
        state.fault_state = st.fault_state;
        strcpy(state.basic_info.ip, st.ip_addr);
        strcpy(state.basic_info.hw_ver, st.firmware_version);   //车辆报硬件版本号
        strcpy(state.basic_info.sw_ver, st.carriages[0].firmware_version);  //载台报软件版本号
        state.basic_info.state = st.online_state;

        state.walk_motor_speed = st.motor_speed;
        state.walk_motor_state = st.motor_state;
        strcpy(state.walk_motor_status_code, st.motor_state_no);

        for(int i = 0; i < st.carriages_count; i++)
        {
            state.carriage_state[i].carriage_id = st.carriages[i].carriage_id;
            strcpy(state.carriage_state[i].carriage_motor_status_code, st.carriages[i].lifting_state_no);
            state.carriage_state[i].carriage_motor_state = st.carriages[i].lifting_state;
            state.carriage_state[i].motor_speed = st.carriages[i].lifting_speed;
            state.carriage_state[i].p_xyz = st.carriages[i].pos_3d;
            state.carriage_state[i].car_type = st.carriages[i].type; 
            state.carriage_state[i].carriage_state = st.carriages[i].work_state;
            state.carriage_state[i].state = true;   //todo
            state.carriage_state[i].platform_count = st.carriages[i].platforms_count;
            for(int j = 0; j < st.carriages[i].platforms_count; j++)
            {
                state.carriage_state[i].platform_state[j].platform_id = st.carriages[i].platforms[j].platform_id;
                state.carriage_state[i].platform_state[j].z = st.carriages[i].platforms[j].location_z;
                state.carriage_state[i].platform_state[j].state = true; //todo
                state.carriage_state[i].platform_state[j].with_load = st.carriages[i].platforms[j].load_state;
                state.carriage_state[i].platform_state[j].conveyor_type = st.carriages[i].platforms[j].type;
                strcpy(state.carriage_state[i].platform_state[j].platform_motor_status_code, st.carriages[i].platforms[j].servo_state_no);
                state.carriage_state[i].platform_state[j].platform_motor_status = st.carriages[i].platforms[j].servo_state;
                state.carriage_state[i].platform_state[j].platform_motor_speed = st.carriages[i].platforms[j].servo_speed;
                state.carriage_state[i].platform_state[j].platform_zero_sensor_status = st.carriages[i].platforms[j].platform_zero_flag;
            }
        }
    }

    // void set_state(const train_state &st)
    // {

    //     state.wk_state = st.work_state;
        
    // }

    // void set_basic_info(const train_basic_info &basic_info)
    // {
    //     strcpy(info.ip, basic_info.ip);
    //     strcpy(info.hw_ver, basic_info.hw_ver);
    //     strcpy(info.sw_ver, basic_info.sw_ver);
    // }

    const vehicle_running_state &get_running_state()
    {
        return state;
    }

    // const train_basic_info &get_info()
    // {
    //     return info;
    // }

private:
    uint32_t id;
    vehicle_running_state state;

    int init()
    {
        state.carriage_count = 0;

        strcpy(state.basic_info.hw_ver, "");
        strcpy(state.basic_info.sw_ver, "");
        strcpy(state.basic_info.ip, "");
        state.basic_info.state = false;

        state.fault_state = 0;
        state.walk_motor_speed = 0;
        state.walk_motor_state = train_dev_state_DEV_UNKNOWN;
        for(int i = 0; i < 20; i++)
        {
            state.carriage_state[i].carriage_motor_state = train_dev_state_DEV_UNKNOWN;
            state.carriage_state[i].motor_speed = 0;
            state.carriage_state[i].p_xyz = {0, 0, 0};
            state.carriage_state[i].car_type = carriage_type_CARRIAGE_TYPE_RESERVE;
            state.carriage_state[i].carriage_state = train_dev_state_DEV_UNKNOWN;
            state.carriage_state[i].state = false;

            for(int j = 0; j < 4; j++)
            {
                state.carriage_state[i].platform_state[j].z = 0;
                state.carriage_state[i].platform_state[j].state = false;   
                state.carriage_state[i].platform_state[j].with_load = false;
                state.carriage_state[i].platform_state[j].conveyor_type = platform_type_PLATFORM_TYPE_RESERVE;
                state.carriage_state[i].platform_state[j].platform_motor_status = train_dev_state_DEV_UNKNOWN;
                state.carriage_state[i].platform_state[j].platform_motor_speed = 0;
                state.carriage_state[i].platform_state[j].platform_zero_sensor_status = false;
            }
        }

        return 0;
    }
};

