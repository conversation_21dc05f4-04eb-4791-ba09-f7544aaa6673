#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../share/libs/x86/include/spdlog/common-inl.h
spdlog/common.h
-

../../share/libs/x86/include/spdlog/common.h
spdlog/tweakme.h
-
spdlog/details/null_mutex.h
-
atomic
-
chrono
-
initializer_list
-
memory
-
exception
-
string
-
type_traits
-
functional
-
spdlog/fmt/fmt.h
-
common-inl.h
../../share/libs/x86/include/spdlog/common-inl.h

../../share/libs/x86/include/spdlog/details/backtracer-inl.h
spdlog/details/backtracer.h
-

../../share/libs/x86/include/spdlog/details/backtracer.h
spdlog/details/log_msg_buffer.h
-
spdlog/details/circular_q.h
-
atomic
-
mutex
-
functional
-
backtracer-inl.h
../../share/libs/x86/include/spdlog/details/backtracer-inl.h

../../share/libs/x86/include/spdlog/details/circular_q.h
vector
-
cassert
-

../../share/libs/x86/include/spdlog/details/console_globals.h
spdlog/details/null_mutex.h
-
mutex
-

../../share/libs/x86/include/spdlog/details/fmt_helper.h
chrono
-
type_traits
-
spdlog/fmt/fmt.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/details/log_msg-inl.h
spdlog/details/log_msg.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/details/log_msg.h
spdlog/common.h
-
string
-
log_msg-inl.h
../../share/libs/x86/include/spdlog/details/log_msg-inl.h

../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
spdlog/details/log_msg_buffer.h
-

../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
spdlog/details/log_msg.h
-
log_msg_buffer-inl.h
../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h

../../share/libs/x86/include/spdlog/details/null_mutex.h
atomic
-
utility
-

../../share/libs/x86/include/spdlog/details/os-inl.h
spdlog/details/os.h
-
spdlog/common.h
-
algorithm
-
chrono
-
cstdio
-
cstdlib
-
cstring
-
ctime
-
string
-
thread
-
array
-
sys/stat.h
-
sys/types.h
-
io.h
-
process.h
-
spdlog/details/windows_include.h
-
share.h
-
limits
-
direct.h
-
fcntl.h
-
unistd.h
-
sys/syscall.h
-
pthread.h
-
pthread_np.h
-
lwp.h
-
thread.h
-

../../share/libs/x86/include/spdlog/details/os.h
spdlog/common.h
-
ctime
-
os-inl.h
../../share/libs/x86/include/spdlog/details/os-inl.h

../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
spdlog/details/periodic_worker.h
-

../../share/libs/x86/include/spdlog/details/periodic_worker.h
chrono
-
condition_variable
-
functional
-
mutex
-
thread
-
periodic_worker-inl.h
../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h

../../share/libs/x86/include/spdlog/details/registry-inl.h
spdlog/details/registry.h
-
spdlog/common.h
-
spdlog/details/periodic_worker.h
-
spdlog/logger.h
-
spdlog/pattern_formatter.h
-
spdlog/sinks/wincolor_sink.h
-
spdlog/sinks/ansicolor_sink.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-

../../share/libs/x86/include/spdlog/details/registry.h
spdlog/common.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-
mutex
-
registry-inl.h
../../share/libs/x86/include/spdlog/details/registry-inl.h

../../share/libs/x86/include/spdlog/details/synchronous_factory.h
registry.h
../../share/libs/x86/include/spdlog/details/registry.h

../../share/libs/x86/include/spdlog/details/windows_include.h
windows.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/core.h
cstdio
-
cstring
-
functional
-
iterator
-
memory
-
string
-
type_traits
-
vector
-
string_view
-
experimental/string_view
-
fmt/core.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
cassert
-
cctype
-
climits
-
cmath
-
cstdarg
-
cstring
-
cwchar
-
exception
-
locale
-
io.h
-
format.h
../../share/libs/x86/include/spdlog/fmt/bundled/format.h

../../share/libs/x86/include/spdlog/fmt/bundled/format.h
algorithm
-
cerrno
-
cmath
-
cstdint
-
limits
-
memory
-
stdexcept
-
core.h
../../share/libs/x86/include/spdlog/fmt/bundled/core.h
intrin.h
-
fmt/format.h
-
format-inl.h
../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h

../../share/libs/x86/include/spdlog/fmt/fmt.h
spdlog/fmt/bundled/core.h
-
spdlog/fmt/bundled/format.h
-
fmt/core.h
-
fmt/format.h
-

../../share/libs/x86/include/spdlog/formatter.h
spdlog/fmt/fmt.h
-
spdlog/details/log_msg.h
-

../../share/libs/x86/include/spdlog/logger-inl.h
spdlog/logger.h
-
spdlog/sinks/sink.h
-
spdlog/details/backtracer.h
-
spdlog/pattern_formatter.h
-
cstdio
-

../../share/libs/x86/include/spdlog/logger.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/backtracer.h
-
spdlog/details/os.h
-
vector
-
logger-inl.h
../../share/libs/x86/include/spdlog/logger-inl.h

../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
spdlog/pattern_formatter.h
-
spdlog/details/fmt_helper.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/fmt/fmt.h
-
spdlog/formatter.h
-
array
-
chrono
-
ctime
-
cctype
-
cstring
-
memory
-
mutex
-
string
-
thread
-
utility
-
vector
-

../../share/libs/x86/include/spdlog/pattern_formatter.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/formatter.h
-
chrono
-
ctime
-
memory
-
string
-
vector
-
unordered_map
-
pattern_formatter-inl.h
../../share/libs/x86/include/spdlog/pattern_formatter-inl.h

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
spdlog/sinks/ansicolor_sink.h
-
spdlog/pattern_formatter.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
ansicolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/sink-inl.h
spdlog/sinks/sink.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/sinks/sink.h
spdlog/details/log_msg.h
-
spdlog/formatter.h
-
sink-inl.h
../../share/libs/x86/include/spdlog/sinks/sink-inl.h

../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
spdlog/sinks/wincolor_sink.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
spdlog/common.h
-
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
spdlog/details/windows_include.h
-
wincon.h
-
wincolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h

../../share/libs/x86/include/spdlog/spdlog-inl.h
spdlog/spdlog.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/spdlog.h
spdlog/common.h
-
spdlog/details/registry.h
-
spdlog/logger.h
-
spdlog/version.h
-
spdlog/details/synchronous_factory.h
-
chrono
-
functional
-
memory
-
string
-
spdlog-inl.h
../../share/libs/x86/include/spdlog/spdlog-inl.h

../../share/libs/x86/include/spdlog/tweakme.h

../../share/libs/x86/include/spdlog/version.h

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp
spdlog/spdlog.h
-
epoll_poller.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
stdio.h
-
stdlib.h
-
string.h
-
sys/epoll.h
-
unistd.h
-
errno.h
-
assert.h
-
sys/eventfd.h
-
fcntl.h
-
iostream
-
unordered_map
-
mutex
-
arpa/inet.h
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
tcp_socket.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
string
-
netinet/ip.h
-
iostream
-
vector
-
sys/epoll.h
-
functional
-
iostream
-
string
-
unordered_map
-
memory
-
stdlib.h
-
string.h
-
mutex
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp
tcp_socket.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
sys/types.h
-
sys/socket.h
-
arpa/inet.h
-
netinet/in.h
-
netinet/tcp.h
-
netdb.h
-
fcntl.h
-
stdio.h
-
stdlib.h
-
string.h
-
stdint.h
-
unistd.h
-
errno.h
-
iostream
-
stdexcept
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
string
-
netinet/ip.h
-
iostream
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp
udp_socket.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
spdlog/spdlog.h
-
sys/types.h
-
sys/socket.h
-
arpa/inet.h
-
netinet/in.h
-
netinet/tcp.h
-
netdb.h
-
fcntl.h
-
stdio.h
-
stdlib.h
-
string.h
-
stdint.h
-
unistd.h
-
errno.h
-
iostream
-
stdexcept
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
string
-
netinet/ip.h
-
iostream
-

