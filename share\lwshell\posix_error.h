#ifndef __POSIXERROR_H_20160202110039__ 
#define __POSIXERROR_H_20160202110039__ 

#define EOK                   (0x00)

#define POSIX_ERROR_BASE      (0x1000)

#define POSIX_EOK             (0x00)
#define POSIX_E2BIG           (POSIX_ERROR_BASE|0x01)
#define POSIX_EACCES          (POSIX_ERROR_BASE|0x02)
#define POSIX_EAGAIN          (POSIX_ERROR_BASE|0x03)
#define POSIX_EBADF           (POSIX_ERROR_BASE|0x04)
#define POSIX_EBADMSG         (POSIX_ERROR_BASE|0x05)
#define POSIX_EBUSY           (POSIX_ERROR_BASE|0x06)
#define POSIX_ECANCELED       (POSIX_ERROR_BASE|0x07)
#define POSIX_ECHILD          (POSIX_ERROR_BASE|0x08)
#define POSIX_EDEADLK         (POSIX_ERROR_BASE|0x09)
#define POSIX_EDOM            (POSIX_ERROR_BASE|0x0a)
#define POSIX_EEXIST          (POSIX_ERROR_BASE|0x0b)
#define POSIX_EFAULT          (POSIX_ERROR_BASE|0x0c)
#define POSIX_EFBIG           (POSIX_ERROR_BASE|0x0d)
#define POSIX_EINPROGRESS     (POSIX_ERROR_BASE|0x0e)
#define POSIX_EINTR           (POSIX_ERROR_BASE|0x0f)
#define POSIX_EINVAL          (POSIX_ERROR_BASE|0x10)
#define POSIX_EIO             (POSIX_ERROR_BASE|0x11) 
#define POSIX_EISDIR          (POSIX_ERROR_BASE|0x12)
#define POSIX_EMFILE          (POSIX_ERROR_BASE|0x13)
#define POSIX_EMLINK          (POSIX_ERROR_BASE|0x14)
#define POSIX_EMSGSIZE        (POSIX_ERROR_BASE|0x15)
#define POSIX_ENAMETOOLONG    (POSIX_ERROR_BASE|0x16)
#define POSIX_ENFILE          (POSIX_ERROR_BASE|0x17)
#define POSIX_ENODEV          (POSIX_ERROR_BASE|0x18)
#define POSIX_ENOENT          (POSIX_ERROR_BASE|0x19)
#define POSIX_ENOEXEC         (POSIX_ERROR_BASE|0x1a)
#define POSIX_ENOLCK          (POSIX_ERROR_BASE|0x1b)
#define POSIX_ENOMEM          (POSIX_ERROR_BASE|0x1c)
#define POSIX_ENOSPC          (POSIX_ERROR_BASE|0x1d)
#define POSIX_ENOSYS          (POSIX_ERROR_BASE|0x1e)
#define POSIX_ENOTDIR         (POSIX_ERROR_BASE|0x1f)
#define POSIX_ENOTEMPTY       (POSIX_ERROR_BASE|0x20)
#define POSIX_ENOTSUP         (POSIX_ERROR_BASE|0x21)
#define POSIX_ENOTTY          (POSIX_ERROR_BASE|0x22)
#define POSIX_ENXIO           (POSIX_ERROR_BASE|0x23)
#define POSIX_EPERM           (POSIX_ERROR_BASE|0x24)
#define POSIX_EPIPE           (POSIX_ERROR_BASE|0x25)
#define POSIX_ERANGE          (POSIX_ERROR_BASE|0x26)
#define POSIX_EROFS           (POSIX_ERROR_BASE|0x27)
#define POSIX_ESPIPE          (POSIX_ERROR_BASE|0x28)
#define POSIX_ESRCH           (POSIX_ERROR_BASE|0x29)
#define POSIX_ETIMEDOUT       (POSIX_ERROR_BASE|0x2a)
#define POSIX_EXDEV           (POSIX_ERROR_BASE|0x2b)

#endif
