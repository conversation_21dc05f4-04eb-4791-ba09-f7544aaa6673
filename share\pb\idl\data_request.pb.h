/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_DATA_REQUEST_PB_H_INCLUDED
#define PB_DATA_REQUEST_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _data_request_cmd {
    data_request_cmd_READ = 0,
    data_request_cmd_WRITE = 1
} data_request_cmd;

/* Struct definitions */
typedef struct _data_request {
    data_request_cmd type;
    char key[64];
} data_request;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _data_request_cmd_MIN data_request_cmd_READ
#define _data_request_cmd_MAX data_request_cmd_WRITE
#define _data_request_cmd_ARRAYSIZE ((data_request_cmd)(data_request_cmd_WRITE+1))

#define data_request_type_ENUMTYPE data_request_cmd


/* Initializer values for message structs */
#define data_request_init_default                {_data_request_cmd_MIN, ""}
#define data_request_init_zero                   {_data_request_cmd_MIN, ""}

/* Field tags (for use in manual encoding/decoding) */
#define data_request_type_tag                    1
#define data_request_key_tag                     2

/* Struct field encoding specification for nanopb */
#define data_request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, STRING,   key,               2)
#define data_request_CALLBACK NULL
#define data_request_DEFAULT NULL

extern const pb_msgdesc_t data_request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define data_request_fields &data_request_msg

/* Maximum encoded size of messages (where known) */
#define DATA_REQUEST_PB_H_MAX_SIZE               data_request_size
#define data_request_size                        67

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
