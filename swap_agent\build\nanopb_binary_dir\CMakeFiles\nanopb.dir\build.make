# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include nanopb_binary_dir/CMakeFiles/nanopb.dir/depend.make

# Include the progress variables for this target.
include nanopb_binary_dir/CMakeFiles/nanopb.dir/progress.make

# Include the compile flags for this target's objects.
include nanopb_binary_dir/CMakeFiles/nanopb.dir/flags.make

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o: nanopb_binary_dir/CMakeFiles/nanopb.dir/flags.make
nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o: /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/nanopb.dir/pb_common.c.o   -c /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nanopb.dir/pb_common.c.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c > CMakeFiles/nanopb.dir/pb_common.c.i

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nanopb.dir/pb_common.c.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_common.c -o CMakeFiles/nanopb.dir/pb_common.c.s

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.requires:

.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.requires

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.provides: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.requires
	$(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.provides.build
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.provides

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.provides.build: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o


nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o: nanopb_binary_dir/CMakeFiles/nanopb.dir/flags.make
nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o: /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/nanopb.dir/pb_encode.c.o   -c /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nanopb.dir/pb_encode.c.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c > CMakeFiles/nanopb.dir/pb_encode.c.i

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nanopb.dir/pb_encode.c.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_encode.c -o CMakeFiles/nanopb.dir/pb_encode.c.s

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.requires:

.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.requires

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.provides: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.requires
	$(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.provides.build
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.provides

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.provides.build: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o


nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o: nanopb_binary_dir/CMakeFiles/nanopb.dir/flags.make
nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o: /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/nanopb.dir/pb_decode.c.o   -c /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nanopb.dir/pb_decode.c.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c > CMakeFiles/nanopb.dir/pb_decode.c.i

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nanopb.dir/pb_decode.c.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && /usr/bin/gcc  $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/pb_decode.c -o CMakeFiles/nanopb.dir/pb_decode.c.s

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.requires:

.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.requires

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.provides: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.requires
	$(MAKE) -f nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.provides.build
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.provides

nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.provides.build: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o


# Object files for target nanopb
nanopb_OBJECTS = \
"CMakeFiles/nanopb.dir/pb_common.c.o" \
"CMakeFiles/nanopb.dir/pb_encode.c.o" \
"CMakeFiles/nanopb.dir/pb_decode.c.o"

# External object files for target nanopb
nanopb_EXTERNAL_OBJECTS =

nanopb_binary_dir/libnanopb.a: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o
nanopb_binary_dir/libnanopb.a: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o
nanopb_binary_dir/libnanopb.a: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o
nanopb_binary_dir/libnanopb.a: nanopb_binary_dir/CMakeFiles/nanopb.dir/build.make
nanopb_binary_dir/libnanopb.a: nanopb_binary_dir/CMakeFiles/nanopb.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C static library libnanopb.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && $(CMAKE_COMMAND) -P CMakeFiles/nanopb.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nanopb.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
nanopb_binary_dir/CMakeFiles/nanopb.dir/build: nanopb_binary_dir/libnanopb.a

.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/build

nanopb_binary_dir/CMakeFiles/nanopb.dir/requires: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_common.c.o.requires
nanopb_binary_dir/CMakeFiles/nanopb.dir/requires: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_encode.c.o.requires
nanopb_binary_dir/CMakeFiles/nanopb.dir/requires: nanopb_binary_dir/CMakeFiles/nanopb.dir/pb_decode.c.o.requires

.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/requires

nanopb_binary_dir/CMakeFiles/nanopb.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir && $(CMAKE_COMMAND) -P CMakeFiles/nanopb.dir/cmake_clean.cmake
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/clean

nanopb_binary_dir/CMakeFiles/nanopb.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/nanopb_binary_dir/CMakeFiles/nanopb.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : nanopb_binary_dir/CMakeFiles/nanopb.dir/depend

