The system is: Linux - 4.15.0-142-generic - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/cc 
Build flags: 
Id flags: 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/3.5.1/CompilerIdC/a.out"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/c++ 
Build flags: 
Id flags: 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/3.5.1/CompilerIdCXX/a.out"

Determining if the C compiler works passed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_b0885/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_b0885.dir/build.make CMakeFiles/cmTC_b0885.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_b0885.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_b0885.dir/testCCompiler.c.o
/usr/bin/cc     -o CMakeFiles/cmTC_b0885.dir/testCCompiler.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp/testCCompiler.c
Linking C executable cmTC_b0885
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b0885.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_b0885.dir/testCCompiler.c.o  -o cmTC_b0885 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


Detecting C compiler ABI info compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_fb73b/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_fb73b.dir/build.make CMakeFiles/cmTC_fb73b.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_fb73b.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o
/usr/bin/cc     -o CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.5/Modules/CMakeCCompilerABI.c
Linking C executable cmTC_fb73b
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fb73b.dir/link.txt --verbose=1
/usr/bin/cc      -v CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o  -o cmTC_fb73b  
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_fb73b' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccPz83IM.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_fb73b /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_fb73b/fast"]
  ignore line: [make: Warning: File 'Makefile' has modification time 41 s in the future]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_fb73b.dir/build.make CMakeFiles/cmTC_fb73b.dir/build]
  ignore line: [make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp']
  ignore line: [make[1]: Warning: File 'CMakeFiles/cmTC_fb73b.dir/flags.make' has modification time 41 s in the future]
  ignore line: [Building C object CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o]
  ignore line: [/usr/bin/cc     -o CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.5/Modules/CMakeCCompilerABI.c]
  ignore line: [Linking C executable cmTC_fb73b]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fb73b.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/cc      -v CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o  -o cmTC_fb73b  ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_fb73b' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccPz83IM.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_fb73b /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccPz83IM.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--sysroot=/] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_fb73b] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..]
    arg [CMakeFiles/cmTC_fb73b.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtend.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  remove lib [gcc]
  remove lib [gcc_s]
  remove lib [gcc]
  remove lib [gcc_s]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5] ==> [/usr/lib/gcc/x86_64-linux-gnu/5]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> [/usr/lib]
  implicit libs: [c]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/5;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []




Detecting C [-std=c11] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_0fed3/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_0fed3.dir/build.make CMakeFiles/cmTC_0fed3.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_0fed3.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_0fed3.dir/feature_tests.c.o
/usr/bin/cc    -std=c11 -o CMakeFiles/cmTC_0fed3.dir/feature_tests.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_0fed3
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0fed3.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_0fed3.dir/feature_tests.c.o  -o cmTC_0fed3 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:1c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c99] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_92a91/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_92a91.dir/build.make CMakeFiles/cmTC_92a91.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_92a91.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_92a91.dir/feature_tests.c.o
/usr/bin/cc    -std=c99 -o CMakeFiles/cmTC_92a91.dir/feature_tests.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_92a91
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_92a91.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_92a91.dir/feature_tests.c.o  -o cmTC_92a91 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c90] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_d7885/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_d7885.dir/build.make CMakeFiles/cmTC_d7885.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_d7885.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_d7885.dir/feature_tests.c.o
/usr/bin/cc    -std=c90 -o CMakeFiles/cmTC_d7885.dir/feature_tests.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_d7885
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d7885.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_d7885.dir/feature_tests.c.o  -o cmTC_d7885 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:0c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:0c_variadic_macros
Determining if the CXX compiler works passed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_3fda0/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_3fda0.dir/build.make CMakeFiles/cmTC_3fda0.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_3fda0.dir/flags.make' has modification time 41 s in the future
Building CXX object CMakeFiles/cmTC_3fda0.dir/testCXXCompiler.cxx.o
/usr/bin/c++      -o CMakeFiles/cmTC_3fda0.dir/testCXXCompiler.cxx.o -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_3fda0
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3fda0.dir/link.txt --verbose=1
/usr/bin/c++        CMakeFiles/cmTC_3fda0.dir/testCXXCompiler.cxx.o  -o cmTC_3fda0 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f7cce/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_f7cce.dir/build.make CMakeFiles/cmTC_f7cce.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_f7cce.dir/flags.make' has modification time 41 s in the future
Building CXX object CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/c++      -o CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.5/Modules/CMakeCXXCompilerABI.cpp
Linking CXX executable cmTC_f7cce
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f7cce.dir/link.txt --verbose=1
/usr/bin/c++       -v CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_f7cce  
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_f7cce' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqA190W.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_f7cce /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_f7cce/fast"]
  ignore line: [make: Warning: File 'Makefile' has modification time 41 s in the future]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_f7cce.dir/build.make CMakeFiles/cmTC_f7cce.dir/build]
  ignore line: [make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp']
  ignore line: [make[1]: Warning: File 'CMakeFiles/cmTC_f7cce.dir/flags.make' has modification time 41 s in the future]
  ignore line: [Building CXX object CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/usr/bin/c++      -o CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.5/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Linking CXX executable cmTC_f7cce]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f7cce.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/c++       -v CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_f7cce  ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 5.4.0-6ubuntu1~16.04.12' --with-bugurl=file:///usr/share/doc/gcc-5/README.Bugs --enable-languages=c,ada,c++,java,go,d,fortran,objc,obj-c++ --prefix=/usr --program-suffix=-5 --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --with-sysroot=/ --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-libmpx --enable-plugin --with-system-zlib --disable-browser-plugin --enable-java-awt=gtk --enable-gtk-cairo --with-java-home=/usr/lib/jvm/java-1.5.0-gcj-5-amd64/jre --enable-java-home --with-jvm-root-dir=/usr/lib/jvm/java-1.5.0-gcj-5-amd64 --with-jvm-jar-dir=/usr/lib/jvm-exports/java-1.5.0-gcj-5-amd64 --with-arch-directory=amd64 --with-ecj-jar=/usr/share/java/eclipse-ecj.jar --enable-objc-gc --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 5.4.0 20160609 (Ubuntu 5.4.0-6ubuntu1~16.04.12) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/5/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/5/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_f7cce' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/5/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqA190W.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --sysroot=/ --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -z relro -o cmTC_f7cce /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o -L/usr/lib/gcc/x86_64-linux-gnu/5 -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/5/../../.. CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/5/crtend.o /usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/5/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccqA190W.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--sysroot=/] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_f7cce] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtbegin.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..]
    arg [CMakeFiles/cmTC_f7cce.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/crtend.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  remove lib [gcc_s]
  remove lib [gcc]
  remove lib [gcc_s]
  remove lib [gcc]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5] ==> [/usr/lib/gcc/x86_64-linux-gnu/5]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/5/../../..] ==> [/usr/lib]
  implicit libs: [stdc++;m;c]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/5;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []




Detecting CXX [-std=c++14] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_be73c/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_be73c.dir/build.make CMakeFiles/cmTC_be73c.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_be73c.dir/flags.make' has modification time 41 s in the future
Building CXX object CMakeFiles/cmTC_be73c.dir/feature_tests.cxx.o
/usr/bin/c++     -std=c++14 -o CMakeFiles/cmTC_be73c.dir/feature_tests.cxx.o -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_be73c
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_be73c.dir/link.txt --verbose=1
/usr/bin/c++        CMakeFiles/cmTC_be73c.dir/feature_tests.cxx.o  -o cmTC_be73c 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++11] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_42a1b/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_42a1b.dir/build.make CMakeFiles/cmTC_42a1b.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_42a1b.dir/flags.make' has modification time 41 s in the future
Building CXX object CMakeFiles/cmTC_42a1b.dir/feature_tests.cxx.o
/usr/bin/c++     -std=c++11 -o CMakeFiles/cmTC_42a1b.dir/feature_tests.cxx.o -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_42a1b
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_42a1b.dir/link.txt --verbose=1
/usr/bin/c++        CMakeFiles/cmTC_42a1b.dir/feature_tests.cxx.o  -o cmTC_42a1b 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++98] compiler features compiled with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_1b396/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_1b396.dir/build.make CMakeFiles/cmTC_1b396.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_1b396.dir/flags.make' has modification time 41 s in the future
Building CXX object CMakeFiles/cmTC_1b396.dir/feature_tests.cxx.o
/usr/bin/c++     -std=c++98 -o CMakeFiles/cmTC_1b396.dir/feature_tests.cxx.o -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_1b396
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1b396.dir/link.txt --verbose=1
/usr/bin/c++        CMakeFiles/cmTC_1b396.dir/feature_tests.cxx.o  -o cmTC_1b396 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:0cxx_alias_templates
    Feature record: CXX_FEATURE:0cxx_alignas
    Feature record: CXX_FEATURE:0cxx_alignof
    Feature record: CXX_FEATURE:0cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:0cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:0cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:0cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:0cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:0cxx_default_function_template_args
    Feature record: CXX_FEATURE:0cxx_defaulted_functions
    Feature record: CXX_FEATURE:0cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:0cxx_delegating_constructors
    Feature record: CXX_FEATURE:0cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:0cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:0cxx_explicit_conversions
    Feature record: CXX_FEATURE:0cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:0cxx_extern_templates
    Feature record: CXX_FEATURE:0cxx_final
    Feature record: CXX_FEATURE:0cxx_func_identifier
    Feature record: CXX_FEATURE:0cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:0cxx_inheriting_constructors
    Feature record: CXX_FEATURE:0cxx_inline_namespaces
    Feature record: CXX_FEATURE:0cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:0cxx_local_type_template_args
    Feature record: CXX_FEATURE:0cxx_long_long_type
    Feature record: CXX_FEATURE:0cxx_noexcept
    Feature record: CXX_FEATURE:0cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:0cxx_nullptr
    Feature record: CXX_FEATURE:0cxx_override
    Feature record: CXX_FEATURE:0cxx_range_for
    Feature record: CXX_FEATURE:0cxx_raw_string_literals
    Feature record: CXX_FEATURE:0cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:0cxx_right_angle_brackets
    Feature record: CXX_FEATURE:0cxx_rvalue_references
    Feature record: CXX_FEATURE:0cxx_sizeof_member
    Feature record: CXX_FEATURE:0cxx_static_assert
    Feature record: CXX_FEATURE:0cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:0cxx_thread_local
    Feature record: CXX_FEATURE:0cxx_trailing_return_types
    Feature record: CXX_FEATURE:0cxx_unicode_literals
    Feature record: CXX_FEATURE:0cxx_uniform_initialization
    Feature record: CXX_FEATURE:0cxx_unrestricted_unions
    Feature record: CXX_FEATURE:0cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:0cxx_variadic_macros
    Feature record: CXX_FEATURE:0cxx_variadic_templates
Determining if the include file pthread.h exists passed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_a25c8/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_a25c8.dir/build.make CMakeFiles/cmTC_a25c8.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_a25c8.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_a25c8.dir/CheckIncludeFile.c.o
/usr/bin/cc     -o CMakeFiles/cmTC_a25c8.dir/CheckIncludeFile.c.o   -c /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_a25c8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a25c8.dir/link.txt --verbose=1
/usr/bin/cc       CMakeFiles/cmTC_a25c8.dir/CheckIncludeFile.c.o  -o cmTC_a25c8 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


Determining if the function pthread_create exists in the pthread passed with the following output:
Change Dir: /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f6edd/fast"
make: Warning: File 'Makefile' has modification time 41 s in the future
/usr/bin/make -f CMakeFiles/cmTC_f6edd.dir/build.make CMakeFiles/cmTC_f6edd.dir/build
make[1]: Entering directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make[1]: Warning: File 'CMakeFiles/cmTC_f6edd.dir/flags.make' has modification time 41 s in the future
Building C object CMakeFiles/cmTC_f6edd.dir/CheckFunctionExists.c.o
/usr/bin/cc    -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_f6edd.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.5/Modules/CheckFunctionExists.c
Linking C executable cmTC_f6edd
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f6edd.dir/link.txt --verbose=1
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_f6edd.dir/CheckFunctionExists.c.o  -o cmTC_f6edd -lpthread 
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make[1]: Leaving directory '/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/CMakeTmp'
make: warning:  Clock skew detected.  Your build may be incomplete.


