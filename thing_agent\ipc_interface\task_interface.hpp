#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
// #include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/task.pb.h"
#include "share/pb/idl/sys_interface.pb.h"

#include "setting/setting.hpp"

class task_interface
{
public:

    int init(zmq::context_t &ctx);

    int get_barcode(sorting_task_msg &task_msg);

    int get_sorting_task_state(sorting_task_state_msg &task_state);

    int issue_sorting_task(sorting_task_msg &task);

    static task_interface *get_instance(void)
    {
        static task_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *goods_info_recver;       //feeder上报码值
    zmq::socket_t *task_state_transfer;     //调度上报状态
    zmq::socket_t *task_issuer;

};
