#include "container_interface.hpp"

int container_interface::init(zmq::context_t &ctx)
{
    container_seal_cmd_sender = new zmq::socket_t {ctx, zmq::socket_type::pub};
    container_seal_cmd_sender -> connect(TOPIC_CONTAINER_SEAL_CMD);

    container_thingtalk_seal_cmd = new zmq::socket_t {ctx, zmq::socket_type::pub};
    container_thingtalk_seal_cmd -> connect(TOPIC_CONTAINER_SEAL_SEND);

	container_seal_state_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	container_seal_state_recver -> connect(TOPIC_CONTAINER_SEAL_STATE);
	container_seal_state_recver -> set(zmq::sockopt::subscribe, "");

    container_color_sender = new zmq::socket_t {ctx, zmq::socket_type::pub};
	container_color_sender -> connect(TOPIC_CONTAINER_ACT);

    container_shelf_cmd = new zmq::socket_t {ctx, zmq::socket_type::pub};
    container_shelf_cmd -> connect(TOPIC_SHELFS_LOCK_CMD);

	container_state_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	container_state_recver -> connect(TOPIC_CONTAINER_INFO);
	container_state_recver -> set(zmq::sockopt::subscribe, "");

	box_state_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	box_state_recver -> connect(TOPIC_CONTAINER_FULL_STATE);
	box_state_recver -> set(zmq::sockopt::subscribe, "");

    shelf_lock_state_recv = new zmq::socket_t {ctx, zmq::socket_type::sub};
	shelf_lock_state_recv -> connect(TOPIC_SHELFS_LOCK_STATE);
	shelf_lock_state_recv -> set(zmq::sockopt::subscribe, "");

	return 0;
}

int container_interface::issue_container_color_control(led_info &led_cmd, bool need_log)
{
    uint8_t pub_msg[led_info_size];
	pb_ostream_t stream_out;

    if (need_log)
        SPDLOG_DEBUG("container_interface issue led control: [{}]-[{}]-[{}]", led_cmd.id, led_cmd.color, led_cmd.flash_freq);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, led_info_fields, &led_cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	container_color_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;
}

int container_interface::get_rfid_state(box_info_multiple &containers_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    container_state_recver->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, box_info_multiple_fields, &containers_state))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
        if (setting::get_instance()->get_setting().mobile_shelf_version)
        {
            for (int i = 0; i < containers_state.boxes_count; i++)
            {
                auto &c = containers_state.boxes[i];
                if (c.box_st == box_state_BIND)
                    SPDLOG_DEBUG("get container {} group state [{}]-[{}]", c.box_id, c.box_st, c.RFID);
                else
                    SPDLOG_DEBUG("get container {} group state [{}]", c.box_id, c.box_st);
            }
        }
        else
        {
            SPDLOG_DEBUG("container_interface get {} containers state---------------", containers_state.boxes_count);
            for (int i = 0; i < containers_state.boxes_count; i++)
            {
                auto &c = containers_state.boxes[i];
                if (c.box_st == box_state_BIND)
                    SPDLOG_DEBUG("get container {} state [{}]-[{}]", c.box_id, c.box_st, c.RFID);
                else
                    SPDLOG_DEBUG("get container {} state [{}]", c.box_id, c.box_st);
            }
        }

        return 1;
    }

    return 0;
}

int container_interface::get_shelf_lock_state(shelves_state &shelf_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    shelf_lock_state_recv->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, shelves_state_fields, &shelf_state))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
        SPDLOG_DEBUG("get shelf_lock {}, state {}", shelf_state.shelves_group, shelf_state.state);

        return 1;
    }

    return 0;

}

int container_interface::get_satr_state(slot_state &box_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    box_state_recver->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, slot_state_fields, &box_state))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
        if (box_state.st == state_FULL)
    	    SPDLOG_DEBUG("container_interface get No.{} box full---------------", box_state.id);
        else if (box_state.st == state_NORMAL)
            SPDLOG_DEBUG("container_interface get No.{} box empty---------------", box_state.id);
        else if (box_state.st == state_RASTER_TRIGGERED)
            SPDLOG_DEBUG("container_interface get No.{} box raster triggered---------------", box_state.id);
        else
            SPDLOG_DEBUG("container_interface get No.{} box wrong state---------------", box_state.id);
        return 1;
    }

    return 0;
}

int container_interface::get_seal_state(container_seal_state_single &seal_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    container_seal_state_recver->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, container_seal_state_single_fields, &seal_state))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
        if (seal_state.seal_state == container_seal_state_IDLE)
    	    SPDLOG_DEBUG("container_interface get No.{} container idle---------------", seal_state.container_id);
        else if (seal_state.seal_state == container_seal_state_SEAL)
            SPDLOG_DEBUG("container_interface get No.{} container SEAL---------------", seal_state.container_id);
        else if (seal_state.seal_state == container_seal_state_CONTAIN)
            SPDLOG_DEBUG("container_interface get No.{} container contain---------------", seal_state.container_id);
        else if (seal_state.seal_state == container_seal_state_UNKNOWN)
            SPDLOG_DEBUG("container_interface get No.{} container disable---------------", seal_state.container_id);
        else
            SPDLOG_DEBUG("container_interface get No.{} box wrong state---------------", seal_state.container_id);
        return 1;
    }

    return 0;
}

int container_interface::issue_container_seal_control(const uint32_t &id)
{
    uint8_t pub_msg[container_seal_cmd_size];
	pb_ostream_t stream_out;

    container_seal_cmd cmd = {id};

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, container_seal_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	container_seal_cmd_sender->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 1;
}
int container_interface::issue_container_seal_cmd(container_seal_state_single &state)
{
    uint8_t pub_msg[container_seal_state_single_size];
	pb_ostream_t stream_out;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, container_seal_state_single_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	container_thingtalk_seal_cmd->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
    SPDLOG_DEBUG("container{} thingtalk seal send state{}", state.container_id,state.seal_state);
	return 1;
}

int container_interface::issue_shelf_lock(uint32_t shelf_no)
{
    uint8_t pub_msg[shelves_cmd_size];
	pb_ostream_t stream_out;

    shelves_cmd cmd;
    cmd.shelves_grout = shelf_no;
    cmd.cmd = shelves_cmd_tab_LOCK;
    
    SPDLOG_DEBUG("container_interface issue shelf_lock id:{}", shelf_no);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, shelves_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	container_shelf_cmd->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;

}

int container_interface::issue_shelf_unlock(uint32_t shelf_no)
{
    uint8_t pub_msg[shelves_cmd_size];
	pb_ostream_t stream_out;

    shelves_cmd cmd;
    cmd.shelves_grout = shelf_no;
    cmd.cmd = shelves_cmd_tab_UNLOCK;
    
    SPDLOG_DEBUG("container_interface issue shelf_unlock id:{}", shelf_no);

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, shelves_cmd_fields, &cmd))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
	}
	container_shelf_cmd->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

	return 0;

}