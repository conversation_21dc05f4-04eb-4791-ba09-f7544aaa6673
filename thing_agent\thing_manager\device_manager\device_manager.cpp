#include "device_manager.hpp"
#include "ipc_interface/vehicle_interface.hpp"

int device_manager::init(zmq::context_t &ctx)
{
    container_interface::get_instance()->init(ctx);
    vehicle_interface::get_instance()->init(ctx);
    plane_interface::get_instance()->init(ctx);
    feeder_interface::get_instance()->init(ctx);

    init_container();
    SPDLOG_DEBUG("init container done");

    init_feeder();
    SPDLOG_DEBUG("init feeder done");

    // init_plane();
    // SPDLOG_DEBUG("init plane done");

    init_vehicle();
    SPDLOG_DEBUG("init vehicle done");

    feeder_state_timer.start();
    // plane_state_timer.start();
    vehicle_state_timer.start();
    vehicle_ext_state_timer.start();

    return 0;
}

bool device_manager::is_device_init_done()
{
    component_state st;
    bool ret = true;
    // get_plane_sys_state(st);
    // if (st == fsm_state_UNKNOW)
    //     ret = false;

    get_feeder_sys_state(st);
    if (st == component_state_C_UNKNOW)
        ret = false;

    get_vehicle_sys_state(st);
    if (st == component_state_C_UNKNOW)
        ret = false;

    if (!ret)
        return ret;

//初始化完成，开始各个模块心跳计时

    vehicle_ext_state_timer.start();

    return true;
}

bool device_manager::is_device_idle()
{
    component_state st;

    get_vehicle_sys_state(st);
    if (st == component_state_C_INIT || st == component_state_C_RUNNING || st == component_state_C_UNKNOW)
        return false;

    return true;
}

// bool device_manager::is_emgbt_triggered()
// {
//     std::unique_lock<std::mutex> lock(feeder_mutex);
//     for (auto &fd: feeder_devs)
//     {
//         if (fd.get_button_state(key_id_KEY_EMERG) == key_evt_type_EVENT_PRESSED)
//             return true;
//     }
//     lock.unlock();
//     std::lock_guard<std::mutex> lck(plane_mutex);
//     if (plane_dev.get_emgbt_state() == plane::TRIGGERED)
//         return true;

//     return false;
// }

int device_manager::init_container()
{
    create_containers();
    SPDLOG_DEBUG("create {} containers done, hospice container id: {}", con_devs.size(), hospice_con_id);

    return 0;
}

int device_manager::create_containers()
{
    zmq::context_t context{1};
	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;
    data_map data;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);
	strncpy(request.key, "map", sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_DEBUG("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t reply;
    pb_istream_t stream_in;
    socket.recv(reply, zmq::recv_flags::none);
    stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
    if (!pb_decode(&stream_in, data_map_fields, &data))
    {
        SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
        return -1;
    }

	socket.disconnect(SERVICE_DATA_ACCESS);

    for (int i = 0; i < data.containers.container_count; i++)
    {
        container c(data.containers.container[i].id, data.containers.container[i].type);

        con_devs.emplace(data.containers.container[i].id, c);
        if (data.containers.container[i].type == data_map_container_type_HOSPICE || data.containers.container[i].type == data_map_container_type_FEEDER)
            hospice_con_id = data.containers.container[i].id;
    }

    return con_devs.size();
}

//get_all = false  只获取状态改变的格口
int device_manager::get_container_rfid(box_info_multiple &sts, bool get_all)
{
    sts.boxes_count = 0;
    std::lock_guard<std::mutex> lock(container_mutex);
    if (get_all)
    {
        for (auto &con: con_devs)
            if (con.second.con.has_rfid())
                sts.boxes[sts.boxes_count++] = con.second.con.get_rfid_state();
        if (sts.boxes_count > 0)
            return 1;
    }
    else
    {
        for (auto &con: con_devs)
            if (con.second.rfid_changed)
            {
                sts.boxes[sts.boxes_count++] = con.second.con.get_rfid_state();
                con.second.rfid_changed = false;
            }
        if (sts.boxes_count > 0)
            return 1;
    }

    return 0;
}

//get_all = false  只获取状态改变的格口
int device_manager::get_container_satr(std::vector<slot_state> &sts, bool get_all)
{
    std::lock_guard<std::mutex> lock(container_mutex);
    if (get_all)
    {
        for (auto &con: con_devs)
            if (con.second.con.has_satr())
                sts.emplace_back(con.second.con.get_satr_state());
        if (sts.size() > 0)
            return 1;
    }
    else
    {
        for (auto &con: con_devs)
            if (con.second.satr_changed)
            {
                sts.emplace_back(con.second.con.get_satr_state());
                con.second.satr_changed = false;
            }
        if (sts.size() > 0)
            return 1;
    }

    return 0;
}
bool device_manager::is_container_full(const uint32_t &id)
{
    std::lock_guard<std::mutex> lock(container_mutex);
    slot_state state;
    for (auto &con: con_devs)
    {
        state = con.second.con.get_satr_state();
        if((state.id == id)&&(state.st == state_FULL))
            return true;
        else if((state.id == id)&&(state.st != state_FULL))
            return false;
    }
    return false;
}
int device_manager::get_container_list(std::vector<uint32_t> &con_list)
{
    std::lock_guard<std::mutex> lock(container_mutex);
    for (auto &con: con_devs)
        con_list.emplace_back(con.first);

    return con_list.size();
}

int device_manager::get_hospice_con_id()
{
    return hospice_con_id;
}

int device_manager::issue_container_contain(const uint32_t &id)
{
    if (con_devs.count(id))
        return container_interface::get_instance()->issue_container_seal_control(id);
    else
        return 0;
}
int device_manager::issue_thingtalk_container_state(container_seal_state_single &state)
{
    if (con_devs.count(state.container_id))
        return container_interface::get_instance()->issue_container_seal_cmd(state);
    else
        return 0;
}

int device_manager::issue_lamp_state(led_info &color)
{
    container_interface::get_instance()->issue_container_color_control(color);

    return 0;
}

int device_manager::issue_shelf_lock(uint32_t shelf_no)
{
    return container_interface::get_instance()->issue_shelf_lock(shelf_no);
}

int device_manager::issue_shelf_unlock(uint32_t shelf_no)
{
    return container_interface::get_instance()->issue_shelf_unlock(shelf_no);
}

void device_manager::update_con_rfid_state()
{
    while (true)
    {
        box_info_multiple rfid_states;
        if (container_interface::get_instance()->get_rfid_state(rfid_states))
        {
            std::lock_guard<std::mutex> lock(container_mutex);
            for (int i = 0; i < rfid_states.boxes_count; i++)
            {
                auto st = rfid_states.boxes[i];
                if (!con_devs[st.box_id].con.has_rfid())
                {
                    con_devs[st.box_id].con.init_rfid_data(st);
                    con_devs[st.box_id].rfid_changed = true;
                    continue;
                }

                if (st.box_st == box_state_BIND)
                    con_devs[st.box_id].con.set_bind(st.RFID);
                else if (st.box_st == box_state_UNBIND)
                    con_devs[st.box_id].con.set_unbind();
                else
                    continue;

                con_devs[st.box_id].rfid_changed = true;
            }
        }
    }
}

void device_manager::update_con_satr_state()
{
    while (true)
    {
        slot_state satr_state;
        if (container_interface::get_instance()->get_satr_state(satr_state))
        {
            std::lock_guard<std::mutex> lock(container_mutex);
            if (con_devs[satr_state.id].con.has_satr())
            {
                if (satr_state.st == state_FULL)
                    con_devs[satr_state.id].con.set_full();
                else if (satr_state.st == state_NORMAL)
                    con_devs[satr_state.id].con.set_empty();
                else if (satr_state.st == state_RASTER_TRIGGERED)
                    con_devs[satr_state.id].con.set_raster_trigger();
                else
                    continue;
            }
            else
            {
                con_devs[satr_state.id].con.init_satr_data(satr_state);
            }

            con_devs[satr_state.id].satr_changed = true;
        }
    }
}

void device_manager::update_con_seal_state()
{
    while (true)
    {
        container_seal_state_single seal_state;
        if (container_interface::get_instance()->get_seal_state(seal_state))
        {
            std::lock_guard<std::mutex> lock(container_mutex);
            if (con_devs.count(seal_state.container_id))
            {
                //auto state_temp = con_devs[seal_state.container_id].con.get_seal_state();
                con_devs[seal_state.container_id].con.set_seal_state(seal_state.seal_state);

                SPDLOG_DEBUG("container {} seal state change to {}", seal_state.container_id, seal_state.seal_state);
                report_container_seal_state(seal_state);
            }
            else
                SPDLOG_DEBUG("recv wrong container {} seal state {}", seal_state.container_id, seal_state.seal_state);
        }
    }
}

int device_manager::run_container()
{
    container_rfid_thread = new std::thread(&device_manager::update_con_rfid_state, this);
    container_satr_thread = new std::thread(&device_manager::update_con_satr_state, this);
    container_test_thread = new std::thread(&device_manager::test_container_lamp, this);
    container_seal_thread = new std::thread(&device_manager::update_con_seal_state, this);

    return 0;
}

void device_manager::test_container_lamp()
{
    while (true)
    {
        if (!is_container_testing)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            continue;
        }

        static int color = COLOR_LIGHT_OFF;
        if (++color == COLOR_CYAN)
            color = COLOR_LIGHT_OFF;

        for (auto &con: con_devs)       //todo::待格口能够自行配置是否具有灯控功能时，需改为全部格口下发,此处暂时用是否兜底格口判断
        {
            if ((con.second.con.has_rfid() || con.second.con.has_satr()) && is_container_testing)
            {
                led_info cmd;
                cmd.id = con.first;
                cmd.color = color;
                cmd.flash_freq = NO_FLASH;
                container_interface::get_instance()->issue_container_color_control(cmd, false);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
}

int device_manager::issue_container_lamp_test_start()
{
    if (!is_container_testing)
    {
        SPDLOG_DEBUG("container start test");
        is_container_testing = true;
    }
    else
        SPDLOG_DEBUG("container already testing");

    return 0;
}

int device_manager::issue_container_lamp_test_stop()
{
    if (is_container_testing)
    {
        SPDLOG_DEBUG("container stop test");
        is_container_testing = false;
    }
    else
        SPDLOG_DEBUG("container not in testing");

    return 0;
}

int device_manager::init_feeder()
{

    return 0;
}

int device_manager::get_button_state(const int &feeder_id, std::map<key_id, key_evt_type> &state)
{
    std::lock_guard<std::mutex> lock(feeder_mutex);
    auto f = get_feeder(feeder_id);
    if (f != nullptr)
        state = (*f).get_button_state();
    else
        SPDLOG_ERROR("wrong feeder id: {}", feeder_id);

    return 0;
}

int device_manager::get_feeder_state(const int &feeder_id, feeder_dev_state_total &state)
{
    std::lock_guard<std::mutex> lock(feeder_mutex);
    auto f = get_feeder(feeder_id);
    if (f != nullptr)
        state = (*f).get_feeder_state();
    else
        SPDLOG_ERROR("wrong feeder id: {}", feeder_id);
    
 //   SPDLOG_INFO("get feeder state : {} seq:{}  exc:{}",state.ready_state,state.sequence,state.excp_handle);    
    return 0;
}
bool device_manager::get_feeder_belt_state_changed(const int &feeder_id)
{
    bool result = false;
    std::lock_guard<std::mutex> lock(feeder_mutex);
    auto f = get_feeder(feeder_id);
    if (f != nullptr)
       result = (*f).get_belt_status_changed();
    else
        SPDLOG_ERROR("wrong feeder id: {}", feeder_id);
    return result;
}
bool device_manager::set_feeder_belt_state_changed(const int &feeder_id,bool value)
{
    bool result = false;
    std::lock_guard<std::mutex> lock(feeder_mutex);
    auto f = get_feeder(feeder_id);
    if (f != nullptr)
       (*f).set_belt_status_changed(value);
    else
        SPDLOG_ERROR("wrong feeder id: {}", feeder_id);
    return result;
}         
int device_manager::get_feeder_sys_state(component_state &st)
{
    std::vector<component_state> feeder_sys_states;
    std::lock_guard<std::mutex> lock(feeder_mutex);
    if (feeder_devs.size() == 0)
    {
        st = component_state_C_UNKNOW;
        return 0;
    }
    for (auto &f: feeder_devs)
        feeder_sys_states.emplace_back(f.get_feeder_state().state);

    if (std::count(feeder_sys_states.begin(), feeder_sys_states.end(), component_state_C_ERR) != 0)
        st = component_state_C_ERR;
    else if (std::count(feeder_sys_states.begin(), feeder_sys_states.end(), component_state_C_RUNNING) != 0)
        st = component_state_C_RUNNING;
    else if (std::count(feeder_sys_states.begin(), feeder_sys_states.end(), component_state_C_IDLE) != 0)
        st = component_state_C_IDLE;
    else if (std::count(feeder_sys_states.begin(), feeder_sys_states.end(), component_state_C_INIT) != 0)
        st = component_state_C_INIT;
    else
        st = component_state_C_UNKNOW;

    return 0;
}

int device_manager::get_feeder_ids(std::vector<int> &ids)
{
    std::lock_guard<std::mutex> lock(feeder_mutex);
    for (auto &f: feeder_devs)
        ids.emplace_back(f.get_id());

    return ids.size();
}

// bool device_manager::is_emerg()
// {
//     bool result = false;
//     std::unique_lock<std::mutex> lock(feeder_mutex);
//     for (auto &fd: feeder_devs)
//     {
//         if (fd.get_button_state(key_id_KEY_EMERG) == key_evt_type_EVENT_PRESSED)
//             result = true;
//     }
//     lock.unlock();

//     std::lock_guard<std::mutex> lck(plane_mutex);
//     if (plane_dev.get_door_state() == plane::TRIGGERED)
//         result = true;

//     if (plane_dev.get_emgbt_state() == plane::TRIGGERED)
//         result = true;

//     if (result)
//         set_sys_state_key_stop();

//     return result;
// }

bool device_manager::is_feeder_sys_key_start()
{
    for (auto &fd: feeder_devs)
    {
        if (fd.get_sys_state_key() == key_id_KEY_START)
            return true;
           
    }

    return false;
}

bool device_manager::is_feeder_sys_key_stop()
{
    bool ret = true;
    for (auto &fd: feeder_devs)
    {
        if (fd.get_sys_state_key() != key_id_KEY_STOP)
            ret = false;
    }

    return ret;
}

void device_manager::set_sys_key_state(feeder *fdr, key_event &key)      //外部加锁
{
    if ((key.evt_type == key_evt_type_EVENT_RELEASE) || (key.key != key_id_KEY_EMERG && key.key != key_id_KEY_STOP && key.key != key_id_KEY_START && key.key != key_id_KEY_RESERVE))
        return;

    auto old_key = fdr->get_sys_state_key();

    if (fdr->get_button_state(key_id_KEY_EMERG) == key_evt_type_EVENT_PRESSED)
    {
        fdr->set_sys_state_key(key_id_KEY_EMERG);
        return;
    }

    if (key.key == key_id_KEY_STOP)
        fdr->set_sys_state_key(key_id_KEY_STOP);    
    else if (key.key == key_id_KEY_START)
        fdr->set_sys_state_key(key_id_KEY_START);
    else if(key.key == key_id_KEY_RESERVE)
    {
        if(setting::get_instance()->get_setting().device_version == "gsbank")   //工商银行对接输送线
            fdr->set_sys_state_key(key_id_KEY_STOP);
    }
    if (old_key != fdr->get_sys_state_key())
        SPDLOG_DEBUG("feeder {} sys state key changed:{}->{}", fdr->get_id(), old_key, fdr->get_sys_state_key());
}

void device_manager::update_fdr_button()
{
    while (true)
    {
        key_event bt;
        if (feeder_interface::get_instance()->get_button_state(bt))
        {
            std::lock_guard<std::mutex> lock(feeder_mutex);
            auto fd = get_feeder(bt.dev_id);
            if (fd)
            {
                (*fd).set_button_state(static_cast<key_id>(bt.key), bt.evt_type);
                set_sys_key_state(fd, bt);
            }
        }
    }
}

feeder *device_manager::get_feeder(const uint32_t &id)      //调用函数外部需加锁
{
    for (auto &f: feeder_devs)
    {
        if (f.get_feeder_state().feeder_id == id)
            return &f;
    }

    return nullptr;
}

void device_manager::update_fdr_state()
{
    while (true)
    {
        feeder_dev_state_total state;
        feeder_dev_state_total old_state = state;
        if (feeder_interface::get_instance()->get_feeder_state(state))
        {
            feeder_state_timer.start();
            std::lock_guard<std::mutex> lock(feeder_mutex);
            auto fd = get_feeder(state.feeder_id); 
            if (fd)
            {
                old_state =(*fd).get_feeder_state();
                (*fd).set_state(state);
                if(old_state.ready_state != state.ready_state)
                    (*fd).set_belt_status_changed(true);
            }

            else
            {
                feeder f(state.feeder_id);
                f.set_state(state);
                feeder_devs.emplace_back(f);
                f.set_belt_status_changed(true);
            }
            
        }
    }
}

int device_manager::issue_feeder_belt_cmd(uint32_t &feeder_id, uint32_t &dev_id, int32_t &speed)
{
    return feeder_interface::get_instance()->issue_belt_speed(feeder_id, dev_id, speed);
}

int device_manager::issue_feeder_scan(uint32_t &feeder_id)
{
    return feeder_interface::get_instance()->issue_feeder_scan(feeder_id);
}

int device_manager::issue_work_status_set(int &status)
{
    feeder_interface::get_instance()->issue_work_status_set(status);
    if(status== DEVICE_WORK_STATUS_SET_STOP)
    {
        device_manager::get_instance()->set_sys_state_key_stop();
        
    }else if(status == DEVICE_WORK_STATUS_SET_START)
    {
        device_manager::get_instance()->set_sys_state_key_start();
    }
    SPDLOG_DEBUG("set work status:{}", status);

    return 0;
}

// int device_manager::issue_exception_handle_set(uint32_t &value)
// {
//     return feeder_interface::get_instance()->issue_exception_handle_set(value);
// }

int device_manager::issue_control_feeder_rotate_belt_forward(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    return feeder_interface::get_instance()->issue_control_feeder_rotate_belt_forward(feeder_id, speed, limit);
}

int device_manager::issue_control_feeder_rotate_belt_backward(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    return feeder_interface::get_instance()->issue_control_feeder_rotate_belt_backward(feeder_id, speed, limit);
}

int device_manager::issue_control_feeder_rotate_belt_stop(uint32_t &feeder_id, uint32_t speed, uint32_t limit)
{
    return feeder_interface::get_instance()->issue_control_feeder_rotate_belt_stop(feeder_id, speed, limit);
}


void device_manager::set_sys_state_key_stop()
{
    std::lock_guard<std::mutex> lock(feeder_mutex);
    for (auto &f: feeder_devs)
        f.set_sys_state_key(key_id_KEY_STOP);
}
void device_manager::set_sys_state_key_start()
{
    std::lock_guard<std::mutex> lock(feeder_mutex);
    for (auto &f: feeder_devs)
    {
        f.set_sys_state_key(key_id_KEY_START);
        SPDLOG_DEBUG("set sys state key start......");
    }

}
int device_manager::run_feeder()
{
    feeder_button_thread = new std::thread(&device_manager::update_fdr_button, this);
    feeder_state_thread = new std::thread(&device_manager::update_fdr_state, this);

    return 0;
}

int device_manager::init_plane()
{

    return 0;
}

// int device_manager::get_switcher_state(const int &id, switch_state_single &state)
// {
//     std::lock_guard<std::mutex> lck(plane_mutex);
//     state = plane_dev.get_switcher_state(id);
//     return 0;
// }

// int device_manager::get_switcher_state(std::unordered_map<int, switch_state_single> &states)
// {
//     std::lock_guard<std::mutex> lck(plane_mutex);
//     states = plane_dev.get_switchers_state();
//     return 0;
// }


// int device_manager::get_plane_sys_state(fsm_state &st)
// {
//     st = plane_sys_state;
//     return 0;
// }

// int device_manager::issue_switcher_open(int &id)
// {
//     return plane_interface::get_instance()->issue_switch_open(id);
// }

// int device_manager::issue_switcher_close(int &id)
// {
//     return plane_interface::get_instance()->issue_switch_close(id);
// }

// int device_manager::issue_switcher_zero_set(int &id)
// {
//     return plane_interface::get_instance()->issue_set_switch_zero(id);
// }

int device_manager::issue_buzzer_on()
{
    return plane_interface::get_instance()->issue_buzzer_on();
}

int device_manager::issue_buzzer_off()
{
    return plane_interface::get_instance()->issue_buzzer_off();
}


int device_manager::issue_hmi_lamp_on()
{
    return plane_interface::get_instance()->issue_led_on();
}

int device_manager::issue_hmi_lamp_off()
{
    return plane_interface::get_instance()->issue_led_off();
}

// void device_manager::update_sw_state()
// {
//     switch_state_multiple state;
//     if (plane_interface::get_instance()->get_switch_state(state))
//     {
//         plane_state_timer.start();
//         plane_sys_state = state.state;
//         std::lock_guard<std::mutex> l(plane_mutex);
//         for (int i = 0; i < state.switches_count; i++)
//         {
//             plane_dev.add_switcher(state.switches[i]);
//             SPDLOG_DEBUG("add switcher: {}", state.switches[i].switch_id);
//         }
//     }

//     while (true)
//     {
//         switch_state_multiple state;
//         if (plane_interface::get_instance()->get_switch_state(state))
//         {
//             plane_state_timer.start();
//             if (plane_sys_state != state.state)
//                 SPDLOG_DEBUG("plane agent fsm state changed:{}->{}", plane_sys_state, state.state);
//             plane_sys_state = state.state;
//             std::lock_guard<std::mutex> l(plane_mutex);
//             for (int i = 0; i < state.switches_count; i++)
//             {
//                 plane_dev.set_sw_state(state.switches[i]);
//             }
//         }
//     }
// }

// void device_manager::update_emerg_dev_state()
// {
//     while (true)
//     {
//         plane_event_multiple emerg_event;
//         plane_interface::get_instance()->get_emerg_dev_state(emerg_event);
//         std::lock_guard<std::mutex> l(plane_mutex);

//         for (int i = 0; i < emerg_event.events_count; i++)
//         {
//             plane_dev.set_emerg_dev_state(emerg_event.events[i]);
//         }
//     }
// }

int device_manager::run_plane()
{
    // switcher_state_thread = new std::thread(&device_manager::update_sw_state, this);
    // safty_door_thread = new std::thread(&device_manager::update_emerg_dev_state, this);

    return 0;
}

int device_manager::create_vehicles()
{
    vehicle_devs.clear();

    train_basic_info_mutilp st;
    vehicle_interface::get_instance()->get_vehicle_info(st);
    for(int i = 0; i < st.train_info_count; i++)
    {
        auto vl = st.train_info[i];
        vehicle vh(vl.train_id);
        vehicle_devs.emplace(vl.train_id, vh);
    }
    
    SPDLOG_DEBUG("create {} vehicles", vehicle_devs.size());

    return vehicle_devs.size();
}

int device_manager::init_vehicle()
{
    return create_vehicles();
}

int device_manager::get_vehicle_state(std::unordered_map<int, vehicle_total_state> &st)
{
    std::lock_guard<std::mutex> l(vehicle_mutex);
    for (auto &vh: vehicle_devs)
    {
        SPDLOG_INFO("vh.first:{}-{}-{}", vh.first, vh.second.get_running_state().carriage_count, vh.second.get_running_state().basic_info.ip);
        if (vh.second.get_running_state().basic_info.state == true)
        {
            vehicle_total_state t;
            // t.info = vh.second.get_info();
            t.running_state = vh.second.get_running_state();
            st.emplace(vh.first, t);
        }
    }
    SPDLOG_INFO("rt.size:{}", st.size());
    return st.size();
}

int device_manager::get_vehicle_state(std::unordered_map<int, vehicle_total_state> &st, uint32_t &id)
{
    std::lock_guard<std::mutex> l(vehicle_mutex);
    for (auto &vh: vehicle_devs)
    {
        if (vh.first == (int)id)
        {
            vehicle_total_state t;
            // t.info = vh.second.get_info();
            t.running_state = vh.second.get_running_state();
            st.emplace(vh.first, t);

            break;
        }
    }

    return st.size();
}

int device_manager::get_vehicle_sys_state(component_state &st)
{
    st = vehicle_sys_state;
    return 0;
}

int device_manager::register_vehicle(uint32_t &id)
{
    sys_state_interface::get_instance()->issue_vehicle_register_cmd(id);
    // std::lock_guard<std::mutex> l(vehicle_mutex);
    // if (vehicle_devs.count(id) == 0)
    //     SPDLOG_ERROR("vehicle {} is not exist", id);
    // else
    //     vehicle_devs[id].set_online();

    return 0;
}

int device_manager::unregister_vehicle(uint32_t &id)
{
    sys_state_interface::get_instance()->issue_vehicle_unregister_cmd(id);
    // sys_manager::get_instance()->remove_vehicle_exception(id);
    // std::lock_guard<std::mutex> l(vehicle_mutex);
    // vehicle_devs[id].set_offline();

    return 0;
}

int device_manager::reset_vehicle(uint32_t &id)
{
    return vehicle_interface::get_instance()->issue_vehicle_reset_cmd(id);
}

int device_manager::issue_vehicle_to_feeder(uint32_t &vehicle_id, uint32_t level_speed, uint32_t turn_speed, int feeder_id)
{
    return sys_state_interface::get_instance()->issue_vehicle_to_feeder(vehicle_id, level_speed, turn_speed, feeder_id);
}

int device_manager::issue_vehicle_to_grayscale_camera(uint32_t &vehicle_id, uint32_t camera_id, uint32_t level_speed, uint32_t turn_speed)
{
    return sys_state_interface::get_instance()->issue_vehicle_to_camera(vehicle_id, camera_id, level_speed, turn_speed);
}

int device_manager::issue_vehicle_to_slot(uint32_t &vehicle_id, uint32_t slot_id, uint32_t level_speed, uint32_t turn_speed)
{
    return sys_state_interface::get_instance()->issue_vehicle_to_slot(vehicle_id, slot_id, level_speed, turn_speed);
}

// int device_manager::issue_vehicle_to_unregister_point(uint32_t &vehicle_id)
// {
//     return debug_interface::get_instance()->issue_vehicle_to_unregister_point(vehicle_id);
// }

// int device_manager::issue_vehicle_target_position(uint32_t &vehicle_id, int32_t position_x, int32_t position_y, int32_t position_z)
// {
//     return debug_interface::get_instance()->issue_vehicle_target_position(vehicle_id, position_x, position_y, position_z);
// }

int device_manager::issue_vehicle_move_forward(uint32_t &vehicle_id, uint32_t &length, uint32_t level_speed, uint32_t turn_speed)
{
    return sys_state_interface::get_instance()->issue_vehicle_move_forward(vehicle_id, length, level_speed, turn_speed);
}

int device_manager::issue_vehicle_belt_forward_control(uint32_t &vehicle_id, uint32_t &carriage_id)
{
    return vehicle_interface::get_instance()->issue_vehicle_belt_left_moving(vehicle_id, carriage_id);
}

int device_manager::issue_vehicle_belt_backward_control(uint32_t &vehicle_id, uint32_t &carriage_id)
{
    return vehicle_interface::get_instance()->issue_vehicle_belt_right_moving(vehicle_id, carriage_id);
}

int device_manager::issue_vehicle_belt_stop(uint32_t &vehicle_id, uint32_t &carriage_id)
{
    return vehicle_interface::get_instance()->issue_vehicle_belt_stop(vehicle_id, carriage_id);
}

int device_manager::issue_carriage_control_y_move(std::string &dev_id, uint16_t distance, int dir, uint16_t speed)
{

    if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_HIGHEST)
        return vehicle_interface::get_instance()->issue_carriage_y_move_control_highest(dev_id);
    else if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_MINIMUM)
        return vehicle_interface::get_instance()->issue_carriage_y_move_control_minimum(dev_id);
    else if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_MIDDLE)
        return vehicle_interface::get_instance()->issue_carriage_y_move_control_middle(dev_id);
    else if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_SPECIFIED_POS)
        return vehicle_interface::get_instance()->issue_carriage_y_move_control_pos(dev_id, distance);

    return 0;
}

int device_manager::issue_carriage_control_y_zero_calibration(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_carriage_y_zero_calibration_control(dev_id);
}

int device_manager::issue_carriage_control_belt_rotate(std::string &dev_id, uint16_t distance, uint16_t speed, int dir)
{
    uint32_t train_id, carriage_id;

    // vehicle_interface::get_instance()->platform_string_to_int(dev_id, train_id, carriage_id);
    vehicle_interface::get_instance()->carriage_string_to_int(dev_id, train_id, carriage_id);
    
    if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_BACKWARD)
        return vehicle_interface::get_instance()->issue_vehicle_belt_right_moving(train_id, carriage_id);
    else if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_FORWARD)
        return vehicle_interface::get_instance()->issue_vehicle_belt_left_moving(train_id, carriage_id);
    else if(dir == FUNC_DIRECTION_DISPATCH_DIRECTION_STOP)
        return vehicle_interface::get_instance()->issue_vehicle_belt_stop(train_id, carriage_id);

    return 0;
}

int device_manager::issue_carriage_control_belt_zeor_calibration(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_belt_zeor_calibration_control(dev_id);
}

int device_manager::issue_carriage_control_dump_truck_rotate(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_control_dump_truck_rotate(dev_id);
}

int device_manager::issue_carriage_control_dump_truck_zero_calibration(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_control_dump_truck_zero_calibration(dev_id);
}

int device_manager::issue_platform_command_reset_control(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_control_command_reset(dev_id);
}

int device_manager::issue_platform_command_enable_control(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_control_command_enable(dev_id);
}

int device_manager::issue_platform_command_disable_control(std::string &dev_id)
{
    return vehicle_interface::get_instance()->issue_platform_control_command_disable(dev_id);
}


// void device_manager::update_vh_state()
// {
//     while (true)
//     {
//         train_state st;
//         if (vehicle_interface::get_instance()->get_vehicle_state(st))
//         {
//             std::lock_guard<std::mutex> l(vehicle_mutex);
//             vehicle_devs[st.train_id].set_state(st);
//         }
//     }
// }

void device_manager::update_vh_ext_state()
{
    while (true)
    {
        train_ext_state_multi st;
        if (vehicle_interface::get_instance()->get_vehicle_extstate(st))
        {
            SPDLOG_INFO("get vehicle ext state, count:{}, train_id:{}, carriage_id:{}, platform_id:{}", st.trains_count, st.trains[0].train_id, st.trains[0].carriages[0].carriage_id, st.trains[0].carriages[0].platforms[0].platform_id);
            vehicle_ext_state_timer.start();
            std::lock_guard<std::mutex> l(vehicle_mutex);
            for(int i = 0; i < st.trains_count; i++)
            {
                vehicle_devs[st.trains[i].train_id].set_state(st.trains[i], st.trains_count);
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void device_manager::update_vh_sys_state()
{
    while (true)
    {
        train_agent_state st;
        if (vehicle_interface::get_instance()->get_vehicle_agent_state(st))
        {
            vehicle_state_timer.start();
            vehicle_sys_state = st.train_agent_work_state;
        }
    }
}

int device_manager::run_vehicle()
{
    // vehicle_state_thread = new std::thread(&device_manager::update_vh_state, this);
    vehicle_ext_state_thread = new std::thread(&device_manager::update_vh_ext_state, this);
    vehicle_agent_state_thread = new std::thread(&device_manager::update_vh_sys_state, this);

    return 0;
}

int device_manager::run_detection()
{
    sub_sys_state_thread = new std::thread(&device_manager::detect_sub_sys_state, this);

    return 0;
}

void device_manager::detect_sub_sys_state()
{
    while (true)
    {
        // auto plane_timer = plane_state_timer.execute_time();
        auto feeder_timer = feeder_state_timer.execute_time();
        // auto vehicle_timer = vehicle_state_timer.execute_time();
        auto vehicle_extimer = vehicle_ext_state_timer.execute_time();
        // if (plane_timer > DEVICE_HEARTBEAT_TIME_LIMIT && plane_timer < 200000 && !is_plane_sys_heartbeat_error)
        // {
        //     is_plane_sys_heartbeat_error = true;
        //     SPDLOG_DEBUG("plane agent heartbeat overtime: {}", plane_timer);
        //     report_dev_heartbeat_overtime(dev_except::plane_sys_heartbeat_overtime(), exception_state_STATE_OCCURED);
        // }
        // else if (plane_timer <= DEVICE_HEARTBEAT_TIME_LIMIT && is_plane_sys_heartbeat_error)
        // {
        //     is_plane_sys_heartbeat_error = false;
        //     report_dev_heartbeat_overtime(dev_except::plane_sys_heartbeat_overtime(), exception_state_STATE_RESET);
        // }

        if (feeder_timer > DEVICE_HEARTBEAT_TIME_LIMIT && feeder_timer < 200000  && !is_feeder_sys_heartbeat_error)
        {
            is_feeder_sys_heartbeat_error = true;
            SPDLOG_DEBUG("feeder agent heartbeat overtime: {}", feeder_timer);
            report_dev_heartbeat_overtime(dev_except::feeder_sys_heartbeat_overtime(), exception_state_STATE_OCCURED);
        }
        else if (feeder_timer <= DEVICE_HEARTBEAT_TIME_LIMIT && is_feeder_sys_heartbeat_error)
        {
            is_feeder_sys_heartbeat_error = false;
            report_dev_heartbeat_overtime(dev_except::feeder_sys_heartbeat_overtime(), exception_state_STATE_RESET);
        }

        // if (vehicle_timer > DEVICE_HEARTBEAT_TIME_LIMIT && vehicle_timer < 200000 && !is_vehicle_sys_heartbeat_error)
        // {
        //     is_vehicle_sys_heartbeat_error = true;
        //     SPDLOG_DEBUG("vehicle agent heartbeat overtime: {}", vehicle_timer);
        //     report_dev_heartbeat_overtime(dev_except::vehicle_sys_heartbeat_overtime(), exception_state_STATE_OCCURED);
        // }
        // else if (vehicle_timer <= DEVICE_HEARTBEAT_TIME_LIMIT && is_vehicle_sys_heartbeat_error)
        // {
        //     is_vehicle_sys_heartbeat_error = false;
        //     report_dev_heartbeat_overtime(dev_except::vehicle_sys_heartbeat_overtime(), exception_state_STATE_RESET);
        // }

        if (vehicle_extimer > DEVICE_HEARTBEAT_TIME_LIMIT && vehicle_extimer < 200000 && !is_scheduler_heartbeat_error)
        {
            is_scheduler_heartbeat_error = true;
            SPDLOG_DEBUG("scheduler heartbeat overtime: {}", vehicle_extimer);
            report_dev_heartbeat_overtime(dev_except::scheduler_sys_heartbeat_overtime(), exception_state_STATE_OCCURED);
        }
        else if (vehicle_extimer <= DEVICE_HEARTBEAT_TIME_LIMIT && is_scheduler_heartbeat_error)
        {
            is_scheduler_heartbeat_error = false;
            report_dev_heartbeat_overtime(dev_except::scheduler_sys_heartbeat_overtime(), exception_state_STATE_RESET);
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
}

int device_manager::run()
{
    run_container();
    SPDLOG_DEBUG("container thread run");
    run_feeder();
    SPDLOG_DEBUG("feeder thread run");
    // run_plane();
    // SPDLOG_DEBUG("plane thread run");
    run_vehicle();
    SPDLOG_DEBUG("vehicle thread run");
    run_detection();

    return 0;
}

int device_manager::report_dev_heartbeat_overtime(const except_info &exp_info, exception_state st)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag;
	e.evt_except.except = exp_info;

    if (st == exception_state_STATE_OCCURED)
    {
    	if (exceptions.except_occur(e.evt_except.except) > 0)
        {
            e.evt_except.except.state = exception_state_STATE_OCCURED;
            return exception_interface::get_instance()->report_exception(e);
        }
    }
    else
    {
        if (exceptions.except_reset(e.evt_except.except) > 0)
        {
            e.evt_except.except.state = exception_state_STATE_RESET;
            return exception_interface::get_instance()->report_exception(e);
        }
    }

	return 0;
}
