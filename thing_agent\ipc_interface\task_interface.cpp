#include "task_interface.hpp"

int task_interface::init(zmq::context_t &ctx)
{	
    if (setting::get_instance()->get_setting().production_mode == 0)
    {
        goods_info_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
        goods_info_recver -> connect(TOPIC_FEEDER_GOODS);
        goods_info_recver -> set(zmq::sockopt::subscribe, "");

        task_issuer = new zmq::socket_t {ctx, zmq::socket_type::req};
        task_issuer -> connect(SERVICE_TASK);
    }

    task_state_transfer = new zmq::socket_t {ctx, zmq::socket_type::sub};
    task_state_transfer -> connect(TOPIC_TASK_STATE);
    task_state_transfer -> set(zmq::sockopt::subscribe, "");

    return 0;
}

int task_interface::get_barcode(sorting_task_msg &task_msg)
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	goods_info_recver->recv(msg, zmq::recv_flags::none);

	stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
	if (!pb_decode(&stream_in, sorting_task_msg_fields, &task_msg))
	{
		SPDLOG_WARN("goods info decode failed");
	}
	else
	{
        SPDLOG_DEBUG("get feeder {} goods:{}, task_id:{}, vol:{}_{}_{}, weight:{}", task_msg.dev_id, task_msg.gd_codes,
            task_msg.task_id, task_msg.vol.height, task_msg.vol.width, task_msg.vol.length, task_msg.weight);

        return 1;
    }

	return 0;
}

int task_interface::get_sorting_task_state(sorting_task_state_msg &task_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    task_state_transfer->recv(msg, zmq::recv_flags::none);

    stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
    if (!pb_decode(&stream_in, sorting_task_state_msg_fields, &task_state))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
    }
    else
    {
	    SPDLOG_DEBUG("get task_state codes:{}, state:{}, taskid:{}, train_id:{}, platform_id:{}, conid:{}", task_state.gd_codes, task_state.state,
            task_state.task_id, task_state.train_id, task_state.platform_id, task_state.container);
        return 1;
    }

    return 0;
}

int task_interface::issue_sorting_task(sorting_task_msg &task)
{
    pb_ostream_t stream_out;
    uint8_t req_msg[1024];
    zmq::message_t zmq_reply;

    SPDLOG_DEBUG("issue task to feeder {}, taskid:{}, conid:{}", task.dev_id, task.task_id, task.container);
    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, sorting_task_msg_fields, &task))
    {
        SPDLOG_WARN("task msg encode failed");
        return -1;
    }
    else
    {
        task_issuer->send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
    }

	task_issuer->recv(zmq_reply);

    return 0;
}
