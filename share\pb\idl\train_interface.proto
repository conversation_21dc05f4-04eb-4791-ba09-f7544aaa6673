syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";
import "sys_interface.proto";

 //protoc --nanopb_out=. train_interface.proto

//状态相关
enum train_dev_state
{
    DEV_RESERVE = 0;
	DEV_INIT = 1;
    DEV_NORMAL = 2;
    DEV_ERROR = 3;
	DEV_FATAL = 4;
	DEV_UNKNOWN = 5;
	
}

enum train_work_state
{
    WORK_RESERVE = 0;
    WORK_INIT = 1;
    WORK_CHECK = 2;
	WORK_IDLE = 3;
	WORK_WORK = 4;
	WORK_CALIB = 5;
	WORK_ERROR = 6;
	WORK_FATAL = 7;
}



enum task_type
{
	TASK_NULL = 0;
    MOVE_POS = 1;
    MOVE_SPEED = 2;
    Y_MOVE = 3;
	CARRIAGE_SHIFT_IN = 4;
	CARRIAGE_SHIFT_OUT = 5;
	//CARRIAGE_BELT_SET_ZERO = 6;
	MOVE_POS_CALIB = 7;
	TASK_INTE	= 8;
	TASK_BELT_ROLL_BACK = 9;
	TASK_BELT_STOP = 10;
}


enum task_state
{
	IDLE = 0;
	INIT = 1;
    START = 2;
    RUNNING = 3;
    SUCCEED_OVER = 4;
    ERROR = 5;
};


message carriage_state 
{
    uint32 carriage_id = 1;
	
	train_dev_state y_axis_encoder_state = 2;			//Y轴电机状态 正常/异常/未知
	int32 y_axis_encoder_value = 3;						//Y轴电机编码器数值
	uint32 y_axis_encoder_err_no = 4;					//Y轴电机故障码
	
	train_dev_state load_platform_state = 5;			//载货台电机状态 正常/异常/未知
	bool load_platform_belt_zero_state = 6;				//载货台皮带寻零标志
	int32 load_platform_encoder_value = 7;				// 
	uint32 load_platform_err_no = 8;					//载货台故障码
	
	bool carriage_load_state = 9;						//载货台带载状态
	task_type carriage_task = 10;						//车厢组执行任务类型
	task_state carriage_task_state = 11;				//车厢组执行任务状态
}


message train_state 
{
	//设备基础信息	
	uint32 train_id = 1;								//车头ID
	uint32 carriage_cnt = 2;							//车厢数量	
	train_dev_state train_state = 3;					//车头设备状态
	//component_state train_agent_work_state = 4;			//车头运行状态
	uint32 train_error_no = 5;							//车头故障码
	
	//供电系统状态
	bool power_electricity_state = 6; 					//动力电标志
	float power_electricity_volt = 7;					//动力电电压
	float power_electricity_current = 8;				//动力电电流
	
	float control_electricity_volt = 9;					//控制电电压
	float control_electricity_current = 10;				//控制电电流
	
	//车头运动状态
	uint32 motion_positon = 11;							//车辆坐标
	bool motion_positon_valid_flag = 12;				//车辆位置有效标志
	int32 motion_velocity = 14;							//车辆速度
	uint32 calib_sensor_value = 15;						//位置检测开关触发标志
	
	//车头运动电机状态
	train_dev_state motor_state = 16;					//行走电机状态
	uint32 motor_encoder_value = 17;					//行走电机编码器数值
	uint32 motor_error_no = 18;							//行走电机故障码
	
	bool dev_comm_ack_state = 19;						//通信ACK标志
	train_work_state work_state = 20;					//车头组工作状态
	
	int32 current_mileage = 21;
	int32 encoder_mileage = 22;
	
	repeated carriage_state carriage_st = 23 [(nanopb).max_count = 16];	//车厢工作状态
	
	
}



message train_agent_state 
{
	//设备基础信息	
	uint32 train_id = 1;								//车头ID
	uint32 carriage_cnt = 2;							//车厢数量	
	train_dev_state train_state = 3;					//车头设备状态
	component_state train_agent_work_state = 4;			//车头运行状态
	train_work_state work_state = 5;					//车头组工作状态
	uint32 train_error_no = 6;							//车头故障码
}


//命令相关

message task_move
{
	task_type type = 1;
	uint32 start = 2;
	uint32 target = 3;
	uint32 speed_limit = 4;
}

message task_lifting
{
	task_type type = 1;
	uint32 target = 2;		//Y轴目的位置
	uint32 lift_speed = 3;
	uint32 lift_acc = 4;
}
enum shift_dir
{
    SHIFT_LEFT = 0;
    SHIFT_RIGHT = 1;
}


enum shift_type
{
    TAKE_LOAD  = 0;
    UNLOAD = 1;
	DROP_OUT = 2;
}

message task_shift
{
	task_type type = 1;
	shift_dir dir = 2;
	uint32 speed = 3;
	uint32 target_pos = 4;	  //执行下包时X轴行走位置
	uint32 target_y_pos = 5;  //执行下包时Y轴位置，用于二次确认
}

//皮带反转及停止的命令，反转命令下 speed 和 target_limit 有意义，停止命令下speed 和target_limit均为0
message task_belt
{
	task_type type = 1;
	shift_dir dir = 2;
	uint32 target_pos = 3;
	uint32 speed = 4;			//反转速度
	uint32 target_limit = 5;	//反转距离
}

enum dev_cmd_tab
{
	EMERG_STOP = 0;			//急停	
	EMERG_RESET = 1;		//解除急停	

	SLEEP = 2; 				//休眠
	WAKEUP = 3;				//唤醒
	
	RESET = 4;				//复位，
	
	BELT_LEFT_MOVING = 5; 	//皮带左转
	BELT_RIGHT_MOVING = 6; 	//皮带右转
	BELT_STOP = 7; 			//皮带停止转动
	
	BELT_POS_ZERO = 8;		//皮带零点复位
	
	Y_AXIS_POS_CALIB = 9;	//Y轴电机零点校准
	
	DEV_ENABLE = 10;			//车组启用
	DEV_DISABLE = 11;		//车组整体禁用
	SUB_CARRIAGE_ENABLE = 12;   //车厢启动
	SUB_CARRIAGE_DISABLE = 13;	//车厢禁用
	SUB_PLATFOEM_ENABLE = 14;     //子设备启用
	SUB_PLATFORM_DISABLE = 15;	 //子设备禁用
	
	RESTART = 16;				//重启，
	
};


message dev_cmd
{
	dev_cmd_tab cmd = 1;
	uint32 para = 2;
}

message task_integration
{
	task_type type = 1;
	uint32 task_info_move_pos = 2;
	uint32 task_info_move_speed_limit = 3;
	bool task_lifting_valid = 4;
	uint32 task_info_y_pos = 5;
	task_shift task_info_shift = 6;
}

message mileage_info
{
	int32 current_mileage = 1;	// 行驶里程参考
    int32 encoder_mileage = 2;	// 行驶里程
}

message train_task
{
	uint32 sequence = 1;
    uint32 dev_id = 2;
	uint32 carriage_id = 3;		//carriage_id为零时，全部车厢
	oneof task
	{
    	task_move move = 4;			//小车移动
    	task_lifting lifting = 5;	//Y轴移动
		task_shift shift = 6;		//皮带移动
		dev_cmd_tab cmd= 7;
		task_integration inte_task = 8;
		uint32 hb_time_sync = 9;
		mileage_info mil_info = 10;
		task_belt roll_back = 11;

	};
};

message train_task_state
{
    uint32 dev_id = 1;
	uint32 carriage_id = 2;
    task_type type = 3;
    task_state state = 4;
}

message position_xyz
{
	int32 x = 1;
	int32 y = 2;
	int32 z = 3;
}

enum carriage_type
{
	CARRIAGE_TYPE_RESERVE = 0;			//NULL	
	CARRIAGE_TYPE_HEADSTOCK = 1;		//车头	
	CARRIAGE_TYPE_CARRIAGE = 2;		//车厢	
	CARRIAGE_TYPE_REAR = 3;			//车尾		
};

enum platform_type
{
	PLATFORM_TYPE_RESERVE = 0;			//NULL	
	PLATFORM_TYPE_BELT = 1;		//皮带版本	
	PLATFORM_TYPE_DUMP = 2;		//翻斗版本	
};

message platform_ext_state
{
	uint32 platform_id = 1;
	int32  location_z = 2;
	bool online_state = 3;
	bool load_state = 4;
	platform_type type = 5;
	
	string servo_state_no = 6 [(nanopb).max_size=16];
	train_dev_state servo_state = 7;					//行走电机状态
	uint32 servo_speed = 8;								//行走电速度
	bool platform_zero_flag = 9;

}


message carriage_ext_state
{
	uint32 carriage_id = 1;
	uint32 pos = 2;
	position_xyz pos_3d = 3;
	carriage_type type = 4;
	train_dev_state work_state = 5;
	bool online_state = 6 ;
	string firmware_version = 7 [(nanopb).max_size=16];
	bool power_flag = 8;
	string lifting_state_no = 9 [(nanopb).max_size=16];
	train_dev_state lifting_state = 10;					//行走电机状态
	uint32 lifting_speed = 11;								//行走电速度
	
	repeated platform_ext_state platforms = 12 [(nanopb).max_count = 4];
	
}

message train_ext_state_single
{
	uint32 train_id = 1;
	bool online_state = 2;
	uint32 speed = 3;
	uint32 fault_state = 4;
	string ip_addr = 5 [(nanopb).max_size=16];
	string firmware_version = 6 [(nanopb).max_size=16];
	
	string motor_state_no = 7 [(nanopb).max_size=16];
	train_dev_state motor_state = 8;					//行走电机状态
	uint32 motor_speed = 9;								//行走电速度
	
	int32  curr_mileage = 10;
	int32  encoder_mileage = 11;
	
	repeated carriage_ext_state carriages = 12 [(nanopb).max_count = 16];
	
}


message train_ext_state_multi
{	
	repeated train_ext_state_single trains = 1 [(nanopb).max_count = 10];	
}