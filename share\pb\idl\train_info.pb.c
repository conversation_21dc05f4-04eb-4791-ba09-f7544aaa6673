/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "train_info.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(platform_basic_info, platform_basic_info, AUTO)


PB_BIND(carriage_basic_info, carriage_basic_info, AUTO)


PB_BIND(train_basic_info, train_basic_info, 2)


PB_BIND(train_basic_info_mutilp, train_basic_info_mutilp, 4)


PB_BIND(train_config_para, train_config_para, AUTO)




