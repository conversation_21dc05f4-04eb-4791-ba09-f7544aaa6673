#include "vehicle_interface.hpp"

int vehicle_interface::init(zmq::context_t &ctx)
{
    // int timeout = 1000;

	vehicle_extstate_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	vehicle_extstate_recver -> connect(TOPIC_TRAIN_EXTSTATE);
	vehicle_extstate_recver -> set(zmq::sockopt::subscribe, "");

    vehicle_basic_info_recver = new zmq::socket_t {ctx, zmq::socket_type::req};
    vehicle_basic_info_recver->connect(SERVICE_DATA_ACCESS);
	// zmq_setsockopt(vehicle_basic_info_recver, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	// zmq_setsockopt(vehicle_basic_info_recver, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

	vehicle_state_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	vehicle_state_recver -> connect(TOPIC_TRAIN_STATE);
	vehicle_state_recver -> set(zmq::sockopt::subscribe, "");

	vehicle_agent_state_recver = new zmq::socket_t {ctx, zmq::socket_type::sub};
	vehicle_agent_state_recver -> connect(TOPIC_TRAIN_AGENT_STATE);
	vehicle_agent_state_recver -> set(zmq::sockopt::subscribe, "");


    vehicle_cmd_sender = new zmq::socket_t {ctx, zmq::socket_type::req};
	vehicle_cmd_sender -> connect(SERVICE_TRAIN_TASK);

	return 0;
}

int vehicle_interface::get_vehicle_extstate(train_ext_state_multi &vehicles_state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (vehicle_extstate_recver -> recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, train_ext_state_multi_fields, &vehicles_state))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
		else
		{
            // for(int i = 0; i < vehicles_state.trains_count; i ++)
            // {
            //     for(int j = 0; j < vehicles_state.trains[i].carriages_count; j++)
            //     {
            //         SPDLOG_INFO("*****123:{}-{}-{}-{}-{}", vehicles_state.trains[i].carriages[j].carriage_id, vehicles_state.trains[i].carriages[j].pos_3d.x, vehicles_state.trains[i].carriages[j].pos_3d.y, vehicles_state.trains[i].carriages[j].pos_3d.z, vehicles_state.trains[i].carriages[j].has_pos_3d);

            //     }
                
            // }

			return 1;
		}
	}

    return 0;
}

int vehicle_interface::get_vehicle_info(train_basic_info_mutilp &vehicles_info)
{
    uint8_t req_msg[32];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;

	// 首先检查当前socket的连接状态
	if( !vehicle_basic_info_recver->connected() )
	{
		SPDLOG_ERROR("vehicle_basic_info_recver zmq unconected");
	}
    
	
	// 发送REQ消息至coreserver
	strncpy(request.key, DATA_KEY_TRAIN_LIST, sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train_enable_list"), "pb encode error: {}", stream_out.errmsg);
		return false;
	}
	else
        vehicle_basic_info_recver->send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	vehicle_basic_info_recver->recv(zmq_reply, zmq::recv_flags::none);		
	if( 0 == zmq_reply.size() )
	{
		SPDLOG_ERROR("failed to get train list from coreserver.");
		return false;
	}
	
	// 收到reply，反序列进行解析
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, train_basic_info_mutilp_fields, &vehicles_info))
	{
		SPDLOG_LOGGER_DEBUG(spdlog::get("train list"), "pb decode error: {}", stream_in.errmsg);
		return false;
	}

	SPDLOG_INFO("vehicle_basic_info_recver receive msg ok");

    vehicle_basic_info_recver->disconnect(SERVICE_DATA_ACCESS);

	return true;

}


int vehicle_interface::get_vehicle_state(train_state &state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (vehicle_state_recver -> recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, train_state_fields, &state))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
		else
		{
			return 1;
		}
	}

    return 0;
}

int vehicle_interface::get_vehicle_agent_state(train_agent_state &state)
{
    zmq::message_t msg;
    pb_istream_t stream_in;

    if (vehicle_agent_state_recver -> recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, train_agent_state_fields, &state))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
		else
		{
			return 1;
		}
	}

    return 0;
}

// int vehicle_interface::get_vehicle_info(vehicle_list &vehicles_info)
// {
// 	uint8_t req_msg[32];
// 	pb_ostream_t stream_out;
// 	data_request request;

// 	SPDLOG_INFO("request vehicle list");

// 	vehicle_info_requester->connect(SERVICE_DATA_ACCESS);
// 	strncpy(request.key, "vehicle_list", sizeof(request.key));
// 	request.type = data_request_cmd_READ;
// 	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	
// 	if (!pb_encode(&stream_out, data_request_fields, &request))
// 	{
// 		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
// 		return 0;
// 	}
// 	else
// 		vehicle_info_requester->send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

// 	zmq::message_t reply;
// 	pb_istream_t stream_in;
// 	//vehicle_list data;
// 	vehicle_info_requester->recv(reply, zmq::recv_flags::none);
// 	stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
// 	if (!pb_decode(&stream_in, vehicle_list_fields, &vehicles_info))
// 	{
// 		SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
// 		return 0;
// 	}

// 	SPDLOG_DEBUG("vehicle_interface get {} vehicle info---------------", vehicles_info.vehicles_count);
// 	for(int i = 0; i < vehicles_info.vehicles_count; i++)
// 	{
// 		vehicle_basic_info vh = vehicles_info.vehicles[i];
// 		SPDLOG_DEBUG("vehicle[{}].hw_ver:{}", vh.id, vh.hw_ver);
// 		SPDLOG_DEBUG("vehicle[{}].ip:{}", vh.id, vh.ip);
// 		SPDLOG_DEBUG("vehicle[{}].state:{}", vh.id, vh.state);
// 		SPDLOG_DEBUG("vehicle[{}].sw_ver:{}", vh.id, vh.sw_ver);
// 	}

// 	vehicle_info_requester->disconnect(SERVICE_DATA_ACCESS);

// 	SPDLOG_INFO("request vehicle list done");
	
// 	return 0;
// }

int vehicle_interface::issue_vehicle_reset_cmd(uint32_t &vehicle_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;

    train_task reset_task;
    reset_task.dev_id = vehicle_id;
    reset_task.which_task = train_task_cmd_tag;
    reset_task.task.cmd = dev_cmd_tab_RESET;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}] reset---------------", vehicle_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &reset_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::issue_vehicle_standby_cmd(uint32_t &vehicle_id)
{
#if 0
	uint8_t req_msg[vehicle_task_size];
	pb_ostream_t stream_out;

    vehicle_task reset_task;
    reset_task.dev_id = vehicle_id;
    reset_task.which_task = vehicle_task_cmd_tag;
    reset_task.task.cmd.cmd = vehicle_misc_cmd_SLEEP;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}] standby---------------", vehicle_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, vehicle_task_fields, &reset_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);
    
    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

#endif
    return 0;
}


int vehicle_interface::issue_vehicle_belt_left_moving(uint32_t &vehicle_id, uint32_t &carriage_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;

    train_task belt_task;
    belt_task.dev_id = vehicle_id;
	belt_task.carriage_id = carriage_id;
    belt_task.which_task = train_task_cmd_tag;
    belt_task.task.cmd = dev_cmd_tab_BELT_LEFT_MOVING;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], belt[{}] left moving---------------", vehicle_id, carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &belt_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::issue_vehicle_belt_right_moving(uint32_t &vehicle_id, uint32_t &carriage_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;

    train_task belt_task;
    belt_task.dev_id = vehicle_id;
	belt_task.carriage_id = carriage_id;
    belt_task.which_task = train_task_cmd_tag;
    belt_task.task.cmd = dev_cmd_tab_BELT_RIGHT_MOVING;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], belt[{}] right moving---------------", vehicle_id, carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &belt_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::issue_vehicle_belt_stop(uint32_t &vehicle_id, uint32_t &carriage_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;

    train_task belt_task;
    belt_task.dev_id = vehicle_id;
	belt_task.carriage_id = carriage_id;
    belt_task.which_task = train_task_cmd_tag;
    belt_task.task.cmd = dev_cmd_tab_BELT_STOP;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], belt[{}] stop---------------", vehicle_id, carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &belt_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}


int vehicle_interface::issue_carriage_y_move_control_pos(std::string &dev_id, uint16_t distance)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task lifting_task;

	carriage_string_to_int(dev_id, lifting_task.dev_id, lifting_task.carriage_id);
    lifting_task.which_task = train_task_lifting_tag;
    lifting_task.task.lifting.type = task_type_Y_MOVE;
	lifting_task.task.lifting.target = distance;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}], distance:{} moving---------------", lifting_task.dev_id, lifting_task.carriage_id, lifting_task.task.lifting.target);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &lifting_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::issue_carriage_y_move_control_highest(std::string &dev_id)
{
    uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task lifting_task;

	carriage_string_to_int(dev_id, lifting_task.dev_id, lifting_task.carriage_id);
    lifting_task.which_task = train_task_lifting_tag;
    lifting_task.task.lifting.type = task_type_Y_MOVE;
	lifting_task.task.lifting.target = setting::get_instance()->get_setting().carriage_max_distance;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}], highest:{} moving---------------", lifting_task.dev_id, lifting_task.carriage_id, lifting_task.task.lifting.target);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &lifting_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_carriage_y_move_control_minimum(std::string &dev_id)
{
    uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task lifting_task;

	carriage_string_to_int(dev_id, lifting_task.dev_id, lifting_task.carriage_id);
    lifting_task.which_task = train_task_lifting_tag;
    lifting_task.task.lifting.type = task_type_Y_MOVE;
	lifting_task.task.lifting.target = 0;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}], minimum:{} moving---------------", lifting_task.dev_id, lifting_task.carriage_id, lifting_task.task.lifting.target);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &lifting_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_carriage_y_move_control_middle(std::string &dev_id)
{
    uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task lifting_task;

	carriage_string_to_int(dev_id, lifting_task.dev_id, lifting_task.carriage_id);
    lifting_task.which_task = train_task_lifting_tag;
    lifting_task.task.lifting.type = task_type_Y_MOVE;
	lifting_task.task.lifting.target = (setting::get_instance()->get_setting().carriage_max_distance / 2);

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}], middle:{} moving---------------", lifting_task.dev_id, lifting_task.carriage_id, lifting_task.task.lifting.target);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &lifting_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_carriage_y_zero_calibration_control(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task calibration_task;

	carriage_string_to_int(dev_id, calibration_task.dev_id, calibration_task.carriage_id);
    calibration_task.which_task = train_task_cmd_tag;
    calibration_task.task.cmd = dev_cmd_tab_Y_AXIS_POS_CALIB;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}] y_zero_calibration---------------", calibration_task.dev_id, calibration_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &calibration_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_platform_belt_zeor_calibration_control(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task belt_calibration_task;

	// platform_string_to_int(dev_id, belt_calibration_task.dev_id, belt_calibration_task.carriage_id);
    carriage_string_to_int(dev_id, belt_calibration_task.dev_id, belt_calibration_task.carriage_id);
    belt_calibration_task.which_task = train_task_cmd_tag;
    belt_calibration_task.task.cmd = dev_cmd_tab_BELT_POS_ZERO;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}] y_zero_calibration---------------", belt_calibration_task.dev_id, belt_calibration_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &belt_calibration_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_platform_control_dump_truck_rotate(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task dump_rotate_task;

    carriage_string_to_int(dev_id, dump_rotate_task.dev_id, dump_rotate_task.carriage_id);
    dump_rotate_task.which_task = train_task_shift_tag;
    dump_rotate_task.task.shift.type = task_type_CARRIAGE_SHIFT_OUT;
    dump_rotate_task.task.shift.target_pos = 0;
    dump_rotate_task.task.shift.target_y_pos = 0;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], dump truck [{}] rotate---------------", dump_rotate_task.dev_id, dump_rotate_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &dump_rotate_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_platform_control_dump_truck_zero_calibration(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task dump_calibration_task;

    carriage_string_to_int(dev_id, dump_calibration_task.dev_id, dump_calibration_task.carriage_id);
    dump_calibration_task.which_task = train_task_cmd_tag;
    dump_calibration_task.task.cmd = dev_cmd_tab_BELT_POS_ZERO;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], dump_truck [{}] calibration---------------", dump_calibration_task.dev_id, dump_calibration_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &dump_calibration_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;
}

int vehicle_interface::issue_platform_control_command_reset(std::string &dev_id)
{
	SPDLOG_INFO("vehicle_interface issue platform [{}] reset, command error---------------, dev_id");

    return 0;
}

int vehicle_interface::issue_platform_control_command_enable(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task platform_enable_task;

	// platform_string_to_int(dev_id, platform_enable_task.dev_id, platform_enable_task.carriage_id);
    carriage_string_to_int(dev_id, platform_enable_task.dev_id, platform_enable_task.carriage_id);
    platform_enable_task.which_task = train_task_cmd_tag;
    platform_enable_task.task.cmd = dev_cmd_tab_SUB_PLATFOEM_ENABLE;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}] platform_enable---------------", platform_enable_task.dev_id, platform_enable_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &platform_enable_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::issue_platform_control_command_disable(std::string &dev_id)
{
	uint8_t req_msg[train_task_size];
	pb_ostream_t stream_out;
    train_task platform_disable_task;

	// platform_string_to_int(dev_id, platform_disable_task.dev_id, platform_disable_task.carriage_id);
    carriage_string_to_int(dev_id, platform_disable_task.dev_id, platform_disable_task.carriage_id);
    platform_disable_task.which_task = train_task_cmd_tag;
    platform_disable_task.task.cmd = dev_cmd_tab_SUB_PLATFORM_DISABLE;

    SPDLOG_DEBUG("vehicle_interface issue vehicle [{}], carriage[{}] platform_disable---------------", platform_disable_task.dev_id, platform_disable_task.carriage_id);

    stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
    if (!pb_encode(&stream_out, train_task_fields, &platform_disable_task))
    {
        SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
        return -1;
    }
    else
        vehicle_cmd_sender -> send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t msg;
    vehicle_cmd_sender->recv(msg);

    return 0;

}

int vehicle_interface::platform_string_to_int(std::string &dev_id, uint32_t &train_id, uint32_t &carriage_id)
{
	std::string temp, train_string, carriage_string, platform_temp;
	uint32_t platform_id, carriage_temp, platform_cnt;

	std::stringstream ss(dev_id);
	std::getline(ss, temp, '-');
	std::getline(ss, platform_temp, '-');

	train_string = temp.substr(0, 2);
	carriage_string = temp.substr(2, 2);

	train_id = atoi(train_string.c_str());
	carriage_temp = atoi(carriage_string.c_str());
	platform_id = atoi(platform_temp.c_str());

	std::unordered_map<int, device_manager::vehicle_total_state> states;
	device_manager::get_instance()->get_vehicle_state(states, train_id);
	for (auto state = states.begin(); state != states.end(); ++state)
    {
		auto &st = state->second;
		if(state->first == (int)train_id)
		{
			for(int i = 0; i < st.running_state.carriage_count; i++)
			{
				if(st.running_state.carriage_state[i].carriage_id == carriage_temp)
				{
					platform_cnt = st.running_state.carriage_state[i].platform_count;

					carriage_id = (carriage_temp - 1) * platform_cnt + platform_id;
				}
			}
		}
	}
	
	return 1;
}

int vehicle_interface::platform_int_to_string(std::string &dev_id, uint32_t train_id, uint32_t carriage_id)
{
	uint32_t platform_id, carriage_temp, platform_cnt;

	std::unordered_map<int, device_manager::vehicle_total_state> states;
	device_manager::get_instance()->get_vehicle_state(states, train_id);
	for (auto state = states.begin(); state != states.end(); ++state)
    {
		auto &st = state->second;
		if(state->first == (int)train_id)
		{
			platform_cnt = st.running_state.carriage_state[0].platform_count;
			carriage_temp = (carriage_id + platform_cnt - 1) / platform_cnt;
			platform_id = (carriage_id + platform_cnt - 1) % platform_cnt + 1;

			std::stringstream ss;
    		ss << std::setfill('0') << std::setw(2) << train_id << std::setw(2) << carriage_temp;
			dev_id = ss.str() + "-" + std::to_string(platform_id);
		}
	}
	
	return 1;
}

int vehicle_interface::carriage_string_to_int(std::string &dev_id, uint32_t &train_id, uint32_t &carriage_id)
{
	std::string train_string, carriage_string;

	train_string = dev_id.substr(0, 2);
	carriage_string = dev_id.substr(2, 2);

	train_id = atoi(train_string.c_str());
	carriage_id = atoi(carriage_string.c_str());
	
	return 1;
}


int vehicle_interface::carriage_int_to_string(std::string &dev_id, uint32_t train_id, uint32_t carriage_id)
{
	std::stringstream ss;
	ss << std::setfill('0') << std::setw(2) << train_id << std::setw(2) << carriage_id;

	dev_id = ss.str();
	
	return 1;
}