#ifndef __TRAIN_AGENT_CONFIG_HPP__
#define __TRAIN_AGENT_CONFIG_HPP__

#include "share/nlohmann_json/json.hpp"


#include <spdlog/spdlog.h>
#include <spdlog/common.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>

#include "multi_train_manager.hpp"



using namespace std;


#define LOG_LEVEL_TRACE				("trace")
#define LOG_LEVEL_DEBUG				("debug")
#define LOG_LEVEL_INFO				("info")
#define LOG_LEVEL_WARN				("warn")
#define LOG_LEVEL_ERR				("err")
#define LOG_LEVEL_OFF				("off")




typedef struct _log_cfg
{
	spdlog::level::level_enum log_level;
}log_cfg;




extern bool train_agent_get_config(log_cfg *log_config, train_agent_cfg *dev_cfg);


#endif
