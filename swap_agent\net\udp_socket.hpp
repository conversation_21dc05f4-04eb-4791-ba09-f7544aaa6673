﻿
/**@file  udp_socket.hpp
* @brief       基于TCP的socket操作软件二次封装对应头文件
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持TCP服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* </table>
*
**********************************************************************************
*/




#ifndef __NET_UDP_SOCKET_HPP__
#define __NET_UDP_SOCKET_HPP__


#include <string>
#include <netinet/ip.h>

#include <iostream>


#define UDP_SOCKET_MIN_PORT	 (10)
#define UDP_SOCKET_MAX_PORT	 (10000)
#define UDP_SOCKET_MAX_LEN	 (20480)

#define is_UDP_SOCKET_PORT_VALID(port)				( (port>=UDP_SOCKET_MIN_PORT)&&(port<=UDP_SOCKET_MAX_PORT) )


/**
* @brief 实现TCP Socket通信的二次封装，将常用的socket操作封装于udp_socket 类中
*/

class udp_server_socket
{
	


public:

	/**@brief  udp_server_socket class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
    explicit udp_server_socket();

	/**@brief  udp_server_socket class析构函数，调用时关闭tcp服务器的文件描述符
	* @param[in]  NULL
	* @return	  NULL
	*/
    ~udp_server_socket();



	/**@brief	  TCP 通信中 Server 构造
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool udp_server_socket_init(void);
	

	/**@brief	  TCP 通信中 Server 地址及端口绑定
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server绑定失败
	* - true	  server绑定成功
	*/
	bool udp_server_socket_bind(void);
	

	/**@brief	  向TCP Server分配IP地址及端口号
	* @param[in]  const std::string &ip --- 服务器IP地址
	* @param[in]  int port --- 服务器端口号
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_server_socket_server_cfg(const std::string &ip, int _port) ;



	/**@brief	  设置TCP 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启地址 reuse
	* @ref			false 禁止地址 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_server_socket_set_reuseaddr(bool option)  ;

	
	/**@brief	  设置TCP 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启端口 reuse
	* @ref			false 禁止端口 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_server_socket_set_reuseport(bool option)  ;


	/**@brief	  获取TCP 服务器端口号
	* @param[in]  NULl
	* @return	  TCP 服务器端口号
	*/	
	unsigned int udp_server_socket_get_port() const ;	
	

	
	/**@brief	  close 套接字
	* @param[in]  int fd
	* @return	  函数执行结果
	* - false	  失败
	* - true	  成功
	*/	
	bool udp_server_socket_close(int fd );

	void udp_server_socket_close(void);


	
	/**@brief	  设置描述符为非阻塞形式
	* @param[in]  int server_fd  ---  待操作描述符
	* @return	   函数执行结果
	* - false	  失败
	* - true	  成功
	*/
	bool udp_server_socket_set_nonblocking(int sock_fd);

	bool udp_server_socket_set_nonblocking(void);


	bool udp_server_socket_recv_msg(int fd, int *msg_len, uint8_t *msg_data, struct sockaddr_in *msg_src);

	bool udp_server_socket_send_msg(int msg_len, char *msg_data, struct sockaddr_in msg_des);

	bool udp_server_socket_recv_msg(int *msg_len, uint8_t *msg_data, struct sockaddr_in *msg_src);


	/**@brief	  获取当前服务器 描述符
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int udp_server_socket_get_fd() const 
	{
		return m_server_sockfd; 
	}

	


private:


	int m_server_sockfd;	 ///< Server套接字描述符


    struct sockaddr_in m_serv_addr;  ///< Server IP 地址及端口结构体


	int m_default_processor;  ///< 

	int m_default_listener;  ///< 默认最大连接数
			
};




class udp_client_socket
{
	
public:

	/**@brief  udp_client_socket class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
    explicit udp_client_socket();

	/**@brief  udp_client_socket class析构函数，调用时关闭tcp服务器的文件描述符
	* @param[in]  NULL
	* @return	  NULL
	*/
    ~udp_client_socket();



	/**@brief	  TCP 通信中 Server 构造
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool udp_client_socket_init(void);

	bool udp_client_socket_send_msg(int msg_len, char *msg_data, struct sockaddr_in msg_des);
	
	bool udp_client_socket_send_msg(std::string msg_string, struct sockaddr_in msg_des);

	bool udp_client_socket_recv_msg(int *msg_len, char *msg_data, struct sockaddr_in *msg_src);

	bool udp_client_socket_set_nonblocking(void);


	void udp_client_socket_close(void);



	/**@brief	  获取当前服务器 描述符
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int udp_client_socket_get_fd() const 
	{
		return m_client_sockfd; 
	}


private:

	int m_client_sockfd;     ///< Client套接字描述符

};




#endif
