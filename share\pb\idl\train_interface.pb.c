/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "train_interface.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(carriage_state, carriage_state, AUTO)


PB_BIND(train_state, train_state, 2)


PB_BIND(train_agent_state, train_agent_state, AUTO)


PB_BIND(task_move, task_move, AUTO)


PB_BIND(task_lifting, task_lifting, AUTO)


PB_BIND(task_shift, task_shift, AUTO)


PB_BIND(task_belt, task_belt, AUTO)


PB_BIND(dev_cmd, dev_cmd, AUTO)


PB_BIND(task_integration, task_integration, AUTO)


PB_BIND(mileage_info, mileage_info, AUTO)


PB_BIND(train_task, train_task, AUTO)


PB_BIND(train_task_state, train_task_state, AUTO)


PB_BIND(position_xyz, position_xyz, AUTO)


PB_BIND(platform_ext_state, platform_ext_state, AUTO)


PB_BIND(carriage_ext_state, carriage_ext_state, 2)


PB_BIND(train_ext_state_single, train_ext_state_single, 4)


PB_BIND(train_ext_state_multi, train_ext_state_multi, 4)












