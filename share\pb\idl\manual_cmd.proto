syntax = "proto3";
option optimize_for = LITE_RUNTIME;
//import "nanopb.proto";

enum manual_cmd_type
{
	MOVE_TO_FEEDER   	= 0;
	MOVE_TO_SLOT     	= 1;
	MOVE_FORWARD_LIMIT  = 2;
	MOVE_CALIBRATE		= 3;
	MOVE_UP_DOWN_SLOT	= 4;
	PLATFORM_MOVE		= 5;
	MOVE_FORWARD_CONTINUOUT = 6;
}



message manual_cmd
{
	uint32 dev_id = 1;
	uint32 sub_dev_id = 2;
    manual_cmd_type cmd_type = 3;
	oneof cmd_para
	{
    	uint32 	feeder_id = 4;
		uint32 	slot_id = 5;
		uint32  forward_len = 6;
		uint32  platform_dir = 7;
		bool  move_continuout_flag = 8;
	}
}
