# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/protocol/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 protocol/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 protocol/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 protocol/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 protocol/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
protocol/CMakeFiles/lib_protocol.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 protocol/CMakeFiles/lib_protocol.dir/rule
.PHONY : protocol/CMakeFiles/lib_protocol.dir/rule

# Convenience name for target.
lib_protocol: protocol/CMakeFiles/lib_protocol.dir/rule

.PHONY : lib_protocol

# fast build rule for target.
lib_protocol/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/build
.PHONY : lib_protocol/fast

train_protocol.o: train_protocol.cpp.o

.PHONY : train_protocol.o

# target to build an object file
train_protocol.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o
.PHONY : train_protocol.cpp.o

train_protocol.i: train_protocol.cpp.i

.PHONY : train_protocol.i

# target to preprocess a source file
train_protocol.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.i
.PHONY : train_protocol.cpp.i

train_protocol.s: train_protocol.cpp.s

.PHONY : train_protocol.s

# target to generate assembly for a file
train_protocol.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f protocol/CMakeFiles/lib_protocol.dir/build.make protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.s
.PHONY : train_protocol.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... lib_protocol"
	@echo "... train_protocol.o"
	@echo "... train_protocol.i"
	@echo "... train_protocol.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

