/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 连接代理(connection agent)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   内置服务(connection agent) 释放 子设备结构体成员变量
 *
 * @param[in] in_info: 子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_sub_dev(JDThingTalkProtoSubDevice_t *in_dev)
{   
    if (in_dev != NULL) {
        if (in_dev->name != NULL) {
            jd_thingtalk_pal_free(in_dev->name);
            in_dev->name = NULL;
        }
        if (in_dev->deviceId != NULL) {
            jd_thingtalk_pal_free(in_dev->deviceId);
            in_dev->deviceId = NULL;
        }
        if (in_dev->description != NULL) {
            jd_thingtalk_pal_free(in_dev->description);
            in_dev->description = NULL;
        }
        if (in_dev->modelId != NULL) {
            jd_thingtalk_pal_free(in_dev->modelId);
            in_dev->modelId = NULL;
        }
        if (in_dev->modelVersion != NULL) {
            jd_thingtalk_pal_free(in_dev->modelVersion);
            in_dev->modelVersion = NULL;
        }
        if (in_dev->appCode != NULL) {
            jd_thingtalk_pal_free(in_dev->appCode);
            in_dev->appCode = NULL;
        }
        if (in_dev->hardwareId != NULL) {
            jd_thingtalk_pal_free(in_dev->hardwareId);
            in_dev->hardwareId = NULL;
        }
        if (in_dev->protocolName != NULL) {
            jd_thingtalk_pal_free(in_dev->protocolName);
            in_dev->protocolName = NULL;
        }
        if (in_dev->protocolSpec != NULL) {
            jd_thingtalk_pal_free(in_dev->protocolSpec);
            in_dev->protocolSpec = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   内置服务(connection agent) 拷贝 子设备结构体成员变量
 *
 * @param[in] dest_dev: 目的子设备信息结构体指针
 * @param[in] src_dev: 源子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_copy_sub_dev(JDThingTalkProtoSubDevice_t *dest_dev, JDThingTalkProtoSubDevice_t *src_dev)
{
    if ((dest_dev == NULL) || (src_dev == NULL)) {
        return -1;
    }
    if (src_dev->name != NULL) {
        dest_dev->name = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->name) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->name, src_dev->name);
    }
    if (src_dev->deviceId != NULL) {
        dest_dev->deviceId = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->deviceId) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->deviceId, src_dev->deviceId);
    }
    if (src_dev->description != NULL) {
        dest_dev->description = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->description) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->description, src_dev->description);
    }
    if (src_dev->modelId != NULL) {
        dest_dev->modelId = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->modelId) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->modelId, src_dev->modelId);
    }
    if (src_dev->modelVersion != NULL) {
        dest_dev->modelVersion = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->modelVersion) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->modelVersion, src_dev->modelVersion);
    }
    if (src_dev->appCode != NULL) {
        dest_dev->appCode = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->appCode) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->appCode, src_dev->appCode);
    }
    if (src_dev->hardwareId != NULL) {
        dest_dev->hardwareId = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->hardwareId) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->hardwareId, src_dev->hardwareId);
    }
    if (src_dev->protocolName != NULL) {
        dest_dev->protocolName = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->protocolName) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->protocolName, src_dev->protocolName);
    }
    if (src_dev->protocolSpec != NULL) {
        dest_dev->protocolSpec = (char *) jd_thingtalk_pal_malloc(
            (jd_thingtalk_pal_strlen(src_dev->protocolSpec) + 1) * sizeof(char));
        jd_thingtalk_pal_strcpy(dest_dev->protocolSpec, src_dev->protocolSpec);
    }

    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   内置服务(connection agent) 释放 子设备信息结构体成员变量
 *
 * @param[in] in_info: 子设备信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_sub_dev_info(JDThingTalkProtoSubDeviceInfo_t *in_info)
{
    int32_t ii;
    if (in_info != NULL) {
        if (in_info->subDev != NULL) {
            if (in_info->dev_num > 0) {
                for (ii = 0; ii < in_info->dev_num; ii++) {
                    if (in_info->subDev[ii] != NULL) {
                        if (in_info->subDev[ii]->name != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->name);
                            in_info->subDev[ii]->name = NULL;
                        }
                        if (in_info->subDev[ii]->deviceId != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->deviceId);
                            in_info->subDev[ii]->deviceId = NULL;
                        }
                        if (in_info->subDev[ii]->description != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->description);
                            in_info->subDev[ii]->description = NULL;
                        }
                        if (in_info->subDev[ii]->modelId != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->modelId);
                            in_info->subDev[ii]->modelId = NULL;
                        }
                        if (in_info->subDev[ii]->modelVersion != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->modelVersion);
                            in_info->subDev[ii]->modelVersion = NULL;
                        }
                        if (in_info->subDev[ii]->appCode != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->appCode);
                            in_info->subDev[ii]->appCode = NULL;
                        }
                        if (in_info->subDev[ii]->hardwareId != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->hardwareId);
                            in_info->subDev[ii]->hardwareId = NULL;
                        }
                        if (in_info->subDev[ii]->protocolName != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->protocolName);
                            in_info->subDev[ii]->protocolName = NULL;
                        }
                        if (in_info->subDev[ii]->protocolSpec != NULL) {
                            jd_thingtalk_pal_free(in_info->subDev[ii]->protocolSpec);
                            in_info->subDev[ii]->protocolSpec = NULL;
                        }
                        jd_thingtalk_pal_free(in_info->subDev[ii]);
                        in_info->subDev[ii] = NULL;
                    }
                }
            }
            jd_thingtalk_pal_free(in_info->subDev);
            in_info->subDev = NULL;    
        }
        // jd_thingtalk_pal_free(in_info);
        // in_info = NULL;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   键值对 解析 connection agent 子设备信息
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_info: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_subdevinfo(char *in_value, JDThingTalkProtoSubDeviceInfo_t *out_info)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_value || NULL == out_info) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    cJSON *pV = NULL;

    // 解析子设备信息
    int32_t iSize, iCnt;
    cJSON *pSub;
    iSize = cJSON_GetArraySize(payload);
    out_info->dev_num = iSize;
    if (iSize != 0) {
        out_info->subDev = (JDThingTalkProtoSubDevice_t **) jd_thingtalk_pal_malloc(iSize * sizeof(JDThingTalkProtoSubDevice_t *));
        jd_thingtalk_pal_memset(out_info->subDev, 0, iSize * sizeof(JDThingTalkProtoSubDevice_t *));
        for (iCnt = 0; iCnt < iSize; iCnt++) {
            pSub = cJSON_GetArrayItem(payload, iCnt);
            if (pSub == NULL) {
                continue;
            }
            out_info->subDev[iCnt] = (JDThingTalkProtoSubDevice_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoSubDevice_t));
            jd_thingtalk_pal_memset(out_info->subDev[iCnt], 0, sizeof(JDThingTalkProtoSubDevice_t));

            // 解析子设备 name
            pV = cJSON_GetObjectItem(pSub, "name");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->name == NULL) {
                    out_info->subDev[iCnt]->name = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->name, pV->valuestring);
            }

            // 解析子设备 device-id 必须要有
            pV = cJSON_GetObjectItem(pSub, "device-id");
            if (pV == NULL) {
                cJSON_Delete(payload);
                ret = JD_THINGTALK_RET_FAILED;
                goto RET;
            }
            if (out_info->subDev[iCnt]->deviceId == NULL) {
                out_info->subDev[iCnt]->deviceId = (char *) jd_thingtalk_pal_malloc(
                        (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
            }
            jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->deviceId, pV->valuestring);

            // 解析子设备 description
            pV = cJSON_GetObjectItem(pSub, "description");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->description == NULL) {
                    out_info->subDev[iCnt]->description = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->description, pV->valuestring);
            }

            // 解析子设备 thing-model-id
            pV = cJSON_GetObjectItem(pSub, "thing-model-id");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->modelId == NULL) {
                    out_info->subDev[iCnt]->modelId = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->modelId, pV->valuestring);
            }

            // 解析子设备 thing-model-id
            pV = cJSON_GetObjectItem(pSub, "thing-model-version");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->modelVersion == NULL) {
                    out_info->subDev[iCnt]->modelVersion = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->modelVersion, pV->valuestring);
            }

            // 解析子设备 app-code
            pV = cJSON_GetObjectItem(pSub, "app-code");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->appCode == NULL) {
                    out_info->subDev[iCnt]->appCode = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->appCode, pV->valuestring);
            }

            // 解析子设备 hardware-id
            pV = cJSON_GetObjectItem(pSub, "hardware-id");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->hardwareId == NULL) {
                    out_info->subDev[iCnt]->hardwareId = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->hardwareId, pV->valuestring);
            }

            // 解析子设备 protocol-name
            pV = cJSON_GetObjectItem(pSub, "protocol-name");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->protocolName == NULL) {
                    out_info->subDev[iCnt]->protocolName = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->protocolName, pV->valuestring);
            }

            // 解析子设备 protocol-spec
            pV = cJSON_GetObjectItem(pSub, "protocol-spec");
            if (pV != NULL) {
                if (out_info->subDev[iCnt]->protocolSpec == NULL) {
                    out_info->subDev[iCnt]->protocolSpec = (char *) jd_thingtalk_pal_malloc(
                            (jd_thingtalk_pal_strlen(pV->valuestring) + 1) * sizeof(char));
                }
                jd_thingtalk_pal_strcpy(out_info->subDev[iCnt]->protocolSpec, pV->valuestring);
            }
        }
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   键值对 打包 connection agent 子设备信息
 *
 * @param[in] in_info: connection agent 子设备信息
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_subdevinfo(JDThingTalkProtoSubDeviceInfo_t *in_info)
{
    if(NULL == in_info) {
        return NULL;
    }

    if ((in_info->dev_num == 0) || (in_info->subDev == NULL)) {
        return NULL;
    }

    int32_t ii;
    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateArray();
    if(NULL == root){
        goto RET;
    }

    cJSON   *subDev = NULL;
    for (ii = 0; ii < in_info->dev_num; ii ++) {
        if (in_info->subDev[ii] == NULL) {
            continue;
        }
        subDev = cJSON_CreateObject();
        if (subDev == NULL) {
            cJSON_Delete(root);
            goto RET;
        }
        // 添加子设备 name
        if (in_info->subDev[ii]->name != NULL) {
            cJSON_AddStringToObject(subDev, "name", in_info->subDev[ii]->name);
        } else {
            cJSON_AddStringToObject(subDev, "name", "");
        }

        // 添加子设备 device-id
        if (in_info->subDev[ii]->deviceId != NULL) {
            cJSON_AddStringToObject(subDev, "device-id", in_info->subDev[ii]->deviceId);
        } else {
            cJSON_AddStringToObject(subDev, "device-id", "");
        }

        // 添加子设备 description
        if (in_info->subDev[ii]->description != NULL) {
            cJSON_AddStringToObject(subDev, "description", in_info->subDev[ii]->description);
        } else {
            cJSON_AddStringToObject(subDev, "description", "");
        }

        // 添加子设备 thing-model-id
        if (in_info->subDev[ii]->modelId != NULL) {
            cJSON_AddStringToObject(subDev, "thing-model-id", in_info->subDev[ii]->modelId);
        } else {
            cJSON_AddStringToObject(subDev, "thing-model-id", "");
        }

        // 添加子设备 thing-model-version
        if (in_info->subDev[ii]->modelId != NULL) {
            cJSON_AddStringToObject(subDev, "thing-model-version", in_info->subDev[ii]->modelVersion);
        } else {
            cJSON_AddStringToObject(subDev, "thing-model-version", "");
        }

        // 添加子设备 app-code
        if (in_info->subDev[ii]->appCode != NULL) {
            cJSON_AddStringToObject(subDev, "app-code", in_info->subDev[ii]->appCode);
        } else {
            cJSON_AddStringToObject(subDev, "app-code", "");
        }

        // 添加子设备 hardware-id
        if (in_info->subDev[ii]->hardwareId != NULL) {
            cJSON_AddStringToObject(subDev, "hardware-id", in_info->subDev[ii]->hardwareId);
        } else {
            cJSON_AddStringToObject(subDev, "hardware-id", "");
        }

        // 添加子设备 protocol-name
        if (in_info->subDev[ii]->protocolName != NULL) {
            cJSON_AddStringToObject(subDev, "protocol-name", in_info->subDev[ii]->protocolName);
        } else {
            cJSON_AddStringToObject(subDev, "protocol-name", "");
        }

        // 添加子设备 protocol-spec
        if (in_info->subDev[ii]->protocolSpec != NULL) {
            cJSON_AddStringToObject(subDev, "protocol-spec",  in_info->subDev[ii]->protocolSpec);
        } else {
            cJSON_AddStringToObject(subDev, "protocol-spec",  "");
        }

        // 添加 子设备数组
        cJSON_AddItemToArray(root, subDev);
    }

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   内置服务(connection agent) 创建操作子设备方法响应
 *
 * @param[in] func_key: 操作方法的名字
 * @param[in] dev: 子设备信息指针，除get-devices外其余填NULL
 * @param[in] code: 内置方法 返回值
 * @param[in] message: 内置方法 返回信息字符串指针
 * @return 
 *    返回 函数调用响应functions成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoFuncCallResFunc_t *jd_thingtalk_proto_agent_create_opsub_func_Res(
                            char *func_key, 
                            JDThingTalkProtoSubDeviceInfo_t *dev,
                            int32_t code, 
                            char *message)
{
    JDThingTalkProtoFuncCallResFunc_t *out = NULL;
    int32_t ii;
    int32_t index = 0;
    if (dev != NULL) {
        index = 1;
    }

    // 创建响应的输出
    out = (JDThingTalkProtoFuncCallResFunc_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoFuncCallResFunc_t));
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoFuncCallResFunc_t));
    if (out->key == NULL) {
        out->key = (char *) jd_thingtalk_pal_malloc(
                (jd_thingtalk_pal_strlen(func_key) + 1) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->key, func_key);
    out->out_num = 2 + index;
    out->out = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(
        out->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out->out, 0, out->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (ii = 0; ii < out->out_num; ii++) {
        out->out[ii] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
        jd_thingtalk_pal_memset(out->out[ii], 0, sizeof(JDThingTalkProtoKeyValue_t));
    }
    if (index == 1) {
        if (out->out[0]->key == NULL) {
                out->out[0]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
        }
        jd_thingtalk_pal_strcpy(out->out[0]->key, "devices");
        out->out[0]->value = jd_thingtalk_proto_keyvalue_pack_subdevinfo(dev);
    }

    // 添加 code 和 message
    if (out->out[index+0]->key == NULL) {
        out->out[index+0]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->out[index+0]->key, "code");
    out->out[index+0]->value = jd_thingtalk_proto_keyvalue_pack_int32(code);
    if (out->out[index+1]->key == NULL) {
        out->out[index+1]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->out[index+1]->key, "message");
    out->out[index+1]->value = jd_thingtalk_proto_keyvalue_pack_string(message);
    return out;
}

/**
 * @brief   内置服务(connection agent) 创建子设备在线状态事件
 *
 * @param[in] evt_key: 事件的名字
 * @param[in] dev: 子设备信息指针
 * @return 
 *    返回 事件上报events成员结构体指针 
 * @see None.
 * @note None.
 */
JDThingTalkProtoEvtPostEvt_t *jd_thingtalk_proto_agent_create_online_event(
                            char *evt_key, 
                            JDThingTalkProtoSubDeviceInfo_t *dev)
{
    JDThingTalkProtoEvtPostEvt_t *out = NULL;
    out = (JDThingTalkProtoEvtPostEvt_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoEvtPostEvt_t));
    jd_thingtalk_pal_memset(out, 0, sizeof(JDThingTalkProtoEvtPostEvt_t));
    if (out->key == NULL) {
        out->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(evt_key) + 1) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->key, evt_key);

    // 添加 parameters
    out->param_num = 1;
    out->parameters = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out->parameters, 0, out->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    out->parameters[0] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    jd_thingtalk_pal_memset(out->parameters[0], 0, sizeof(JDThingTalkProtoKeyValue_t));
    if (out->parameters[0]->key == NULL) {
        out->parameters[0]->key = (char *) jd_thingtalk_pal_malloc(16 * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out->parameters[0]->key, "devices");
    out->parameters[0]->value = jd_thingtalk_proto_keyvalue_pack_subdevinfo(dev);
    return out;
}

// end of file
