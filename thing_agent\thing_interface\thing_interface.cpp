#include "thing_interface.hpp"
#include "thing_manager/thing_manager.hpp"

int thing_interface::init(jd_thingtalk_sdk_t *sdk)
{
    SPDLOG_DEBUG("thing_interface init");

    suresort_device.thing_model_id = setting::get_instance()->get_setting().thing_model_id;
    suresort_device.version = setting::get_instance()->get_setting().thing_model_version;
    suresort_device.sdk_device_id = setting::get_instance()->get_setting().sdk_config_device_id;

    creat_avoid_repetition_list();

    creat_synchronous_function_list();

    thing_interface::get_instance()->suresort_device.sdk = sdk;

    return 0;
}

int thing_interface::func_construct(JDThingTalkProtoFuncCallFunc_t *func, const nlohmann::json &root)
{
    if (!root.contains("key") || !root.contains("parameters") || !root["parameters"].size())
    {
        SPDLOG_ERROR("error json msg: {}", root.dump());
        return -1;
    }

    func->key = new char[std::string(root["key"]).length() + 1];
    strcpy(func->key, std::string(root["key"]).c_str());
    func->in_num = root["parameters"].size();
    func->in = new JDThingTalkProtoKeyValue_t*[func->in_num];
    int i = 0;
    for (auto &element: root["parameters"].items())
    {
        func->in[i] = new JDThingTalkProtoKeyValue_t;
        func->in[i]->key = new char[std::string(element.key()).length() + 1];
        strcpy(func->in[i]->key, element.key().c_str());
        func->in[i]->value = new char[nlohmann::to_string(element.value()).length() + 1];
        strcpy(func->in[i]->value, nlohmann::to_string(element.value()).c_str());
        i++;
    }

    return 0;
}

int thing_interface::func_deconstruct(JDThingTalkProtoFuncCallFunc_t &func)
{
    return 0;
}

#if 0
void thing_interface::zmq_func_accept()
{
    zmq::context_t *ctx = new zmq::context_t;
    zmq::socket_t *accept = new zmq::socket_t {*ctx, zmq::socket_type::rep};
    accept->bind("tcp://10.41.151.24:1111");        //todo::addr
    while(1)
    {
        zmq::message_t msg;
        accept->recv(msg);
        SPDLOG_DEBUG("{}", msg.to_string());
        zmq::message_t ack;
        accept->send(ack, zmq::send_flags::none);

        nlohmann::json root = nlohmann::json::parse(msg.to_string());
        JDThingTalkProtoFuncCallFunc_t func_tmp;

        func_construct(&func_tmp, root);
        std::vector<int> vec;
        thing_manager().get_instance()->func_issue(func_tmp, vec);
    }
}
#endif

int thing_interface::creat_avoid_repetition_list()
{//多次回ack不造成影响，防重不考虑
    function_info_init(FUNC_KEY_PALLET_TO_GRID_NOTIFY, false);
    function_info_init(FUNC_KEY_GRID_LAMP_STATUS_NOTIFY, false);
    function_info_init(FUNC_KEY_GRID_SEAL_STATUS_NOTIFY, false);
    function_info_init(FUNC_KEY_MODE_SWITCH, true);
    function_info_init(FUNC_KEY_COMMAND_DISPATCH, false);
    function_info_init(FUNC_KEY_CONTROL_DISPATCH, false);
    function_info_init(FUNC_KEY_CONTROL_DEST_DISPATCH, false);
    function_info_init(FUNC_KEY_CONTROL_WALK_DISPATCH, false);
    function_info_init(FUNC_KEY_EXECUTE_SWITCH, false);
    function_info_init(FUNC_MCU_KEY_CONTROL_DISPATCH, true);
    function_info_init(FUNC_KEY_SORTS_CONTINUATION_RUN, true);
    function_info_init(FUNC_KEY_PILOT_LAMP_STATUS_NOTIFY, true);
    function_info_init(FUNC_KEY_BUZZER_CONTROL, true);
    function_info_init(FUNC_KEY_GRID_IN_ORDER_CONTROL, true);
    function_info_init(FUNC_KEY_AUTO_CODE_SCAN, false);
    function_info_init(FUNC_KEY_FEEDER_CONTROL_DISIPATCH, false);
    function_info_init(FUNC_KEY_BELT_DIRECTION_DISPATCH, false);
    function_info_init(FUNC_KEY_ZERO_SET_DISPATCH, false);
    function_info_init(FUNC_WORK_STATUS_SET, false);
    // function_info_init(FUNC_CONFIG_SET, false);

    function_info_init(FUNC_KEY_PLATFORM_COMMAND_DISPATCH, false);
    function_info_init(FUNC_KEY_PLATFORM_CONTROL_DISPATCH, false);
    function_info_init(FUNC_KEY_SORTGROUP_COMMAND_DISPATCH, false);
    function_info_init(FUNC_KEY_SORTGROUP_CONTROL_WALK_DISPATCH, false);
    function_info_init(FUNC_KEY_SORTGROUP_SORT_COMMAND_DISPATCH, false);
    function_info_init(FUNC_SHELF_LOCK_CONTROL, true);



    SPDLOG_INFO("init {} functions to avoid repetition", avoid_repetition_function_list.size());
    return avoid_repetition_function_list.size();
}
void thing_interface::function_sync_init(std::string function_key, bool ack_nowait)
{
    function_sync_info function;
    function.function_key = function_key;
    function.ack_nowait = ack_nowait;

    function_sync_list.emplace_back(function);
}
int thing_interface::creat_synchronous_function_list()  //同步方法列表
{
    function_sync_init(FUNC_WORK_STATUS_SET, false);
    // function_sync_init(FUNC_CONFIG_SET, false);
    function_sync_init(FUNC_CONFIG_QUERY, true);
    function_sync_init(FUNC_FEEDER_BELT_STATUS_QUERY, true);

    SPDLOG_INFO("init {} functions to avoid repetition", avoid_repetition_function_list.size());
    return function_sync_list.size();
}
void thing_interface::function_info_init(std::string function_key, bool dev_unique)
{
    latest_function_info function;
    function.function_key = function_key;
    function.dev_unique = dev_unique;

    avoid_repetition_function_list.emplace_back(function);
}

thing_interface::latest_function_info* thing_interface::get_function_info(std::string key)
{
    for (auto &fun: avoid_repetition_function_list)
    {
        if (!fun.function_key.compare(key))
        {
            return &fun;
        }
    }

    return nullptr;
}

int32_t thing_interface::suresort_device_callback_connect(struct jd_thingtalk_sdk_t *sdk)
{
    thing_interface::get_instance()->suresort_device.is_connected = true;

    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::suresort_device_callback_disconnect(struct jd_thingtalk_sdk_t *sdk)
{
    // 用户可根据自身情况进行逻辑处理
    thing_interface::get_instance()->suresort_device.is_connected = false;

    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::suresort_device_callback_thingmodel_post_response(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoThingModelPostRes_t *in_res)
{
    // 对象空间(obj_name) 仅支持 "device"，建议实现时进行一下判别
    // 服务实例名，对于物模型上报响应来说，目前为空，开发者可以暂时不用进行逻辑判断
    
    /* 示例代码
     * if (in_res->code != 200) {log_error("failed")};
     */

    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::suresort_device_callback_property_set(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoPropSet_t *in_set)
{
    /*
     * 属性值 in_set->properties[0]->value 为json字符串，sdk 提供了与bool，int32_t，double和string转换的接口,
     * 其它类型数据，请自行根据定义进行解析
     * 例如：从value中解析出int32_t可调用：
     *     jd_thingtalk_proto_keyvalue_parse_int32(char *in_value, int32_t *out_val);
     * 如果想将int32_t 打包成 value 可调用：
     *     char *jd_thingtalk_proto_keyvalue_pack_int32(int32_t in_int32);
     */ 

            // log_info("\t111111deviceId:%s\r\n\ttimestamp:%d\r\n\tmessageId:%s\r\n\tversion:%d\r\n\tprop_num:%d\r\n\tproperties:[%s:%s]", 

            //     in_set->deviceId,
            //     in_set->timestamp,
            //     in_set->messageId,
            //     in_set->version,
            //     in_set->prop_num,
            //     in_set->properties[0]->key,
            //     in_set->properties[0]->value
            //     );
    

    // 发送 属性设置响应示例代码
    JDThingTalkProtoPropSetRes_t out_res;
    jd_thingtalk_pal_memset(&out_res, 0, sizeof(JDThingTalkProtoPropSetRes_t));
    out_res.deviceId = in_set->deviceId;
    out_res.messageId = in_set->messageId;
    out_res.timestamp = jd_thingtalk_pal_time(nullptr);
    out_res.code = 200;
    out_res.version = in_set->version;
    out_res.prop_num = in_set->prop_num;

    out_res.properties = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(in_set->prop_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out_res.properties, 0, in_set->prop_num * sizeof(JDThingTalkProtoKeyValue_t *));
    out_res.properties[0] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    out_res.properties[0]->key = (char *) jd_thingtalk_pal_malloc (32 * sizeof(char));
    jd_thingtalk_pal_strcpy(out_res.properties[0]->key, in_set->properties[0]->key);
    out_res.properties[0]->value = (char *) jd_thingtalk_pal_malloc (32 * sizeof(char));
    jd_thingtalk_pal_strcpy(out_res.properties[0]->value, in_set->properties[0]->value);


    thing_manager::get_instance()->properties_issue((in_set->properties));
  

    jd_thingtalk_sdk_dev_prop_set_response(sdk, obj_name, service_key, &out_res);
    

    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::suresort_device_callback_property_get(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoPropGet_t *in_get)
{
    // 处理流程及属性值(properties[0]->value) 参见属性设置的说明

    // 发送 属性设置响应示例代码
    JDThingTalkProtoPropGetRes_t out_res;
    jd_thingtalk_pal_memset(&out_res, 0, sizeof(JDThingTalkProtoPropGetRes_t));
    out_res.deviceId = in_get->deviceId;
    out_res.messageId = in_get->messageId;
    out_res.timestamp = jd_thingtalk_pal_time(nullptr);
    /*
     * out_res.properties[0]->value = jd_thingtalk_proto_keyvalue_pack_int32(200);
     * 注意内存释放问题
     */

#if 0
    out_res.prop_num = 1;
    out_res.properties = (JDThingTalkProtoKeyValue_t **) jd_thingtalk_pal_malloc(out_res.prop_num * sizeof(JDThingTalkProtoKeyValue_t *));
    jd_thingtalk_pal_memset(out_res.properties, 0, out_res.prop_num * sizeof(JDThingTalkProtoKeyValue_t *));
    out_res.properties[0] = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    out_res.properties[0]->key = (char *) jd_thingtalk_pal_malloc (32 * sizeof(char));
    jd_thingtalk_pal_strcpy(out_res.properties[0]->key, PROP_KEY);
    out_res.properties[0]->value = jd_thingtalk_proto_keyvalue_pack_bool(thing_agent::get_instance()->suresort_device.switch_button);
    
    out_res.version = thing_agent::get_instance()->suresort_device.prop_version;
    out_res.code = 200;
#endif

    out_res.prop_num = 0;
    out_res.code = 200;
    out_res.version = thing_interface::get_instance()->suresort_device.prop_version;

    jd_thingtalk_sdk_dev_prop_get_response(sdk, obj_name, service_key, &out_res);
    
    out_res.deviceId = nullptr;
    out_res.messageId = nullptr;
    jd_thingtalk_proto_free_prop_get_res(&out_res);

    return JD_THINGTALK_RET_SUCCESS;
}

int thing_interface::get_function_call_dev(int in_num, JDThingTalkProtoKeyValue_t **in, std::list<int> &dev_list)
{
    for (int i = 0; i < in_num; i++)
    {
        if (!strcmp(in[i]->key, FUNC_COMMAND_DISPATCH_KEY_SORT_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_GRID_LAMP_STATUS_INFO))
        {
            std::list<led_info> container_states;
            cjs_cvt::get_instance()->to_container_state(in[i]->value, container_states);

            for (auto &con: container_states)
                dev_list.emplace_back(con.id);
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_GRID_LAMP_STATUS_INFO))
        {
            std::list<container_seal_state_single> container_seal_states;
            cjs_cvt::get_instance()->to_container_seal_state(in[i]->value, container_seal_states);

            for (auto &con: container_seal_states)
                dev_list.emplace_back(con.container_id);
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_CONTROl_DISPATCH_KEY_SORT_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_CONTROL_DEST_DISPATCH_KEY_SORT_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_CONTROL_WALK_KEY_SORT_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_EXECUTE_SWITCH_KEY_SWITCHER_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_AUTO_CODE_SCAN_KEY_CODE_SCANNER_INFO))
        {
            std::vector<std::string> ids; std::vector<int> modes;
            cjs_cvt::get_instance()->to_scanner_info(in[i]->value, ids, modes);

            for (auto &id: ids)
                dev_list.emplace_back(atoi(id.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_CONTROL_DISPATHCH_KEY_FEEDER_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_BELT_DIRECTION_DISPATCH_KEY_SORT_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
        else if (!strcmp(in[i]->key, FUNC_ZERO_SET_DISPATCH_KEY_ORBITAL_TRANSFER_NO))
        {
            std::string dev = "";
            thing_interface::get_instance()->string_transfer(in[i]->value, dev);
            dev_list.emplace_back(atoi(dev.c_str()));
            return dev_list.size();
        }
    }

    if (dev_list.size() == 0)
    {
        SPDLOG_DEBUG("wrong function call");
    }
    return 0;
}

thing_interface::latest_info *thing_interface::get_latest_info(std::list<latest_info> &list, int dev)
{
    for (auto &info: list)
    {
        if (info.dev == dev)
        {
            return &info;
        }
    }

    return nullptr;
}

void thing_interface::function_response_ack(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call)
{
    if (strcmp(in_call->functions[0]->key, FUNC_KEY_ACKNOWLEDGE))       //不是ack时，回respose
    {
        JDThingTalkProtoFuncCallRes_t out_res;
        jd_thingtalk_pal_memset(&out_res, 0, sizeof(JDThingTalkProtoFuncCallRes_t));
        out_res.deviceId = in_call->deviceId;
        out_res.messageId = in_call->messageId;
        out_res.timestamp = thing_interface::get_instance()->get_current_local_timestamp();

        out_res.func_num = 1;
        out_res.functions = (JDThingTalkProtoFuncCallResFunc_t **) malloc(out_res.func_num * sizeof(JDThingTalkProtoFuncCallResFunc_t *));
        out_res.functions[0] = (JDThingTalkProtoFuncCallResFunc_t *) malloc(sizeof(JDThingTalkProtoFuncCallResFunc_t));
        memset(out_res.functions[0], 0, sizeof(JDThingTalkProtoFuncCallResFunc_t ));
        out_res.functions[0]->key = (char *) malloc(64 * sizeof(char));
        strcpy(out_res.functions[0]->key, in_call->functions[0]->key);
        out_res.functions[0]->out_num = 1;
        out_res.functions[0]->out = (JDThingTalkProtoKeyValue_t  **) malloc(out_res.functions[0]->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
        out_res.functions[0]->out[0] = (JDThingTalkProtoKeyValue_t *) malloc(sizeof(JDThingTalkProtoKeyValue_t));
        memset(out_res.functions[0]->out[0], 0, sizeof(JDThingTalkProtoKeyValue_t));
        out_res.functions[0]->out[0]->key = (char *) malloc(64 * sizeof(char));
        out_res.functions[0]->out[0]->value = nullptr;

        out_res.code = 200;

        strcpy(out_res.functions[0]->out[0]->key, FUNC_RESPONSE_KEY);
        char value_temp[2];
        sprintf(value_temp, "%d", FUNC_RESPONSE_SUCCESS);
        out_res.functions[0]->out[0]->value = (char*)value_temp;

        jd_thingtalk_sdk_dev_func_call_response(sdk, obj_name, service_key, &out_res);

        thing_interface::get_instance()->function_free(&out_res);
    }
    else
        return;
}

int thing_interface::function_check_validity(JDThingTalkProtoFuncCall_t *in_call)
{
    if (!strcmp(in_call->functions[0]->key, FUNC_KEY_MODE_SWITCH) || !strcmp(in_call->functions[0]->key, FUNC_KEY_CONTROL_DISPATCH))
        return 0;

    for (auto i = 0; i < in_call->func_num; i++)
        for (auto j = 0; j < in_call->functions[i]->in_num; j++)
        {
            cJSON *root = cJSON_Parse(in_call->functions[i]->in[j]->value);
            if (root->type == cJSON_NULL)
            {
                SPDLOG_ERROR("recv null value, function key:{}, in key:{}",
                    in_call->functions[i]->key, in_call->functions[i]->in[j]->key);
                return -1;
            }
        }

    return 0;
}
int thing_interface::function_check_async(JDThingTalkProtoFuncCall_t *in_call)
{
    if(function_sync_list.size() != 0)
    {
        for (auto &dev: function_sync_list)
        {
            if (!dev.function_key.compare(std::string(in_call->functions[0]->key)))
            {
                return -1;
            }
        }
    }
    return 0;
}
int thing_interface::function_check_response_nowait(JDThingTalkProtoFuncCall_t *in_call)
{
    if(function_sync_list.size() != 0)
    {
        for (auto &dev: function_sync_list)
        {
            if (!dev.function_key.compare(std::string(in_call->functions[0]->key)))   //同步方法
                if(dev.ack_nowait == true)
                {
                    return -1;
                }
        }
    }
    return 0;
}

int thing_interface::function_sync_response_handle(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call)
{
    int result = -1;
    JDThingTalkProtoFuncCallRes_t out_res;
    jd_thingtalk_pal_memset(&out_res, 0, sizeof(JDThingTalkProtoFuncCallRes_t));
    out_res.deviceId = in_call->deviceId;
    out_res.messageId = in_call->messageId;
    out_res.timestamp = thing_interface::get_instance()->get_current_local_timestamp();

    out_res.func_num = 1;
    out_res.functions = (JDThingTalkProtoFuncCallResFunc_t **) malloc(out_res.func_num * sizeof(JDThingTalkProtoFuncCallResFunc_t *));

    if(function_check_response_nowait(in_call) == -1)
    {
        out_res.code = 200;
        if(!strcmp(in_call->functions[0]->key, FUNC_CONFIG_QUERY)) 
        { 

            function_sync_response_malloc(out_res.functions,in_call->functions[0]->key,1);
            uint32_t value;
            value = setting::get_instance()->get_setting().connect_to_thing;
           // device_manager::get_instance()->get_feeder_state(0, fd_st);                          
            cjs_cvt::get_instance()->from_config_query(*out_res.functions, value); 
            SPDLOG_DEBUG("function is sync config query{} ", in_call->functions[0]->key);
            result = -1;
        }else if(!strcmp(in_call->functions[0]->key, FUNC_FEEDER_BELT_STATUS_QUERY))
        {

            function_sync_response_malloc(out_res.functions,in_call->functions[0]->key,2);
            feeder_dev_state_total fd_st;
            int feeder_id;
            if (!strcmp(in_call->functions[0]->in[0]->key, FUNC_FEEDER_BELT_STATUS_KEY_FEEDER_NO))
            cjs_cvt::get_instance()->to_enum(in_call->functions[0]->in[0]->value, feeder_id);

            device_manager::get_instance()->get_feeder_state(feeder_id, fd_st);                          
            cjs_cvt::get_instance()->from_belt_state_get(*out_res.functions, fd_st);
            SPDLOG_DEBUG("function is sync belt status query{} ", in_call->functions[0]->key);
            result = -1;
        }
        /*else if(!strcmp(in_call->functions[0]->key, FUNC_CONFIG_SET))
        {
            function_sync_response_malloc(out_res.functions,in_call->functions[0]->key,1);
            cjs_cvt::get_instance()->from_config_set_response(*out_res.functions);
            SPDLOG_DEBUG("function is sync config set{} ", in_call->functions[0]->key);
            result = 0;
        }*/
        jd_thingtalk_sdk_dev_func_call_response(sdk, obj_name, service_key, &out_res);
        thing_interface::get_instance()->function_free(&out_res);
        SPDLOG_DEBUG("function is sync success");
        
    }else
    {
        function_need_ack func;
        out_res.deviceId = NULL;
        out_res.messageId = NULL;
        if (out_res.deviceId == NULL) {
            out_res.deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(in_call->deviceId) + 2) * sizeof(char));
            jd_thingtalk_pal_strcpy(out_res.deviceId, in_call->deviceId);
        }
       
        if (out_res.messageId == NULL) {
            out_res.messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(in_call->messageId) + 2) * sizeof(char));
            jd_thingtalk_pal_strcpy(out_res.messageId, in_call->messageId);
        }
        function_sync_response_malloc(out_res.functions, in_call->functions[0]->key, 1);
        if(!strcmp(in_call->functions[0]->key, FUNC_WORK_STATUS_SET))
        {
            if (!strcmp(in_call->functions[0]->in[0]->key, FUNC_WORK_STATUS_SET_VALUE))
              //  cjs_cvt::get_instance()->to_enum(in[i]->value, status);
                cjs_cvt::get_instance()->to_enum(in_call->functions[0]->in[0]->value, func.set_value);
            strcpy(out_res.functions[0]->out[0]->key, FUNC_WORK_STATUS_SET);        
        }
        // else if(!strcmp(in_call->functions[0]->key, FUNC_CONFIG_SET))
        // {
        //     std::list<config_set_cmd> config_set;
        //     if (!strcmp(in_call->functions[0]->in[0]->key, FUNC_ONFIG_SET_KEY_CONFIGS))
        //         cjs_cvt::get_instance()->to_config_set(in_call->functions[0]->in[0]->value, config_set);

        //     func.set_value = config_set.front().set_value;           
        //     SPDLOG_DEBUG("function sync set value {} ", func.set_value);
        //     strcpy(out_res.functions[0]->out[0]->key, FUNC_CONFIG_SET);
        // }       
        func.is_ack = true;
        func.out_res = out_res;
        func.report_timer.start();       
        std::lock_guard<std::mutex> lock(function_waiting_ack_lock);
        function_waiting_ack_list.emplace_back(func);          //收到ack后再释放资源
        SPDLOG_DEBUG("function sync{} add to waiting ack from thing", in_call->functions[0]->key);
        result = 0;   
    }
    SPDLOG_DEBUG("function sync result {} ", result);
    return result;
}
/// @brief 物控下发方法防重
/// @param in_call 输入信息
/// @param devs 应该下发的设备号（目前只有格口）
/// @return -1:是重复方法或不需要继续执行方法
int thing_interface::function_avoid_duplication(JDThingTalkProtoFuncCall_t *in_call, std::vector<int> &devs)
{
    if (!strcmp(in_call->functions[0]->key, FUNC_KEY_ACKNOWLEDGE))
        return 0;

    SPDLOG_DEBUG("in_call timestamp:{}", in_call->timestamp);
    latest_function_info *fun = thing_interface::get_instance()->get_function_info(std::string(in_call->functions[0]->key));

    if (!fun)
    {
        SPDLOG_ERROR("error function key: {}", in_call->functions[0]->key);
        return 0;
    }

    if (fun->dev_unique)
    {
        if (in_call->timestamp > fun->info.timestamp)
            fun->info.timestamp = in_call->timestamp;
        else
        {
            SPDLOG_DEBUG("recv function {} with lower timestamp {}, latest timestamp {}, return", fun->function_key, in_call->timestamp, fun->info.timestamp);
            return -1;
        }
    }
    else
    {
        std::list<int> dev_list;
        thing_interface::get_instance()->get_function_call_dev(in_call->functions[0]->in_num, in_call->functions[0]->in, dev_list);
        if (dev_list.size() != 0)
        {
            for (auto &dev: dev_list)
            {
                latest_info *info = thing_interface::get_instance()->get_latest_info(fun->dev_info_list, dev);
                if (info) // 找到了
                {
                    if (in_call->timestamp > info->timestamp)
                    {
                        info->timestamp = in_call->timestamp;
                        devs.emplace_back(dev);
                    }
                    else
                    {
                        SPDLOG_DEBUG("recv function {} with lower timestamp {}, latest timestamp {}, continue", fun->function_key, in_call->timestamp, info->timestamp);
                        continue;
                    }
                }
                else // 没找到则创建
                {
                    latest_info info;
                    info.dev = dev;
                    info.timestamp = in_call->timestamp;
                    fun->dev_info_list.emplace_back(info);
                    SPDLOG_DEBUG("function {} add dev {}, timestamp {}", fun->function_key, fun->dev_info_list.back().dev, fun->dev_info_list.back().timestamp);
                    devs.emplace_back(dev);
                }
            }

            if (devs.empty())
            {
                SPDLOG_WARN("avoid repeat function to issue, return");
                return -1;
            }
        }
        else
            SPDLOG_DEBUG("dev size is 0");
    }

    return 0;
}

int thing_interface::string_transfer(const char *in, std::string &value)
{
    if (in == nullptr)
        return 0;

    cJSON *root = cJSON_Parse(in);
    value = root->valuestring;
    cJSON_Delete(root);

    return 1;
}

int32_t thing_interface::suresort_device_callback_function_call(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *in_call)
{
    // 方法调用入参及出参 参数值(in[0]->value) 与属性值相同为 json 字符串
    // int result = -1;
    // ota?
    //if ((service_key != nullptr)&&!jd_thingtalk_pal_strcmp(service_key, JD_THINGTALK_PROTO_SERVICE_OTA)) {
    //    return device_ota_process_func_call(sdk, obj_name, in_call);
    //}
    // 发送 属性设置响应示例代码
   // function_sync_info *fun = thing_interface::get_instance()->get_sync_function_info(std::string(in_call->functions[0]->key));

    if(thing_interface::get_instance()->function_check_async(in_call) == -1)   //同步方法 执行完之后再会ACK
    {
        SPDLOG_DEBUG("function is sync {} ", in_call->functions[0]->key);
       // result = thing_interface::get_instance()->function_sync_response_handle(sdk, obj_name, service_key, in_call);
       // SPDLOG_DEBUG("function sync succed {} result",result);
       // if(result == -1)
        if(thing_interface::get_instance()->function_sync_response_handle(sdk, obj_name, service_key, in_call) == -1)//已回，不需要再执行程序
            return JD_THINGTALK_RET_SUCCESS;
        
        SPDLOG_DEBUG("function sync succed {} for waiting ack to thing", in_call->functions[0]->key);
    }
    else
        thing_interface::get_instance()->function_response_ack(sdk, obj_name, service_key, in_call);//异步方法先回ack再处理


//方法下发空判断  
    if (thing_interface::get_instance()->function_check_validity(in_call) == -1)
        return JD_THINGTALK_RET_SUCCESS;

    //用于存放去除重复设备后的实际需要方法下发的设备号
    //如果放重处理之后为空，则方法不需要下发到设备
    //目前主要用于格口，防止物控一次方法下发的格口中既有需要下发设备的又有放重去掉的  其他一个方法只会下发一个设备
    std::vector<int> devs;
    if (thing_interface::get_instance()->function_avoid_duplication(in_call, devs) == -1)
        return JD_THINGTALK_RET_SUCCESS;

    // 目前默认每次只有一个函数调用 即 in_call->func_num == 1
    if (!strcmp(in_call->functions[0]->key, FUNC_KEY_ACKNOWLEDGE))
    {
        std::string event_name, message_id;
        for (int i = 0; i < in_call->functions[0]->in_num; i++)
        {
            if (!strcmp(in_call->functions[0]->in[i]->key, FUNC_ACKNOWLEDGE_KEY_EVENT_NAME))
            {
                thing_interface::get_instance()->string_transfer(in_call->functions[0]->in[i]->value, event_name);
            }
            else if (!strcmp(in_call->functions[0]->in[i]->key, FUNC_ACKNOWLEDGE_KEY_MESSAGE_ID))
            {
                thing_interface::get_instance()->string_transfer(in_call->functions[0]->in[i]->value, message_id);
            }
            else
                SPDLOG_ERROR("invalid value");
        }
        SPDLOG_DEBUG("func_acknowledge, event:{}, message_id:{}", event_name, message_id);

        thing_interface::get_instance()->remove_event_with_ack(message_id);
    }
    else
        thing_manager::get_instance()->func_issue(*(in_call->functions[0]), devs);

    return JD_THINGTALK_RET_SUCCESS;
}

int thing_interface::remove_event_with_ack(std::string message_id)
{
    std::unique_lock<std::mutex> lock(events_without_ack_lock);
    if (!events_waiting_ack_list.empty())
    {
        for (auto evt = events_waiting_ack_list.begin(); evt != events_waiting_ack_list.end();)
        {
            if (!strcmp(evt->event_post.messageId, message_id.c_str()))
            {
                event_free(&(evt->event_post));
                evt = events_waiting_ack_list.erase(evt);
                return 1;
            }
            else
                ++evt;
        }

        SPDLOG_WARN("recv ack, but event is not in waiting ack event list, message_id:{}", message_id);
    }
    else
        SPDLOG_WARN("recv ack, but waiting ack event list is empty");

    return 0;
}

int32_t thing_interface::suresort_device_callback_register_response(struct jd_thingtalk_sdk_t *sdk,
                        char *obj_name, char *service_key, JDThingTalkProtoRegReqRes_t *in_res)
{
    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::suresort_device_callback_ntp_response(struct jd_thingtalk_sdk_t *sdk, jd_thingtalk_time_stamp_t *set_time)
{
    // TODO 如果有需要，进行时钟同步

    SPDLOG_DEBUG("NTP Time Needed to be Set: {}.{}", set_time->second, set_time->ms);

    //thing_interface::get_instance()->suresort_device.is_ntp_sync = 1;

    return JD_THINGTALK_RET_SUCCESS;
}

int32_t thing_interface::device_thing_model_post(struct jd_thingtalk_sdk_t *sdk)
{
    // 物模型上报 示例代码
    JDThingTalkProtoThingModelPost_t post;
    jd_thingtalk_pal_memset(&post, 0, sizeof(JDThingTalkProtoThingModelPost_t));
    post.deviceId = const_cast<char *>(setting::get_instance()->get_setting().sdk_config_device_id.c_str());
    post.messageId = jd_thingtalk_sdk_get_messageId(nullptr);
    post.timestamp = jd_thingtalk_pal_time(nullptr);
    post.thing_model.id = const_cast<char *>(thing_interface::get_instance()->suresort_device.thing_model_id.c_str());
    post.thing_model.version = const_cast<char *>(thing_interface::get_instance()->suresort_device.version.c_str());
    jd_thingtalk_sdk_dev_thing_model_post(sdk, (char*)JD_THINGTALK_PROTO_OBJ_NAME_DEV, nullptr, &post);
    jd_thingtalk_pal_free(post.messageId);

    return JD_THINGTALK_RET_SUCCESS;
}

void thing_interface::retry_event_report_thread(void)
{
    while (true)
    {
        {
            std::lock_guard<std::mutex> lock(events_without_ack_lock);
            for (auto &event: events_waiting_ack_list)
            {
                if (event.report_timer.execute_time() > 3000)
                {
                    SPDLOG_WARN("event {} overtime 3 seconds without ack, message_id:{}", event.event_post.events[0]->key, event.event_post.messageId);
                    jd_thingtalk_sdk_dev_evt_post(thing_interface::get_instance()->suresort_device.sdk, (char*)JD_THINGTALK_PROTO_OBJ_NAME_DEV, nullptr, &event.event_post);
                    event.report_timer.start();
                    SPDLOG_INFO("resend done");
                }
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}

void thing_interface::function_sync_ack_thread(void)
{
    while (true)
    {
        {
            if(function_waiting_ack_list.size() != 0)
            {
                std::lock_guard<std::mutex> lock(function_waiting_ack_lock);
                for (auto &func:function_waiting_ack_list)
                {
                    SPDLOG_INFO("function sync status set ");
                    uint32_t result = 0; 
                    char value_temp[2];
                    if(func.report_timer.execute_time() >8*1000)  //10S超时
                    {
                        func.out_res.code = 500;
                        sprintf(value_temp, "%d", FUNC_RESPONSE_FAIL); 
                        func.is_ack = false; 
                        result = 0; 
                        SPDLOG_INFO("function sync :{} set fail",func.out_res.functions[0]->out[0]->key);     
                    }else{   //设置值和实际值一样
                        if(!strcmp(func.out_res.functions[0]->out[0]->key, FUNC_WORK_STATUS_SET))
                        {
                            sys_mode_state st;
                            sys_manager::get_instance()->get_sys_state(st);
                            if(((func.set_value == 1)&&(st.state == e_wkstate_SYS_AUTO_RUNNING))||((func.set_value == 0)&&(st.state == e_wkstate_SYS_STOP)))
                            {
                                func.out_res.code = 200;
                                func.is_ack = false;
                                sprintf(value_temp, "%d", FUNC_RESPONSE_SUCCESS);
                                SPDLOG_INFO("function sync status set success");  
                            }else
                                continue;
                        }
                        // else if(!strcmp(func.out_res.functions[0]->out[0]->key, FUNC_CONFIG_SET))
                        // {
                        //     uint32_t value;
                        //     value = setting::get_instance()->get_setting().connect_to_thing;
                        //     SPDLOG_DEBUG("get connect to thing value {} ", value);
                        //     if(((func.set_value == 1)&&(value == 0))||((func.set_value == 2)&&(value == 1)))
                        //     {
                        //         func.out_res.code = 200;
                        //         func.is_ack = false;
                        //         result = 1; 
                        //         sprintf(value_temp, "%d", FUNC_RESPONSE_SUCCESS);
                        //         SPDLOG_INFO("function sync exception set success");  
                        //     }else
                        //         continue;
                        // }
                    }
                   
                    if(!strcmp(func.out_res.functions[0]->out[0]->key, FUNC_WORK_STATUS_SET))
                    {
                        strcpy(func.out_res.functions[0]->out[0]->key, FUNC_RESPONSE_KEY);
                        func.out_res.functions[0]->out[0]->value = (char*)value_temp;                   
                    }else if(!strcmp(func.out_res.functions[0]->out[0]->key, FUNC_CONFIG_SET))
                    {
                        cjs_cvt::get_instance()->from_config_set_response(*func.out_res.functions,result);
                       // strcpy(func.out_res.functions[0]->out[0]->key, FUNC_CONFIG_QUERY_KEY_CONFIGS);
                       // func.out_res.functions[0]->out[0]->value = (char*)value_temp;
                    }

                    jd_thingtalk_sdk_dev_func_call_response(thing_interface::get_instance()->suresort_device.sdk, (char*)JD_THINGTALK_PROTO_OBJ_NAME_DEV, nullptr, &func.out_res); 
                    function_id_free(&func.out_res);
                    thing_interface::get_instance()->function_free(&func.out_res);  
                    // function_waiting_ack_list.erase(&func);  
                    //  return ;               
                }
                for (auto evt = function_waiting_ack_list.begin(); evt != function_waiting_ack_list.end();)
                {
                    if (evt->is_ack == false)
                    {
                        SPDLOG_INFO("function sync status set delete");
                        evt = function_waiting_ack_list.erase(evt);
                       // return ;
                    }
                    else
                        ++evt;
                }
            }
   
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}


void thing_interface::thing_model_post_thread(void)
{
    while (true)
    {
        if (thing_interface::get_instance()->suresort_device.is_connected == true)
        {
            thing_interface::device_thing_model_post(thing_interface::get_instance()->suresort_device.sdk);
            SPDLOG_DEBUG("thing model post done");
            thing_interface::get_instance()->suresort_device.is_model_post_done = true;
            return;
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void thing_interface::function_sync_response_malloc(JDThingTalkProtoFuncCallResFunc_t **response, const char *response_key,int32_t out_num)  //同步方法回复
{
    *response = (JDThingTalkProtoFuncCallResFunc_t *) malloc (sizeof(JDThingTalkProtoFuncCallResFunc_t));
    memset(*response, 0, sizeof(JDThingTalkProtoFuncCallResFunc_t));
    (*response)->key = (char *) malloc(64 * sizeof(char));
    strcpy((*response)->key, response_key);

    (*response)->out_num = out_num;
    (*response)->out = (JDThingTalkProtoKeyValue_t **) malloc((*response)->out_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (int i = 0; i < (*response)->out_num; i++)
    {
        (*response)->out[i] = (JDThingTalkProtoKeyValue_t *) malloc(sizeof(JDThingTalkProtoKeyValue_t));
        memset((*response)->out[i], 0, sizeof(JDThingTalkProtoKeyValue_t));
        (*response)->out[i]->key = (char *) malloc(64 * sizeof(char));
        (*response)->out[i]->value = nullptr;
    }
}

void thing_interface::event_malloc(JDThingTalkProtoEvtPostEvt_t **event, const char *event_key, int32_t param_num)
{
    *event = (JDThingTalkProtoEvtPostEvt_t *) malloc (sizeof(JDThingTalkProtoEvtPostEvt_t));
    memset(*event, 0, sizeof(JDThingTalkProtoEvtPostEvt_t));
    (*event)->key = (char *) malloc(64 * sizeof(char));
    strcpy((*event)->key, event_key);

    (*event)->param_num = param_num;
    (*event)->parameters = (JDThingTalkProtoKeyValue_t **) malloc((*event)->param_num * sizeof(JDThingTalkProtoKeyValue_t *));
    for (int i = 0; i < (*event)->param_num; i++)
    {
        (*event)->parameters[i] = (JDThingTalkProtoKeyValue_t *) malloc(sizeof(JDThingTalkProtoKeyValue_t));
        memset((*event)->parameters[i], 0, sizeof(JDThingTalkProtoKeyValue_t));
        (*event)->parameters[i]->key = (char *) malloc(64 * sizeof(char));
        (*event)->parameters[i]->value = nullptr;
    }
}

int thing_interface::send_event(JDThingTalkProtoEvtPostEvt_t &event)
{
    JDThingTalkProtoEvtPost_t evt_post;
    memset(&evt_post, 0, sizeof(JDThingTalkProtoEvtPost_t));
    evt_post.deviceId = const_cast<char *>(thing_interface::get_instance()->suresort_device.sdk_device_id.c_str());
    evt_post.evt_num = 1;
    evt_post.events = (JDThingTalkProtoEvtPostEvt_t **) malloc (evt_post.evt_num * sizeof(JDThingTalkProtoEvtPostEvt_t *));

    evt_post.events[0] = nullptr;
    evt_post.events[0] = &event;

    evt_post.messageId = nullptr;
    evt_post.messageId = jd_thingtalk_sdk_get_messageId(nullptr);
    evt_post.timestamp = get_current_local_timestamp();


    if (!strcmp(event.key, EVT_SORT_GROUP_KEY_STATUS_REPORT) || !strcmp(event.key, EVT_KEY_STATUS_REPORT)
        || !strcmp(event.key, EVT_FEEDER_KEY_STATUS_REPORT) || !strcmp(event.key, EVT_KEY_SAFETYGATE_STATE_REPORT)
        || !strcmp(event.key, EVT_FEEDER_BELT_STATUS_REPORT) || !strcmp(event.key, EVT_KEY_ELECTRICAL_CABINET_STATE_REPORT))        //心跳无ack，发送后直接释放
    {
        jd_thingtalk_sdk_dev_evt_post(thing_interface::get_instance()->suresort_device.sdk, (char*)JD_THINGTALK_PROTO_OBJ_NAME_DEV, nullptr, &evt_post);
        event_free(&evt_post);
    }
    else          //心跳无ack，其他事件加到等待回ack的队列
    {
        event_without_ack evt;
        evt.event_post = evt_post;
        evt.report_timer.start();

        std::lock_guard<std::mutex> lock(events_without_ack_lock);
        events_waiting_ack_list.emplace_back(evt);          //收到ack后再释放资源
        SPDLOG_DEBUG("add event {} for waiting ack, message id:{}", event.key, evt.event_post.messageId);

        jd_thingtalk_sdk_dev_evt_post(thing_interface::get_instance()->suresort_device.sdk, (char*)JD_THINGTALK_PROTO_OBJ_NAME_DEV, nullptr, &evt_post);
    }

    return 0;
}

int thing_interface::event_free(JDThingTalkProtoEvtPost_t *in_post)
{
    int ii;
    if (in_post != nullptr)
    {
        if (in_post->messageId != nullptr)
        {
            jd_thingtalk_pal_free(in_post->messageId);
            in_post->messageId = nullptr; 
        }
        if (in_post->events != nullptr)
        {
            for (ii = 0; ii < in_post->evt_num; ii++)
            {
                free_evt_post(in_post->events[ii]);
            }
            jd_thingtalk_pal_free(in_post->events);
            in_post->events = nullptr; 
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}
int thing_interface::function_id_free(JDThingTalkProtoFuncCallRes_t *in_res)
{
    if (in_res != nullptr)
    {
        if (in_res->messageId != nullptr)
        {
            jd_thingtalk_pal_free(in_res->messageId);     //外部无malloc
            //in_res->messageId = nullptr;
        }
        if (in_res->deviceId != nullptr)
        {
            jd_thingtalk_pal_free(in_res->deviceId);     //外部无malloc
           // in_res->deviceId = nullptr;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;    
}
int thing_interface::function_free(JDThingTalkProtoFuncCallRes_t *in_res)
{
    int ii;
    if (in_res != nullptr)
    {
        if (in_res->messageId != nullptr)
        {
            //jd_thingtalk_pal_free(in_res->messageId);     //外部无malloc
            in_res->messageId = nullptr;
        }
        if (in_res->deviceId != nullptr)
        {
            //jd_thingtalk_pal_free(in_res->deviceId);     //外部无malloc
            in_res->deviceId = nullptr;
        }
        if (in_res->functions != nullptr)
        {
            for (ii = 0; ii < in_res->func_num; ii++)
            {
                free_fun_res(in_res->functions[ii]);
            }
            jd_thingtalk_pal_free(in_res->functions);
            in_res->functions = nullptr; 
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

int thing_interface::free_evt_post(JDThingTalkProtoEvtPostEvt_t *event)
{
    if (event != nullptr)
    {
        if (event->key != nullptr)
        {
            free(event->key);
            event->key = nullptr;
        }
        for (int jj = 0; jj < event->param_num; jj++)
        {
            if (event->parameters != nullptr)
            {
                if (event->parameters[jj] != nullptr)
                {
                    if (event->parameters[jj]->key != nullptr)
                    {
                        free(event->parameters[jj]->key);
                        event->parameters[jj]->key = nullptr;
                    }
                    if (event->parameters[jj]->value != nullptr)
                    {
                        free(event->parameters[jj]->value);
                        event->parameters[jj]->value = nullptr;
                    }
                    free(event->parameters[jj]);
                    event->parameters[jj] = nullptr;
                }
            }
        }
        free(event->parameters);
        event->parameters = nullptr;
        free(event);
        event = nullptr;
    }

    return 0;
}

int thing_interface::free_fun_res(JDThingTalkProtoFuncCallResFunc_t *function)
{
    if (function != nullptr)
    {
        if (function->key != nullptr)
        {
            free(function->key);
            function->key = nullptr;
        }
        for (int jj = 0; jj < function->out_num; jj++)
        {
            if (function->out != nullptr)
            {
                if (function->out[jj] != nullptr)
                {
                    if (function->out[jj]->key != nullptr)
                    {
                        free(function->out[jj]->key);
                        function->out[jj]->key = nullptr;
                    }
#if 0
                    if (function->out[jj]->value != nullptr)
                    {
                        free(function->out[jj]->value);
                        function->out[jj]->value = nullptr;
                    }
#endif
                    free(function->out[jj]);
                    function->out[jj] = nullptr;
                }
            }
        }
        free(function->out);
        function->out = nullptr;
        free(function);
        function = nullptr;
    }

    return 0;
}

int thing_interface::run()
{
    SPDLOG_DEBUG("event thread run");

    model_post = new std::thread(&thing_interface::thing_model_post_thread, this);

    retry_event_report = new std::thread(&thing_interface::retry_event_report_thread, this);
    function_sync_handle = new std::thread(&thing_interface::function_sync_ack_thread,this);

    //new std::thread(&thing_interface::zmq_func_accept, this);

    return 0;
}
