#ifndef __JD_THINGTALK_MQTT_H__
#define __JD_THINGTALK_MQTT_H__
#include <stdbool.h>
#include "jd_thingtalk_stdint.h"


#ifdef __cplusplus
extern "C"
{
#endif
/**
 * @brief   MQTT 设置参数结构体
 */
typedef struct {
    char protocol[16]; // 协议类型： [1]tcp, [2]tls
    char *hostname;   // 服务器地址
    int32_t port;     // 服务器连接的端口号
    char *clientId;   // client Id
    char *username;   // 用户名
    char *password;   // 密码
    char *cafile;     // ca 文件路径
    char *cert;       // certificate 文件路径
    char *key;        // key 文件路径
    bool insecure;    // 是否验证主机名
} jd_thingtalk_mqtt_config_t;

/**
 * @brief   MQTT 消息结构体定义
 */
typedef struct {
    char *topic;    // 消息主题
    char *payload;  // 消息内容
    int32_t qos;    // 消息服务等级
    int32_t mid;    // 消息id
} jd_thingtalk_mqtt_msg_t;

/**
 * @brief   MQTT 客户端句柄
 */
typedef void *jd_thingtalk_mqtt_t;

/**
 * @brief   创建一个MQTT客户端
 *
 * @param[in] config: MQTT的设置参指针
 * @return 
 	MQTT客户端句柄
 * @see None.
 * @note None.
 */
jd_thingtalk_mqtt_t jd_thingtalk_pal_mqtt_create(jd_thingtalk_mqtt_config_t *config);

/**
 * @brief   销毁一个MQTT客户端
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_destory(jd_thingtalk_mqtt_t mqtt_handler);

/**
 * @brief   MQTT客户端连接到broker
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cfg: MQTT 配置参数指针
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_connect(jd_thingtalk_mqtt_t mqtt_handler, jd_thingtalk_mqtt_config_t *cfg);

/**
 * @brief   MQTT客户端断开连接
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_disconnect(jd_thingtalk_mqtt_t mqtt_handler);

/**
 * @brief   MQTT客户端运单步行执行函数，会循环调用
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] timeout_ms: 运行超时时间，单位：毫秒
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_yield(jd_thingtalk_mqtt_t mqtt_handler, int32_t timeout_ms);

/**
 * @brief   MQTT客户端 订阅消息主题函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] topic: 订阅主题字符串的指针
 * @param[in] qos: 对应主题的消息服务等级
 * @param[in] mid: 对应MQTT的消息id
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_subscribe(jd_thingtalk_mqtt_t mqtt_handler, char *topic, int32_t qos, int32_t *mid);

/**
 * @brief   MQTT客户端 消息发布函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] message: MQTT 消息结构体指针
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_publish(jd_thingtalk_mqtt_t mqtt_handler, jd_thingtalk_mqtt_msg_t *message);

/**
 * @brief   MQTT客户端 设置 连接成功回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_connect: MQTT 连接成功后需要调用的函数句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_connect(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_connect)(jd_thingtalk_mqtt_t, void *), void *user_data);

/**
 * @brief   MQTT客户端 设置 连接断开回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_disconnect: MQTT 连接断开后需要调用的函数句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_disconnect(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_disconnect)(jd_thingtalk_mqtt_t, void *), void *user_data);

/**
 * @brief   MQTT客户端 设置 收到消息后的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_message: MQTT 收到消息后需要调用的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_message(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_message)(jd_thingtalk_mqtt_t, jd_thingtalk_mqtt_msg_t *, void *), void *user_data);

/**
 * @brief   MQTT客户端 设置 订阅ACK的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_subscribe: MQTT 收到订阅ACK后的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_subscribe(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_subscribe)(jd_thingtalk_mqtt_t, int32_t, void *), void *user_data);

/**
 * @brief   MQTT客户端 设置 消息发布ACk的回调函数
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] cb_publish: MQTT 收到消息发送ACK后的回调函数
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_cb_publish(jd_thingtalk_mqtt_t mqtt_handler, int32_t (*cb_publish)(jd_thingtalk_mqtt_t, int32_t, void *), void *user_data);

/**
 * @brief   MQTT客户端 设置用户数据
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @param[in] user_data: 用户数据
 * @return 
 	返回值 == 0,成功
    返回值 >< 0,失败 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mqtt_set_user_data(jd_thingtalk_mqtt_t mqtt_handler, void *user_data);

/**
 * @brief   MQTT客户端 获取用户数据
 *
 * @param[in] mqtt_handler: MQTT 客户端句柄
 * @return 
 	返回值 用户数据指针
 * @see None.
 * @note None.
 */
void *jd_thingtalk_pal_mqtt_get_user_data(jd_thingtalk_mqtt_t mqtt_handler);

#ifdef __cplusplus
}
#endif


#endif /* __JD_THINGTALK_MQTT_H_ */
