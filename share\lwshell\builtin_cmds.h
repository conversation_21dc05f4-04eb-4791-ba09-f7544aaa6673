#ifndef LAGA_USER_MIDWARE_LWSHELL_BUILTIN_CMDS_H
#define LAGA_USER_MIDWARE_LWSHELL_BUILTIN_CMDS_H
#include "lwshell_cmd.h"

#ifdef __cplusplus
extern "C" {
#endif
/**
 * @Description: ͨ�������ַ�������ȡ����
 * @param cmd: �����Ӧ���ַ���
 * @return NULL��δ�ҵ���Ӧ����     Else: ����ṹ��ָ�� 
 */
const cmd_desc_t* lwshell_get_buitin_cmd(const char* cmd);

/**
 * @Description: �����ڽ����lwshell��Ҫ���ô������������ҵ���ǰƥ��������ִ��
 * @param callback: �ɵ������ṩ�Ļص���������ÿһ�����������������ṹ��ָ��Ϊ���������ô˻ص�������
 * @return 0��������������������      1:�ص���������RET_QUIT_TRAVERSE�˳������� 
 */
int lwshell_travers_builtin_cmd(cmds_travers_callback_t *callback);

int help(const char* args[], const lwshell_interface_t *interface);


#ifdef __cplusplus
}
#endif

#endif
