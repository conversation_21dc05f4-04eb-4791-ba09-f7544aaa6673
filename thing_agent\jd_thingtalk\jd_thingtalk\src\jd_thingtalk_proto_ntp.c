/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 设备NTP授时相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   设备NTP授时 时间戳转成字符串
 *
 * @param[in] time_stamp: 时间戳指针
 * @param[in] time_str: 时间戳字符串指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_ntp_stamp_to_string(jd_thingtalk_time_stamp_t *time_stamp, char *time_str)
{
    if (time_stamp == NULL || time_str == NULL) {
        return JD_THINGTALK_RET_FAILED;    
    }

    jd_thingtalk_pal_sprintf(time_str, "%010d%03d", time_stamp->second, time_stamp->ms);

    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   设备NTP授时 字符串转成时间戳
 *
 * @param[in] time_str: 时间戳字符串指针
 * @param[in] time_stamp: 时间戳指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
static int32_t jd_thingtalk_ntp_string_to_stamp(char *time_str, jd_thingtalk_time_stamp_t *time_stamp)
{
    if (time_stamp == NULL || time_str == NULL) {
        return JD_THINGTALK_RET_FAILED;    
    }
    int32_t str_len = jd_thingtalk_pal_strlen(time_str);
    if (str_len <= 3) {
        log_error("The Length of Time String is too short!!");
        return JD_THINGTALK_RET_FAILED;
    }
    char str_sec[16];
    jd_thingtalk_pal_memset(str_sec, 0, 16 * sizeof(char));
    jd_thingtalk_pal_memcpy(str_sec, time_str, (str_len-3) * sizeof(char));
    
    time_stamp->second = (uint32_t) jd_thingtalk_pal_atoi(str_sec);
    time_stamp->ms = (uint32_t) jd_thingtalk_pal_atoi(&time_str[str_len-3]);

    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   设备NTP授时 释放 请求信息结构体成员变量
 *
 * @param[in] in_req: NTP请求信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_ntp_req(JDThingTalkProtoNTPReq *in_req)
{
    if (in_req != NULL) {
        if (in_req->deviceId != NULL) {
            jd_thingtalk_pal_free(in_req->deviceId);
            in_req->deviceId = NULL;
        }
        if (in_req->messageId != NULL) {
            jd_thingtalk_pal_free(in_req->messageId);
            in_req->messageId = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   设备NTP授时 打包NTP请求结构体
 *
 * @param[in] in_req: 请求NTP结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_ntp_req(JDThingTalkProtoNTPReq *in_req)
{
    if(NULL == in_req) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_req->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_req->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 messageId
    if (in_req->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_req->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 设备发送时间
    // FIXME
    char time_string[16];
    jd_thingtalk_pal_memset(time_string, 0, 16 * sizeof(char));
    jd_thingtalk_ntp_stamp_to_string(&in_req->devSendTime, time_string);
    cJSON_AddStringToObject(root, "deviceSendTime", time_string);

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   设备NTP授时 释放 请求响应信息结构体成员变量
 *
 * @param[in] in_res: NTP请求响应信息结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_ntp_req_res(JDThingTalkProtoNTPReqRes *in_res)
{
    if (in_res != NULL) {
        if (in_res->deviceId != NULL) {
            jd_thingtalk_pal_free(in_res->deviceId);
            in_res->deviceId = NULL;
        }
        if (in_res->messageId != NULL) {
            jd_thingtalk_pal_free(in_res->messageId);
            in_res->messageId = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   设备NTP授时 解析NTP请求响应消息体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res: NTP请求响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_parse_ntp_req_res(char *in_json, JDThingTalkProtoNTPReqRes *out_res)
{
    int ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_res) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }
    
    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->deviceId == NULL) {
        out_res->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->deviceId, pV->valuestring);

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->messageId == NULL) {
        out_res->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->messageId, pV->valuestring);

    // TODO 解析 code
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_CODE);
    if (NULL != pV) {
        out_res->code = pV->valueint;
    }

    // 解析 设备发送时间
    pV = cJSON_GetObjectItem(payload, "deviceSendTime");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    jd_thingtalk_ntp_string_to_stamp(pV->valuestring, &out_res->devSendTime);

    // 解析 服务端接收时间
    pV = cJSON_GetObjectItem(payload, "serverRecvTime");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    jd_thingtalk_ntp_string_to_stamp(pV->valuestring, &out_res->serRecvTime);

    // 解析 服务端发送时间
    pV = cJSON_GetObjectItem(payload, "serverSendTime");
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    jd_thingtalk_ntp_string_to_stamp(pV->valuestring, &out_res->serSendTime);

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

// end of file
