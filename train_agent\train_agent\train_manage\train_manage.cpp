#include "train_manage.hpp"
#include "train_agent_debug.h"
#include "share/pb/idl/train_interface.pb.h"
#include "train_list.hpp"
#include "multi_train_manager.hpp"


/**@brief     根据网络数据包，解析车辆传输的数据并判断车辆目标行为类型
* @param[in]  uint8_t *buf --- 输入的车辆网络数据包
* @param[in]  uint16_t buf_len --- 输入的车辆网络数据包长度
* @param[in]  uint8_t *data_buf --- 解码得到的data字段数据缓冲区
* @param[out] int *data_cnt  --- 解码得到的data字段长度
* @param[out] uint32_t *id  --- 解码得到的车辆ID 
* @param[out] uint32_t *sequeue  --- 解码得到的通信序列号
* @return     车辆消息类型，详见头文件定义
*/
TRAIN_MSG_TYPE train_manage_msg_type(uint8_t *buf, uint16_t buf_len, uint8_t *data_buf, int *data_cnt, uint8_t *id, uint32_t *sequeue)
{	
	int cnt_temp;
	uint32_t seq_temp;
	uint8_t id_temp;
	uint8_t msg_net_type;
	train_protocol_err_tab err;
	
	err = train_protocol_decodec(buf, buf_len, data_buf, &cnt_temp, &seq_temp, &id_temp);

	if( err != TRAIN_PROTOCOL_SUCESS )
	{	
		OUTPUT_LOG;
		std::cout << "trail protocol decodec error : " << err << std::endl;
		return TRAIN_MSG_UNDEFINED;
	}
	
    msg_net_type = buf[10];
    std::cout << "log>>: msg_net_type: " << (uint16_t)msg_net_type << std::endl;
	
	*data_cnt = cnt_temp;
	*id = id_temp;
	*sequeue = seq_temp;
	
	if(TRAIN_PROTOCOL_OPERATION_REGISTER_UDP == msg_net_type)
	{
		return TRAIN_MSG_REG;
	}
	else if(TRAIN_PROTOCOL_SESSION_HEART_BEAT_UDP == msg_net_type)
	{
		return TRAIN_MSG_STATE;
	}
	else if(TRAIN_PROTOCOL_SESSION_EXCEPTION_UDP == msg_net_type)
	{
		return TRAIN_MSG_EXCEPTIONAL;
	}
	else if(TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP == msg_net_type)
	{
		return TRAIN_MSG_CMD_ACK;
	}
	else
	{
		return TRAIN_MSG_UNDEFINED;
	}		
}


/**@brief     车辆注册消息ACK生成
* @param[in]  int dev_id --- 车辆ID
* @param[in]  uint32_t seque  --- 有效通信序列号
* @param[in]  train_agent_cfg *cfg  ---待ack的配置参数
* @param[out] uint8_t *send  --- 输出的注册ACK数据缓冲区
* @param[out] uint16_t *send_len  --- 输出的注册ACK数据缓冲区长度
* @return     NULL
*/
void train_manage_train_register_ack(int dev_id, uint32_t seque, uint8_t *send, uint16_t *send_len, train_agent_cfg *cfg)
{
	uint16_t opt_type_tcp = 0x00;

	//车辆注册消息回复
	opt_type_tcp = TRAIN_PROTOCOL_SESSION_REGISTER_ACK;

	//ACK消息编码
	train_protocol_codec_cfg(cfg, send, send_len, dev_id, opt_type_tcp, seque);


#ifdef TRAIN_MANAGE_DEBUG
	printf("------------------------------------------------\r\n");
	printf("data_send_len %d\r\n", *send_len);
	printf("register_ack_data: ");
	for(int i = 0; i < *send_len; i++)
	{
		printf("%x ", send[i]);
	}
	printf("\r\n");
#endif

}


void train_manage_get_dev_state(train_state *train_dev_state, train_state_net *dev_net_state, uint8_t *data_buf, uint8_t dev_id, uint8_t *carriage_id, uint8_t *excep_level, uint32_t *excep_code)
{

	train_state_net *p = (train_state_net *)data_buf;

	cfg_display(p);
	
	*carriage_id = p->exception_carriage_id;
	*excep_level = p->exception_level;
	*excep_code = p->exception_code;

	train_dev_state->train_id = dev_id;
	train_dev_state->carriage_cnt = p->carriage_count;
	train_dev_state->train_state = train_dev_state_DEV_UNKNOWN;
	train_dev_state->train_error_no = 0;
	train_dev_state->power_electricity_state = true;
	train_dev_state->power_electricity_volt = 220;
	train_dev_state->power_electricity_current = 0;
	train_dev_state->control_electricity_volt = 24;
	train_dev_state->control_electricity_current = 0;
	train_dev_state->motion_positon = p->train_motor_pos;
	train_dev_state->motion_positon_valid_flag = false;
	train_dev_state->motion_velocity = p->train_motor_speed;
	train_dev_state->calib_sensor_value = p->proximity_sensor_st;
	train_dev_state->motor_state = train_dev_state_DEV_UNKNOWN;
	train_dev_state->motor_encoder_value = p->train_motor_encoder_val;
	train_dev_state->motor_error_no = p->train_motor_error_code;
	train_dev_state->dev_comm_ack_state = false;
	train_dev_state->work_state = train_work_state_WORK_INIT;
	train_dev_state->encoder_mileage = p->mileage;
	train_dev_state->current_mileage = p->mileage_reference;
	train_dev_state->carriage_st_count = p->carriage_count;

	switch(p->train_run_st)
	{
		case 0x00:
			train_dev_state->train_state = train_dev_state_DEV_NORMAL;
			break;

		case 0x01:	//异常
			train_dev_state->train_state = train_dev_state_DEV_ERROR;
			break;

		case 0x02:	//急停
			train_dev_state->train_state = train_dev_state_DEV_FATAL;
			break;

		case 0x03:	//设备初始化
			train_dev_state->train_state = train_dev_state_DEV_INIT;
			break;

		default:
			train_dev_state->train_state = train_dev_state_DEV_UNKNOWN;
			break;
	}

	switch(p->train_motor_run_st)
	{
		case 0x00:
			train_dev_state->motor_state = train_dev_state_DEV_NORMAL;
			break;

		case 0x01:	//异常
			train_dev_state->motor_state = train_dev_state_DEV_ERROR;
			break;

		default:
			train_dev_state->motor_state = train_dev_state_DEV_UNKNOWN;
			break;
	}

	if((0x01 == p->train_locate_st) || (0x02 == p->train_locate_st))	//-1 未定位，0x00预定位，0x01已定位，0x02已上线
	{
		train_dev_state->motion_positon_valid_flag = true;
		train_dev_state->work_state = train_work_state_WORK_WORK;
	}
	else
	{
		train_dev_state->motion_positon_valid_flag = false;
		train_dev_state->work_state = train_work_state_WORK_CALIB;
	}


	for(int i = 0; i < train_dev_state->carriage_st_count; i++)
	{
		train_dev_state->carriage_st[i].carriage_id = p->carriages_info[i].carriage_id;
		train_dev_state->carriage_st[i].carriage_task_state = task_state_IDLE;
		train_dev_state->carriage_st[i].carriage_task = task_type_TASK_NULL;
		train_dev_state->carriage_st[i].carriage_load_state = false;

		switch(p->carriages_info[i].carriage_motor_run_st_Y)
		{
			case 0x00:
				train_dev_state->carriage_st[i].y_axis_encoder_state = train_dev_state_DEV_NORMAL;
				break;
			case 0x01:
				train_dev_state->carriage_st[i].y_axis_encoder_state = train_dev_state_DEV_ERROR;
				break;
			default:
				train_dev_state->carriage_st[i].y_axis_encoder_state = train_dev_state_DEV_UNKNOWN;
				break;
		}

		switch(p->carriages_info[i].belt_motor_run_st)
		{
			case 0x00:
				train_dev_state->carriage_st[i].load_platform_state = train_dev_state_DEV_NORMAL;
				break;
			case 0x01:
				train_dev_state->carriage_st[i].load_platform_state = train_dev_state_DEV_ERROR;
				break;
			default:
				train_dev_state->carriage_st[i].load_platform_state = train_dev_state_DEV_UNKNOWN;
				break;
		}

		train_dev_state->carriage_st[i].y_axis_encoder_value = p->carriages_info[i].carriage_motor_pos_Y;
		train_dev_state->carriage_st[i].y_axis_encoder_err_no = p->carriages_info[i].carriage_motor_error_code_Y;	
		train_dev_state->carriage_st[i].load_platform_belt_zero_state = (p->carriages_info[i].carriage_sensor_st >> 4) & 0xF;
		train_dev_state->carriage_st[i].load_platform_encoder_value = p->carriages_info[i].belt_motor_encoder_val;
		train_dev_state->carriage_st[i].load_platform_err_no = p->carriages_info[i].belt_motor_error_code;
		

		if((0x00 == p->carriages_info[i].carriage_cmd_result_Y) && (0x00 == p->carriages_info[i].belt_motor_cmd_ack))
			train_dev_state->carriage_st[i].carriage_task_state = task_state_SUCCEED_OVER;
		
		else if((0x01 == p->carriages_info[i].carriage_cmd_result_Y) || (0x01 == p->carriages_info[i].belt_motor_cmd_ack))
			train_dev_state->carriage_st[i].carriage_task_state = task_state_RUNNING;
			
		else if((0x02 == p->carriages_info[i].carriage_cmd_result_Y) || (0x02 == p->carriages_info[i].belt_motor_cmd_ack))
			train_dev_state->carriage_st[i].carriage_task_state = task_state_ERROR;

		if((0x02 == p->carriages_info[i].carriage_current_cmd_Y) && (0x01 == p->carriages_info[i].carriage_cmd_result_Y))
			train_dev_state->carriage_st[i].carriage_task = task_type_Y_MOVE;

		if((0x06 == p->carriages_info[i].belt_motor_current_cmd) && (0x01 == p->carriages_info[i].belt_motor_cmd_ack))
		{
			train_dev_state->carriage_st[i].carriage_load_state = true;
			train_dev_state->carriage_st[i].carriage_task = task_type_CARRIAGE_SHIFT_IN;
		}

		if((0x07 == p->carriages_info[i].belt_motor_current_cmd) && (0x00 == p->carriages_info[i].belt_motor_cmd_ack))
		{
			train_dev_state->carriage_st[i].carriage_task = task_type_CARRIAGE_SHIFT_OUT;
			train_dev_state->carriage_st[i].carriage_load_state = false;
		}		

	}

	*dev_net_state = *p;
}


void cfg_display(train_state_net *cfg)
{
	std::cout << "cfg>>: train_run_st: " << (uint32_t)cfg->train_run_st << "\n" << std::endl;
	std::cout << "cfg>>: train_locate_st: " << (int32_t)cfg->train_locate_st << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_speed: " << cfg->train_motor_speed << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_pos: " << cfg->train_motor_pos << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_run_st: " << (uint32_t)cfg->train_motor_run_st << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_st_code: " << cfg->train_motor_st_code << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_error_code: " << cfg->train_motor_error_code << "\n" << std::endl;
	std::cout << "cfg>>: train_motor_encoder_val: " << cfg->train_motor_encoder_val << "\n" << std::endl;
	std::cout << "cfg>>: rotate_motor_encoder_val: " << cfg->rotate_motor_encoder_val << "\n" << std::endl;
	std::cout << "cfg>>: encoder_val: " << cfg->encoder_val << "\n" << std::endl;
	std::cout << "cfg>>: mileage: " << cfg->mileage << "\n" << std::endl;
	std::cout << "cfg>>: mileage_reference: " << cfg->mileage_reference << "\n" << std::endl;
	std::cout << "cfg>>: proximity_sensor_st: " << (uint32_t)cfg->proximity_sensor_st << "\n" << std::endl;
	std::cout << "cfg>>: exception_carriage_id: " << (uint32_t)cfg->exception_carriage_id << "\n" << std::endl;
	std::cout << "cfg>>: exception_level: " << (uint32_t)cfg->exception_level << "\n" << std::endl;
	std::cout << "cfg>>: exception_code: " << cfg->exception_code << "\n" << std::endl;
	std::cout << "cfg>>: carriage_count: " << (uint32_t)cfg->carriage_count << "\n" << std::endl;
	
	for(int i = 0; i < cfg->carriage_count; i++)
	{
		std::cout << "cfg>>: carriage_id: " << (uint32_t)cfg->carriages_info[i].carriage_id << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_run_st_Y: " << (uint32_t)cfg->carriages_info[i].carriage_motor_run_st_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_st_code_Y: " << cfg->carriages_info[i].carriage_motor_st_code_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_error_code_Y: " << cfg->carriages_info[i].carriage_motor_error_code_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_pos_Y: " << cfg->carriages_info[i].carriage_motor_pos_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_encoder_val_Y: " << cfg->carriages_info[i].carriage_motor_encoder_val_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_zero_encoder_val_Y: " << cfg->carriages_info[i].carriage_zero_encoder_val_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_speed_Y: " << cfg->carriages_info[i].carriage_motor_speed_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_motor_contorl_quantity_Y: " << cfg->carriages_info[i].carriage_motor_contorl_quantity_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_current_cmd_Y: " << (uint32_t)cfg->carriages_info[i].carriage_current_cmd_Y << "\n" << std::endl;
		std::cout << "cfg>>: carriage_cmd_result_Y: " << (uint32_t)cfg->carriages_info[i].carriage_cmd_result_Y << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_run_st: " << (uint32_t)cfg->carriages_info[i].belt_motor_run_st << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_st_code: " << cfg->carriages_info[i].belt_motor_st_code << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_error_code: " << cfg->carriages_info[i].belt_motor_error_code << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_encoder_val: " << cfg->carriages_info[i].belt_motor_encoder_val << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_current_cmd: " << (uint32_t)cfg->carriages_info[i].belt_motor_current_cmd << "\n" << std::endl;
		std::cout << "cfg>>: belt_motor_cmd_ack: " << (uint32_t)cfg->carriages_info[i].belt_motor_cmd_ack << "\n" << std::endl;
		std::cout << "cfg>>: carriage_sensor_st: " << (uint32_t)cfg->carriages_info[i].carriage_sensor_st << "\n" << std::endl;
	}

}

void byte_conversion_uint32(uint8_t *buff_ptr, uint32_t data)
{
	buff_ptr[0] = (uint8_t)(data & 0xFF);
	buff_ptr[1] = (uint8_t)(data >> 8);
	buff_ptr[2] = (uint8_t)(data >> 16);
	buff_ptr[3] = (uint8_t)(data >> 24);
}

void byte_conversion_uint16(uint8_t *buff_ptr, uint16_t data)
{
	buff_ptr[0] = (uint8_t)(data & 0xFF);
	buff_ptr[1] = (uint8_t)(data >> 8);
}



/**@brief     判断待执行任务的类型
* @param[in]  train_task *v_task --- 接收到的任务数据结构体
* @param[out] uint8_t *data_out --- 根据任务数据生成的网络字节流
* @param[out] uint16_t *data_len --- 生成的网络字节流长度
* @param[in]  train_agent_cfg *cfg  --- 配置参数
* @return     操作结果，用来判断数据有效性
* - true      操作成功，数据有效
* - false     操作失败，数据无效
*/
int train_manage_release_task(train_task *v_task, uint8_t *data_out, uint16_t *data_len, train_agent_cfg *cfg, sys_mode_state *m_dev_curr_state, train_position_info *pos_info_temp)
{
	train_protocol_cmd task_temp;
	uint16_t data_send_len = 0x00;
	int task_type_temp = 0;
	uint8_t data_send[TRAIN_PROTOCOL_CMD_VALUE_MAX_LEN];
	uint8_t type_opt = TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP;

	uint8_t train_id = (uint8_t)v_task->dev_id;
	uint32_t sequence = v_task->sequence;
	uint8_t carriage = (uint8_t)v_task->carriage_id;

	SPDLOG_INFO("train_id:{}, sequence:{}, carriage:{}, which_task:{}, sys_state:{}-{} ", train_id, sequence, carriage, v_task->which_task, m_dev_curr_state->dev_st.emerg_pressed, m_dev_curr_state->dev_st.safty_door_open);


	if( train_task_lifting_tag == v_task->which_task )
	{	//Y轴移动任务
		if(v_task->task.lifting.type == task_type_Y_MOVE)
		{
			if((uint16_t )v_task->task.lifting.target > cfg->carriage_max_travel)
				return 0;

			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
			task_temp.cmd_value[1] = carriage;
			byte_conversion_uint16(&task_temp.cmd_value[2], (uint16_t )v_task->task.lifting.target);
			byte_conversion_uint16(&task_temp.cmd_value[4], (uint16_t )v_task->task.lifting.lift_speed);
			byte_conversion_uint16(&task_temp.cmd_value[6], (uint16_t )v_task->task.lifting.lift_acc);
		}
		else
		{
			SPDLOG_INFO("carriage lifting type error, devid:{}, carriage:{}", train_id, carriage);
			return -1;
		}
	}

	if( train_task_shift_tag == v_task->which_task )
	{	//皮带上下包任务
		task_type_temp = v_task->task.shift.type;
		
		if(v_task->task.shift.type == task_type_CARRIAGE_SHIFT_IN)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
			task_temp.cmd_value[1] = carriage;
		}
		else if(v_task->task.shift.type == task_type_CARRIAGE_SHIFT_OUT)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
			task_temp.cmd_value[1] = carriage;
			byte_conversion_uint32(&task_temp.cmd_value[2], v_task->task.shift.target_pos);
			byte_conversion_uint32(&task_temp.cmd_value[6], v_task->task.shift.target_y_pos);
		}
	}

	if( train_task_roll_back_tag == v_task->which_task )
	{	//皮带反转任务	
		task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
		task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
		task_temp.cmd_value[1] = carriage;
	
		if(v_task->task.roll_back.type == task_type_TASK_BELT_ROLL_BACK)
		{
			byte_conversion_uint32(&task_temp.cmd_value[2], v_task->task.roll_back.target_pos);
			byte_conversion_uint16(&task_temp.cmd_value[6], (uint16_t )v_task->task.roll_back.speed);
			byte_conversion_uint16(&task_temp.cmd_value[8], (uint16_t )v_task->task.roll_back.target_limit * (-1));
		}
		
		if(v_task->task.roll_back.type == task_type_TASK_BELT_STOP)
		{
			byte_conversion_uint32(&task_temp.cmd_value[2], 0);
			byte_conversion_uint16(&task_temp.cmd_value[6], 0);
			byte_conversion_uint16(&task_temp.cmd_value[8], 0);
		}		
	}
	
	if( train_task_cmd_tag == v_task->which_task )
	{
		switch(v_task->task.cmd)
		{
			case dev_cmd_tab_EMERG_STOP:	//急停
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[1] = 0x01;
				break;

			case dev_cmd_tab_EMERG_RESET:	//解除急停
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[1] = 0x02;
				break;

			case dev_cmd_tab_SLEEP:			//休眠

				break;
				
			case dev_cmd_tab_WAKEUP:		//唤醒

				break;

			case dev_cmd_tab_RESET:			//复位
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[1] = 0x02;
				break;

			case dev_cmd_tab_RESTART:	//重启
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
				task_temp.cmd_value[1] = 0x03;
				break;

			case dev_cmd_tab_BELT_LEFT_MOVING:	//皮带左转
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
				task_temp.cmd_value[1] = carriage;
				byte_conversion_uint16(&task_temp.cmd_value[2], cfg->belt_rotation_distance);
				break;

			case dev_cmd_tab_BELT_RIGHT_MOVING:	//皮带右转
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE;
				task_temp.cmd_value[1] = carriage;
				byte_conversion_uint16(&task_temp.cmd_value[2], cfg->belt_rotation_distance * (-1));
				break;

			case dev_cmd_tab_BELT_STOP:			//皮带停止转动

				break;

			case dev_cmd_tab_BELT_POS_ZERO:		//皮带零点复位
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO;
				task_temp.cmd_value[1] = carriage;
				
				break;

			case dev_cmd_tab_Y_AXIS_POS_CALIB:	//Y轴电机零点校准
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION;
				task_temp.cmd_value[1] = carriage;

				break;

			case dev_cmd_tab_DEV_ENABLE:		//车组启用

				break;

			case dev_cmd_tab_DEV_DISABLE:		//车组整体禁用

				break;

			case dev_cmd_tab_SUB_CARRIAGE_ENABLE:		//车厢启动

				break;

			case dev_cmd_tab_SUB_CARRIAGE_DISABLE:	//车厢禁用

				break;

			case dev_cmd_tab_SUB_PLATFOEM_ENABLE:		//子设备启用

				break;

			case dev_cmd_tab_SUB_PLATFORM_DISABLE:	//子设备禁用

				break;


			default:
				break;
		}
	}

	if( train_task_inte_task_tag == v_task->which_task )
	{	//集成任务：行走+Y轴移动；行走+上下包
		if(v_task->task.inte_task.type == task_type_TASK_INTE)
		{
			if(v_task->task.inte_task.task_lifting_valid)
			{
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
				task_temp.cmd_value[1] = carriage;

				if(v_task->task.inte_task.task_info_y_pos <= cfg->carriage_max_travel)
					byte_conversion_uint16(&task_temp.cmd_value[2], (uint16_t )v_task->task.inte_task.task_info_y_pos);
				else
				{
					byte_conversion_uint16(&task_temp.cmd_value[2], 0);
					SPDLOG_INFO("carriage travel exceeds the limit:{} - {}", v_task->task.inte_task.task_info_y_pos, cfg->carriage_max_travel);
				}
			}

			if(v_task->task.inte_task.has_task_info_shift)
			{
				if(v_task->task.inte_task.task_info_shift.type == task_type_CARRIAGE_SHIFT_IN)
				{
					task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
					task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
					task_temp.cmd_value[1] = carriage;
					task_type_temp = task_type_CARRIAGE_SHIFT_IN;
				}
				else if(v_task->task.inte_task.task_info_shift.type == task_type_CARRIAGE_SHIFT_OUT)
				{
					task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
					task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
					task_temp.cmd_value[1] = carriage;
					byte_conversion_uint32(&task_temp.cmd_value[2], v_task->task.inte_task.task_info_shift.target_pos);
					byte_conversion_uint32(&task_temp.cmd_value[6], v_task->task.inte_task.task_info_shift.target_y_pos);
					task_type_temp = task_type_CARRIAGE_SHIFT_OUT;
				}
			}	
		}
		else
		{
			SPDLOG_INFO("inte_task type error...");
			return -1;
		}

	}
	
	if( train_task_hb_time_sync_tag == v_task->which_task )
	{
		SPDLOG_INFO("pos_info_temp, speed:{}, pos:{}", pos_info_temp->speed, pos_info_temp->pos);
		task_temp.cmd_type = TRAIN_PROTOCOL_CMD_WALK_LOCATE;
		if(pos_info_temp->is_locate == true)
			task_temp.cmd_value[0] = TRAIN_PROTOCOL_CMD_WALK_LOCATE;
		else
			task_temp.cmd_value[0] = TRAIN_PROTOCOL_CMD_WALK_NOLOCATE;

		if( (m_dev_curr_state->dev_st.emerg_pressed == true) || (m_dev_curr_state->dev_st.safty_door_open == true) )
		{
			byte_conversion_uint16(&task_temp.cmd_value[1], 0);
			byte_conversion_uint32(&task_temp.cmd_value[3], 0);
		}
		else
		{
			byte_conversion_uint16(&task_temp.cmd_value[1], pos_info_temp->speed);
			byte_conversion_uint32(&task_temp.cmd_value[3], pos_info_temp->pos);
		}
	}

	if( train_task_mil_info_tag == v_task->which_task )
	{
		task_temp.cmd_type = TRAIN_PROTOCOL_CMD_MILEAGE_INFO;

		task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_MILEAGE_INFO;
		byte_conversion_uint32(&task_temp.cmd_value[1], v_task->task.mil_info.encoder_mileage);
		byte_conversion_uint32(&task_temp.cmd_value[5], v_task->task.mil_info.current_mileage);
	}


	train_protocol_codec(&task_temp, data_send, &data_send_len, train_id, type_opt, sequence);

	memcpy(data_out, data_send, data_send_len);
	*data_len = data_send_len;

	return task_type_temp;
}


uint8_t train_manage_release_inte_task(train_task *v_task, uint8_t *data_out, uint16_t *data_len, train_agent_cfg *cfg)
{
	train_protocol_cmd task_temp;
	uint16_t data_send_len = 0x00;
	uint8_t task_type_temp = 0;
	uint8_t data_send[TRAIN_PROTOCOL_CMD_VALUE_MAX_LEN];
	uint8_t type_opt = TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP;

	uint8_t train_id = (uint8_t)v_task->dev_id;
	uint32_t sequence = v_task->sequence;
	uint8_t carriage = (uint8_t)v_task->carriage_id;

	//集成任务：行走+Y轴移动；行走+上下包
	if( train_task_inte_task_tag == v_task->which_task )
	{
		if(v_task->task.inte_task.type == task_type_TASK_INTE)
		{
			if(v_task->task.inte_task.task_lifting_valid)
			{
				task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
				task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS;
				task_temp.cmd_value[1] = carriage;

				if(v_task->task.inte_task.task_info_y_pos <= cfg->carriage_max_travel)
					byte_conversion_uint16(&task_temp.cmd_value[2], (uint16_t )v_task->task.inte_task.task_info_y_pos);
				else
				{
					byte_conversion_uint16(&task_temp.cmd_value[2], 0);
					SPDLOG_INFO("carriage travel exceeds the limit:{} - {}", v_task->task.inte_task.task_info_y_pos, cfg->carriage_max_travel);
				}
			}

			if(v_task->task.inte_task.has_task_info_shift)
			{
				if(v_task->task.shift.type == task_type_CARRIAGE_SHIFT_IN)
				{
					task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
					task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_PACK;
					task_temp.cmd_value[1] = carriage;
					task_type_temp = task_type_CARRIAGE_SHIFT_IN;
				}
				else if(v_task->task.shift.type == task_type_CARRIAGE_SHIFT_OUT)
				{
					task_temp.cmd_type = TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
					task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK;
					task_temp.cmd_value[1] = carriage;
					byte_conversion_uint32(&task_temp.cmd_value[2], v_task->task.inte_task.task_info_shift.target_pos);
					byte_conversion_uint32(&task_temp.cmd_value[6], v_task->task.inte_task.task_info_shift.target_y_pos);
					task_type_temp = task_type_CARRIAGE_SHIFT_OUT;
				}
			}	
		}
		else
			SPDLOG_INFO("inte_task type error...");
	}
		
	train_protocol_codec(&task_temp, data_send, &data_send_len, train_id, type_opt, sequence);

	memcpy(data_out, data_send, data_send_len);
	*data_len = data_send_len;

	return task_type_temp;
}

void train_query_para_recv(train_run_para *para, uint8_t *data_buf)
{
	train_run_para * p = (train_run_para *)data_buf;

	run_para_display(p);

	*para = *p;
}

void run_para_display(train_run_para *cfg)
{
	std::cout << "train_run_cfg>>: timeout_ack: " << cfg->timeout_ack << "\n" << std::endl;
	std::cout << "train_run_cfg>>: train_motor_speed: " << cfg->train_motor_speed << "\n" << std::endl;
	std::cout << "train_run_cfg>>: walk_motor_acc: " << cfg->walk_motor_acc << "\n" << std::endl;
	std::cout << "train_run_cfg>>: walk_motor_dec: " << cfg->walk_motor_dec << "\n" << std::endl;
	std::cout << "train_run_cfg>>: carriage_motor_speed: " << cfg->carriage_motor_speed << "\n" << std::endl;
	std::cout << "train_run_cfg>>: carriage_motor_acc: " << cfg->carriage_motor_acc << "\n" << std::endl;
	std::cout << "train_run_cfg>>: carriage_motor_dec: " << cfg->carriage_motor_dec << "\n" << std::endl;
	std::cout << "train_run_cfg>>: belt_motor_speed: " << cfg->belt_motor_speed << "\n" << std::endl;
	std::cout << "train_run_cfg>>: belt_zero_speed: " << cfg->belt_zero_speed << "\n" << std::endl;
	std::cout << "train_run_cfg>>: belt_motor_acc: " << cfg->belt_motor_acc << "\n" << std::endl;
	std::cout << "train_run_cfg>>: carriage_num: " << (uint32_t)cfg->carriage_num << "\n" << std::endl;
	std::cout << "train_run_cfg>>: unpack_exceed_threshold: " << cfg->unpack_exceed_threshold << "\n" << std::endl;
	std::cout << "train_run_cfg>>: unpack_sensor_switch: " << (uint32_t)cfg->unpack_sensor_switch << "\n" << std::endl;
}


void hb_log_decode(uint8_t *buffer, uint8_t *data_buf, std::string *net_msg_decode)
{
	train_state_net *p = (train_state_net *)data_buf;
	std::stringstream ss;
	string net_msg_temp = {""};
	char data[4];
	
	for(int i = 0; i < 11; i++)
	{
		sprintf(data, "%x ", buffer[i]);
		data[3] = 0x00;
		net_msg_temp += data;
	}

	ss << std::hex << net_msg_temp << ' ';
	ss << "trst:" << std::hex << (int)p->train_run_st << ' ';
	ss << "lst:" << std::hex << (int)p->train_locate_st << ' ';
	ss << "speed:" << std::hex << htons(p->train_motor_speed) << ' ';
	ss << "mpos:" << std::hex << htonl(p->train_motor_pos) << ' ';
	ss << "mrst:" << std::hex << (int)p->train_motor_run_st << ' ';
	ss << "stcode:" << std::hex << htons(p->train_motor_st_code) << ' ';
	ss << "ecode:" << std::hex << htons(p->train_motor_error_code) << ' ';
	ss << "mencode:" << std::hex << htonl(p->train_motor_encoder_val) << ' ';
	ss << "rencode:" << std::hex << htonl(p->rotate_motor_encoder_val) << ' ';
	ss << "encode:" << std::hex << htonl(p->encoder_val) << ' ';
	ss << "miles:" << std::hex << htonl(p->mileage) << ' ';
	ss << "miles_ref:" << std::hex << htonl(p->mileage_reference) << ' ';
	ss << "psensor:" << std::hex << (int)p->proximity_sensor_st << ' ';
	ss << "expid:" << std::hex << (int)p->exception_carriage_id << ' ';
	ss << "exlev:" << std::hex << (int)p->exception_level << ' ';
	ss << "excode:" << std::hex << htonl(p->exception_code) << ' ';
	ss << "pnum:" << std::hex << (int)p->carriage_count << std::string(4, ' ');

	for(int i = 0; i < p->carriage_count; i++)
	{
		ss << "pid:" << std::hex << (int)p->carriages_info[i].carriage_id << ' ';
		ss << "rst_y:" << std::hex << (int)p->carriages_info[i].carriage_motor_run_st_Y << ' ';
		ss << "stcode_y:" << std::hex << htons(p->carriages_info[i].carriage_motor_st_code_Y) << ' ';
		ss << "ecode_y:" << std::hex << htons(p->carriages_info[i].carriage_motor_error_code_Y) << ' ';
		ss << "pos_y:" << std::hex << htons(p->carriages_info[i].carriage_motor_pos_Y) << ' ';
		ss << "encode_y:" << std::hex << htonl(p->carriages_info[i].carriage_motor_encoder_val_Y) << ' ';
		ss << "zencode_y:" << std::hex << htonl(p->carriages_info[i].carriage_zero_encoder_val_Y) << ' ';
		ss << "speed_y:" << std::hex << htons(p->carriages_info[i].carriage_motor_speed_Y) << ' ';
		ss << "cv_y:" << std::hex << htons(p->carriages_info[i].carriage_motor_contorl_quantity_Y) << ' ';
		ss << "cmd_y:" << std::hex << (int)p->carriages_info[i].carriage_current_cmd_Y << ' ';
		ss << "result:" << std::hex << (int)p->carriages_info[i].carriage_cmd_result_Y << ' ';
		ss << "rst_z:" << std::hex << (int)p->carriages_info[i].belt_motor_run_st << ' ';
		ss << "stcode_z:" << std::hex << htons(p->carriages_info[i].belt_motor_st_code) << ' ';
		ss << "ecode_z:" << std::hex << htons(p->carriages_info[i].belt_motor_error_code) << ' ';
		ss << "encode_z:" << std::hex << htonl(p->carriages_info[i].belt_motor_encoder_val) << ' ';
		ss << "cmd_z:" << std::hex << (int)p->carriages_info[i].belt_motor_current_cmd << ' ';
		ss << "result:" << std::hex << (int)p->carriages_info[i].belt_motor_cmd_ack << ' ';
		ss << "sensor:" << std::hex << (int)p->carriages_info[i].carriage_sensor_st << std::string(4, ' ');
	}

	*net_msg_decode = ss.str();
}

