#pragma once

#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/sys_interface.pb.h"
// #include "share/pb/idl/sys_state.pb.h"
// #include "share/pb/idl/plane_hmi.pb.h"

class sys_state_interface
{
public:

    int init(zmq::context_t &ctx);

    int issue_sys_state(sys_mode_state &st);

    int issue_vehicle_start_moving();  //启动车辆持续空跑测试

    int issue_vehicle_stop_moving();   //停止车辆持续空跑测试

    int issue_simulate_sorting(task_cmd &task);

    int issue_mcu_reboot();     //给控制板下发板卡重启

    int issue_mcu_reset();      //给控制板下发复位，复位程序

    int issue_vehicle_to_feeder(uint32_t &vehicle_id, uint32_t level_speed, uint32_t turn_speed, int feeder_id = -1);     //直接下发供包台位置
    
    int issue_vehicle_to_camera(uint32_t &vehicle_id, uint32_t camera_id, uint32_t level_speed, uint32_t turn_speed);       //直接下发灰度相机位置

    int issue_vehicle_to_slot(uint32_t &vehicle_id, uint32_t &slot_id, uint32_t level_speed, uint32_t turn_speed);     //直接下发指定格口位置

    int issue_vehicle_move_forward(uint32_t &vehicle_id, uint32_t &length, uint32_t level_speed, uint32_t turn_speed);

    int issue_vehicle_register_cmd(uint32_t &vehicle_id);

    int issue_vehicle_unregister_cmd(uint32_t &vehicle_id);

    int issue_set_manual_mode();
    int issue_set_auto_mode();



    static sys_state_interface *get_instance(void)
    {
        static sys_state_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *sys_state_recv;
    zmq::socket_t *sys_cmd_sender;            //由物控节点下发系统状态
	zmq::socket_t *sub_keyevt;  //从按键订阅按键动作
};
