﻿
/**@file  	   device_manage.cpp
* @brief       本播墙调度系统的设备管理接口文件
* @details     NULL
* <AUTHOR>
* @date        2022-02-14
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/


#include "fsm_manager.hpp"
#include "share/global_def.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>    
#include <spdlog/async.h>

#include "./threadpool/blocking_queue.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/sys_interface.pb.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex>          

using namespace std;



/**@brief  vehicle_list_map class构造函数
* @param[in]  NULL
* @return     NULL
*/
fsm_manager::fsm_manager(zmq::context_t &context)
:m_dev_fsm_subscriber(zmq::socket_t(context, ZMQ_SUB))
,m_dev_fsm(e_wkstate_SYS_INIT)
,m_dev_excp_run_flag(false)
{
	return ;
}


/**@brief  vehicle_list_map class析构函数
* @param[in]  NULL
* @return     NULL
*/
fsm_manager::~fsm_manager() 
{

}



void fsm_manager::fsm_manager_init(void) 
{
	m_dev_fsm_subscriber.connect(TOPIC_SYS_STATE);
	m_dev_fsm_subscriber.set(zmq::sockopt::subscribe, "");
}


//实时获取系统状态
void fsm_manager::fsm_manager_run(void) 
{
	m_dev_state_get_thread = new std::thread(&fsm_manager::fsm_manager_main_thread, this);

}


e_wkstate fsm_manager::fsm_manager_get_fsm(void) 
{
	return m_dev_fsm;
}

sys_mode_state fsm_manager::fsm_manager_get_sys_state(void) 
{
	std::lock_guard<std::mutex> lock(m_dev_sys_state_mutex);

	return m_dev_sys_state;
}

void fsm_manager::fsm_manager_main_thread(void) 
{
	SPDLOG_INFO("fsm_manager_main_thread start... ");

	while(1)
	{
		fsm_manager_get_fsm_state();

		std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
}


bool fsm_manager::fsm_manager_get_fsm_state(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	sys_mode_state dev_state_temp;

	if(m_dev_fsm_subscriber.recv(msg, zmq::recv_flags::none))
	{
		SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber rec msg ");
		
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());

		if (!pb_decode(&stream_in, sys_mode_state_fields, &dev_state_temp))
		{
			SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber msg pb decode fail ");

			return false;
		}
		else
		{
			if( e_wkstate_SYS_ERROR_RECOVERY == dev_state_temp.state )
			{
				if( e_wkstate_SYS_AUTO_RUNNING == m_dev_fsm )
				{
					m_dev_excp_run_flag = true;
				}
			}
			else
			{
				m_dev_excp_run_flag = false;
			}

			m_dev_fsm = dev_state_temp.state;
			m_dev_sys_state = dev_state_temp;

			SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber e_wkstate:{}, safty_door_open:{}, emerg_pressed:{} ", m_dev_fsm, m_dev_sys_state.dev_st.safty_door_open, m_dev_sys_state.dev_st.emerg_pressed);	
		}
	}
	else
	{
		return false;
	}

	return true;
}


bool fsm_manager::fsm_manager_get_exce_run_flag(void)
{
	return m_dev_excp_run_flag;
}

