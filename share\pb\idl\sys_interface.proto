	syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";


// 系统状态相关
enum e_wkstate
{
	SYS_RESERVE =0;
	SYS_INIT = 1;
	SYS_CHECK = 2;
	SYS_CALIBRATE = 3;
	SYS_STOP = 4;
	SYS_STOPING = 5;
	SYS_AUTO_RUNNING = 6;
	SYS_PART_ERROR   = 7;
	SYS_ERROR_RECOVERY = 8;
	SYS_MANUAL = 9;
};

enum e_errstate
{
	NOERROR = 0;
	GLOBAL_ERROR =1;
	LOCAL_ERROR=2;

	GLOBAL_EMERG_ERROR = 3;
};

enum e_sys_mode
{
	AUTO = 0;
	MANNUAL = 1;
	DEMONSTRATE = 2;
};

enum safty_door_state_offset
{
	FRONT_DOOR = 0;
	TAIL_DOOR = 1;
	SYS_TRAIN_POWER = 2;
	SYS_RELAY_POWER = 3;
};

enum emerg_button_state_offset
{
	FRONT_DOOR_EMERG = 0;
	TAIL_DOOR_EMERG = 1;
	CONTROL_CABINET = 2;
	FEEDER_1	= 3;
	FEEDER_2 = 4;
	FEEDER_3 = 5;
	FEEDER_4 = 6;
	FEEDER_5 = 7;
	FEEDER_6 = 8;
};

message dev_state
{
 	bool safty_door_open =1;
	uint32 safty_door_state = 2;
	bool emerg_pressed = 3;
	uint32 emerg_button_state = 4;
}

message sys_mode_state
{
	e_sys_mode mode = 1;
	e_wkstate state = 2;
	e_errstate err = 3;
	dev_state dev_st = 4;
	uint32 error_code =5;
	bool maintenance_state = 6;
};


enum component_state
{
	C_RESERVE = 0;
	C_IDLE = 1;
	C_RUNNING = 2;
	C_ERR = 3;
	C_INIT = 4;
	C_UNKNOW = 5;
}


//系统命令相关

enum manual_cmd_type
{
	FEEDER_BELT_FORWARD  = 0;
	FEEDER_BELT_BACKWORD = 1;
	FEEDER_BLET_STOP	 = 2;
	TOWER_LIGHT_ON		 = 3;
	TOWER_LIGHT_OFF		 = 4;
	TOWER_BEEP_ON		 = 5;
	TOWER_BEEP_OFF		 = 6;
	TRAIN_MOVE_FORWARD_LIMIT	 = 7;
	TRAIN_MOVE_FORWARD	 = 8;
	TRAIN_MOVE_TO_FEEDER = 9;
	TRAIN_MOVE_TO_HOS	 = 10;
	TRAIN_MOVE_TO_REDUN  = 11;
	TRAIN_MOVE_TO_SLOT   = 12;
	TRAIN_MOVE_TO_CAMERA = 13;
	TRAIN_MOVE_TO_TARGET = 14;
	Y_AXIS_MOVE			 = 15;
	Y_AXIS_CALIBRATE	 = 16;
	PLATFORM_BELT_FORWARD = 17;
	PLATFORM_BELT_BACKWORD = 18;
	PLATFORM_BELT_ZEOR_CALIB = 19;
	CONTAINER_LED_ON	 = 20;
	CONTAINER_LED_OFF	 = 21;
	CONTAINER_LED_TRAVERSE = 22;
	DEVICE_SIMULATE_RUN	 = 23;
	
	TRAIN_DISABLE		= 24;
	TRAIN_ENABLE 		= 25;
	CARRIAGE_DISABLE    = 26;
	CARRIAGE_ENABLE		= 27;
	PLATFORM_DISABLE	= 28;
	PLATFORM_ENABLE		= 29;
	
	SHELVES_LOCK		= 30;
	SEELVER_UNLOCK		= 31;
	
	DEV_ENTER_MAINTENANCE_STATE  = 32;
	DEV_EXIT_MAINTENANCE_STATE  = 33;
	DEV_CTRL_PLATFORM_HEIGTH = 34;
	
}

enum dev_platform_heigth_ctrl
{
	DEV_PLATFORM_HEIGHT_RESERVE = 0;
	DEV_PLATFORM_HEIGHT_TOP = 1;
	DEV_PLATFORM_HEIGHT_FEEDER = 2;
	DEV_PLATFORM_HEIGHT_DYNAMICS = 3;
};

message train_move_para
{
	uint32  target_id = 1;
	uint32  move_length = 2;
	uint32  move_staight_speed = 3;
	uint32  move_arc_speed = 4;
}

message dev_move_para
{
	uint32  move_dir = 1;	// 0-停止  1-正向 2-反向
	uint32  move_length = 2;
	uint32  move_speed = 3;
}


message manual_cmd
{
	uint32 dev_id = 1;
	uint32 sub_dev_id = 2;
    manual_cmd_type cmd_type = 3;
	oneof cmd_para
	{
    	train_move_para train_move = 4;
		dev_move_para 	dev_move = 5;
		bool  move_continuout_flag = 6;
		dev_platform_heigth_ctrl platform_height_para = 7;
	}
}



message set_state
{
	uint32 state = 1;
};

message task_cmd
{
	enum task_cmd_type
	{
		SIMULATE_FINISH = 0;
		CANCEL = 1;
		HOSPICE_FINISH = 2;
	};

	bytes task_id = 1 [(nanopb).max_size = 36, (nanopb).fixed_length = true];
	uint32 cmd = 2;
	uint32 param = 3;		//对于SIMULATE_FINISH，param是格口号
}

message reset_exception
{
	uint32 exception_src = 1;
	uint32 dev = 2;
	uint32 error =3;
};




message sys_cmd
{
	oneof cmd 
	{
		set_state state = 1;
		task_cmd task = 2;
		reset_exception excep = 3;
		manual_cmd misc = 4;
	};
};