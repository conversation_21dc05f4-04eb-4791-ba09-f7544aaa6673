/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.5 */

#ifndef PB_CONTAINER_INTERFACE_PB_H_INCLUDED
#define PB_CONTAINER_INTERFACE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _box_state { 
    box_state_BIND = 0, 
    box_state_UNBIND = 1, 
    box_state_ERROR = 2 
} box_state;

typedef enum _container_seal_state { 
    container_seal_state_IDLE = 0, 
    container_seal_state_SEAL = 1, 
    container_seal_state_CONTAIN = 2, 
    container_seal_state_UNKNOWN = 3 
} container_seal_state;

typedef enum _state { 
    state_ABSENT = 0, 
    state_FULL = 1, 
    state_NORMAL = 2, 
    state_RASTER_TRIGGERED = 3 
} state;

typedef enum _shelves_cmd_tab { 
    shelves_cmd_tab_RESERVE = 0, 
    shelves_cmd_tab_LOCK = 1, 
    shelves_cmd_tab_UNLOCK = 2 
} shelves_cmd_tab;

/* Struct definitions */
typedef struct _box_info_single { 
    uint32_t box_id; 
    char RFID[33]; 
    box_state box_st; 
} box_info_single;

typedef struct _container_agent_state { 
    component_state state; 
} container_agent_state;

typedef struct _container_seal_cmd { 
    uint32_t container_id; 
} container_seal_cmd;

typedef struct _container_seal_state_single { 
    uint32_t container_id; 
    container_seal_state seal_state; 
} container_seal_state_single;

typedef struct _led_info { 
    uint32_t id; 
    uint32_t color; 
    uint32_t flash_freq; 
} led_info;

typedef struct _shelves_cmd { 
    uint32_t shelves_grout; 
    shelves_cmd_tab cmd; 
} shelves_cmd;

typedef struct _shelves_state { 
    uint32_t shelves_group; 
    shelves_cmd_tab state; 
} shelves_state;

typedef struct _slot_state { 
    uint32_t id; 
    state st; 
} slot_state;

typedef struct _box_info_multiple { 
    pb_size_t boxes_count;
    box_info_single boxes[1000]; 
} box_info_multiple;

typedef struct _led_info_multiple { 
    pb_size_t led_state_count;
    led_info led_state[1000]; 
} led_info_multiple;


/* Helper constants for enums */
#define _box_state_MIN box_state_BIND
#define _box_state_MAX box_state_ERROR
#define _box_state_ARRAYSIZE ((box_state)(box_state_ERROR+1))

#define _container_seal_state_MIN container_seal_state_IDLE
#define _container_seal_state_MAX container_seal_state_UNKNOWN
#define _container_seal_state_ARRAYSIZE ((container_seal_state)(container_seal_state_UNKNOWN+1))

#define _state_MIN state_ABSENT
#define _state_MAX state_RASTER_TRIGGERED
#define _state_ARRAYSIZE ((state)(state_RASTER_TRIGGERED+1))

#define _shelves_cmd_tab_MIN shelves_cmd_tab_RESERVE
#define _shelves_cmd_tab_MAX shelves_cmd_tab_UNLOCK
#define _shelves_cmd_tab_ARRAYSIZE ((shelves_cmd_tab)(shelves_cmd_tab_UNLOCK+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define box_info_single_init_default             {0, "", _box_state_MIN}
#define box_info_multiple_init_default           {0, {box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default, box_info_single_init_default}}
#define container_seal_state_single_init_default {0, _container_seal_state_MIN}
#define led_info_init_default                    {0, 0, 0}
#define container_seal_cmd_init_default          {0}
#define container_agent_state_init_default       {_component_state_MIN}
#define led_info_multiple_init_default           {0, {led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default, led_info_init_default}}
#define slot_state_init_default                  {0, _state_MIN}
#define shelves_cmd_init_default                 {0, _shelves_cmd_tab_MIN}
#define shelves_state_init_default               {0, _shelves_cmd_tab_MIN}
#define box_info_single_init_zero                {0, "", _box_state_MIN}
#define box_info_multiple_init_zero              {0, {box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero, box_info_single_init_zero}}
#define container_seal_state_single_init_zero    {0, _container_seal_state_MIN}
#define led_info_init_zero                       {0, 0, 0}
#define container_seal_cmd_init_zero             {0}
#define container_agent_state_init_zero          {_component_state_MIN}
#define led_info_multiple_init_zero              {0, {led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero, led_info_init_zero}}
#define slot_state_init_zero                     {0, _state_MIN}
#define shelves_cmd_init_zero                    {0, _shelves_cmd_tab_MIN}
#define shelves_state_init_zero                  {0, _shelves_cmd_tab_MIN}

/* Field tags (for use in manual encoding/decoding) */
#define box_info_single_box_id_tag               1
#define box_info_single_RFID_tag                 2
#define box_info_single_box_st_tag               3
#define container_agent_state_state_tag          1
#define container_seal_cmd_container_id_tag      1
#define container_seal_state_single_container_id_tag 1
#define container_seal_state_single_seal_state_tag 2
#define led_info_id_tag                          1
#define led_info_color_tag                       2
#define led_info_flash_freq_tag                  3
#define shelves_cmd_shelves_grout_tag            1
#define shelves_cmd_cmd_tag                      2
#define shelves_state_shelves_group_tag          1
#define shelves_state_state_tag                  2
#define slot_state_id_tag                        1
#define slot_state_st_tag                        2
#define box_info_multiple_boxes_tag              1
#define led_info_multiple_led_state_tag          1

/* Struct field encoding specification for nanopb */
#define box_info_single_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   box_id,            1) \
X(a, STATIC,   SINGULAR, STRING,   RFID,              2) \
X(a, STATIC,   SINGULAR, UENUM,    box_st,            3)
#define box_info_single_CALLBACK NULL
#define box_info_single_DEFAULT NULL

#define box_info_multiple_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  boxes,             1)
#define box_info_multiple_CALLBACK NULL
#define box_info_multiple_DEFAULT NULL
#define box_info_multiple_boxes_MSGTYPE box_info_single

#define container_seal_state_single_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   container_id,      1) \
X(a, STATIC,   SINGULAR, UENUM,    seal_state,        2)
#define container_seal_state_single_CALLBACK NULL
#define container_seal_state_single_DEFAULT NULL

#define led_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UINT32,   color,             2) \
X(a, STATIC,   SINGULAR, UINT32,   flash_freq,        3)
#define led_info_CALLBACK NULL
#define led_info_DEFAULT NULL

#define container_seal_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   container_id,      1)
#define container_seal_cmd_CALLBACK NULL
#define container_seal_cmd_DEFAULT NULL

#define container_agent_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    state,             1)
#define container_agent_state_CALLBACK NULL
#define container_agent_state_DEFAULT NULL

#define led_info_multiple_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  led_state,         1)
#define led_info_multiple_CALLBACK NULL
#define led_info_multiple_DEFAULT NULL
#define led_info_multiple_led_state_MSGTYPE led_info

#define slot_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UENUM,    st,                2)
#define slot_state_CALLBACK NULL
#define slot_state_DEFAULT NULL

#define shelves_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   shelves_grout,     1) \
X(a, STATIC,   SINGULAR, UENUM,    cmd,               2)
#define shelves_cmd_CALLBACK NULL
#define shelves_cmd_DEFAULT NULL

#define shelves_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   shelves_group,     1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2)
#define shelves_state_CALLBACK NULL
#define shelves_state_DEFAULT NULL

extern const pb_msgdesc_t box_info_single_msg;
extern const pb_msgdesc_t box_info_multiple_msg;
extern const pb_msgdesc_t container_seal_state_single_msg;
extern const pb_msgdesc_t led_info_msg;
extern const pb_msgdesc_t container_seal_cmd_msg;
extern const pb_msgdesc_t container_agent_state_msg;
extern const pb_msgdesc_t led_info_multiple_msg;
extern const pb_msgdesc_t slot_state_msg;
extern const pb_msgdesc_t shelves_cmd_msg;
extern const pb_msgdesc_t shelves_state_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define box_info_single_fields &box_info_single_msg
#define box_info_multiple_fields &box_info_multiple_msg
#define container_seal_state_single_fields &container_seal_state_single_msg
#define led_info_fields &led_info_msg
#define container_seal_cmd_fields &container_seal_cmd_msg
#define container_agent_state_fields &container_agent_state_msg
#define led_info_multiple_fields &led_info_multiple_msg
#define slot_state_fields &slot_state_msg
#define shelves_cmd_fields &shelves_cmd_msg
#define shelves_state_fields &shelves_state_msg

/* Maximum encoded size of messages (where known) */
#define box_info_multiple_size                   44000
#define box_info_single_size                     42
#define container_agent_state_size               2
#define container_seal_cmd_size                  6
#define container_seal_state_single_size         8
#define led_info_multiple_size                   20000
#define led_info_size                            18
#define shelves_cmd_size                         8
#define shelves_state_size                       8
#define slot_state_size                          8

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
