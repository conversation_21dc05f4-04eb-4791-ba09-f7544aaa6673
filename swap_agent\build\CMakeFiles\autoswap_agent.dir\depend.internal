# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

CMakeFiles/autoswap_agent.dir/readme.c.o
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/readme.c
CMakeFiles/autoswap_agent.dir/exception/dev_except.cpp.o
 ../../share/exception_code.hpp
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/nanopb/pb.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.hpp
CMakeFiles/autoswap_agent.dir/fsm_manager/fsm_manager.cpp.o
 ../../share/global_def.h
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/async.h
 ../../share/libs/x86/include/spdlog/async_logger-inl.h
 ../../share/libs/x86/include/spdlog/async_logger.h
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/mpmc_blocking_q.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/thread_pool-inl.h
 ../../share/libs/x86/include/spdlog/details/thread_pool.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/vehicle_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 ../././threadpool/blocking_queue.hpp
 ../././threadpool/condition.hpp
 ../././threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
CMakeFiles/autoswap_agent.dir/main.cpp.o
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/async.h
 ../../share/libs/x86/include/spdlog/async_logger-inl.h
 ../../share/libs/x86/include/spdlog/async_logger.h
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/mpmc_blocking_q.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/thread_pool-inl.h
 ../../share/libs/x86/include/spdlog/details/thread_pool.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/vehicle_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/nlohmann_json/json.hpp
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././swap_agent_debug.h
 .././threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/main.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/cfg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
CMakeFiles/autoswap_agent.dir/multi_swap_manager.cpp.o
 ../../share/event_code.hpp
 ../../share/exception_code.hpp
 ../../share/global_def.h
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/nlohmann_json/json.hpp
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././threadpool/blocking_queue.hpp
 .././threadpool/condition.hpp
 .././threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/exception/dev_except.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/cfg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
CMakeFiles/autoswap_agent.dir/net/epoll_poller.cpp.o
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
CMakeFiles/autoswap_agent.dir/net/tcp_socket.cpp.o
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
CMakeFiles/autoswap_agent.dir/net/udp_socket.cpp.o
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
CMakeFiles/autoswap_agent.dir/protocol/train_protocol.cpp.o
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/nlohmann_json/json.hpp
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././multi_swap_manager.hpp
 .././protocol/train_protocol.hpp
 .././swap_agent_debug.h
 .././swap_manage/cfg.hpp
 .././swap_manage/swap_list.hpp
 .././swap_manage/swap_manage.hpp
 .././threadpool/blocking_queue.hpp
 .././threadpool/condition.hpp
 .././threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
CMakeFiles/autoswap_agent.dir/scheduler_msg/scheduler_msg.cpp.o
 ../../share/exception_code.hpp
 ../../share/global_def.h
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././fsm_manager/fsm_manager.hpp
 .././swap_agent_debug.h
 .././swap_manage/cfg.hpp
 .././swap_manage/swap_list.hpp
 .././threadpool/blocking_queue.hpp
 .././threadpool/condition.hpp
 .././threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
CMakeFiles/autoswap_agent.dir/swap_agent_config.cpp.o
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/nlohmann_json/json.hpp
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././swap_agent_debug.h
 .././threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/epoll_poller.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/tcp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/net/udp_socket.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_config.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/cfg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
CMakeFiles/autoswap_agent.dir/swap_manage/swap_list.cpp.o
 ../../share/exception_code.hpp
 ../../share/libs/x86/include/spdlog/async.h
 ../../share/libs/x86/include/spdlog/async_logger-inl.h
 ../../share/libs/x86/include/spdlog/async_logger.h
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/mpmc_blocking_q.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/thread_pool-inl.h
 ../../share/libs/x86/include/spdlog/details/thread_pool.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/vehicle_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/cfg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
CMakeFiles/autoswap_agent.dir/swap_manage/swap_manage.cpp.o
 ../../share/libs/x86/include/cppzmq/zmq.hpp
 ../../share/libs/x86/include/spdlog/common-inl.h
 ../../share/libs/x86/include/spdlog/common.h
 ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
 ../../share/libs/x86/include/spdlog/details/backtracer.h
 ../../share/libs/x86/include/spdlog/details/circular_q.h
 ../../share/libs/x86/include/spdlog/details/console_globals.h
 ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
 ../../share/libs/x86/include/spdlog/details/file_helper.h
 ../../share/libs/x86/include/spdlog/details/fmt_helper.h
 ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
 ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
 ../../share/libs/x86/include/spdlog/details/null_mutex.h
 ../../share/libs/x86/include/spdlog/details/os-inl.h
 ../../share/libs/x86/include/spdlog/details/os.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
 ../../share/libs/x86/include/spdlog/details/periodic_worker.h
 ../../share/libs/x86/include/spdlog/details/registry-inl.h
 ../../share/libs/x86/include/spdlog/details/registry.h
 ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
 ../../share/libs/x86/include/spdlog/details/windows_include.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
 ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
 ../../share/libs/x86/include/spdlog/fmt/fmt.h
 ../../share/libs/x86/include/spdlog/formatter.h
 ../../share/libs/x86/include/spdlog/logger-inl.h
 ../../share/libs/x86/include/spdlog/logger.h
 ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
 ../../share/libs/x86/include/spdlog/pattern_formatter.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/base_sink.h
 ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
 ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/sink.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
 ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
 ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
 ../../share/libs/x86/include/spdlog/spdlog-inl.h
 ../../share/libs/x86/include/spdlog/spdlog.h
 ../../share/libs/x86/include/spdlog/tweakme.h
 ../../share/libs/x86/include/spdlog/version.h
 ../../share/libs/x86/include/zmq.h
 ../../share/nlohmann_json/json.hpp
 ../../share/pb/idl/ack.pb.h
 ../../share/pb/idl/auto_exchange.pb.h
 ../../share/pb/idl/auto_exchange_info.pb.h
 ../../share/pb/idl/auto_exchange_map.pb.h
 ../../share/pb/idl/data_map.pb.h
 ../../share/pb/idl/data_request.pb.h
 ../../share/pb/idl/exception.pb.h
 ../../share/pb/idl/sys_interface.pb.h
 ../../share/pb/nanopb/pb.h
 ../../share/pb/nanopb/pb_decode.h
 ../../share/pb/nanopb/pb_encode.h
 .././fsm_manager/fsm_manager.hpp
 .././multi_swap_manager.hpp
 .././net/epoll_poller.hpp
 .././net/tcp_socket.hpp
 .././net/udp_socket.hpp
 .././scheduler_msg/scheduler_msg.hpp
 .././swap_agent_debug.h
 .././swap_manage/swap_list.hpp
 .././threadpool/blocking_queue.hpp
 .././threadpool/thread_pool.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/multi_swap_manager.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/protocol/train_protocol.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/cfg.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_list.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_manage/swap_manage.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
CMakeFiles/autoswap_agent.dir/threadpool/condition.cpp.o
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
CMakeFiles/autoswap_agent.dir/threadpool/thp_mutex.cpp.o
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
CMakeFiles/autoswap_agent.dir/threadpool/thread_pool.cpp.o
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/swap_agent_debug.h
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/blocking_queue.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/condition.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thp_mutex.hpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.cpp
 /home/<USER>/myfile/project/auto_replace_box/swap_agent/threadpool/thread_pool.hpp
