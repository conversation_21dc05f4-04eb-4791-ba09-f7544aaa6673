#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/global_def.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "converter/converter.hpp"

class vehicle_interface
{
public:

    int init(zmq::context_t &ctx);

    int get_vehicle_extstate(train_ext_state_multi &vehicles_state);
    int get_vehicle_info(train_basic_info_mutilp &vehicles_info);

    int get_vehicle_state(train_state &state);

    int get_vehicle_agent_state(train_agent_state &state);

    // int get_vehicle_info(vehicle_list &vehicles_info);

    int issue_vehicle_reset_cmd(uint32_t &vehicle_id);

    int issue_vehicle_standby_cmd(uint32_t &vehicle_id);         //下发待机命令，即车辆休眠

    int issue_vehicle_belt_left_moving(uint32_t &vehicle_id, uint32_t &carriage_id);

    int issue_vehicle_belt_right_moving(uint32_t &vehicle_id, uint32_t &carriage_id);

    int issue_vehicle_belt_stop(uint32_t &vehicle_id, uint32_t &carriage_id);

    int issue_carriage_y_move_control_pos(std::string &dev_id, uint16_t distance);
    int issue_carriage_y_move_control_highest(std::string &dev_id);
    int issue_carriage_y_move_control_minimum(std::string &dev_id);
    int issue_carriage_y_move_control_middle(std::string &dev_id);
    int issue_carriage_y_zero_calibration_control(std::string &dev_id);
    int issue_platform_belt_zeor_calibration_control(std::string &dev_id);
    int issue_platform_control_dump_truck_rotate(std::string &dev_id);
    int issue_platform_control_dump_truck_zero_calibration(std::string &dev_id);
    // int issue_carriage_belt_rotate_control(std::string &dev_id, uint16_t distance, uint16_t speed, int dir);
    int issue_platform_control_command_reset(std::string &dev_id);
    int issue_platform_control_command_enable(std::string &dev_id);
    int issue_platform_control_command_disable(std::string &dev_id);
    int platform_string_to_int(std::string &dev_id, uint32_t &train_id, uint32_t &carriage_id);
    int carriage_string_to_int(std::string &dev_id, uint32_t &train_id, uint32_t &carriage_id);
    int carriage_int_to_string(std::string &dev_id, uint32_t train_id, uint32_t carriage_id);
    int platform_int_to_string(std::string &dev_id, uint32_t train_id, uint32_t carriage_id);

    static vehicle_interface *get_instance(void)
    {
        static vehicle_interface instance;
        return &instance;
    }

private:

    zmq::socket_t *vehicle_extstate_recver;            //接收调度上报的车辆状态
    zmq::socket_t *vehicle_basic_info_recver;            //获取coreserver的车辆信息
    zmq::socket_t *vehicle_state_recver;            //接收车辆上报的车辆状态
    zmq::socket_t *vehicle_agent_state_recver;            //接收车辆代理上报的系统车辆状态
    // zmq::socket_t *vehicle_info_requester;          //请求data_base的车辆信息
    zmq::socket_t *vehicle_cmd_sender;              //由物控节点下发车辆命令

};
