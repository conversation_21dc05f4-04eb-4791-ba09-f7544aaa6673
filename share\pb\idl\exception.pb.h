/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_EXCEPTION_PB_H_INCLUDED
#define PB_EXCEPTION_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _exception_src {
    exception_src_TRAIN = 0, /* 分播车头 */
    exception_src_CARRIAGE = 1, /* 分播车厢 */
    exception_src_PLATFORM = 2, /* 载货台 */
    exception_src_FEEDER = 3, /* 供包台: 皮带，扫码头等 */
    exception_src_SECURITY = 4, /* 安全组件 */
    exception_src_CONTAINER = 5, /* 格口 */
    exception_src_CAMERA = 6, /* 灰度相机 */
    exception_src_CONT_SHELVES = 7, /* 移动货架 */
    exception_src_CORESERVICE = 8, /* 核心组件 */
    exception_src_TRAIN_AGENT = 9, /* 车组代理 */
    exception_src_SCHEDULER = 10, /* 调度: 位置匹配错误等 */
    exception_src_FEEDER_AGENT = 11, /* 供包台代理 */
    exception_src_CONTAINER_AGENT = 12, /* 格口代理 */
    exception_src_THING_AGENT = 13, /* 物控代理 */
    exception_src_AUTO_EXCHANGE_DEV = 14, /* 自动取换箱设备 */
    exception_src_AUTO_EXCHANGE_AGENT = 15 /* 自动取换箱代理 */
} exception_src;

typedef enum _exception_level {
    exception_level_RESERVE = 0,
    exception_level_NORMAL = 1,
    exception_level_WARNNING = 2,
    exception_level_ERROR = 3,
    exception_level_FATAL = 4
} exception_level;

typedef enum _exception_state {
    exception_state_STATE_OCCURED = 0,
    exception_state_STATE_RESET = 1
} exception_state;

typedef enum _exception_handle {
    exception_handle_HANDLE_SELF_RECOVERY = 0,
    exception_handle_HANDLE_HIGH_LEVEL = 1
} exception_handle;

/* Struct definitions */
typedef struct _except_info {
    exception_src src;
    exception_level level;
    uint32_t code;
    uint32_t sub_code;
    char description[128];
    uint32_t dev;
    uint32_t sub_dev;
    exception_state state;
    exception_handle handle;
} except_info;

typedef struct _event_info {
    exception_src src;
    uint32_t dev;
    uint32_t sub_dev;
    uint32_t code;
    uint32_t sub_code;
} event_info;

typedef struct _event_exception {
    pb_size_t which_evt_except;
    union {
        event_info evt;
        except_info except;
    } evt_except;
} event_exception;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _exception_src_MIN exception_src_TRAIN
#define _exception_src_MAX exception_src_AUTO_EXCHANGE_AGENT
#define _exception_src_ARRAYSIZE ((exception_src)(exception_src_AUTO_EXCHANGE_AGENT+1))

#define _exception_level_MIN exception_level_RESERVE
#define _exception_level_MAX exception_level_FATAL
#define _exception_level_ARRAYSIZE ((exception_level)(exception_level_FATAL+1))

#define _exception_state_MIN exception_state_STATE_OCCURED
#define _exception_state_MAX exception_state_STATE_RESET
#define _exception_state_ARRAYSIZE ((exception_state)(exception_state_STATE_RESET+1))

#define _exception_handle_MIN exception_handle_HANDLE_SELF_RECOVERY
#define _exception_handle_MAX exception_handle_HANDLE_HIGH_LEVEL
#define _exception_handle_ARRAYSIZE ((exception_handle)(exception_handle_HANDLE_HIGH_LEVEL+1))

#define except_info_src_ENUMTYPE exception_src
#define except_info_level_ENUMTYPE exception_level
#define except_info_state_ENUMTYPE exception_state
#define except_info_handle_ENUMTYPE exception_handle

#define event_info_src_ENUMTYPE exception_src



/* Initializer values for message structs */
#define except_info_init_default                 {_exception_src_MIN, _exception_level_MIN, 0, 0, "", 0, 0, _exception_state_MIN, _exception_handle_MIN}
#define event_info_init_default                  {_exception_src_MIN, 0, 0, 0, 0}
#define event_exception_init_default             {0, {event_info_init_default}}
#define except_info_init_zero                    {_exception_src_MIN, _exception_level_MIN, 0, 0, "", 0, 0, _exception_state_MIN, _exception_handle_MIN}
#define event_info_init_zero                     {_exception_src_MIN, 0, 0, 0, 0}
#define event_exception_init_zero                {0, {event_info_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define except_info_src_tag                      1
#define except_info_level_tag                    2
#define except_info_code_tag                     3
#define except_info_sub_code_tag                 4
#define except_info_description_tag              5
#define except_info_dev_tag                      6
#define except_info_sub_dev_tag                  7
#define except_info_state_tag                    8
#define except_info_handle_tag                   9
#define event_info_src_tag                       1
#define event_info_dev_tag                       2
#define event_info_sub_dev_tag                   3
#define event_info_code_tag                      4
#define event_info_sub_code_tag                  5
#define event_exception_evt_tag                  1
#define event_exception_except_tag               2

/* Struct field encoding specification for nanopb */
#define except_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    src,               1) \
X(a, STATIC,   SINGULAR, UENUM,    level,             2) \
X(a, STATIC,   SINGULAR, UINT32,   code,              3) \
X(a, STATIC,   SINGULAR, UINT32,   sub_code,          4) \
X(a, STATIC,   SINGULAR, STRING,   description,       5) \
X(a, STATIC,   SINGULAR, UINT32,   dev,               6) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev,           7) \
X(a, STATIC,   SINGULAR, UENUM,    state,             8) \
X(a, STATIC,   SINGULAR, UENUM,    handle,            9)
#define except_info_CALLBACK NULL
#define except_info_DEFAULT NULL

#define event_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    src,               1) \
X(a, STATIC,   SINGULAR, UINT32,   dev,               2) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev,           3) \
X(a, STATIC,   SINGULAR, UINT32,   code,              4) \
X(a, STATIC,   SINGULAR, UINT32,   sub_code,          5)
#define event_info_CALLBACK NULL
#define event_info_DEFAULT NULL

#define event_exception_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (evt_except,evt,evt_except.evt),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (evt_except,except,evt_except.except),   2)
#define event_exception_CALLBACK NULL
#define event_exception_DEFAULT NULL
#define event_exception_evt_except_evt_MSGTYPE event_info
#define event_exception_evt_except_except_MSGTYPE except_info

extern const pb_msgdesc_t except_info_msg;
extern const pb_msgdesc_t event_info_msg;
extern const pb_msgdesc_t event_exception_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define except_info_fields &except_info_msg
#define event_info_fields &event_info_msg
#define event_exception_fields &event_exception_msg

/* Maximum encoded size of messages (where known) */
#define EXCEPTION_PB_H_MAX_SIZE                  event_exception_size
#define event_exception_size                     165
#define event_info_size                          26
#define except_info_size                         162

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
