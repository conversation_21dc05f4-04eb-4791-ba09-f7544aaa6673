/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 头文件，提供对外的API函数
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */
#ifndef __JD_THINGTALK_SDK_H__
#define __JD_THINGTALK_SDK_H__

#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"

#include "jd_thingtalk/pal/inc/jd_thingtalk_stdint.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_mqtt.h"
#include "jd_thingtalk/pal/inc/jd_thingtalk_time.h"

#ifdef __cplusplus
extern "C"{
#endif /* __cplusplus */

/**
 * @brief  sdk 设置参数结构体
 */
typedef struct {
    char      protocol[16];      // 协议类型{"mqtt_tcp", "mqtt_tls"}
    char      *hostname;        // 服务器地址
    int32_t   port;             // 服务器连接的端口号
    char      *deviceId;        // 设备标识
    char      *username;        // 用户名
    char      *password;        // 密码
    char      *cafile;          // ca 文件路径
    char      *cert;            // certificate 文件路径
    char      *key;             // key 文件路径
    bool      insecure;         // 是否验证主机名
    char      *devpubkey;     // 设备公钥
    char      *devprtkey;     // 设备私钥
    char      *platpubkey;    // 平台公钥
} jd_thingtalk_sdk_config_t;

/**
 * @brief  sdk 句柄定义
 */
struct jd_thingtalk_sdk_t;

/**
 * @brief  一个简单的测试函数，TODO 最终会删除
 */
int32_t jd_thingtalk_sdk_test(void);

/**
 * @brief   创建一个sdk config
 *
 * @return 
 *    sdk config 指针
 * @see None.
 * @note None.
 */
jd_thingtalk_sdk_config_t *jd_thingtalk_sdk_config_create();

/**
 * @brief   销毁 sdk config
 *
 * @param[in] config: sdk 设置参指针
 * @return 
 *	   返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_config_destory(jd_thingtalk_sdk_config_t *config);

/**
 * @brief   创建 sdk
 *
 * @param[in] config: sdk 设置参指针
 * @return 
 *    sdk 句柄
 * @see None.
 * @note None.
 */
struct jd_thingtalk_sdk_t *jd_thingtalk_sdk_create(jd_thingtalk_sdk_config_t *config);

/**
 * @brief   销毁 sdk
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *	   返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_destory(struct jd_thingtalk_sdk_t *sdk);

/**
 * @brief   sdk 初始化函数
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_initialise(struct jd_thingtalk_sdk_t *sdk);

/**
 * @brief   sdk 连接broker函数
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_connect(struct jd_thingtalk_sdk_t *sdk);

/**
 * @brief   sdk 进入主循环
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *	   返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_main_loop(struct jd_thingtalk_sdk_t *sdk);

/**
 * @brief   sdk 获取消息id
 *
 * @param[in] prefix: 消息id的前缀
 * @return 
 *    消息id字符串地址
 * @see None.
 * @note None.
 */
char *jd_thingtalk_sdk_get_messageId(char *prefix);

/**
 * @brief   sdk 连接成功回调函数 包括断开重连
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_connect) (struct jd_thingtalk_sdk_t *);

/**
 * @brief   sdk 设置连接成功 回调函数
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_connect: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_connect(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_connect cb_connect);

/**
 * @brief   sdk 连接断开回调函数
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_disconnect) (struct jd_thingtalk_sdk_t *);

/**
 * @brief   sdk 设置连接断开 回调函数
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_disconnect: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_disconnect(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_disconnect cb_disconnect);

/**
 * @brief  sdk 属性版本号最大最小值宏定义
 *
 */
#define JD_THINGTALK_PROP_VERSION_MIN    (0)
#define JD_THINGTALK_PROP_VERSION_MAX    (65535)

/**
 * @brief  sdk 属性版本号检测容忍度宏定义
 *
 * @note: 属性版本号 范围为 0～65535，在进行属性设置时需要进行版本号检测，只有请求的版本号不小于当前的版本号才能执行操作
 *        另外约定 0大于65535, 由于版本号差别可能会超过1,因此需要设置一个容错的范围
 */
#define JD_THINGTALK_PROP_VERSION_TOLERANCE    (100)

/**
 * @brief   sdk 设备属性版本号检测
 *
 * @param[in] cur_version: 当前的版本号
 * @param[in] req_version: 请求的版本号
 * @return
 *    返回值：1 表示请求接受，0 表示请求拒绝
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_prop_version_check(uint16_t cur_version, uint16_t req_version);

/**
 * @brief   sdk 设备属性设置 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_prop_set) (struct jd_thingtalk_sdk_t *, char *obj_name, char *service_key, JDThingTalkProtoPropSet_t *);

/**
 * @brief   sdk 设备回调函数设置 属性设置
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_set: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 当设备断电重启或者断网重连后,云端管理平台会主动下发一次属性设置消息(即使用户不在管理平台或者APP中进行任何下发指令的操作,属性设置指令也会下发),
 *       如果设备断电重启或者断网重连后,自身特有的处理逻辑或者会涉及到一些安全问题,包括但不局限于设备安全,人身安全等,请自行谨慎处理
 */
int32_t jd_thingtalk_sdk_set_dev_cb_prop_set(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_prop_set cb_prop_set);

/**
 * @brief   sdk 设备属性设置响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_set_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropSetRes_t *in_res);

/**
 * @brief   sdk 设备属性获取 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_prop_get) (struct jd_thingtalk_sdk_t *, char *obj_name, char *service_key, JDThingTalkProtoPropGet_t *);

/**
 * @brief   sdk 设备回调函数设置 属性获取
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_get: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_prop_get(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_prop_get cb_prop_get);

/**
 * @brief   sdk 设备属性获取响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_get_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropGetRes_t *in_res);

/**
 * @brief   sdk 属性上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 上报的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_prop_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoPropPost_t *in_post);

/**
 * @brief   sdk 设备方法调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_func_call) (struct jd_thingtalk_sdk_t *, char *obj_name, char *service_key, JDThingTalkProtoFuncCall_t *);

/**
 * @brief   sdk 设备回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_func_call cb_func_call);

/**
 * @brief   sdk 设备方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_func_call_response(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief   sdk 事件 上下线状态上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_status: 上下线消息体结构指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_evt_online_status_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoEvtOnlineStatus_t *in_status);

/**
 * @brief   sdk 事件 事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_evt_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoEvtPost_t *in_post);

/**
 * @brief   sdk 自动注册请求
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_req: 自动注册请求结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_reg_request(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoRegReq_t *in_req);

/**
 * @brief   sdk 设备自动注册响应调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_reg_res) (struct jd_thingtalk_sdk_t *, char *obj_name, char *service_key, JDThingTalkProtoRegReqRes_t *);

/**
 * @brief   sdk 设备回调函数设置 自动注册响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_reg_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_reg_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_reg_res cb_reg_res);

/**
 * @brief   sdk 物模型上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] service_key: 服务名字, 通用属性时填指针
 * @param[in] in_post: 物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_thing_model_post(struct jd_thingtalk_sdk_t *sdk,
            char *obj_name, char *service_key, JDThingTalkProtoThingModelPost_t *in_post);

/**
 * @brief   sdk 物模型上报响应调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_thmd_post_res) (struct jd_thingtalk_sdk_t *, char *obj_name, char *service_key, JDThingTalkProtoThingModelPostRes_t *);

/**
 * @brief   sdk 设备回调函数设置 物模型上报响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_thmd_post_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_cb_thmd_post_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_thmd_post_res cb_thmd_post_res);

/**
 * @brief   sdk 设备NTP授时请求
 *
 * @param[in] sdk: sdk 的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ntp_request(struct jd_thingtalk_sdk_t *sdk);

/**
 * @brief   sdk 设备NTP授时请求响应 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_ntp_req_res) (struct jd_thingtalk_sdk_t *, jd_thingtalk_time_stamp_t *);

/**
 * @brief   sdk 设备回调函数设置 设备NTP授时请求响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_ntp_req_res: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_ntp_req_res(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_ntp_req_res cb_ntp_req_res);

/**
 * @brief   sdk OTA方法调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_dev_ota_func_call) (struct jd_thingtalk_sdk_t *, char *, JDThingTalkProtoFuncCall_t *);

/**
 * @brief   sdk OTA回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_dev_ota_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_dev_ota_func_call cb_func_call);

/**
 * @brief   sdk OTA方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_func_call_response(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief   sdk ota 当前状态上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] task_id: 任务标识
 * @param[in] state: 当前状态
 * @param[in] e_code: 错误码
 * @param[in] progress: 当前进度百分比
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_state_changed_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name,
            char *task_id, JD_THINGTALK_PROTO_OTA_STATE_T state, JD_THINGTALK_PROTO_OTA_ERROR_T e_code, int8_t progress);

/**
 * @brief   sdk ota 版本号变化上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] object_id: 子设备的设备标识，可为空，表示自身的
 * @param[in] version: 当前的升级包版本号
 * @param[in] type: 升级对象的类型
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_dev_ota_version_changed_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name,
            char *object_id, char *version, char *type);

/**
 * @brief   sdk 连接代理方法调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_agent_func_call) (struct jd_thingtalk_sdk_t *, JDThingTalkProtoFuncCall_t *);

/**
 * @brief   sdk 连接代理回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_agent_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_agent_func_call cb_func_call);

/**
 * @brief   sdk 连接代理方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_func_call_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief   sdk 连接代理 设备绑定事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] sub_dev_info: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_event_add_devices(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoSubDeviceInfo_t *sub_dev_info);

/**
 * @brief   sdk 连接代理 设备解绑事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] sub_dev_info: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_agent_event_delete_devices(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoSubDeviceInfo_t *sub_dev_info);

/**
 * @brief   sdk 子设备上线上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] sub_dev: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_online_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoSubDevice_t *sub_dev);

/**
 * @brief   sdk 子设备下线上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] obj_name: object namespace 可能取值 {"device", "edge", "group"}
 * @param[in] sub_dev: 子设备信息
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_offline_post(struct jd_thingtalk_sdk_t *sdk, char *obj_name, JDThingTalkProtoSubDevice_t *sub_dev);

/**
 * @brief   sdk 子设备属性设置 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_sub_dev_prop_set) (struct jd_thingtalk_sdk_t *, JDThingTalkProtoPropSet_t *);

/**
 * @brief   sdk 子设备回调函数设置 属性设置
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_set: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 当设备断电重启或者断网重连后,云端管理平台会主动下发一次属性设置消息(即使用户不在管理平台或者APP中进行任何下发指令的操作,属性设置指令也会下发),
 *       如果设备断电重启或者断网重连后,自身特有的处理逻辑或者会涉及到一些安全问题,包括但不局限于设备安全,人身安全等,请自行谨慎处理
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_prop_set(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_prop_set cb_prop_set);

/**
 * @brief   sdk 子设备属性设置响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_set_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropSetRes_t *in_res);

/**
 * @brief   sdk 子设备属性获取 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_sub_dev_prop_get) (struct jd_thingtalk_sdk_t *, JDThingTalkProtoPropGet_t *);

/**
 * @brief   sdk 子设备回调函数设置 属性获取
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_prop_get: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_prop_get(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_prop_get cb_prop_get);

/**
 * @brief   sdk 子设备属性获取响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 属性设置响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_get_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropGetRes_t *in_res);

/**
 * @brief   sdk 子设备方法调用 回调函数句柄
 *
 */
typedef int32_t (*jd_thingtalk_sdk_callback_sub_dev_func_call) (struct jd_thingtalk_sdk_t *, JDThingTalkProtoFuncCall_t *);

/**
 * @brief   sdk 子设备回调函数设置 方法调用
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] cb_func_call: 回调函数句柄
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_set_sub_dev_cb_func_call(struct jd_thingtalk_sdk_t *sdk,
            jd_thingtalk_sdk_callback_sub_dev_func_call cb_func_call);

/**
 * @brief   sdk 子设备方法调用响应
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_res: 方法调用响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_func_call_response(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoFuncCallRes_t *in_res);

/**
 * @brief   sdk 子设备属性上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_post: 上报的结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_prop_post(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoPropPost_t *in_post);

/**
 * @brief   sdk 子设备事件上报
 *
 * @param[in] sdk: sdk 的指针
 * @param[in] in_post: 事件上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_sdk_sub_dev_evt_post(struct jd_thingtalk_sdk_t *sdk, JDThingTalkProtoEvtPost_t *in_post);


#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __JD_THINGTALK_SDK_H__ */
