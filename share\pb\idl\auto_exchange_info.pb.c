/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "auto_exchange_info.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(workstation_basic_info, workstation_basic_info, AUTO)


PB_BIND(auto_exchange_basic_info, auto_exchange_basic_info, AUTO)


PB_BIND(auto_exchange_basic_info_mutilp, auto_exchange_basic_info_mutilp, 2)




