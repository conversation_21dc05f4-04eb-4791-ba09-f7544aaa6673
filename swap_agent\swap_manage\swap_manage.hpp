#ifndef __SWAP_MANAGE_HPP__
#define __SWAP_MANAGE_HPP__

// #include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/auto_exchange.pb.h"
#include "multi_swap_manager.hpp"
#include "swap_list.hpp"
#include "../protocol/train_protocol.hpp"

using namespace std;

/**@enum TRAIN_MSG_TYPE
* @brief 定义网络数传的车辆协议数据所对应的车辆行为
*/
typedef enum
{
	TRAIN_MSG_UNDEFINED = 0,    ///<  无定义
	SWAP_MSG_REG     = 1,      ///<  车辆注册
	SWAP_MSG_STATE	= 2,        ///<  车辆状态上报
    SWAP_MSG_EXCEPTIONAL = 3,  ///<  车辆异常上报
	SWAP_MSG_CMD_ACK  = 4,     ///<  车辆命令回复
}TRAIN_MSG_TYPE;


// 行为定义 UDP 协议
#define TRAIN_PROTOCOL_OPERATION_REGISTER_UDP									(TRAIN_PROTOCOL_SESSION_REGISTER)
#define TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP										(TRAIN_PROTOCOL_SESSION_CMD_OPT)
#define TRAIN_PROTOCOL_SESSION_HEART_BEAT_UDP									(TRAIN_PROTOCOL_SESSION_HEART_BEAT)
#define TRAIN_PROTOCOL_SESSION_EXCEPTION_UDP									(TRAIN_PROTOCOL_SESSION_EXCEPTION)


//小车指令具体参数
#define TRAIN_PROTOCOL_CMD_MOVE_X		                            ( 0x10 )
#define TRAIN_PROTOCOL_CMD_MOVE_X_ACK								( 0x11 )

#define TRAIN_PROTOCOL_CMD_MOVE_Y									( 0x20 )
#define TRAIN_PROTOCOL_CMD_MOVE_Y_ACK								( 0x21 )

#define TRAIN_PROTOCOL_CMD_DOWNLOAD_HEARTBEAT						( 0x30 )

#define TRAIN_PROTOCOL_CMD_MOVE_Z									( 0x40 )
#define TRAIN_PROTOCOL_CMD_MOVE_Z_ACK								( 0x41 )

#define TRAIN_PROTOCOL_CMD_SWAP_PACK								( 0x60 )
#define TRAIN_PROTOCOL_CMD_SWAP_PACK_ACK							( 0x61 )

#define TRAIN_PROTOCOL_CMD_SWAP_UNPACK								( 0x70 )
#define TRAIN_PROTOCOL_CMD_SWAP_UNPACK_ACK							( 0x71 )

#define TRAIN_PROTOCOL_CMD_EXCEPTION                                ( 0x80 ) //0x01:小车急停；0x02:小车复位；0x03:小车重启指令(暂时没有)
#define TRAIN_PROTOCOL_CMD_EXCEPTION_ACK							( 0x81 )

#define TRAIN_PROTOCOL_CMD_QUERY_PARA                               ( 0x90 )
#define TRAIN_PROTOCOL_CMD_QUERY_PARA_ACK                           ( 0x91 )

#define TRAIN_PROTOCOL_CMD_REAL_TIME_PARA_SEND						( 0xA0 )
#define TRAIN_PROTOCOL_CMD_VERSION_INFO_ACK							( 0xA1 )

#define TRAIN_PROTOCOL_CMD_MILEAGE_INFO								( 0xB0 )
#define TRAIN_PROTOCOL_CMD_MILEAGE_INFO_ACK							( 0xB1 )

#define TRAIN_PROTOCOL_CMD_PACK_MOVE								( 0xD0 )
#define TRAIN_PROTOCOL_CMD_PACK_MOVE_ACK							( 0xD1 )

#define TRAIN_PROTOCOL_CMD_UNPACK_MOVE								( 0xE0 )
#define TRAIN_PROTOCOL_CMD_UNPACK_MOVE_ACK							( 0xE1 )

#define TRAIN_PROTOCOL_CMD_PACK_UNPACK								( 0xF0 )
#define TRAIN_PROTOCOL_CMD_PACK_UNPACK_ACK							( 0xF1 )




#define TRAIN_PROTOCOL_CMD_MOVE_X_LEN 								( 9 )
#define TRAIN_PROTOCOL_CMD_MOVE_Y_LEN								( 7 )
#define TRAIN_PROTOCOL_CMD_DOWNLOAD_HEARTBEAT_LEN					( 5 )
#define TRAIN_PROTOCOL_CMD_MOVE_Z_LEN								( 6 )
#define TRAIN_PROTOCOL_CMD_SWAP_PACK_LEN							( 10 )
#define TRAIN_PROTOCOL_CMD_SWAP_UNPACK_LEN							( 10 )
#define TRAIN_PROTOCOL_CMD_EXCEPTION_LEN							( 2 )
#define TRAIN_PROTOCOL_CMD_REAL_TIME_PARA_SEND_LEN					( 44 )
#define TRAIN_PROTOCOL_CMD_MILEAGE_INFO_LEN							( 9 )
#define TRAIN_PROTOCOL_CMD_PACK_MOVE_LEN							( 25 )
#define TRAIN_PROTOCOL_CMD_UNPACK_MOVE_LEN							( 25 )
#define TRAIN_PROTOCOL_CMD_PACK_UNPACK_LEN							( 37 )





/**@brief     根据网络数据包，解析车辆传输的数据并判断车辆目标行为类型
* @param[in]  uint8_t *buf --- 输入的车辆网络数据包
* @param[in]  uint16_t buf_len --- 输入的车辆网络数据包长度
* @param[in]  uint8_t *data_buf --- 解码得到的data字段数据缓冲区
* @param[out] int *data_cnt  --- 解码得到的data字段长度
* @param[out] uint32_t *id  --- 解码得到的车辆ID 
* @param[out] uint32_t *sequeue  --- 解码得到的通信序列号
* @return     车辆消息类型，详见头文件定义
*/
extern TRAIN_MSG_TYPE swap_manage_msg_type(uint8_t *buf, uint16_t buf_len, uint8_t *data_buf, int *data_cnt, uint8_t *id, uint32_t *sequeue);


/**@brief     车辆注册消息ACK生成
* @param[in]  int dev_id --- 车辆ID
* @param[in]  uint32_t seque  --- 有效通信序列号
* @param[in]  dev_agent_cfg *cfg  ---待ack的配置参数
* @param[out] uint8_t *send  --- 输出的注册ACK数据缓冲区
* @param[out] uint16_t *send_len  --- 输出的注册ACK数据缓冲区长度
* @return     NULL 
*/
extern void swap_manage_train_register_ack(int dev_id, uint32_t seque, uint8_t *send, uint16_t *send_len, dev_agent_cfg *cfg);


/**@brief     获取取换箱状态信息
* @param[out] auto_exchange_dev_state *swap_state_temp --- 取换箱状态信息
* @param[out] dev_state_net *dev_net_state ---网络数据包
* @param[in]  uint8_t *data_buf	--- data字段数据缓冲区
* @param[in]  uint8_t dev_id	 --- 车头ID
* @param[out] uint8_t *excep_level	 --- 异常等级
* @param[out] uint32_t *excep_code	 --- 异常码
*/
extern void swap_manage_get_dev_state(auto_exchange_dev_state *swap_state_temp, dev_state_net *dev_net_state, uint8_t *data_buf, uint8_t dev_id, uint8_t *excep_id, uint8_t *excep_level, uint32_t *excep_code);


/**@brief     判断待执行任务的类型
* @param[in]  auto_exchange_task *v_task --- 接收到的任务数据结构体
* @param[out] uint8_t *data_out --- 根据任务数据生成的网络字节流
* @param[out] uint16_t *data_len --- 生成的网络字节流长度
* @param[in]  dev_agent_cfg *cfg  --- 配置参数
* @return     操作结果，用来判断数据有效性
* - true      操作成功，数据有效
* - false     操作失败，数据无效
*/
extern int swap_manage_release_task(auto_exchange_task *v_task, uint8_t *data_out, uint16_t *data_len, dev_agent_cfg *cfg, int task_id);
extern void dev_query_para_recv(dev_run_para *para, uint8_t *data_buf);
void run_para_display(dev_run_para *cfg);

void byte_conversion_uint32(uint8_t *buff_ptr, uint32_t data);
void byte_conversion_uint16(uint8_t *buff_ptr, uint16_t data);
void cfg_display(dev_state_net *dev_net_state);

void hb_log_decode(uint8_t *buffer, uint8_t *data_buf, string *net_msg_decode);



#endif