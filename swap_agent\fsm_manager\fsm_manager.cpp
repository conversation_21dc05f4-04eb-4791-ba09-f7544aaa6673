﻿#include "fsm_manager.hpp"
#include "share/global_def.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>    
#include <spdlog/async.h>

#include "./threadpool/blocking_queue.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/sys_interface.pb.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex>          

using namespace std;


/**@brief  vehicle_list_map class析构函数
* @param[in]  NULL
* @return     NULL
*/
fsm_manager::~fsm_manager() 
{

}



void fsm_manager::fsm_manager_init(zmq::context_t &ctx) 
{
	m_dev_fsm_subscriber = new zmq::socket_t {ctx, zmq::socket_type::sub};
	m_dev_fsm_subscriber->connect(TOPIC_SYS_STATE);
	m_dev_fsm_subscriber->set(zmq::sockopt::subscribe, "");

}


//实时获取系统状态
void fsm_manager::fsm_manager_run(void) 
{
	m_dev_state_get_thread = new std::thread(&fsm_manager::fsm_manager_main_thread, this);

}


sys_mode_state fsm_manager::fsm_manager_get_sys_state(void) 
{
	std::lock_guard<std::mutex> lock(m_dev_sys_state_mutex);

	return m_dev_sys_state;
}

void fsm_manager::fsm_manager_main_thread(void) 
{
	SPDLOG_INFO("fsm_manager_main_thread start... ");

	while(1)
	{
		fsm_manager_get_fsm_state();

		std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
}


bool fsm_manager::fsm_manager_get_fsm_state(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	sys_mode_state dev_state_temp;

	if(m_dev_fsm_subscriber->recv(msg, zmq::recv_flags::none))
	{
		SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber rec msg ");
		
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());

		if (!pb_decode(&stream_in, sys_mode_state_fields, &dev_state_temp))
		{
			SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber msg pb decode fail ");

			return false;
		}
		else
		{
			m_dev_sys_state = dev_state_temp;

			SPDLOG_INFO("[ZMQ] m_dev_fsm_subscriber e_wkstate:{}, safty_door_open:{}, emerg_pressed:{} ", m_dev_sys_state.state, m_dev_sys_state.dev_st.safty_door_open, m_dev_sys_state.dev_st.emerg_pressed);	
		}
	}
	else
	{
		return false;
	}

	return true;
}

