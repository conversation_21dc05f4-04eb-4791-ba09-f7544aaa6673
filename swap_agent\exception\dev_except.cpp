#include "dev_except.hpp"

namespace dev_except
{
	enum except_attr
	{
		DEVEXCEPT_ATTR_RESUMABLE = 1,
		DEVEXCEPT_ATTR_SUBCODE_MEANS = 2,
		
		DEVEXCEPT_ATTR_SUBCODE_ATTR_BITS = (1<<16),
	};

	struct except_map
	{
		uint32_t errcode;		//设备上报异常码
		uint32_t subcode;

		except_info except;		//系统内定义的异常描述
		int attr;		
	};

	/*设备上报异常与系统内异常描述的对照表，用来实现异常相互转换*/
	static const except_map except_map_table[] =
	{	
		// 故障码	子码	异常源	异常等级	异常码	异常描述	异常子码	异常子码描述	异常状态
		// 严重故障		PARA_NOT_CONFIG  
		{1, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_EMERGENCY_ERROR, 0, "取换箱急停触发", 0, 0, exception_state_STATE_OCCURED}, 0},
		{2, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SOFT_EMGE_ERROR, 0, "取换箱软急停触发", 0, 0, exception_state_STATE_OCCURED}, 0},
		{3, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SAFE_TRG_ERROR, 0, "取换箱安全触边", 0, 0, exception_state_STATE_OCCURED}, 0},
		{4, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_PARA_NOT_CONFIG_ERROR, 0, "参数异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{5, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_INIT_ERROR, 0, "行走伺服初始化失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{6, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_MOVE_SERVO_ERROR, 0, "取换箱行走伺服异常", 0, 0, exception_state_STATE_OCCURED}, 0},
		{7, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_X_ZERO_ERROR, 0, "取换箱X轴寻零失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{8, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_Y_ZERO_ERROR, 0, "取换箱Y轴寻零失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{9, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_Z_ZERO_ERROR, 0, "取换箱Z轴寻零失败", 0, 0, exception_state_STATE_OCCURED}, 0},
		{10, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_MOVE_SENSOR_BOX, 0, "取换箱卡货风险", 0, 0, exception_state_STATE_OCCURED}, 0},
		{11, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_MOVE_X_TIMOUT, 0, "取换箱X轴执行动作超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{12, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_MOVE_Y_TIMOUT, 0, "取换箱Y轴执行动作超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{13, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_MOVE_Z_TIMOUT, 0, "取换箱Z轴执行动作超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{14, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_ERR_X_DIFF_LARGE, 0, "取换箱X轴行走偏差过大", 0, 0, exception_state_STATE_OCCURED}, 0},
		{15, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_ZERO_X_TIMOUT, 0, "取换箱X轴寻零超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{16, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_ZERO_Y_TIMOUT, 0, "取换箱Y轴寻零超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{17, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_ZERO_Z_TIMOUT, 0, "取换箱Z轴寻零超时", 0, 0, exception_state_STATE_OCCURED}, 0},
		{18, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_SERVO_ERR_Y_DIFF_LARGE, 0, "取换箱Y轴偏差过大", 0, 0, exception_state_STATE_OCCURED}, 0},
		
		{50, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_ERROR, AUTO_EXCHANGE_MOVE_WHEEL_SLID, 0, "行走编码器脱轨或行走轮打滑", 0, 0, exception_state_STATE_OCCURED}, 0},

		

		{99, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_AGENT, exception_level_ERROR, AUTO_EXCHANGE_SELF_RESET, 0, "取换箱发生重启", 0, 0, exception_state_STATE_OCCURED}, 0},


		//警告
		{102, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_WARNNING, AUTO_EXCHANGE_PARA_NOT_CONFIG_WARNING , 0, "取换箱使用默认参数告警", 0, 0, exception_state_STATE_OCCURED}, 0},


		{201, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_DEV, exception_level_WARNNING, AUTO_EXCHANGE_DOWN_HEARTBEAT_TIME_OUT_WARNING , 0, "取换箱下行心跳超时", 0, 0, exception_state_STATE_OCCURED}, 0},


		{300, EXCEPTION_ALL, {exception_src_AUTO_EXCHANGE_AGENT, exception_level_WARNNING, AUTO_EXCHANGE_CMD_RESEND_WARNING, 0, "取换箱指令重发次数超限", 0, 0, exception_state_STATE_OCCURED}, 0},

		

	};

	//static bool comp(excpet_map a, )
	/*设备上报的异常码转为系统内定义的异常*/
	int errcode_to_exception(uint32_t err_code, uint32_t subcode, except_info &except)
	{
		for(auto const & e: except_map_table)
		{
			if ((e.errcode == err_code) && ((e.subcode == EXCEPTION_ALL) || (e.subcode == subcode)))
			{
				except = e.except;
				except.sub_code = subcode;

				// if(e.attr & DEVEXCEPT_ATTR_SUBCODE_MEANS)
				// {
				// 	strcat(except.description, std::to_string(subcode).c_str());
				// }
				return 0;
			}
		}
		
		return -1;
	}


	/*系统内定义的异常转为设备上报的异常码*/
	int exception_to_errcode(const except_info &except, uint32_t &err_code, uint32_t &subcode)
	{
		for(auto const & e: except_map_table)
		{
			if ((e.except.src == except.src) && (e.except.dev == except.dev) && (e.except.sub_dev == except.sub_dev) &&
				(e.except.code == except.code) && ((e.except.sub_code == except.sub_code) || (e.except.sub_code == EXCEPTION_ALL)))
			{
				err_code = e.errcode;
				subcode = except.sub_code;
				return 0;
			}
		}
		
		return -1;
	}
	
}