cmake_minimum_required(VERSION 3.5)

SET( CROSS_COMPILE OFF )

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()


project(train_agent LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 11)

set(CMAKE_CXX_STANDARD_REQUIRED ON)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread ")

add_definitions(-Wall)

add_definitions(-D UNIT_TEST)

include_directories("../")

include_directories("../share/pb/nanopb" ".")

include_directories("../share/libs/include")

link_libraries(spdlog zmq)

add_subdirectory("../share/pb/nanopb" nanopb_binary_dir)
add_subdirectory("../share/pb/idl" idl_binary_dir)

#添加线程池库
add_subdirectory(threadpool)
aux_source_directory(threadpool THREADPOOL)
#添加网络库
add_subdirectory(net)
aux_source_directory(net NET)
#添加协议库
add_subdirectory(protocol)
aux_source_directory(protocol PROTOCOL)
#添加车辆管理模块
add_subdirectory(train_manage)
aux_source_directory(train_manage TRAIN_MANAGE)
#ZMQ消息模块
add_subdirectory(scheduler_msg)
aux_source_directory(scheduler_msg  SCHEDULER_MSG)

#系统状态模块
add_subdirectory(fsm_manager)
aux_source_directory(fsm_manager FSM_MANAGER)

aux_source_directory(exception DEV_EXCEPTION_SRC)

#默认当前文件夹下所有文件均参与编译
aux_source_directory(. DIR_SRCS)

#生成所需文件
add_executable(train_agent ${DIR_SRCS} ${THREADPOOL} ${SCHEDULER_MSG} ${NET} ${PROTOCOL} ${TRAIN_MANAGE} ${FSM_MANAGER} ${DEV_EXCEPTION_SRC} )

#添加外部库依赖
target_link_libraries(train_agent nanopb idl lib_net lib_threadpool lib_msg lib_protocol lib_train_manage lib_fsm_manager)
