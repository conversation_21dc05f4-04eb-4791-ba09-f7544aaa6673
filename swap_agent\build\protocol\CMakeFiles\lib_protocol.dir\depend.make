# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/cppzmq/zmq.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/common-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/common.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/circular_q.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/console_globals.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/fmt_helper.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/null_mutex.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/os-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/os.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/registry-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/registry.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/details/windows_include.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/fmt/fmt.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/formatter.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/logger-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/logger.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/spdlog-inl.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/spdlog.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/tweakme.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/spdlog/version.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/libs/x86/include/zmq.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/nlohmann_json/json.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/ack.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/auto_exchange.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/auto_exchange_info.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/auto_exchange_map.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/data_map.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/data_request.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/exception.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/idl/sys_interface.pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/nanopb/pb.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/nanopb/pb_decode.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../../share/pb/nanopb/pb_encode.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././multi_swap_manager.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././protocol/train_protocol.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././swap_agent_debug.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././swap_manage/cfg.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././swap_manage/swap_list.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././swap_manage/swap_manage.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././threadpool/blocking_queue.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././threadpool/condition.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: .././threadpool/thp_mutex.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../fsm_manager/fsm_manager.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../multi_swap_manager.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../net/epoll_poller.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../net/tcp_socket.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../net/udp_socket.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../protocol/train_protocol.cpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../protocol/train_protocol.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../scheduler_msg/scheduler_msg.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../swap_agent_debug.h
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../swap_manage/swap_list.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../threadpool/blocking_queue.hpp
protocol/CMakeFiles/lib_protocol.dir/train_protocol.cpp.o: ../threadpool/thread_pool.hpp

