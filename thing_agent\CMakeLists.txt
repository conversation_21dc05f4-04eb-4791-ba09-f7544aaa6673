cmake_minimum_required(VERSION 3.5)

SET(CROSS_COMPILE OFF)

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
#	SET(CMAKE_C_COMPILER "/usr/bin/aarch64-linux-gnu-gcc")
#	SET(CMAKE_CXX_COMPILER "/usr/bin/aarch64-linux-gnu-g++")
#	link_directories("../share/libs/aarch64/lib")
#	include_directories("../share/libs/aarch64/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()

SET(CMAKE_BUILD_TYPE Debug)

project(thing_agent LANGUAGES CXX C)

SET(CMAKE_CXX_STANDARD 11)

SET(CMAKE_CXX_STANDARD_REQUIRED ON)

SET(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g2 -g")
SET(CMAKE_CXX_FLAGS_RELEASE "$ENV{CXXFLAGS} -O3 -Wall")

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread")

add_definitions(-Wall)

string(TIMESTAMP COMPILE_TIME %Y-%m-%d_%H:%M:%S)
add_definitions(-DBUILD_TIME="${COMPILE_TIME}")
add_definitions(-DBUILD_USER="$ENV{HOME}")

include_directories("../")

include_directories("../share/pb/nanopb" ".")

link_libraries(mosquitto zmq PocoFoundation PocoData PocoDataSQLite ssl crypto)

add_subdirectory("../share/pb/nanopb" nanopb_binary_dir)
add_subdirectory("../share/pb/idl" idl_binary_dir)

add_subdirectory("../share/lwshell" lwshell_binary_dir)

add_subdirectory(converter)
add_subdirectory(jd_thingtalk)
add_subdirectory(ipc_interface)
add_subdirectory(setting)
add_subdirectory(thing_interface)
add_subdirectory(thing_manager)
add_subdirectory(diagnose)

add_executable(thing_agent main.cpp thing_agent.cpp)

target_link_libraries(thing_agent jd_thingtalk diagnose thing_interface thing_manager ipc_interface converter setting idl nanopb lwshell)
