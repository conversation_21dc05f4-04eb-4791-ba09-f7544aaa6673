/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: sdk 协议 键值对相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk.h"
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   键值对结构体内存空间释放
 *
 * @param[in] key_value: 键值对结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 释放所有的，不只是成员变量的
 */
int32_t jd_thingtalk_proto_free_key_value(JDThingTalkProtoKeyValue_t *key_value)
{
    if (key_value != NULL) {
        if (key_value->key != NULL) {
            jd_thingtalk_pal_free(key_value->key);
            key_value->key = NULL;
        }
        if (key_value->value != NULL) {
            jd_thingtalk_pal_free(key_value->value);
            key_value->value = NULL;
        }
        jd_thingtalk_pal_free(key_value);
        key_value = NULL;
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   键值对结构体解析
 *
 * @param[in] in_json: json结构体指针
 * @return 
 *    解析后的键值对结构体指针
 * @see None.
 * @note None.
 */
JDThingTalkProtoKeyValue_t *jd_thingtalk_proto_parse_key_value(cJSON *in_json)
{
    if (in_json == NULL) {
        return NULL;
    }

    JDThingTalkProtoKeyValue_t *key_value = (JDThingTalkProtoKeyValue_t *) jd_thingtalk_pal_malloc(sizeof(JDThingTalkProtoKeyValue_t));
    if (key_value == NULL) {
        log_error("memory malloc for key_value is failed");
        return NULL;
    }
    jd_thingtalk_pal_memset(key_value, 0, sizeof(JDThingTalkProtoKeyValue_t));

    // 解析 key
    key_value->key = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(in_json->string) + 1) * sizeof(char));
    jd_thingtalk_pal_strcpy(key_value->key, in_json->string);

    // 解析 value 并转换成 json 字符串
    key_value->value = cJSON_PrintUnformatted(in_json);

RET:
    return key_value;
}

/**
 * @brief   键值对结构体打包
 *
 * @param[in] key_value: 键值对结构体指针
 * @return 
 *    打包完成后的json指针
 * @see None.
 * @note None.
 */
cJSON *jd_thingtalk_proto_pack_key_value(JDThingTalkProtoKeyValue_t *key_value)
{
    if(NULL == key_value) {
        return NULL;
    }

    cJSON *root = NULL;

    if (key_value->value != NULL) {
        root = cJSON_Parse(key_value->value);
    }

RET:
    return root;
}

/**
 * @brief   键值对 解析布尔值
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_bool(char *in_value, bool *out_val)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_value || NULL == out_val) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    // 解析 value
    if (payload->valueint == 0) {
        *out_val = false;
    } else {
        *out_val = true;
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   键值对 打包布尔值
 *
 * @param[in] in_bool: 待打包的bool值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_bool(bool in_bool)
{
    char *out = NULL;
    cJSON *root = NULL;
    if (in_bool) {
        root = cJSON_CreateTrue();
    } else {
        root = cJSON_CreateFalse();
    }
    if (root != NULL) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }
    return out;
}

/**
 * @brief   键值对 解析 int32_t
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_int32(char *in_value, int32_t *out_val)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_value || NULL == out_val) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    // 解析 value
    *out_val = payload->valueint;

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   键值对 打包 int32_t
 *
 * @param[in] in_int32: 待打包的int32值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_int32(int32_t in_int32)
{
    char *out = NULL;
    cJSON *root = cJSON_CreateNumber((double)in_int32);
    if (root != NULL) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }
    return out;
}

/**
 * @brief   键值对 解析 double
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_double(char *in_value, double *out_val)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_value || NULL == out_val) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    // 解析 value
    *out_val = payload->valuedouble;

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

/**
 * @brief   键值对 打包 double
 *
 * @param[in] in_double: 待打包的double值
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_double(double in_double)
{
    char *out = NULL;
    cJSON *root = cJSON_CreateNumber(in_double);
    if (root != NULL) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }
    return out;
}

/**
 * @brief   键值对 解析 string
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回 解析后的值 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_parse_string(char *in_value)
{
    char *out = NULL;
    if (NULL == in_value) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    // 解析 value
    if (payload->valuestring != NULL) {
        out = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(payload->valuestring) + 1) * sizeof(char));
        if (out != NULL) {
            jd_thingtalk_pal_strcpy(out, payload->valuestring);
        }
    }

    // 删除 payload
    cJSON_Delete(payload);

RET:
    return out;
}

/**
 * @brief   键值对 解析 string
 *
 * @param[in] in_value: JDThingTalkProtoKeyValue_t 中value jSON 字符串指针
 * @param[in] out_val: 指向解析结果的指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_proto_keyvalue_parse_string1(char *in_value, char *out_val)
{
    int32_t ret = JD_THINGTALK_RET_FAILED;
    if ((NULL == in_value) || (out_val == NULL)) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_value);
    if (NULL == payload) {
        goto RET;
    }

    // 解析 value
    if (payload->valuestring != NULL) {
        jd_thingtalk_pal_strcpy(out_val, payload->valuestring);
    }

    // 删除 payload
    cJSON_Delete(payload);

RET:
    return ret;
}

/**
 * @brief   键值对 打包 string
 *
 * @param[in] in_string: 待打包的string
 * @return 
 *    返回 打包后 JDThingTalkProtoKeyValue_t 中value jSON 字符串指针 
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_keyvalue_pack_string(char *in_string)
{
    char *out = NULL;
    cJSON *root = cJSON_CreateString(in_string);
    if (root != NULL) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }
    return out;
}

// end of file
