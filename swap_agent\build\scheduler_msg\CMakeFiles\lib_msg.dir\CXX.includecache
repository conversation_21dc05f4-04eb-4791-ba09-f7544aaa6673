#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../share/exception_code.hpp

../../share/global_def.h

../../share/libs/x86/include/cppzmq/zmq.hpp
zmq.h
-
cassert
-
cstring
-
algorithm
-
exception
-
iomanip
-
sstream
-
string
-
vector
-
array
-
chrono
-
tuple
-
memory
-
optional
-
string_view
-
type_traits
-

../../share/libs/x86/include/spdlog/common-inl.h
spdlog/common.h
-

../../share/libs/x86/include/spdlog/common.h
spdlog/tweakme.h
-
spdlog/details/null_mutex.h
-
atomic
-
chrono
-
initializer_list
-
memory
-
exception
-
string
-
type_traits
-
functional
-
spdlog/fmt/fmt.h
-
common-inl.h
../../share/libs/x86/include/spdlog/common-inl.h

../../share/libs/x86/include/spdlog/details/backtracer-inl.h
spdlog/details/backtracer.h
-

../../share/libs/x86/include/spdlog/details/backtracer.h
spdlog/details/log_msg_buffer.h
-
spdlog/details/circular_q.h
-
atomic
-
mutex
-
functional
-
backtracer-inl.h
../../share/libs/x86/include/spdlog/details/backtracer-inl.h

../../share/libs/x86/include/spdlog/details/circular_q.h
vector
-
cassert
-

../../share/libs/x86/include/spdlog/details/console_globals.h
spdlog/details/null_mutex.h
-
mutex
-

../../share/libs/x86/include/spdlog/details/file_helper-inl.h
spdlog/details/file_helper.h
-
spdlog/details/os.h
-
spdlog/common.h
-
cerrno
-
chrono
-
cstdio
-
string
-
thread
-
tuple
-

../../share/libs/x86/include/spdlog/details/file_helper.h
spdlog/common.h
-
tuple
-
file_helper-inl.h
../../share/libs/x86/include/spdlog/details/file_helper-inl.h

../../share/libs/x86/include/spdlog/details/fmt_helper.h
chrono
-
type_traits
-
spdlog/fmt/fmt.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/details/log_msg-inl.h
spdlog/details/log_msg.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/details/log_msg.h
spdlog/common.h
-
string
-
log_msg-inl.h
../../share/libs/x86/include/spdlog/details/log_msg-inl.h

../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
spdlog/details/log_msg_buffer.h
-

../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
spdlog/details/log_msg.h
-
log_msg_buffer-inl.h
../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h

../../share/libs/x86/include/spdlog/details/null_mutex.h
atomic
-
utility
-

../../share/libs/x86/include/spdlog/details/os-inl.h
spdlog/details/os.h
-
spdlog/common.h
-
algorithm
-
chrono
-
cstdio
-
cstdlib
-
cstring
-
ctime
-
string
-
thread
-
array
-
sys/stat.h
-
sys/types.h
-
io.h
-
process.h
-
spdlog/details/windows_include.h
-
share.h
-
limits
-
direct.h
-
fcntl.h
-
unistd.h
-
sys/syscall.h
-
pthread.h
-
pthread_np.h
-
lwp.h
-
thread.h
-

../../share/libs/x86/include/spdlog/details/os.h
spdlog/common.h
-
ctime
-
os-inl.h
../../share/libs/x86/include/spdlog/details/os-inl.h

../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
spdlog/details/periodic_worker.h
-

../../share/libs/x86/include/spdlog/details/periodic_worker.h
chrono
-
condition_variable
-
functional
-
mutex
-
thread
-
periodic_worker-inl.h
../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h

../../share/libs/x86/include/spdlog/details/registry-inl.h
spdlog/details/registry.h
-
spdlog/common.h
-
spdlog/details/periodic_worker.h
-
spdlog/logger.h
-
spdlog/pattern_formatter.h
-
spdlog/sinks/wincolor_sink.h
-
spdlog/sinks/ansicolor_sink.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-

../../share/libs/x86/include/spdlog/details/registry.h
spdlog/common.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-
mutex
-
registry-inl.h
../../share/libs/x86/include/spdlog/details/registry-inl.h

../../share/libs/x86/include/spdlog/details/synchronous_factory.h
registry.h
../../share/libs/x86/include/spdlog/details/registry.h

../../share/libs/x86/include/spdlog/details/windows_include.h
windows.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/core.h
cstdio
-
cstring
-
functional
-
iterator
-
memory
-
string
-
type_traits
-
vector
-
string_view
-
experimental/string_view
-
fmt/core.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
cassert
-
cctype
-
climits
-
cmath
-
cstdarg
-
cstring
-
cwchar
-
exception
-
locale
-
io.h
-
format.h
../../share/libs/x86/include/spdlog/fmt/bundled/format.h

../../share/libs/x86/include/spdlog/fmt/bundled/format.h
algorithm
-
cerrno
-
cmath
-
cstdint
-
limits
-
memory
-
stdexcept
-
core.h
../../share/libs/x86/include/spdlog/fmt/bundled/core.h
intrin.h
-
fmt/format.h
-
format-inl.h
../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h

../../share/libs/x86/include/spdlog/fmt/fmt.h
spdlog/fmt/bundled/core.h
-
spdlog/fmt/bundled/format.h
-
fmt/core.h
-
fmt/format.h
-

../../share/libs/x86/include/spdlog/formatter.h
spdlog/fmt/fmt.h
-
spdlog/details/log_msg.h
-

../../share/libs/x86/include/spdlog/logger-inl.h
spdlog/logger.h
-
spdlog/sinks/sink.h
-
spdlog/details/backtracer.h
-
spdlog/pattern_formatter.h
-
cstdio
-

../../share/libs/x86/include/spdlog/logger.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/backtracer.h
-
spdlog/details/os.h
-
vector
-
logger-inl.h
../../share/libs/x86/include/spdlog/logger-inl.h

../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
spdlog/pattern_formatter.h
-
spdlog/details/fmt_helper.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/fmt/fmt.h
-
spdlog/formatter.h
-
array
-
chrono
-
ctime
-
cctype
-
cstring
-
memory
-
mutex
-
string
-
thread
-
utility
-
vector
-

../../share/libs/x86/include/spdlog/pattern_formatter.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/formatter.h
-
chrono
-
ctime
-
memory
-
string
-
vector
-
unordered_map
-
pattern_formatter-inl.h
../../share/libs/x86/include/spdlog/pattern_formatter-inl.h

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
spdlog/sinks/ansicolor_sink.h
-
spdlog/pattern_formatter.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
ansicolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
spdlog/sinks/base_sink.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-
memory
-

../../share/libs/x86/include/spdlog/sinks/base_sink.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/sinks/sink.h
-
base_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/daily_file_sink.h
spdlog/common.h
-
spdlog/details/file_helper.h
-
spdlog/details/null_mutex.h
-
spdlog/fmt/fmt.h
-
spdlog/sinks/base_sink.h
-
spdlog/details/os.h
-
spdlog/details/circular_q.h
-
spdlog/details/synchronous_factory.h
-
chrono
-
cstdio
-
ctime
-
mutex
-
string
-

../../share/libs/x86/include/spdlog/sinks/sink-inl.h
spdlog/sinks/sink.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/sinks/sink.h
spdlog/details/log_msg.h
-
spdlog/formatter.h
-
sink-inl.h
../../share/libs/x86/include/spdlog/sinks/sink-inl.h

../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h
spdlog/sinks/stdout_sinks.h
-
spdlog/details/console_globals.h
-
spdlog/pattern_formatter.h
-
memory
-
spdlog/details/windows_include.h
-
fileapi.h
-
io.h
-
stdio.h
-

../../share/libs/x86/include/spdlog/sinks/stdout_sinks.h
spdlog/details/console_globals.h
-
spdlog/details/synchronous_factory.h
-
spdlog/sinks/sink.h
-
cstdio
-
spdlog/details/windows_include.h
-
stdout_sinks-inl.h
../../share/libs/x86/include/spdlog/sinks/stdout_sinks-inl.h

../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
spdlog/sinks/wincolor_sink.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
spdlog/common.h
-
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
spdlog/details/windows_include.h
-
wincon.h
-
wincolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h

../../share/libs/x86/include/spdlog/spdlog-inl.h
spdlog/spdlog.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/spdlog.h
spdlog/common.h
-
spdlog/details/registry.h
-
spdlog/logger.h
-
spdlog/version.h
-
spdlog/details/synchronous_factory.h
-
chrono
-
functional
-
memory
-
string
-
spdlog-inl.h
../../share/libs/x86/include/spdlog/spdlog-inl.h

../../share/libs/x86/include/spdlog/tweakme.h

../../share/libs/x86/include/spdlog/version.h

../../share/libs/x86/include/zmq.h
errno.h
-
stddef.h
-
stdio.h
-
winsock2.h
-
inttypes.h
-
stdint.h
-
poll.h
-

../../share/pb/idl/ack.pb.h
pb.h
-

../../share/pb/idl/auto_exchange.pb.h
pb.h
-
sys_interface.pb.h
../../share/pb/idl/sys_interface.pb.h

../../share/pb/idl/auto_exchange_info.pb.h
pb.h
-

../../share/pb/idl/auto_exchange_map.pb.h
pb.h
-

../../share/pb/idl/data_map.pb.h
pb.h
-

../../share/pb/idl/data_request.pb.h
pb.h
-

../../share/pb/idl/exception.pb.h
pb.h
-

../../share/pb/idl/sys_interface.pb.h
pb.h
-

../../share/pb/nanopb/pb.h
stdint.h
-
stddef.h
-
stdbool.h
-
string.h
-
limits.h
-
stdlib.h
-
avr/pgmspace.h
-

../../share/pb/nanopb/pb_decode.h
pb.h
../../share/pb/nanopb/pb.h

../../share/pb/nanopb/pb_encode.h
pb.h
../../share/pb/nanopb/pb.h

.././fsm_manager/fsm_manager.hpp
../threadpool/blocking_queue.hpp
.././threadpool/blocking_queue.hpp
share/pb/nanopb/pb_encode.h
.././fsm_manager/share/pb/nanopb/pb_encode.h
share/pb/nanopb/pb_decode.h
.././fsm_manager/share/pb/nanopb/pb_decode.h
share/pb/idl/sys_interface.pb.h
.././fsm_manager/share/pb/idl/sys_interface.pb.h
string
-
netinet/ip.h
-
iostream
-
vector
-
map
-
sys/epoll.h
-
functional
-
memory
-
unordered_map
-
list
-
zmq.h
-
cppzmq/zmq.hpp
-
mutex
-
thread
-

.././swap_agent_debug.h
iostream
-

.././swap_manage/cfg.hpp

.././swap_manage/swap_list.hpp
../threadpool/blocking_queue.hpp
.././threadpool/blocking_queue.hpp
share/pb/idl/auto_exchange.pb.h
.././swap_manage/share/pb/idl/auto_exchange.pb.h
share/pb/idl/exception.pb.h
.././swap_manage/share/pb/idl/exception.pb.h
spdlog/spdlog.h
-
spdlog/sinks/daily_file_sink.h
-
spdlog/sinks/stdout_sinks.h
-
spdlog/logger.h
-
iostream
-
sys/socket.h
-
arpa/inet.h
-
cfg.hpp
.././swap_manage/cfg.hpp
list
-

.././threadpool/blocking_queue.hpp
queue
-
climits
-
condition.hpp
.././threadpool/condition.hpp

.././threadpool/condition.hpp
thp_mutex.hpp
.././threadpool/thp_mutex.hpp
memory
-

.././threadpool/thp_mutex.hpp
pthread.h
-
memory
-

/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.cpp
scheduler_msg.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
share/global_def.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/global_def.h
swap_agent_debug.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/swap_agent_debug.h
swap_manage/swap_list.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/swap_manage/swap_list.hpp
arpa/inet.h
-
zmq.h
-
spdlog/logger.h
-
spdlog/spdlog.h
-
share/exception_code.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/exception_code.hpp
fsm_manager/fsm_manager.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/fsm_manager/fsm_manager.hpp

/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/scheduler_msg.hpp
zmq.h
-
cppzmq/zmq.hpp
-
threadpool/blocking_queue.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/threadpool/blocking_queue.hpp
share/pb/idl/data_map.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/data_map.pb.h
share/pb/idl/auto_exchange_info.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/auto_exchange_info.pb.h
share/pb/nanopb/pb_encode.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/nanopb/pb_encode.h
share/pb/nanopb/pb_decode.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/nanopb/pb_decode.h
share/pb/idl/data_request.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/data_request.pb.h
share/pb/idl/auto_exchange_map.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/auto_exchange_map.pb.h
share/pb/idl/auto_exchange.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/auto_exchange.pb.h
share/pb/idl/exception.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/exception.pb.h
share/pb/idl/ack.pb.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/share/pb/idl/ack.pb.h
swap_manage/swap_list.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/swap_manage/swap_list.hpp
fsm_manager/fsm_manager.hpp
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/fsm_manager/fsm_manager.hpp
iostream
-
swap_agent_debug.h
/home/<USER>/myfile/project/auto_replace_box/swap_agent/scheduler_msg/swap_agent_debug.h

