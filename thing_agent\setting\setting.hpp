#ifndef __THING_AGENT_SETTING_H__
#define __THING_AGENT_SETTING_H__

#include <spdlog/spdlog.h>
#include "share/nlohmann_json/json.hpp"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"

class setting
{
    using json = nlohmann::json;

public:

    static setting *get_instance(void)
	{
		static setting instance;
		return &instance;
	}

    struct setting_data
    {
        std::string sdk_config_host_name;
        int32_t sdk_config_host_port;
        std::string sdk_config_device_id;
        std::string sdk_config_ca_path;
        std::string sdk_config_cert_path;
        std::string sdk_config_key_path;

        std::string thing_model_id;
        std::string thing_model_version;

        std::string device_version;   //兼容现场不同设备

        uint32_t time_report_vehicle_state_auto;
        uint32_t time_report_vehicle_state_manual;
        uint32_t time_report_sys_state;
        uint32_t time_report_feeder_state;
        uint32_t time_report_switcher_state;
        uint32_t time_report_feeder_belt_state;
        uint32_t time_issue_sys_state;
        uint32_t time_issue_slot_delay;

        int mobile_shelf_version;
        int connect_to_thing;
        bool need_report_query_result;
        bool need_report_seal_state;

        int production_mode;            //0:分播，1:分拣
        std::string wcs_function_ip;
        std::string wcs_event_ip;
        int device_id;

        int container_reprort_mode;            //0:分播，1:分拣
        std::string con_seal_ip;
        std::string con_slot_ip;

        int carriage_max_distance;

        int scanner_id;

        std::string connect_protocol;
    };

    const setting_data & get_setting(void) const
    {
        return settings;
    }

    int load_setting(const char *file_name);
    int load_thing_agent_connect_setting();
    int save_thing_agent_connect_setting(int param_value);
    bool thing_manager_get_data_map_info();

private:
    std::mutex setting_lock; 
    setting_data settings;
    int load_thing_agent_setting(const char *file_name);

};

#endif