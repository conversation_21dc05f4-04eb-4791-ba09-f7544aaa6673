#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include "spdlog/async.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/rotating_file_sink.h"

#include <thread>
#include <stdio.h>

#include "share/pb/nanopb/pb_common.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/global_def.h"
#include "setting/setting.hpp"
#include "diagnose/diagnose.hpp"

#include "thing_agent.hpp"


using namespace std;

std::string GetHomePath(){
	std::string home_path;
	char *home;
    home = getenv("HOME");
    home_path = home;
	return home_path;
}

int init_log(void)
{
	std::string str_home_path = GetHomePath();
	std::string str_log_path = str_home_path + THING_AGENT_LOG_FILE_PATH + "thing_agent.log";
    auto thing_logger = spdlog::rotating_logger_mt("thing_agent", str_log_path, 1024 * 1024 * 128, 10);
	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();
    
	thing_logger->set_level(spdlog::level::trace);
	thing_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出
	spdlog::set_default_logger(thing_logger);

	spdlog::set_pattern("[%m-%d %H:%M:%S %e] [%s:%#] [%l] %v");

	SPDLOG_INFO("initialize loging ok");
	return 0;	
}

int init_setting()
{
	// bool result = false;
	// result = setting::get_instance()->thing_manager_get_data_map_info();
	// SPDLOG_INFO("thing_manager_get_data_map_info result:{}, carriage_distance:{}", result, setting::get_instance()->get_setting().carriage_max_distance);


	std::string str_home_path = GetHomePath();
	std::string str_thing_path = str_home_path + DATA_FILE_PATH + THING_AGENT_FILE_NAME;
	if (setting::get_instance()->load_setting(str_thing_path.c_str()) == 0)
		exit(1);
	return 0;
}

int main(int argc, char *argv[])
{
    init_log();

    init_setting();

    diagnose_init();
	diagnose_run();

    jd_thingtalk_sdk_t *my_sdk = nullptr;
    zmq::context_t context{1};
    thing_agent::get_instance()->init(&my_sdk, context);
    thing_agent::get_instance()->run();

    jd_thingtalk_sdk_main_loop(my_sdk);

    return 0;
}