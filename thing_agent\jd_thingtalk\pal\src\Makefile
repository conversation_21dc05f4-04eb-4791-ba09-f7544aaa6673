include ../../Makefile.rule

# Header and source files
HEADERS = $(wildcard *.h)
SOURCES = $(wildcard *.c)
OBJS = $(patsubst %.c, %.o, $(SOURCES))

# Include path
INCLUDES += -I${PROJECT_ROOT_PATH}/pal/inc
INCLUDES += ${MQTT_INC_PATH}

ifeq (${ARCH}, x86)  
all:${OBJS} libso liba
else
all:${OBJS} liba 
endif

.SUFFIXES: .c .o
.c.o:
	${CC} ${CFLAGS} -c $(INCLUDES) $*.c

liba:${OBJS}
	${AR} -crs lib${LIB_NAME_PAL}.a ${OBJS}
	${MV} lib${LIB_NAME_PAL}.a ${TARGET_LIB}
	${MV} *.o ${TARGET_OBJ}

libso:${OBJS}
	${CC}  ${OBJS} -shared -fPIC -o lib${LIB_NAME_PAL}.so
	${MV} lib${LIB_NAME_PAL}.so ${TARGET_LIB} 

clean:
	${RM} *.o *.so *.a

distclean:clean
	${RM} ${TARGET_LIB}/lib${LIB_NAME_PAL}.*

.PHONY:all clean distclean
