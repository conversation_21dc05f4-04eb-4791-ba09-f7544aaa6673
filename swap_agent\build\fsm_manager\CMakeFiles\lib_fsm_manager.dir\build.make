# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

# Include any dependencies generated for this target.
include fsm_manager/CMakeFiles/lib_fsm_manager.dir/depend.make

# Include the progress variables for this target.
include fsm_manager/CMakeFiles/lib_fsm_manager.dir/progress.make

# Include the compile flags for this target's objects.
include fsm_manager/CMakeFiles/lib_fsm_manager.dir/flags.make

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o: fsm_manager/CMakeFiles/lib_fsm_manager.dir/flags.make
fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o: ../fsm_manager/fsm_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && /usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o -c /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.i"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp > CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.i

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.s"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && /usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager/fsm_manager.cpp -o CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.s

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.requires:

.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.requires

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.provides: fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.requires
	$(MAKE) -f fsm_manager/CMakeFiles/lib_fsm_manager.dir/build.make fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.provides.build
.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.provides

fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.provides.build: fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o


# Object files for target lib_fsm_manager
lib_fsm_manager_OBJECTS = \
"CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o"

# External object files for target lib_fsm_manager
lib_fsm_manager_EXTERNAL_OBJECTS =

fsm_manager/liblib_fsm_manager.a: fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o
fsm_manager/liblib_fsm_manager.a: fsm_manager/CMakeFiles/lib_fsm_manager.dir/build.make
fsm_manager/liblib_fsm_manager.a: fsm_manager/CMakeFiles/lib_fsm_manager.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library liblib_fsm_manager.a"
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && $(CMAKE_COMMAND) -P CMakeFiles/lib_fsm_manager.dir/cmake_clean_target.cmake
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lib_fsm_manager.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
fsm_manager/CMakeFiles/lib_fsm_manager.dir/build: fsm_manager/liblib_fsm_manager.a

.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/build

fsm_manager/CMakeFiles/lib_fsm_manager.dir/requires: fsm_manager/CMakeFiles/lib_fsm_manager.dir/fsm_manager.cpp.o.requires

.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/requires

fsm_manager/CMakeFiles/lib_fsm_manager.dir/clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager && $(CMAKE_COMMAND) -P CMakeFiles/lib_fsm_manager.dir/cmake_clean.cmake
.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/clean

fsm_manager/CMakeFiles/lib_fsm_manager.dir/depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/myfile/project/auto_replace_box/swap_agent /home/<USER>/myfile/project/auto_replace_box/swap_agent/fsm_manager /home/<USER>/myfile/project/auto_replace_box/swap_agent/build /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/fsm_manager/CMakeFiles/lib_fsm_manager.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : fsm_manager/CMakeFiles/lib_fsm_manager.dir/depend

