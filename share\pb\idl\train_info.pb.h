/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_TRAIN_INFO_PB_H_INCLUDED
#define PB_TRAIN_INFO_PB_H_INCLUDED
#include <pb.h>
#include "train_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _enable_state {
    enable_state_DEV_ENABLE_STATE_RESERVE = 0,
    enable_state_DEV_ENABLE_STATE_ENABLE = 1,
    enable_state_DEV_ENABLE_STATE_DISABLE = 2
} enable_state;

/* Struct definitions */
typedef struct _platform_basic_info {
    uint32_t platform_id;
    enable_state state;
    platform_type type;
} platform_basic_info;

typedef struct _carriage_basic_info {
    uint32_t carriage_id;
    enable_state state;
    carriage_type type;
    char sw_ver[16];
    uint32_t platform_cnt;
    pb_size_t platform_info_count;
    platform_basic_info platform_info[5];
} carriage_basic_info;

typedef struct _train_basic_info {
    uint32_t train_id;
    enable_state state;
    char ip[16];
    char sw_ver[16];
    char hw_ver[16];
    uint32_t carriage_cnt;
    pb_size_t carriage_info_count;
    carriage_basic_info carriage_info[16];
} train_basic_info;

typedef struct _train_basic_info_mutilp {
    pb_size_t train_info_count;
    train_basic_info train_info[30];
} train_basic_info_mutilp;

typedef struct _train_config_para {
    uint32_t x_speed;
    uint32_t x_acc;
    uint32_t x_d_acc;
    uint32_t y_speed;
    uint32_t y_acc;
    uint32_t y_d_acc;
    uint32_t z_speed;
    uint32_t z_cali_speed;
    uint32_t z_acc;
    uint32_t z_limit;
} train_config_para;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _enable_state_MIN enable_state_DEV_ENABLE_STATE_RESERVE
#define _enable_state_MAX enable_state_DEV_ENABLE_STATE_DISABLE
#define _enable_state_ARRAYSIZE ((enable_state)(enable_state_DEV_ENABLE_STATE_DISABLE+1))

#define platform_basic_info_state_ENUMTYPE enable_state
#define platform_basic_info_type_ENUMTYPE platform_type

#define carriage_basic_info_state_ENUMTYPE enable_state
#define carriage_basic_info_type_ENUMTYPE carriage_type

#define train_basic_info_state_ENUMTYPE enable_state




/* Initializer values for message structs */
#define platform_basic_info_init_default         {0, _enable_state_MIN, _platform_type_MIN}
#define carriage_basic_info_init_default         {0, _enable_state_MIN, _carriage_type_MIN, "", 0, 0, {platform_basic_info_init_default, platform_basic_info_init_default, platform_basic_info_init_default, platform_basic_info_init_default, platform_basic_info_init_default}}
#define train_basic_info_init_default            {0, _enable_state_MIN, "", "", "", 0, 0, {carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default, carriage_basic_info_init_default}}
#define train_basic_info_mutilp_init_default     {0, {train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default, train_basic_info_init_default}}
#define train_config_para_init_default           {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define platform_basic_info_init_zero            {0, _enable_state_MIN, _platform_type_MIN}
#define carriage_basic_info_init_zero            {0, _enable_state_MIN, _carriage_type_MIN, "", 0, 0, {platform_basic_info_init_zero, platform_basic_info_init_zero, platform_basic_info_init_zero, platform_basic_info_init_zero, platform_basic_info_init_zero}}
#define train_basic_info_init_zero               {0, _enable_state_MIN, "", "", "", 0, 0, {carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero, carriage_basic_info_init_zero}}
#define train_basic_info_mutilp_init_zero        {0, {train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero, train_basic_info_init_zero}}
#define train_config_para_init_zero              {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define platform_basic_info_platform_id_tag      1
#define platform_basic_info_state_tag            2
#define platform_basic_info_type_tag             3
#define carriage_basic_info_carriage_id_tag      1
#define carriage_basic_info_state_tag            2
#define carriage_basic_info_type_tag             3
#define carriage_basic_info_sw_ver_tag           4
#define carriage_basic_info_platform_cnt_tag     5
#define carriage_basic_info_platform_info_tag    6
#define train_basic_info_train_id_tag            1
#define train_basic_info_state_tag               2
#define train_basic_info_ip_tag                  3
#define train_basic_info_sw_ver_tag              4
#define train_basic_info_hw_ver_tag              5
#define train_basic_info_carriage_cnt_tag        6
#define train_basic_info_carriage_info_tag       7
#define train_basic_info_mutilp_train_info_tag   1
#define train_config_para_x_speed_tag            1
#define train_config_para_x_acc_tag              2
#define train_config_para_x_d_acc_tag            3
#define train_config_para_y_speed_tag            4
#define train_config_para_y_acc_tag              5
#define train_config_para_y_d_acc_tag            6
#define train_config_para_z_speed_tag            7
#define train_config_para_z_cali_speed_tag       8
#define train_config_para_z_acc_tag              9
#define train_config_para_z_limit_tag            10

/* Struct field encoding specification for nanopb */
#define platform_basic_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   platform_id,       1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3)
#define platform_basic_info_CALLBACK NULL
#define platform_basic_info_DEFAULT NULL

#define carriage_basic_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_id,       1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3) \
X(a, STATIC,   SINGULAR, STRING,   sw_ver,            4) \
X(a, STATIC,   SINGULAR, UINT32,   platform_cnt,      5) \
X(a, STATIC,   REPEATED, MESSAGE,  platform_info,     6)
#define carriage_basic_info_CALLBACK NULL
#define carriage_basic_info_DEFAULT NULL
#define carriage_basic_info_platform_info_MSGTYPE platform_basic_info

#define train_basic_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, STRING,   ip,                3) \
X(a, STATIC,   SINGULAR, STRING,   sw_ver,            4) \
X(a, STATIC,   SINGULAR, STRING,   hw_ver,            5) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_cnt,      6) \
X(a, STATIC,   REPEATED, MESSAGE,  carriage_info,     7)
#define train_basic_info_CALLBACK NULL
#define train_basic_info_DEFAULT NULL
#define train_basic_info_carriage_info_MSGTYPE carriage_basic_info

#define train_basic_info_mutilp_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  train_info,        1)
#define train_basic_info_mutilp_CALLBACK NULL
#define train_basic_info_mutilp_DEFAULT NULL
#define train_basic_info_mutilp_train_info_MSGTYPE train_basic_info

#define train_config_para_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   x_speed,           1) \
X(a, STATIC,   SINGULAR, UINT32,   x_acc,             2) \
X(a, STATIC,   SINGULAR, UINT32,   x_d_acc,           3) \
X(a, STATIC,   SINGULAR, UINT32,   y_speed,           4) \
X(a, STATIC,   SINGULAR, UINT32,   y_acc,             5) \
X(a, STATIC,   SINGULAR, UINT32,   y_d_acc,           6) \
X(a, STATIC,   SINGULAR, UINT32,   z_speed,           7) \
X(a, STATIC,   SINGULAR, UINT32,   z_cali_speed,      8) \
X(a, STATIC,   SINGULAR, UINT32,   z_acc,             9) \
X(a, STATIC,   SINGULAR, UINT32,   z_limit,          10)
#define train_config_para_CALLBACK NULL
#define train_config_para_DEFAULT NULL

extern const pb_msgdesc_t platform_basic_info_msg;
extern const pb_msgdesc_t carriage_basic_info_msg;
extern const pb_msgdesc_t train_basic_info_msg;
extern const pb_msgdesc_t train_basic_info_mutilp_msg;
extern const pb_msgdesc_t train_config_para_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define platform_basic_info_fields &platform_basic_info_msg
#define carriage_basic_info_fields &carriage_basic_info_msg
#define train_basic_info_fields &train_basic_info_msg
#define train_basic_info_mutilp_fields &train_basic_info_mutilp_msg
#define train_config_para_fields &train_config_para_msg

/* Maximum encoded size of messages (where known) */
#define TRAIN_INFO_PB_H_MAX_SIZE                 train_basic_info_mutilp_size
#define carriage_basic_info_size                 93
#define platform_basic_info_size                 10
#define train_basic_info_mutilp_size             47640
#define train_basic_info_size                    1585
#define train_config_para_size                   60

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
