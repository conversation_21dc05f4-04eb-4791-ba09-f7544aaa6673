#ifndef __JD_THINGTALK_THREAD_H__
#define __JD_THINGTALK_THREAD_H__

#include <stddef.h>
#include "jd_thingtalk_stdint.h"

#ifndef PTHREAD_MUTEX_RECURSIVE
#define PTHREAD_MUTEX_RECURSIVE PTHREAD_MUTEX_RECURSIVE_NP 
#endif

#ifdef __cplusplus
extern "C"
{
#endif
//Mutex
typedef void* jd_thingtalk_mutex_t;

//Semaphore
typedef void* jd_thingtalk_semaphore_t;

//Queue
typedef void* jd_thingtalk_queue_handle;

//Task
typedef void* jd_thingtalk_task_handle_t;

//Timer
typedef void* jd_thingtalk_timer_handle_t;

typedef unsigned char JD_THINGTALK_THREAD_PRI_T;

enum JD_THINGTALK_THREAD_PRI{
	JD_THINGTALK_THREAD_PRI_DEFAULT, /*priority: default*/
	JD_THINGTALK_THREAD_PRI_LOWEST,  /*priority: lowest*/
	JD_THINGTALK_THREAD_PRI_LOW,     /*priority: low*/
	JD_THINGTALK_THREAD_PRI_HIGH,    /*priority: high*/
	JD_THINGTALK_THREAD_PRI_HIGHEST, /*priority: highest*/
} ;

enum JD_THINGTALK_MUTEX_TYPE {
    JD_THINGTALK_MUTEX_TYPE_NORMAL,
    JD_THINGTALK_MUTEX_TYPE_RECURSIVE,
};

typedef  void (*threadtask)(void *arg);

typedef struct{
	jd_thingtalk_task_handle_t  handle;      /*thread handle*/
	JD_THINGTALK_THREAD_PRI_T   priority;    /*initial thread priority*/    
	size_t            stackSize;   /*stack size requirements in bytes; 0 is default stack size*/
	threadtask        thread_task; /*thread task func*/
	void             *parameter;   /*thread user para*/
	char              isRunning;   /*thread running state 0:thread is running 1:thread is ideal*/
}jd_thingtalk_thread_t;

typedef void (*TimerCallback)( void *userData);

typedef struct _jd_thingtalk_timer_t{
    uint32_t          periodicInterval;
    TimerCallback     callback;
    void             *userData;
    jd_thingtalk_timer_handle_t handle;
}jd_thingtalk_timer_t;


/*********************************** mutex interface ***********************************/

/** @defgroup group_platform_mutex mutex
 *  @{
 */

/**
 * @brief 创建互斥锁
 *
 * @retval NULL : Initialize mutex failed.
 * @retval NOT_NULL : The mutex handle.
 * @see None.
 * @note None.
 */
jd_thingtalk_mutex_t jd_thingtalk_pal_mutex_create(int32_t type);

/**
 * @brief 等待指定的互斥锁
 *
 * @param [in] mutex @n the specified mutex.
 * @return None.
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mutex_lock(jd_thingtalk_mutex_t handle);

/**
 * @brief 释放指定的互斥锁
 *
 * @param [in] mutex @n the specified mutex.
 * @return None.
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_mutex_unlock(jd_thingtalk_mutex_t handle);

/**
 * @brief 销毁互斥锁，并回收所占用的资源
 *
 * @param [in] mutex @n The specified mutex.
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_mutex_delete(jd_thingtalk_mutex_t handle);

/**
 * @brief   创建一个计数信号量
 *
 * @return semaphore handle.
 * @see None.
 * @note The recommended value of maximum count of the semaphore is 255.
 */
jd_thingtalk_semaphore_t jd_thingtalk_pal_semaphore_create(void);

/**
 * @brief   销毁一个计数信号量, 回收其所占用的资源
 *
 * @param[in] sem @n the specified sem.
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_semaphore_destroy(jd_thingtalk_semaphore_t handle);

/**
 * @brief   在指定的计数信号量上做自减操作并等待
 *
 * @param[in] sem @n the specified semaphore.
 * @param[in] timeout_ms @n timeout interval in millisecond.
     If timeout_ms is PLATFORM_WAIT_INFINITE, the function will return only when the semaphore is signaled.
 * @return
   @verbatim
   =  0: The state of the specified object is signaled.
   =  -1: The time-out interval elapsed, and the object's state is nonsignaled.
   @endverbatim
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_semaphore_wait(jd_thingtalk_semaphore_t handle, uint32_t timeout_ms);

/**
 * @brief   在指定的计数信号量上做自增操作, 解除其它线程的等待
 *
 * @param[in] sem @n the specified semaphore.
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_semaphore_post(jd_thingtalk_semaphore_t handle);

/**
 * @brief   按照指定入参创建一个线程
 *
 * @param[out] thread_handle @n The new thread handle, memory allocated before thread created and return it, free it after thread joined or exit.
 * @param[in] pri @n thread priority
 * @param[in] stacksize @n stack size requirements in bytes
 * @return
   @verbatim
     = 0 : on success.
     = -1: error occur.
   @endverbatim
 * @see None.
 * @note None.
 */
int32_t  jd_thingtalk_pal_thread_create(jd_thingtalk_thread_t* thread_handle, JD_THINGTALK_THREAD_PRI_T pri, size_t stacksize);

/**
 * @brief   通过线程句柄启动指定得任务
 *
 * @param[in] thread_handle @n the thread handle 
 * @param[in] task @n specify the task to start on thread_handle
 * @param[in] parameter @n user parameter input
 * @return
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_thread_start(jd_thingtalk_thread_t* thread_handle);

/**
 * @brief   设置指定的线程为`Detach`状态
 *
 * @param[in] thread_handle: pointer to thread handle.
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_thread_detach(jd_thingtalk_thread_t* thread_handle);

/**
 * @brief   线程主动退出
 *
 * @param[in] thread_handle: pointer to thread handle.
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_thread_exit(jd_thingtalk_thread_t* thread_handle);

/**
 * @brief   杀死指定的线程
 *
 * @param[in] thread_handle: pointer to thread handle, NULL means itself
 * @return None.
 * @see None.
 * @note None.
 */
void jd_thingtalk_pal_thread_delete(jd_thingtalk_thread_t* thread_handle);

/**
 * @brief   获取线程执行状态
 *
* @param[in] thread_handle: pointer to thread handle.
 * @return
 	0:idel
	1:running
 * @see None.
 * @note None.
 */
int32_t jd_thingtalk_pal_thread_isrunning(jd_thingtalk_thread_t* thread_handle);


/**
 * 创建定时器
 *
 * @param htimer:Timer handler
 * @return 0:success, -1:failed.
 *
 */
int32_t  jd_thingtalk_pal_timer_create(jd_thingtalk_timer_t *htimer);

/**
 * 启动定时器
 *
 * @param htimer:Timer handler
 * @return 0:success, -1:failed.
 */
int32_t  jd_thingtalk_pal_timer_start(jd_thingtalk_timer_t *htimer);

/**
 * 停止定时器
 *
 * @param htimer:Timer handler
 * @return 0:success, -1:failed.
 *
 */
int32_t jd_thingtalk_pal_timer_stop(jd_thingtalk_timer_t *htimer);

/**
 * 删除定时器
 *
 * @param htimer:Timer handler
 * @return 0:success, -1:failed.
 *
 */
int32_t  jd_thingtalk_pal_timer_delete(jd_thingtalk_timer_t *htimer);

/**
 * @brief 毫秒级休眠
 *
 * @param [in] ms @n the time interval for which execution is to be suspended, in milliseconds.
 * @return None.
 * @see None.
 * @note None.
 */
void  jd_thingtalk_pal_msleep(uint32_t ms);

#ifdef __cplusplus
}
#endif

#endif


