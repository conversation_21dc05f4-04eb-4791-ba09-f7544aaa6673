#include "train_protocol.hpp"
#include "../train_agent_debug.h"
#include "train_manage/train_manage.hpp"
#include <iostream>
#include <string.h>

/**@brief     车辆段数据协议头匹配
 * @param[in]  uint8_t *buff --- 接收到的源数据包
 * @param[in]  uint16_t buff_len --- 接收到的源数据包长度
 * @return     train_protocol_err_tab 解码操作结果
 * - TRAIN_PROTOCOL_SUCESS     ---   正常解码
 * - TRAIN_PROTOCOL_HEAD_FALT  ---   接收数据包头不正确
 */
train_protocol_err_tab train_protocol_head_match(uint8_t *buff, uint16_t len)
{
	int strcmp_result = -1;

	const uint8_t head[3] = {TRAIN_PROTOCOL_UDP_HEAD_STRING};
	// std::cout << "log>>: data len 	" << len << std::endl;

	strcmp_result = strncmp((const char *)head, (const char *)(&buff[0]), 3);
	// std::cout << "log>>: protocol head match strcmp_result	" << strcmp_result << std::endl;
	if (0 == strcmp_result)
		return TRAIN_PROTOCOL_SUCESS;
	else
		return TRAIN_PROTOCOL_HEAD_FALT;
}

/**@brief     车辆段数据协议尾匹配
 * @param[in]  uint8_t *buff --- 接收到的源数据包
 * @param[in]  uint16_t buff_len --- 接收到的源数据包长度
 * @return     train_protocol_err_tab 解码操作结果
 * - TRAIN_PROTOCOL_SUCESS     ---   正常解码
 * - TRAIN_PROTOCOL_TAIL_FALT  ---   接收数据包尾不正确
 */
train_protocol_err_tab train_protocol_tail_match(uint8_t *buff, uint16_t len)
{
	int strcmp_result = -1;

	const uint8_t tail[1] = {TRAIN_PROTOCOL_UDP_TAIL_STRING};
	// std::cout << "log>>: data len 	" << len << std::endl;

	strcmp_result = strncmp((const char *)tail, (const char *)(&buff[len - 1]), 1);
	// std::cout << "log>>: protocol tail match strcmp_result	" << strcmp_result << std::endl;
	if (0 == strcmp_result)
		return TRAIN_PROTOCOL_SUCESS;
	else
		return TRAIN_PROTOCOL_TAIL_FALT;
}

/**@brief     车辆端协议解码API
 * @param[in]  uint8_t *buff --- 接收到的元数据包
 * @param[in]  uint16_t buff_len --- 接收到的元数据包长度
 * @param[out] uint8_t *data_buf  --- 解码得到的data字段数据缓冲区
 * @param[out] int *data_cnt --- 解码得到的data字段长度
 * @param[out] uint32_t *comm_seq --- 解码得到的当前元数据包UDP 序列号
 * @param[out] uint32_t *train_id --- 解码得到的当前元数据包UDP 中的设备ID
 * @return     train_protocol_err_tab 解码操作结果
 * - TRAIN_PROTOCOL_SUCESS     ---   正常解码
 * - TRAIN_PROTOCOL_HEAD_FALT  ---   接收数据包头不正确
 * - TRAIN_PROTOCOL_LEN_FALT   ---   接收数据长度不正确
 * - TRAIN_PROTOCOL_OPT_FALT   ---   接收数据操作类型不正确
 */
train_protocol_err_tab train_protocol_decodec(uint8_t *buff, uint16_t buff_len, uint8_t *data_buf, int *data_cnt, uint32_t *comm_seq, uint8_t *train_id)
{
	int head_strcmp_result = -1;
	int tail_strcmp_result = -1;
	uint16_t frame_len = 0;

	uint8_t head[3] = {TRAIN_PROTOCOL_UDP_HEAD_STRING};
	uint8_t tail[1] = {TRAIN_PROTOCOL_UDP_TAIL_STRING};
	uint8_t *buff_ptr_temp = &buff[0];

	head_strcmp_result = strncmp((const char *)head, (const char *)buff, 3);
	tail_strcmp_result = strncmp((const char *)tail, (const char *)(&buff[buff_len - 1]), 1);

	if (0 != head_strcmp_result)
	{
#ifdef PROTOCOL_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: data head is not correct \n\n" << std::endl;
#endif
		*data_cnt = 0;
		return TRAIN_PROTOCOL_HEAD_FALT;
	}

	if (0 != tail_strcmp_result)
	{
#ifdef PROTOCOL_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: data tail is not correct \n\n" << std::endl;
#endif
		*data_cnt = 0;
		return TRAIN_PROTOCOL_TAIL_FALT;
	}

	// 对源数据长度及解析得到的数据长度进行比对，若数据长度不匹配，返回错误信息
	frame_len = *(uint16_t *)(buff_ptr_temp + 3);
	if ((frame_len != (buff_len - 6)) || (frame_len < 8))
	{
		*data_cnt = 0;
		return TRAIN_PROTOCOL_LEN_FALT;
	}

	//crc校验
	uint8_t extracted_data[frame_len - 2];
    // std::copy(&buff_ptr_temp[5], &buff_ptr_temp[buff_len - 4], extracted_data);
	memcpy(extracted_data, &buff_ptr_temp[5], frame_len - 2);
	uint16_t crc = calc_crc16_net_msg(extracted_data, sizeof(extracted_data));
	if(crc != (*(uint16_t *)(buff_ptr_temp + buff_len - 3)))
	{
		*data_cnt = 0;
		return TRAIN_PROTOCOL_CRC_FALT;
	}

	// 解析UDP协议包中的序列号及设备ID信息
	*comm_seq = *(uint32_t *)(buff_ptr_temp + 5);
	*train_id = *(uint8_t *)(buff_ptr_temp + 9);

	std::cout << "info>>: udp recv sequence:" << *comm_seq << ", train_id: " << (uint16_t)*train_id << std::endl;

	// 遍历剩余字节，解码得到data字段消息
	memcpy(data_buf, &buff_ptr_temp[11], (frame_len - 8));
	*data_cnt = (buff_len - 14);

	return TRAIN_PROTOCOL_SUCESS;
}


void train_protocol_codec_data(train_protocol_cmd *cmd_buf, uint8_t *data_buf, uint16_t *data_len)
{
	uint16_t len_temp = 0;
	data_buf[0] =  cmd_buf->cmd_type;

	switch(cmd_buf->cmd_type)
	{
		case TRAIN_PROTOCOL_CMD_WALK_LOCATE:
		case TRAIN_PROTOCOL_CMD_WALK_NOLOCATE:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_WALK_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_WALK_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_MOV_POS_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_MOTOR_ZERO_CALIBRATION_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ROTATE_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_BELT_MOTOR_ZERO_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_PACK:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_PACK_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_PACK_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_CARRIAGR_UNPACK_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_EXCEPTION:
			memcpy(&data_buf[0], cmd_buf->cmd_value, TRAIN_PROTOCOL_CMD_EXCEPTION_LEN);
        	len_temp = TRAIN_PROTOCOL_CMD_EXCEPTION_LEN;
			break;
		case TRAIN_PROTOCOL_CMD_QUERY_PARA:
			memcpy(&data_buf[0], cmd_buf->cmd_value, 1);
			len_temp = 1;
			break;

		default:
			break;
	}
	
	*data_len = len_temp;

}

/**@brief     车辆端协议编码API
 * @param[in]  train_agent_cfg *cfg --- 待ack的配置参数
 * @param[out] uint8_t *calib_buf --- 编码完成后输出的校准点数据
 * @param[out] uint16_t *calib_data_len --- 编码完成后输出的校准点帧长度
 * @return     NULL
 */
void map_dev_calib_point_info(train_agent_cfg *cfg, uint8_t *calib_buf, uint16_t *calib_data_len)
{
	uint16_t len_temp = 0;
	uint16_t relativ_position = 0;
	uint32_t position_temp = 0;
	uint16_t relativ_position_temp = 0;
	int i;

	for(i = 0; i < cfg->map_dev_calib_point_cnt; i++)
	{
		memcpy(&calib_buf[len_temp], (const void *)&cfg->map_calib_points[i].position, 4);
		len_temp += 4;
	}
	for(i = 0; i < (15 - cfg->map_dev_calib_point_cnt); i++)
	{
		memcpy(&calib_buf[len_temp], &position_temp, 4);
		len_temp += 4;
	}

	relativ_position = cfg->map_total_length - cfg->map_calib_points[cfg->map_dev_calib_point_cnt - 1].position;
	memcpy(&calib_buf[len_temp], (const void *)&relativ_position, 2);
	len_temp += 2;

	for(i = 0; i < cfg->map_dev_calib_point_cnt - 1; i++)
	{
		relativ_position = cfg->map_calib_points[i+1].position - cfg->map_calib_points[i].position;

		memcpy(&calib_buf[len_temp], (const void *)&relativ_position, 2);
		len_temp += 2;
	}

	for(i = 0; i < (15 - cfg->map_dev_calib_point_cnt); i++)
	{
		memcpy(&calib_buf[len_temp], &relativ_position_temp, 2);
		len_temp += 2;
	}


	*calib_data_len = len_temp;
}


/**@brief     车辆端协议编码API
 * @param[in]  uint8_t train_id  ---车辆id
 * @param[in]  train_agent_cfg *cfg --- 待ack的配置参数
 * @param[out] uint8_t *data --- 编码完成后输出的数据
 * @param[out] uint16_t *data_len --- 编码完成后输出的数据帧长度
 * @return     NULL
 */
void train_protocol_codec_data_cfg(uint8_t train_id, train_agent_cfg *cfg, uint8_t * data, uint16_t *data_len)
{
	uint16_t len_temp = 0;
	uint32_t time_stamp;
	uint8_t calib_buf[256];
	uint16_t calib_data_len;
	uint16_t temp = 0;

	memcpy(&data[len_temp], (const void *)&cfg->heartbeat_cycle, 1);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->resend_timeout, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->map_total_length, 4);
	len_temp += 4;

	memcpy(&data[len_temp], (const void *)&cfg->map_dev_calib_point_cnt, 1);
	len_temp += 1;

	map_dev_calib_point_info(cfg, calib_buf, &calib_data_len);
	memcpy(&data[len_temp], (const void *)calib_buf, calib_data_len);
	len_temp += calib_data_len;

	memcpy(&data[len_temp], (const void *)&cfg->proximity_sensors_tolerant_num, 1);
	len_temp += 1;

	for(int i = 0; i < cfg->train_cfg_info.train_info_count; i++)
	{
		if(train_id == cfg->train_cfg_info.train_info[i].train_id)
		{
			memcpy(&data[len_temp], (const void *)&cfg->train_cfg_info.train_info[i].carriage_cnt, 1);
			len_temp += 1;
		}
	}

	memcpy(&data[len_temp], (const void *)&cfg->walk_motor_speed, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->walk_motor_acc, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->walk_motor_dec, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->carriage_motor_speed, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->carriage_motor_acc, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->carriage_motor_dec, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->belt_motor_speed, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->belt_zero_speed, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->belt_motor_acc, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->belt_rotation_distance, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->carriage_max_travel, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->unpack_exceed_threshold, 2);
	len_temp += 2;

	memcpy(&data[len_temp], (const void *)&cfg->unpack_sensor_switch, 1);
	len_temp += 1;

	for(int i = 0; i < cfg->train_cfg_info.train_info_count; i++)
	{
		if(train_id == cfg->train_cfg_info.train_info[i].train_id)
		{
			for(int j = 0; j < cfg->train_cfg_info.train_info[i].carriage_info_count; j++)
			{
				SPDLOG_INFO("carriage_info_state:{}-{}-{}", cfg->train_cfg_info.train_info[i].train_id, cfg->train_cfg_info.train_info[i].carriage_info[j].carriage_id, cfg->train_cfg_info.train_info[i].carriage_info[j].state);
				switch(cfg->train_cfg_info.train_info[i].carriage_info[j].state)
				{
					case enable_state_DEV_ENABLE_STATE_ENABLE:
						temp &= ~(1 << j);
						break;
					case enable_state_DEV_ENABLE_STATE_DISABLE:
						temp |= (1 << j);
						break;
					default:
						break;
				}
			}
		}
	}

	memcpy(&data[len_temp], &temp, 2);
	len_temp += 2;

	for(int i = 0; i < cfg->train_cfg_info.train_info_count; i++)
	{
		if(train_id == cfg->train_cfg_info.train_info[i].train_id)
		{
			memcpy(&data[len_temp], (const void *)&cfg->train_cfg_info.train_info[i].carriage_info[0].platform_info[0].type, 1);
			len_temp += 1;
		}
	}

	get_current_time_32bit(&time_stamp);
	memcpy(&data[len_temp], (const void *)&time_stamp, 4);
	len_temp += 4;

	*data_len = len_temp;
}

void train_protocol_codec(train_protocol_cmd *cmd_buf, uint8_t *buffer, uint16_t *data_len, uint8_t id, uint8_t type, uint32_t sequeue)
{
	uint8_t head[3] = {TRAIN_PROTOCOL_UDP_HEAD_STRING};
	uint8_t tail[1] = {TRAIN_PROTOCOL_UDP_TAIL_STRING};
	uint16_t crc_val = 0x00;
	uint16_t len_temp = 0x0;
	uint16_t data_len_temp;
	uint8_t data_buf[256];


	// 填充指令头
	memcpy(buffer, head, 3);
	len_temp += 3;

	memcpy(&buffer[len_temp], (const void *)&len_temp, 2);
	len_temp += 2;

	memcpy(&buffer[len_temp], (const void *)&sequeue, 4);
	len_temp += 4;

	memcpy(&buffer[len_temp], (const void *)&id, 1);
	len_temp += 1;

	memcpy(&buffer[len_temp], (const void *)&type, 1);
	len_temp += 1;

	train_protocol_codec_data(cmd_buf, data_buf, &data_len_temp);
	
	memcpy(&buffer[len_temp], (const void *)data_buf, data_len_temp);
	len_temp += data_len_temp;

	uint8_t extracted_data[data_len_temp + 6];
  	memcpy(extracted_data, &buffer[5], data_len_temp + 6);
	crc_val = calc_crc16_net_msg(extracted_data, 6 + data_len_temp);
	memcpy(&buffer[len_temp], (const void *)&crc_val, 2);
	len_temp += 2;

	memcpy(&buffer[len_temp], (const void *)tail, 1);
	len_temp += 1;

	uint16_t frame_length;
	frame_length = data_len_temp + 8;
	memcpy(&buffer[3], (const void *)&frame_length, 2);

	*data_len = len_temp;
}


/**@brief     车辆端协议编码API
 * @param[in]  train_agent_cfg *cfg --- 待ack的配置参数
 * @param[out] uint8_t *send --- 编码完成后输出的数据
 * @param[out] uint16_t *send_len --- 编码完成后输出的数据帧长度
 * @param[in]  uint8_t id --- 设备ID，填充至TCP协议中的指定位置
 * @param[in]  uint8_t type --- 指令类型，填充至TCP协议中的指定位置
 * @param[in]  uint32_t sequeue --- 指令序列号，填充至TCP协议中的指定位置
 * @return     NULL
 */
void train_protocol_codec_cfg(train_agent_cfg *cfg, uint8_t *send, uint16_t *send_len, uint8_t id, uint8_t type, uint32_t sequeue)
{
	uint8_t head[3] = {TRAIN_PROTOCOL_UDP_HEAD_STRING};
	uint8_t tail[1] = {TRAIN_PROTOCOL_UDP_TAIL_STRING};
	uint8_t data_buf[256];
	uint16_t data_len;
	uint16_t len_temp = 0x0;
	uint16_t frame_length;


	// 填充指令头
	memcpy(send, head, 3);
	len_temp += 3;

	memcpy(&send[len_temp], (const void *)&len_temp, 2);
	len_temp += 2;

	memcpy(&send[len_temp], (const void *)&sequeue, 4);
	len_temp += 4;

	memcpy(&send[len_temp], (const void *)&id, 1);
	len_temp += 1;

	memcpy(&send[len_temp], (const void *)&type, 1);
	len_temp += 1;

	train_protocol_codec_data_cfg(id, cfg, data_buf, &data_len);

	memcpy(&send[len_temp], (const void *)data_buf, data_len);
	len_temp += data_len;

	uint8_t extracted_data[data_len + 6];
  	memcpy(extracted_data, &send[5], data_len + 6);
	uint16_t crc_val = calc_crc16_net_msg(extracted_data, 6 + data_len);
	memcpy(&send[len_temp], (const void *)&crc_val, 2);
	len_temp += 2;

	memcpy(&send[len_temp], (const void *)tail, 1);
	len_temp += 1;

	frame_length = data_len + 8;
	memcpy(&send[3], (const void *)&frame_length, 2);

	*send_len = len_temp;
}


void get_current_time_32bit(uint32_t *time_stamp)
{
	uint32_t time_bits = 0;

	std::time_t now = std::time(0);
	std::tm* current_time = std::localtime(&now);
	
	// year (bit31-bit26)
	time_bits |= ((current_time->tm_year - 100) << 26);

	// month (bit25-bit22)
	time_bits |= ((current_time->tm_mon + 1) << 22);

	// day (bit21-bit17)
	time_bits |= ((current_time->tm_mday) << 17);

	// hour (bit16-bit12)
	time_bits |= ((current_time->tm_hour) << 12);

	// minute (bit11-bit6)
	time_bits |= ((current_time->tm_min) << 6);

	// second (bit5-bit0)
	time_bits |= (current_time->tm_sec);

	*time_stamp = time_bits;

#if 0
	std::cout << "year: " << (current_time->tm_year - 100) << ", mon: "<< (current_time->tm_mon + 1) << ", mday: " << (current_time->tm_mday) << std::endl;
	std::cout << "hour: "<< current_time->tm_hour << ", min: " << (current_time->tm_min) << ", sec: " << current_time->tm_sec << std::endl;
#endif
}


uint16_t calc_crc16_net_msg(uint8_t *data, int length)
{
    uint8_t i;
    //SPDLOG_DEBUG("data0:{}, data1:{}, data2:{}, data3:{}, data4:{}, data5:{}, data6:{}, data7:{}", data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7]);
    uint16_t crc = 0xffff;
    while(length--)
    {
        crc ^= *data++;
        for (i = 0; i < 8; ++i)
        {
            if (crc & 1)
                crc = (crc >> 1) ^ 0xA001;
            else
                crc = (crc >> 1);
        }
    }

    return crc;
}

