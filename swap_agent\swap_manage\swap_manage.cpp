#include "swap_manage.hpp"
#include "swap_agent_debug.h"
// #include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/auto_exchange.pb.h"
#include "swap_list.hpp"
#include "multi_swap_manager.hpp"


/**@brief     根据网络数据包，解析车辆传输的数据并判断车辆目标行为类型
* @param[in]  uint8_t *buf --- 输入的车辆网络数据包
* @param[in]  uint16_t buf_len --- 输入的车辆网络数据包长度
* @param[in]  uint8_t *data_buf --- 解码得到的data字段数据缓冲区
* @param[out] int *data_cnt  --- 解码得到的data字段长度
* @param[out] uint32_t *id  --- 解码得到的车辆ID 
* @param[out] uint32_t *sequeue  --- 解码得到的通信序列号
* @return     车辆消息类型，详见头文件定义
*/
TRAIN_MSG_TYPE swap_manage_msg_type(uint8_t *buf, uint16_t buf_len, uint8_t *data_buf, int *data_cnt, uint8_t *id, uint32_t *sequeue)
{	
	int cnt_temp;
	uint32_t seq_temp;
	uint8_t id_temp;
	uint8_t msg_net_type;
	train_protocol_err_tab err;
	
	err = train_protocol_decodec(buf, buf_len, data_buf, &cnt_temp, &seq_temp, &id_temp);

	if( err != TRAIN_PROTOCOL_SUCESS )
	{	
		OUTPUT_LOG;
		std::cout << "trail protocol decodec error : " << err << std::endl;
		return TRAIN_MSG_UNDEFINED;
	}
	
    msg_net_type = buf[10];
    std::cout << "log>>: msg_net_type: " << (uint16_t)msg_net_type << std::endl;
	
	*data_cnt = cnt_temp;
	*id = id_temp;
	*sequeue = seq_temp;
	
	if(TRAIN_PROTOCOL_OPERATION_REGISTER_UDP == msg_net_type)
	{
		return SWAP_MSG_REG;
	}
	else if(TRAIN_PROTOCOL_SESSION_HEART_BEAT_UDP == msg_net_type)
	{
		return SWAP_MSG_STATE;
	}
	else if(TRAIN_PROTOCOL_SESSION_EXCEPTION_UDP == msg_net_type)
	{
		return SWAP_MSG_EXCEPTIONAL;
	}
	else if(TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP == msg_net_type)
	{
		return SWAP_MSG_CMD_ACK;
	}
	else
	{
		return TRAIN_MSG_UNDEFINED;
	}		
}


/**@brief     车辆注册消息ACK生成
* @param[in]  int dev_id --- 车辆ID
* @param[in]  uint32_t seque  --- 有效通信序列号
* @param[in]  dev_agent_cfg *cfg  ---待ack的配置参数
* @param[out] uint8_t *send  --- 输出的注册ACK数据缓冲区
* @param[out] uint16_t *send_len  --- 输出的注册ACK数据缓冲区长度
* @return     NULL
*/
void swap_manage_train_register_ack(int dev_id, uint32_t seque, uint8_t *send, uint16_t *send_len, dev_agent_cfg *cfg)
{
	uint16_t opt_type_tcp = 0x00;

	//车辆注册消息回复
	opt_type_tcp = TRAIN_PROTOCOL_SESSION_REGISTER_ACK;

	//ACK消息编码
	train_protocol_codec_cfg(cfg, send, send_len, dev_id, opt_type_tcp, seque);


#ifdef SWAP_MANAGE_DEBUG
	printf("------------------------------------------------\r\n");
	printf("data_send_len %d\r\n", *send_len);
	printf("register_ack_data: ");
	for(int i = 0; i < *send_len; i++)
	{
		printf("%x ", send[i]);
	}
	printf("\r\n");
#endif

}


void swap_manage_get_dev_state(auto_exchange_dev_state *swap_state_temp, dev_state_net *dev_net_state, uint8_t *data_buf, uint8_t dev_id, uint8_t *excep_id, uint8_t *excep_level, uint32_t *excep_code)
{
	dev_state_net *p = (dev_state_net *)data_buf;

	cfg_display(p);
	
	*excep_id = p->excep_id;
	*excep_level = p->exception_level;
	*excep_code = p->exception_code;

	swap_state_temp->dev_id = dev_id;
	swap_state_temp->work_state = auto_exchange_work_state_WORK_STATE_INIT;
	swap_state_temp->curr_state = auto_exchange_device_state_DEV_UNKNOWN;
	swap_state_temp->dev_error_level = exception_level_RESERVE;
	swap_state_temp->dev_error_no = 0;
	swap_state_temp->dev_task_type = p->task_type;
	swap_state_temp->dev_task_state = p->task_state;
	swap_state_temp->dev_sub_task_state = p->sub_task_state;
	swap_state_temp->motion_positon = p->curr_position_x;
	swap_state_temp->motion_velocity = p->curr_speed_x;

	if((0x01 == p->dev_locate_st) || (0x02 == p->dev_locate_st))	//-1 未定位，0x00预定位，0x01已定位，0x02已上线
	{
		swap_state_temp->motion_positon_valid_flag = true;
		swap_state_temp->pos_state = dev_pos_calib_state_STATE_NORMAL;
		swap_state_temp->work_state = auto_exchange_work_state_WORK_STATE_WORK;
	}
	else if(0x00 == p->dev_locate_st)
	{
		swap_state_temp->motion_positon_valid_flag = false;
		swap_state_temp->pos_state = dev_pos_calib_state_STATE_INIT;
		swap_state_temp->work_state = auto_exchange_work_state_WORK_STATE_CALIB;
	}
	else
	{
		swap_state_temp->motion_positon_valid_flag = false;
		swap_state_temp->pos_state = dev_pos_calib_state_STATE_ERROR;
		swap_state_temp->work_state = auto_exchange_work_state_WORK_STATE_CALIB;
	}

	switch(p->dev_run_st)
	{
		case 0x00:
			swap_state_temp->curr_state = auto_exchange_device_state_DEV_NORMAL;
			break;

		case 0x01:	//异常
			swap_state_temp->curr_state = auto_exchange_device_state_DEV_ERROR;
			break;

		case 0x02:	//急停
			swap_state_temp->curr_state = auto_exchange_device_state_DEV_FATAL;
			break;

		case 0x03:	//设备初始化
			swap_state_temp->curr_state = auto_exchange_device_state_DEV_INIT;
			break;

		default:
			swap_state_temp->curr_state = auto_exchange_device_state_DEV_UNKNOWN;
			break;
	}

	if(0x05 == p->exception_level)
		swap_state_temp->dev_error_level = exception_level_FATAL;
	else if(0x04 == p->exception_level)
		swap_state_temp->dev_error_level = exception_level_WARNNING;
	else if(0x00 == p->exception_level)
		swap_state_temp->dev_error_level = exception_level_NORMAL;
	else
		swap_state_temp->dev_error_level = exception_level_RESERVE;

	switch(p->box_state)
	{
		case 0x00:
			swap_state_temp->slot_1_good_state = false;
			swap_state_temp->slot_2_good_state = false;
			break;
		case 0x01:
			swap_state_temp->slot_1_good_state = true;
			swap_state_temp->slot_2_good_state = false;
			break;
		case 0x02:
			swap_state_temp->slot_1_good_state = false;
			swap_state_temp->slot_2_good_state = true;
			break;
		case 0x03:
			swap_state_temp->slot_1_good_state = true;
			swap_state_temp->slot_2_good_state = true;
			break;
		default:
			break;
	}

	swap_state_temp->has_x_axis_work_state = true;
	swap_state_temp->x_axis_work_state.move_target = p->target_position_x;
	swap_state_temp->x_axis_work_state.curr_pos = p->curr_position_x;
	swap_state_temp->x_axis_work_state.ins_speed = p->cmd_speed_x;
	swap_state_temp->x_axis_work_state.curr_speed = p->curr_speed_x;
	swap_state_temp->x_axis_work_state.contrl_speed = p->adjust_speed_x;
	swap_state_temp->x_axis_work_state.dev_state = p->state_x;

	swap_state_temp->has_x1_motor_state = true;
	swap_state_temp->x1_motor_state.curr_speed = p->speed_x1;
	swap_state_temp->x1_motor_state.curr_pos = p->position_x1;
	swap_state_temp->x1_motor_state.error_no = p->motor_error_code_x1;

	swap_state_temp->has_x2_motor_state = true;
	swap_state_temp->x2_motor_state.curr_speed = p->speed_x2;
	swap_state_temp->x2_motor_state.curr_pos = p->position_x2;
	swap_state_temp->x2_motor_state.error_no = p->motor_error_code_x2;

	swap_state_temp->has_y_axis_work_state = true;
	swap_state_temp->y_axis_work_state.move_target = p->target_position_y;
	swap_state_temp->y_axis_work_state.curr_pos = p->curr_position_y;
	swap_state_temp->y_axis_work_state.ins_speed = p->cmd_speed_y;
	swap_state_temp->y_axis_work_state.curr_speed = p->curr_speed_y;
	swap_state_temp->y_axis_work_state.contrl_speed = p->adjust_speed_y;
	swap_state_temp->y_axis_work_state.dev_state = p->state_y;

	swap_state_temp->has_y1_motor_state = true;
	swap_state_temp->y1_motor_state.curr_speed = p->speed_y1;
	swap_state_temp->y1_motor_state.curr_pos = p->position_y1;
	swap_state_temp->y1_motor_state.error_no = p->motor_error_code_y1;

	swap_state_temp->has_y2_motor_state = true;
	swap_state_temp->y2_motor_state.curr_speed = p->speed_y2;
	swap_state_temp->y2_motor_state.curr_pos = p->position_y2;
	swap_state_temp->y2_motor_state.error_no = p->motor_error_code_y2;

	swap_state_temp->has_z1_hook_state = true;
	swap_state_temp->z1_hook_state.target_pos = p->target_position_z1;
	swap_state_temp->z1_hook_state.curr_pos = p->curr_position_z1;
	swap_state_temp->z1_hook_state.motion_state = p->action_state_z1;
	swap_state_temp->z1_hook_state.motor_err_no = p->motor_error_code_z1;

	swap_state_temp->has_z2_hook_state = true;
	swap_state_temp->z2_hook_state.target_pos = p->target_position_z2;
	swap_state_temp->z2_hook_state.curr_pos = p->curr_position_z2;
	swap_state_temp->z2_hook_state.motion_state = p->action_state_z2;
	swap_state_temp->z2_hook_state.motor_err_no = p->motor_error_code_z2;

	swap_state_temp->has_z3_hook_state = true;
	swap_state_temp->z3_hook_state.target_pos = p->target_position_z3;
	swap_state_temp->z3_hook_state.curr_pos = p->curr_position_z3;
	swap_state_temp->z3_hook_state.motion_state = p->action_state_z3;
	swap_state_temp->z3_hook_state.motor_err_no = p->motor_error_code_z3;

	swap_state_temp->current_mileage = p->mileage_reference;
	swap_state_temp->encoder_mileage = p->mileage;


	*dev_net_state = *p;
}


void cfg_display(dev_state_net *cfg)
{
	std::cout << "cfg>>: dev_run_st: " << (uint32_t)cfg->dev_run_st << "\n" << std::endl;
	std::cout << "cfg>>: dev_locate_st: " << (int32_t)cfg->dev_locate_st << "\n" << std::endl;
	std::cout << "cfg>>: exception_level: " << (uint32_t)cfg->exception_level << "\n" << std::endl;
	std::cout << "cfg>>: exception_code: " << cfg->exception_code << "\n" << std::endl;
	std::cout << "cfg>>: curr_task_id: " << cfg->curr_task_id << "\n" << std::endl;
	std::cout << "cfg>>: task_type: " << (uint32_t)cfg->task_type << "\n" << std::endl;
	std::cout << "cfg>>: task_control_object: " << (uint32_t)cfg->task_control_object << "\n" << std::endl;
	std::cout << "cfg>>: task_state: " << (uint32_t)cfg->task_state << "\n" << std::endl;
	std::cout << "cfg>>: sub_task_state: " << (uint32_t)cfg->sub_task_state << "\n" << std::endl;
	std::cout << "cfg>>: box_state: " << (uint32_t)cfg->box_state << "\n" << std::endl;
	std::cout << "cfg>>: target_position_x: " << cfg->target_position_x << "\n" << std::endl;
	std::cout << "cfg>>: curr_position_x: " << cfg->curr_position_x << "\n" << std::endl;
	std::cout << "cfg>>: cmd_speed_x: " << cfg->cmd_speed_x << "\n" << std::endl;
	std::cout << "cfg>>: curr_speed_x: " << cfg->curr_speed_x << "\n" << std::endl;
	std::cout << "cfg>>: adjust_speed_x: " << cfg->adjust_speed_x << "\n" << std::endl;
	std::cout << "cfg>>: state_x: " << (uint32_t)cfg->state_x << "\n" << std::endl;
	std::cout << "cfg>>: speed_x1: " << cfg->speed_x1 << "\n" << std::endl;
	std::cout << "cfg>>: position_x1: " << cfg->position_x1 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_x1: " << cfg->motor_error_code_x1 << "\n" << std::endl;
	std::cout << "cfg>>: speed_x2: " << cfg->speed_x2 << "\n" << std::endl;
	std::cout << "cfg>>: position_x2: " << cfg->position_x2 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_x2: " << cfg->motor_error_code_x2 << "\n" << std::endl;
	std::cout << "cfg>>: target_position_y: " << cfg->target_position_y << "\n" << std::endl;
	std::cout << "cfg>>: curr_position_y: " << cfg->curr_position_y << "\n" << std::endl;
	std::cout << "cfg>>: cmd_speed_y: " << cfg->cmd_speed_y << "\n" << std::endl;
	std::cout << "cfg>>: curr_speed_y: " << cfg->curr_speed_y << "\n" << std::endl;
	std::cout << "cfg>>: adjust_speed_y: " << cfg->adjust_speed_y << "\n" << std::endl;
	std::cout << "cfg>>: state_y: " << (uint32_t)cfg->state_y << "\n" << std::endl;
	std::cout << "cfg>>: speed_y1: " << cfg->speed_y1 << "\n" << std::endl;
	std::cout << "cfg>>: position_y1: " << cfg->position_y1 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_y1: " << cfg->motor_error_code_y1 << "\n" << std::endl;
	std::cout << "cfg>>: speed_y2: " << cfg->speed_y2 << "\n" << std::endl;
	std::cout << "cfg>>: position_y2: " << cfg->position_y2 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_y2: " << cfg->motor_error_code_y2 << "\n" << std::endl;
	std::cout << "cfg>>: target_position_z1: " << cfg->target_position_z1 << "\n" << std::endl;
	std::cout << "cfg>>: curr_position_z1: " << cfg->curr_position_z1 << "\n" << std::endl;
	std::cout << "cfg>>: action_state_z1: " << (uint32_t)cfg->action_state_z1 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_z1: " << cfg->motor_error_code_z1 << "\n" << std::endl;
	std::cout << "cfg>>: target_position_z2: " << cfg->target_position_z2 << "\n" << std::endl;
	std::cout << "cfg>>: curr_position_z2: " << cfg->curr_position_z2 << "\n" << std::endl;
	std::cout << "cfg>>: action_state_z2: " << (uint32_t)cfg->action_state_z2 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_z2: " << cfg->motor_error_code_z2 << "\n" << std::endl;
	std::cout << "cfg>>: target_position_z3: " << cfg->target_position_z3 << "\n" << std::endl;
	std::cout << "cfg>>: curr_position_z3: " << cfg->curr_position_z3 << "\n" << std::endl;
	std::cout << "cfg>>: action_state_z3: " << (uint32_t)cfg->action_state_z3 << "\n" << std::endl;
	std::cout << "cfg>>: motor_error_code_z3: " << cfg->motor_error_code_z3 << "\n" << std::endl;
	std::cout << "cfg>>: mileage: " << cfg->mileage << "\n" << std::endl;
	std::cout << "cfg>>: mileage_reference: " << cfg->mileage_reference << "\n" << std::endl;

}


void byte_conversion_uint32(uint8_t *buff_ptr, uint32_t data)
{
	buff_ptr[0] = (uint8_t)(data & 0xFF);
	buff_ptr[1] = (uint8_t)(data >> 8);
	buff_ptr[2] = (uint8_t)(data >> 16);
	buff_ptr[3] = (uint8_t)(data >> 24);
}

void byte_conversion_uint16(uint8_t *buff_ptr, uint16_t data)
{
	buff_ptr[0] = (uint8_t)(data & 0xFF);
	buff_ptr[1] = (uint8_t)(data >> 8);
}



/**@brief     判断待执行任务的类型
* @param[in]  auto_exchange_task *v_task --- 接收到的任务数据结构体
* @param[out] uint8_t *data_out --- 根据任务数据生成的网络字节流
* @param[out] uint16_t *data_len --- 生成的网络字节流长度
* @param[in]  dev_agent_cfg *cfg  --- 配置参数
* @return     操作结果，用来判断数据有效性
* - true      操作成功，数据有效
* - false     操作失败，数据无效
*/
int swap_manage_release_task(auto_exchange_task *v_task, uint8_t *data_out, uint16_t *data_len, dev_agent_cfg *cfg, int task_id)
{
	train_protocol_cmd task_temp;
	uint16_t data_send_len = 0x00;
	uint8_t data_send[TRAIN_PROTOCOL_CMD_VALUE_MAX_LEN];
	uint8_t type_opt = TRAIN_PROTOCOL_SESSION_CMD_OPT_UDP;

	swap_cmd_send cmd_send;
	memset(&cmd_send, 0, sizeof(swap_cmd_send));

	cmd_send.acc_x = cfg->motor_acc_x;
	cmd_send.speed_x = cfg->motor_speed_x;
	cmd_send.acc_y = cfg->motor_acc_y;
	cmd_send.speed_y = cfg->motor_speed_y;
	cmd_send.acc_load_z = cfg->motor_load_acc_z;
	cmd_send.acc_unload_z = cfg->motor_unload_acc_z;
	cmd_send.speed_load_z = cfg->motor_load_speed_z;
	cmd_send.speed_unload_z = cfg->motor_no_load_speed_z;

	uint8_t swap_id = (uint8_t)v_task->dev_id;
	uint32_t sequence = v_task->sequence;
	uint8_t sub_id = (uint8_t)v_task->sub_dev_id;

	SPDLOG_INFO("swap_id:{}, sub_id:{}, sequence:{}, task_id:{}, which_task:{} ", swap_id, sub_id, sequence, task_id, v_task->which_task);

	if(auto_exchange_task_move_tag == v_task->which_task)
	{
		if(auto_exchange_task_type_MOVE_X_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_X;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.move.target;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Y_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_Y;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_y = v_task->task.move.target;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_XY_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_XY;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.move.target;
			cmd_send.load_pos_y = v_task->task.move.move_distance;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z1_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP1;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.move.move_distance;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z2_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP2;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.move.move_distance;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z3_AXIS == v_task->task.move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP_DISTANCE;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.move.move_distance;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}
	}

	if(auto_exchange_task_grab_tag == v_task->which_task)
	{
		if(auto_exchange_task_type_HOOK_GRAB == v_task->task.grab.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_LOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}
	}

	if(auto_exchange_task_grab_move_tag == v_task->which_task)
	{
		if(auto_exchange_task_type_TASK_INTE_GRAB_MOVE == v_task->task.grab_move.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_LOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_move.x_target_pos;
			cmd_send.load_pos_x = v_task->task.grab_move.y_target_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}
	}

	if(auto_exchange_task_grab_inte_tag == v_task->which_task)
	{
		if(auto_exchange_task_type_MOVE_X_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_X;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_inte.x_grab_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Y_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_Y;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_y = v_task->task.grab_inte.y_grab_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_XY_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_XY;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_inte.x_grab_pos;
			cmd_send.load_pos_y = v_task->task.grab_inte.y_grab_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z1_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP1;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.grab_inte.z_stroke_length;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z2_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP2;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.grab_inte.z_stroke_length;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_MOVE_Z3_AXIS == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_MOVE_SWAP_DISTANCE;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.move_distance_z = v_task->task.grab_inte.z_stroke_length;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_HOOK_GRAB == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_LOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_inte.x_grab_pos;
			cmd_send.load_pos_y = v_task->task.grab_inte.y_grab_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_HOOK_UNLOAD == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_UBLOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.unload_pos_x = v_task->task.grab_inte.x_unload_pos;
			cmd_send.unload_pos_y = v_task->task.grab_inte.y_unload_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_TASK_INTE_GRAB_MOVE == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_LOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_inte.x_grab_pos;
			cmd_send.load_pos_y = v_task->task.grab_inte.y_grab_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_TASK_INTE_UNLOAD_MOVE == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_UBLOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.unload_pos_x = v_task->task.grab_inte.x_unload_pos;
			cmd_send.unload_pos_y = v_task->task.grab_inte.y_unload_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(auto_exchange_task_type_TASK_INTE_GRAB_UNLOAD == v_task->task.grab_inte.type)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_LOAD_UNLOAD;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			cmd_send.load_pos_x = v_task->task.grab_inte.x_grab_pos;
			cmd_send.load_pos_y = v_task->task.grab_inte.y_grab_pos;
			cmd_send.unload_pos_x = v_task->task.grab_inte.x_unload_pos;
			cmd_send.unload_pos_y = v_task->task.grab_inte.y_unload_pos;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}
	}

	if(auto_exchange_task_cmd_tag == v_task->which_task)
	{
		if(cmd_type_CMD_CALIB == v_task->task.cmd.cmd)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_PACK_UNPACK;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_FIND_ZERO;
			byte_conversion_uint32(&task_temp.cmd_value[2], task_id);
			task_temp.cmd_value[6] = sub_id;

			memcpy(&task_temp.cmd_value[7], &cmd_send, sizeof(cmd_send));
		}

		if(cmd_type_CMD_EMERG == v_task->task.cmd.cmd)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_EMERG;
		}

		if(cmd_type_CMD_RESET == v_task->task.cmd.cmd)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_RESET;
		}

		if(cmd_type_CMD_REBOOT == v_task->task.cmd.cmd)
		{
			task_temp.cmd_type = TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_EXCEPTION;
			task_temp.cmd_value[1] = SWAP_TASK_SEND_RESTART;
		}
	}

	
	if( auto_exchange_task_hb_time_sync_tag == v_task->which_task )
	{
		task_temp.cmd_type = TRAIN_PROTOCOL_CMD_DOWNLOAD_HEARTBEAT;
		task_temp.cmd_value[0] = TRAIN_PROTOCOL_CMD_DOWNLOAD_HEARTBEAT;

		uint32_t time_stamp;
		get_current_time_32bit(&time_stamp);
		byte_conversion_uint32(&task_temp.cmd_value[1], time_stamp);
	}

	if( auto_exchange_task_mil_info_tag == v_task->which_task )
	{
		task_temp.cmd_type = TRAIN_PROTOCOL_CMD_MILEAGE_INFO;

		task_temp.cmd_value[0] = (uint8_t )TRAIN_PROTOCOL_CMD_MILEAGE_INFO;
		byte_conversion_uint32(&task_temp.cmd_value[1], v_task->task.mil_info.encoder_mileage);
		byte_conversion_uint32(&task_temp.cmd_value[5], v_task->task.mil_info.current_mileage);
	}


	train_protocol_codec(&task_temp, data_send, &data_send_len, swap_id, type_opt, sequence);

	memcpy(data_out, data_send, data_send_len);
	*data_len = data_send_len;

	return 0;
}


void dev_query_para_recv(dev_run_para *para, uint8_t *data_buf)
{
	dev_run_para * p = (dev_run_para *)data_buf;

	run_para_display(p);

	*para = *p;
}

void run_para_display(dev_run_para *cfg)
{
	std::cout << "dev_run_cfg>>: heartbeat_cycle: " << cfg->heartbeat_cycle << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: resend_timeout: " << cfg->resend_timeout << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: box_num: " << (uint32_t)cfg->box_num << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: speed_x: " << cfg->speed_x << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: acc_x: " << cfg->acc_x << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: dec_x: " << cfg->dec_x << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: speed_y: " << cfg->speed_y << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: acc_y: " << cfg->acc_y << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: dec_y: " << cfg->dec_y << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: no_load_speed_z: " << cfg->no_load_speed_z << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: load_speed_z: " << cfg->load_speed_z << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: acc_z: " << cfg->acc_z << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: max_travel_x: " << cfg->max_travel_x << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: max_travel_y: " << cfg->max_travel_y << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: max_travel_z1: " << cfg->max_travel_z1 << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: max_travel_z3: " << cfg->max_travel_z3 << "\n" << std::endl;
	std::cout << "dev_run_cfg>>: max_travel_negative_z3: " << cfg->max_travel_negative_z3 << "\n" << std::endl;
}


void hb_log_decode(uint8_t *buffer, uint8_t *data_buf, std::string *net_msg_decode)
{
	dev_state_net *p = (dev_state_net *)data_buf;
	std::stringstream ss;
	string net_msg_temp = {""};
	char data[4];
	
	for(int i = 0; i < 11; i++)
	{
		sprintf(data, "%x ", buffer[i]);
		data[3] = 0x00;
		net_msg_temp += data;
	}

	ss << std::hex << net_msg_temp << ' ';
	ss << "rst:" << std::hex << (int)p->dev_run_st << ' ';
	ss << "lst:" << std::hex << (int)p->dev_locate_st << ' ';
	ss << "exid:" << std::hex << (int)p->excep_id << ' ';
	ss << "exlev:" << std::hex << (int)p->exception_level << ' ';
	ss << "excode:" << std::hex << htonl(p->exception_code) << ' ';
	ss << "task_id:" << std::hex << htonl(p->curr_task_id) << ' ';
	ss << "task_type:" << std::hex << (int)p->task_type << ' ';
	ss << "task_obj:" << std::hex << (int)p->task_control_object << ' ';
	ss << "tast_state:" << std::hex << (int)p->task_state << ' ';
	ss << "sub_task_state:" << std::hex << (int)p->sub_task_state << ' ';
	ss << "box_state:" << std::hex << (int)p->box_state << ' ';

	ss << "tar_pos_x:" << std::hex << htonl(p->target_position_x) << ' ';
	ss << "cur_pos_x:" << std::hex << htonl(p->curr_position_x) << ' ';
	ss << "cmd_speed_x:" << std::hex << htons(p->cmd_speed_x) << ' ';
	ss << "curr_speed_x:" << std::hex << htons(p->curr_speed_x) << ' ';
	ss << "adj_speed_x:" << std::hex << htons(p->adjust_speed_x) << ' ';
	ss << "state_x:" << std::hex << (int)p->state_x << ' ';

	ss << "speed_x1:" << std::hex << htons(p->speed_x1) << ' ';
	ss << "pos_x1:" << std::hex << htonl(p->position_x1) << ' ';
	ss << "err_code_x1:" << std::hex << htons(p->motor_error_code_x1) << ' ';

	ss << "speed_x2:" << std::hex << htons(p->speed_x2) << ' ';
	ss << "pos_x2:" << std::hex << htonl(p->position_x2) << ' ';
	ss << "err_code_x2:" << std::hex << htons(p->motor_error_code_x2) << ' ';

	ss << "tar_pos_y:" << std::hex << htons(p->target_position_y) << ' ';
	ss << "curr_pos_y:" << std::hex << htons(p->curr_position_y) << ' ';
	ss << "cmd_speed_y:" << std::hex << htons(p->cmd_speed_y) << ' ';
	ss << "curr_speed_y:" << std::hex << htons(p->curr_speed_y) << ' ';
	ss << "adj_speed_y:" << std::hex << htons(p->adjust_speed_y) << ' ';
	ss << "state_y:" << std::hex << (int)p->state_y << ' ';

	ss << "speed_y1:" << std::hex << htons(p->speed_y1) << ' ';
	ss << "pos_y1:" << std::hex << htons(p->position_y1) << ' ';
	ss << "err_code_y1:" << std::hex << htons(p->motor_error_code_y1) << ' ';

	ss << "speed_y2:" << std::hex << htons(p->speed_y2) << ' ';
	ss << "pos_y2:" << std::hex << htons(p->position_y2) << ' ';
	ss << "err_code_y2:" << std::hex << htons(p->motor_error_code_y2) << ' ';
	
	ss << "tar_pos_z1:" << std::hex << htons(p->target_position_z1) << ' ';
	ss << "curr_pos_z1:" << std::hex << htons(p->curr_position_z1) << ' ';
	ss << "act_st_z1:" << std::hex << (int)p->action_state_z1 << ' ';
	ss << "err_code_z1:" << std::hex << htons(p->motor_error_code_z1) << ' ';

	ss << "tar_pos_z2:" << std::hex << htons(p->target_position_z2) << ' ';
	ss << "curr_pos_z2:" << std::hex << htons(p->curr_position_z2) << ' ';
	ss << "act_st_z2:" << std::hex << (int)p->action_state_z2 << ' ';
	ss << "err_code_z2:" << std::hex << htons(p->motor_error_code_z2) << ' ';

	ss << "tar_pos_z3:" << std::hex << htons(p->target_position_z3) << ' ';
	ss << "curr_pos_z3:" << std::hex << htons(p->curr_position_z3) << ' ';
	ss << "act_st_z3:" << std::hex << (int)p->action_state_z3 << ' ';
	ss << "err_code_z3:" << std::hex << htons(p->motor_error_code_z3) << ' ';

	ss << "mileage:" << std::hex << htonl(p->mileage) << ' ';
	ss << "mileage_ref:" << std::hex << htonl(p->mileage_reference) << ' ';
	
	
	*net_msg_decode = ss.str();
}
