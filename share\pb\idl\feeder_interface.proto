syntax = "proto3";
option optimize_for = LITE_RUNTIME;

import "nanopb.proto";
import "sys_interface.proto";


//cmd


enum action
{
	RESERVE = 0;	//无意义
	START = 1;		//启动命令
	STOP = 2;		//停止命令
	RESET = 3;		//重启命令
	ROLL_FORWARD = 4;	//供包台皮带前向转动，用于测试
	ROLL_BACKWARD = 5;	//供包台皮带后向转动，用于测试和商品回退
	SUPPLY = 6;		//供包命令
	SCANNING = 7;	//扫码命令，强制扫码
	SET = 8;
}


enum action_para
{
	BELT_RESERVE = 0;	//无意义
	BELT_1 = 1;		//1段皮带
	BELT_2 = 2;		//2段皮带
	BELT_3 = 3;		//3段皮带
	ROLL_4 = 4;		//4段皮带，用于测试
	ROLL_ALL = 5;	//所有皮带，
}




message feeder_cmd
{
	uint32 sequnce = 1;
	uint32 feeder_id = 2;
	uint32 sub_dev_id = 3;
	action act = 4;
	action_para para = 5;
	int32 belt_speed = 6;
	uint32 train_id = 7;
	uint32 platform_id = 8;
	uint32 manual_length = 9;
}



//state

enum feeder_supply_state_tab
{
	SUPPLY_RESERVE = 0;
	SUPPLY_IDLE = 1;
	SUPPLY_START = 2;
	SUPPLY_RUNNING = 3;
	SUPPLY_FINISH = 4;
	SUPPLY_ERROR = 5;
}

message feeder_supply_state
{
	uint32 feeder_id = 1;
	uint32 sub_dev_id = 2;
	feeder_supply_state_tab state = 3;
}

enum feeder_belt_motor_id
{
	BELT_MOTOR_1 = 0;
	BELT_MOTOR_2 = 1;
	BELT_MOTOR_3 = 2;
	BELT_MOTOR_4 = 3;
	BELT_MOTOR_5 = 4;
	BELT_MOTOR_6 = 5;
}

enum feeder_belt_sensor_id
{
	SENSOR_1 = 0;
	SENSOR_2 = 1;
	SENSOR_3 = 2;
	SENSOR_4 = 3;
	SENSOR_5 = 4;
	SENSOR_6 = 5;
	SENSOR_7 = 6;
	SENSOR_8 = 7;
}

enum feeder_dev_work_state
{
	DEV_WORK_STATE_INIT = 0;
	DEV_WORK_STATE_POWER_UP = 1;
	DEV_WORK_STATE_BUSY = 2;
	DEV_WORK_STATE_IDEL = 3;
	DEV_WORK_STATE_SHIELD = 4;
}

enum feeder_dev_state
{
	DEV_STATE_NORMAL = 0;
	DEV_STATE_ERR = 1;
	DEV_STATE_UNKNOWN = 2;
}

message belt_motor_single_state
{
    feeder_belt_motor_id dev_id = 1;
	feeder_dev_work_state wk_state = 2;
    feeder_dev_state state = 3;
	int32 speed = 4;
	uint32 state_code = 5;
}

enum feeder_sensor_trigger_state
{
	ON = 0;
	OFF = 1;
	UNKNOW = 2;
}

message belt_sensor_single_state
{
	feeder_belt_sensor_id dev_id = 1;
	feeder_sensor_trigger_state state = 2;
}


message feeder_belt_sensor_state_multiple
{
	repeated belt_sensor_single_state sensor = 2 [(nanopb).max_count = 8];

}

message feeder_belt_motor_state_multiple
{
	repeated belt_motor_single_state motor = 2 [(nanopb).max_count = 6];
}

message scanner_state
{
	feeder_dev_work_state wk_state = 1;
    feeder_dev_state state = 2;
	uint32 err_code = 3;
	uint32 feeder_id = 4;
}

message charger_state
{
	feeder_dev_work_state wk_state = 1;
    feeder_dev_state state = 2;
	uint32 err_code = 3;
	uint32 charger_vol = 4;
	uint32 charger_curr = 5;
	uint32 feeder_id = 6;
}



message feeder_dev_state_total
{
	uint32 dev_id = 1;
    feeder_belt_motor_state_multiple belt_motor = 2;
	scanner_state auto_scanner = 3;
	scanner_state manual_scanner = 4;
	charger_state charger = 5;
	feeder_belt_sensor_state_multiple belt_sensor = 6;
	uint32 err_code = 7;
	uint32 feeder_id = 8;
	component_state state = 9;
	int32 has_goods = 10;
	bool ready_state = 11;  
    uint32 excp_handle = 12; 
	uint32 sequence = 13;
}


enum goods_position
{
	//手持扫码，商品尚未放到皮带上。有可能用不到。
	UNDETERMINED = 0; 	
	FIRST_BELT = 1;
	SECOND_BELT = 2;
	THIRD_BELT = 3;
	FOURTH_BELT = 4;
	LAST_BELT = 0xFF;
}

enum scanner
{
	NONE = 0;
}

message goods_info
{
	//单个商品会有多个码，传到调度系统的是一串字符串由逗号隔开。调度系统内部也不做解析，沿用此格式即可。
	string codes = 1 [(nanopb).max_length = 350];	

	goods_position position = 2;

	//扫码信息上报时，scanner_id按照实际扫码设备上报。上报末端商品信息时，scanner_id 设NONE即可。
	uint32 scanner_id = 3;		
}