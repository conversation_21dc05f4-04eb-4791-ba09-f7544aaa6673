#include <cstdint>
#include <string>

#include <spdlog/spdlog.h>

#include "share/pb/idl/exception.pb.h"
#include "share/exception_code.hpp"

namespace dev_except
{
	// inline except_info feeder_sys_init_timeout()
	// {
	// 	except_info e = {exception_src_WCS, exception_level_FATAL, FEEDER_SYS_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

	// 	std::string text = "供包机软件系统启动超时";
	// 	strncpy(e.description, text.c_str(), sizeof(e.description));

	// 	return e;
	// }

	// inline except_info vehicle_sys_init_timeout()
	// {
	// 	except_info e = {exception_src_WCS, exception_level_FATAL, TRAIN_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

	// 	std::string text = "车辆代理软件系统启动超时";
	// 	strncpy(e.description, text.c_str(), sizeof(e.description));

	// 	return e;
	// }
	
	// inline except_info plane_sys_init_timeout()
	// {
	// 	except_info e = {exception_src_WCS, exception_level_FATAL, PLANE_SYS_INIT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

	// 	std::string text = "分播架软件系统启动超时";
	// 	strncpy(e.description, text.c_str(), sizeof(e.description));

	// 	return e;
	// }

	inline except_info feeder_sys_heartbeat_overtime()
	{
		except_info e = {exception_src_THING_AGENT, exception_level_FATAL, FEEDER_SYS_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

		std::string text = "供包台软件系统状态上报超时";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

	inline except_info vehicle_sys_heartbeat_overtime()
	{
		except_info e = {exception_src_THING_AGENT, exception_level_FATAL, TRAIN_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

		std::string text = "车辆代理软件系统状态上报超时";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

	inline except_info scheduler_sys_heartbeat_overtime()
	{
		except_info e = {exception_src_THING_AGENT, exception_level_FATAL, SCHEDULER_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

		std::string text = "调度软件系统状态上报超时";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

	// inline except_info plane_sys_heartbeat_overtime()
	// {
	// 	except_info e = {exception_src_THING_AGENT, exception_level_FATAL, PLANE_SYS_HEARTBEAT_OVERTIME, 0, "", 0, exception_state_STATE_OCCURED};

	// 	std::string text = "分播架软件系统状态上报超时";
	// 	strncpy(e.description, text.c_str(), sizeof(e.description));

	// 	return e;
	// }
}