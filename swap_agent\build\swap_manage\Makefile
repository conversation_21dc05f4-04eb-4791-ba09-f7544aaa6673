# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/swap_manage/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 swap_manage/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 swap_manage/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 swap_manage/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 swap_manage/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
swap_manage/CMakeFiles/lib_swap_manage.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 swap_manage/CMakeFiles/lib_swap_manage.dir/rule
.PHONY : swap_manage/CMakeFiles/lib_swap_manage.dir/rule

# Convenience name for target.
lib_swap_manage: swap_manage/CMakeFiles/lib_swap_manage.dir/rule

.PHONY : lib_swap_manage

# fast build rule for target.
lib_swap_manage/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/build
.PHONY : lib_swap_manage/fast

swap_list.o: swap_list.cpp.o

.PHONY : swap_list.o

# target to build an object file
swap_list.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.o
.PHONY : swap_list.cpp.o

swap_list.i: swap_list.cpp.i

.PHONY : swap_list.i

# target to preprocess a source file
swap_list.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.i
.PHONY : swap_list.cpp.i

swap_list.s: swap_list.cpp.s

.PHONY : swap_list.s

# target to generate assembly for a file
swap_list.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_list.cpp.s
.PHONY : swap_list.cpp.s

swap_manage.o: swap_manage.cpp.o

.PHONY : swap_manage.o

# target to build an object file
swap_manage.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.o
.PHONY : swap_manage.cpp.o

swap_manage.i: swap_manage.cpp.i

.PHONY : swap_manage.i

# target to preprocess a source file
swap_manage.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.i
.PHONY : swap_manage.cpp.i

swap_manage.s: swap_manage.cpp.s

.PHONY : swap_manage.s

# target to generate assembly for a file
swap_manage.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f swap_manage/CMakeFiles/lib_swap_manage.dir/build.make swap_manage/CMakeFiles/lib_swap_manage.dir/swap_manage.cpp.s
.PHONY : swap_manage.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... lib_swap_manage"
	@echo "... swap_list.o"
	@echo "... swap_list.i"
	@echo "... swap_list.s"
	@echo "... swap_manage.o"
	@echo "... swap_manage.i"
	@echo "... swap_manage.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

