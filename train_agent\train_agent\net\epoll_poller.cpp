﻿
/**@file  epoll_poller.cpp
* @brief       基于epoll的并发客户端管理软件
* @details     NULL
* <AUTHOR>
* @date        2021-06-22
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持多客户端连接              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对epoll 机制API进行二次封装，使用class成员函数实现功能调用 
* -# 使用virtual定义epoll的主循环和事件处理 
* </table>
*
**********************************************************************************
*/


#include <spdlog/spdlog.h>


#include "epoll_poller.hpp"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/epoll.h>
#include <unistd.h>
#include <errno.h>
#include <assert.h>
#include <sys/eventfd.h>
#include <fcntl.h>
#include <iostream>
#include <unordered_map>
#include <mutex>
#include <arpa/inet.h>


/**@brief     epoll 创建
* @param[in]  int sock_num --- 最大可监听事件数量
* @return     创建的epoll描述符
*/
static int epoll_create_fd(int sock_num )  
{
	int epoll_fd = epoll_create(sock_num);
	
	if( epoll_fd<0 )
	{

		return EPOLL_POLLER_CREATE_ERR;
	}

	return epoll_fd;

}


/**@brief     设置指定的描述符类型为非阻塞性，以对应非阻塞网络处理
* @param[in]  int fd --- 待处理的client设备的 fd
* @return     函数执行结果
* - false     添加失败
* - true      添加成功
*/
static bool epoll_set_nonblocking(int fd)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(fd, F_GETFL);

	if(curr_option < 0)
	{
        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(fd,F_SETFL,new_option)<0)
	{
        return false;
	}
	

    return true;
}




/**@brief  epoll_poller class构造函数
* @param[in]  NULL
* @return     NULL
*/
epoll_poller::epoll_poller()
{
	return;
}



/**@brief  epoll_poller class析构函数
* @param[in]  NULL
* @return     NULL
*/
epoll_poller::~epoll_poller()
{
	::close(m_epollfd);
}


/**@brief     TCP 通信中 Server 构造
* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
void epoll_poller::epoll_init(int listen_fd)
{
	m_epollfd = epoll_create_fd(20);
	m_server_fd = listen_fd;
	m_eventfd = epoll_create_fd(20);
}


/**@brief     向已创建的epoll fd中添加待监听的client描述
* @param[in]  int fd --- 待添加的client设备的 fd
* @param[in]  int mode --- 指定的epoll触发模式
* @ref  	    EPOLLIN  水平触发
* @ref          EPOLLET  边沿触发
* @param[in]  bool one_shot --- 是否触发一次的选项
* @ref  	    true  设置为单次触发，后续若需要再次触发需二次处理
* @ref          false 设置为自动触发
* @return     函数执行结果
* - false     添加失败
* - true      添加成功
*/
bool epoll_poller::epoll_add_fd(int fd, int mode, bool one_shot)
{
	struct epoll_event ev;

	ev.data.fd = fd;

	if(((unsigned int)mode) == EPOLLET)
	{
		ev.events = EPOLLIN|EPOLLET;
	}
	else
	{
		ev.events = EPOLLIN;
	}

	if (one_shot)
	{
		ev.events |= EPOLLONESHOT;  // 使用EPOLLONESHOT模式
	}
	   
	if( epoll_ctl(m_epollfd, EPOLL_CTL_ADD, fd, &ev)<0 )
	{
	   return false;
	}

	epoll_set_nonblocking(fd);
	
	return true;
  
}



/**@brief     向已创建的epoll fd中移除指定的client
* @param[in]  int fd --- 待处理的client设备的 fd
* @return     函数执行结果
* - false     移除失败
* - true      移除成功
*/
bool epoll_poller::epoll_remove_fd(int fd)
{
	   
	if( epoll_ctl(m_epollfd, EPOLL_CTL_DEL, fd, nullptr)<0 )
	{
	
	   return false;
	}
	
	return true;
  
}


/**@brief     重置已有的epoll对象，整体流程同添加操作类似
* @param[in]  int fd --- 待添加的client设备的 fd
* @param[in]  int mode --- 指定的epoll触发模式
* @ref  	    EPOLLIN  水平触发
* @ref          EPOLLET  边沿触发
* @param[in]  bool one_shot --- 是否触发一次的选项
* @ref  	    true  设置为单次触发，后续若需要再次触发需二次处理
* @ref          false 设置为自动触发
* @return     函数执行结果
* - false     移除失败
* - true      移除成功
*/
bool epoll_poller::epoll_reset_fd(int fd, int mode, bool one_shot)
{
	struct epoll_event ev;

	ev.data.fd = fd;

	if(((unsigned int)mode) == EPOLLET)
	{
		ev.events = EPOLLIN|EPOLLET;
	}
	else
	{
		ev.events = EPOLLIN;
	}

	if (one_shot)
	{
		ev.events |= EPOLLONESHOT;  
	}
	   
	if( epoll_ctl(m_epollfd, EPOLL_CTL_MOD, fd, &ev)<0 )
	{

	   return false;
	}

	
	return true;
  
}


/**@brief     epoll事件等待
* @param[in]  int timeout --- 等待超时函数，避免长时间阻塞等待
* @return     需要处理处理的事件数目
*/
int epoll_poller::epoll_wait_fd(int timeout)
{
	int ev_ready_fd = -1;

	ev_ready_fd = epoll_wait(m_epollfd, m_events, EPOLL_POLLER_MAX_EVENT_CNT, timeout);
	
	if(ev_ready_fd == -1)
	{

	}

	return ev_ready_fd;

}

/**@brief     epoll监听停止，暂时没用
* @param[in]  NULL
* @return     NULL
*/
void epoll_poller::epoll_poller_stop()
{
	m_isLooping = false;
}


/**@brief     在已有活跃对象中查找指定fd是否存在(暂时没用)
* @param[in]  uint32_t cin_addr  --- 待查找的 IP
* @return     查找执行结果
* - false     查找失败，该IP不存在
* - true      查找成功，该IP存在
*/
bool epoll_poller::epoll_poller_dev_find(uint32_t cin_addr)
{
	//std::lock_guard<std::mutex> opt_lock(m_opt_lock);

	unordered_map<uint32_t, struct sockaddr_in>::const_iterator result;

	result = m_epoll_dev_list.find(cin_addr);

	if( result == m_epoll_dev_list.end() )
	{
		return false;
	}
	else
	{
		return true;
	}


}


/**@brief     向当前列表中插入新连接
* @param[in]  int epoll_fd --- 待插入的client设备的 fd
* @param[in]  struct sockaddr_in sock_info --- 该连接对应的IP地址及端口号
* @return     函数执行结果
* - false     插入失败
* - true      插入成功
*/
bool epoll_poller::epoll_poller_dev_insert(uint32_t cin_addr, struct sockaddr_in sock_info)
{
	bool result;

	//首先查找元素是否存在
	result = epoll_poller_dev_find(cin_addr);

	std::lock_guard<std::mutex> opt_lock(m_opt_lock);

	if( false == result )
	{
		m_epoll_dev_list[cin_addr] = sock_info;
		return true;
	}
	else
	{
		return false;
	}


}

/**@brief     向当前列表中移除连接
* @param[in]  uint32_t cin_addr --- 待处理的client设备的 ip
* @return     函数执行结果
* - false     移除失败
* - true      移除成功
*/
bool epoll_poller::epoll_poller_dev_delete(uint32_t cin_addr)
{
	
	std::lock_guard<std::mutex> opt_lock(m_opt_lock);

	if( m_epoll_dev_list.find(cin_addr) == m_epoll_dev_list.end() )
	{
		return false;
	}

	auto result = m_epoll_dev_list.erase(cin_addr);

	if( 0 == result )
	{
		return false;
	}
	else
	{
		return true;
	}
}



/**@brief     查找指定的ip的IP地址等信息
* @param[in]  uint32_t cin_addr --- 待处理的client设备的 ip
* @return     该连接的 IP地址等信息
*/
struct sockaddr_in epoll_poller::epoll_poller_dev_get_sock_addr(uint32_t cin_addr)
{
	std::lock_guard<std::mutex> opt_lock(m_opt_lock);
	
	sockaddr_in temp;
	temp.sin_family = 0;
	temp.sin_port = 0;

	unordered_map<uint32_t, struct sockaddr_in>::iterator  iter = m_epoll_dev_list.find(cin_addr);

	if( iter!= m_epoll_dev_list.end() )
	{
		return iter->second;
	}
	else
	{
		return temp;
	}
}


/**@brief     显示当前活跃连接的所有内容(调试使用)
* @param[in]  NULL
* @return     NULL
*/

void epoll_poller::display(void)
{
	char ipv4[16];
	std::lock_guard<std::mutex> opt_lock(m_opt_lock);
	
	SPDLOG_INFO("m_epoll_dev_list:");
    
	for (auto& x: m_epoll_dev_list)
	{
		inet_ntop(AF_INET, &(x.second.sin_addr), ipv4, sizeof(x.second));

		SPDLOG_INFO("epoll list :{} :{} :{}", x.first, ipv4, htons(x.second.sin_port) );
	}

    cout << endl;
}


