#pragma once

#include <assert.h>

#include <future>
#include <map>
#include <list>
#include <mutex>
#include <string>
#include <memory>
#include <cstdint>
#include <queue>
#include <deque>
#include <iostream>
#include <unordered_map>
#include <functional>

#include "pb_common.h"
#include "pb_decode.h"
#include "pb_encode.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "share/pb/idl/ack.pb.h"
#include "share/pb/idl/task.pb.h"
// #include "share/pb/idl/feeder_goods.pb.h"
#include "share/global_def.h"

#include "share/pb/idl/sys_interface.pb.h"
// #include "share/pb/idl/sys_state.pb.h"

#include "ipc_interface/task_interface.hpp"
#include "ipc_interface/sys_state_interface.hpp"
#include "ipc_interface/exception_interface.hpp"

#include "event_vector.hpp"
#include "thing_manager/device_manager/device_manager.hpp"


class sys_manager
{
public:

    int init(zmq::context_t &ctx);

    int run();

    bool is_sys_state_changed();

    int get_sys_state(sys_mode_state &st);

    int get_exception(except_info &e);

    int set_manual_mode();

    int set_auto_mode();

    int issue_mcu_reboot();

    int issue_program_restart();

    int issue_vehicle_start_demo();
    int issue_vehicle_stop_demo();

    int remove_vehicle_exception(const uint32_t &id);          //车下线时使用

    static sys_manager *get_instance(void)
    {
        static sys_manager instance;
        return &instance;
    }

    class timer
    {
    private:
        std::chrono::high_resolution_clock::time_point start_time;   //定时查询用
        std::chrono::high_resolution_clock::time_point end_time;

    public:
        void start()
        {
            start_time = std::chrono::high_resolution_clock::now();
        }

        uint32_t execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

private:

    //std::mutex state_mutex;       //全部是基础类型的结构体可不加锁

	event_vector vec;

    void update_sys_state();     //开线程轮询更新，和异常列表在同一线程

	int on_exception(const event_exception &e);

    int update_err_state();

    void state_thread_manage();

    void exception_thread_manage();

	sys_mode_state sys_st =
    {
        mode : e_sys_mode_AUTO,
        state : e_wkstate_SYS_INIT,
        err : e_errstate_NOERROR,
        has_dev_st : true,
        dev_st : {false, false},
        error_code : 0
    };

    // bool sys_state_changed_ipc = false;         //用于表示系统状态改变后立即进程间下发
    bool sys_state_changed_thing = false;         //用于表示系统状态改变后立即上报物控
    bool sys_state_changed_falut_thing = false;         //用于表示系统状态改变后立即上报物控
    
    timer state_issue_timer;

    std::mutex error_list_lock;
	std::list<except_info> warning_exception_list;
	std::list<except_info> error_exception_list;
	std::list<except_info> fatal_exception_list;

    std::mutex except_queue_mutex;
    std::condition_variable except_cv;
    std::queue<except_info> except_report_queue;

	std::thread *state_manage;
    std::thread *except_manage;

};

