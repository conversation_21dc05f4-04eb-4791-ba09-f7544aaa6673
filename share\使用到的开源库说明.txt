1、zeromq(zmq)和cppzmq
zeromq是一个开源的消息库，用来实现基于TCP/IPC/inproc(线程间)的消息通讯，支持的通讯模式有，pub-sub, req-rep, pull-push。
我们主要使用的模式是，pub-sub和req-rep(及其变种)。
cppzmq是对zeromq的一个C++语言封装。
使用这两个库我们主要是用来实现节点间的通讯，实现比较彻底的解耦。参考底层架构设计。
https://zeromq.org/
使用版本:libzmq:4.2.5  cppzmq:4.7.1

2、nanopb
nanopb是一个C语言的轻量级protobuf实现。用来实现序列化和反序列化，配合zmq实现节点间通讯。
https://github.com/nanopb/nanopb
使用版本:0.4.4

3、spdlog
开源日志库。
https://github.com/gabime/spdlog
使用版本:1.8.2

4、catch2
开源C++单元测试框架。我们用它来做单元测试。
https://github.com/catchorg/Catch2
使用版本:2.13.6

5、nlohmann_json
非常好用的开源C++ json库。目前地图文件是以json格式保存的。我们使用它来解析地图文件。
https://github.com/nlohmann/json
使用版本:3.9.1

6、poco
一系列C++类库，类似Java类库，.Net框架，Apple的Cocoa;基于BSL协议开源。
我们目前主要用他的Data库，访问SQLite数据库。
https://pocoproject.org/
使用版本：1.11.0（内置SQLite:3.35.5）
