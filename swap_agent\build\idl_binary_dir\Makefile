# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/idl_binary_dir/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 idl_binary_dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 idl_binary_dir/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 idl_binary_dir/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 idl_binary_dir/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
idl_binary_dir/CMakeFiles/idl.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 idl_binary_dir/CMakeFiles/idl.dir/rule
.PHONY : idl_binary_dir/CMakeFiles/idl.dir/rule

# Convenience name for target.
idl: idl_binary_dir/CMakeFiles/idl.dir/rule

.PHONY : idl

# fast build rule for target.
idl/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/build
.PHONY : idl/fast

ack.pb.o: ack.pb.c.o

.PHONY : ack.pb.o

# target to build an object file
ack.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/ack.pb.c.o
.PHONY : ack.pb.c.o

ack.pb.i: ack.pb.c.i

.PHONY : ack.pb.i

# target to preprocess a source file
ack.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/ack.pb.c.i
.PHONY : ack.pb.c.i

ack.pb.s: ack.pb.c.s

.PHONY : ack.pb.s

# target to generate assembly for a file
ack.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/ack.pb.c.s
.PHONY : ack.pb.c.s

auto_exchange.pb.o: auto_exchange.pb.c.o

.PHONY : auto_exchange.pb.o

# target to build an object file
auto_exchange.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange.pb.c.o
.PHONY : auto_exchange.pb.c.o

auto_exchange.pb.i: auto_exchange.pb.c.i

.PHONY : auto_exchange.pb.i

# target to preprocess a source file
auto_exchange.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange.pb.c.i
.PHONY : auto_exchange.pb.c.i

auto_exchange.pb.s: auto_exchange.pb.c.s

.PHONY : auto_exchange.pb.s

# target to generate assembly for a file
auto_exchange.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange.pb.c.s
.PHONY : auto_exchange.pb.c.s

auto_exchange_info.pb.o: auto_exchange_info.pb.c.o

.PHONY : auto_exchange_info.pb.o

# target to build an object file
auto_exchange_info.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_info.pb.c.o
.PHONY : auto_exchange_info.pb.c.o

auto_exchange_info.pb.i: auto_exchange_info.pb.c.i

.PHONY : auto_exchange_info.pb.i

# target to preprocess a source file
auto_exchange_info.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_info.pb.c.i
.PHONY : auto_exchange_info.pb.c.i

auto_exchange_info.pb.s: auto_exchange_info.pb.c.s

.PHONY : auto_exchange_info.pb.s

# target to generate assembly for a file
auto_exchange_info.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_info.pb.c.s
.PHONY : auto_exchange_info.pb.c.s

auto_exchange_map.pb.o: auto_exchange_map.pb.c.o

.PHONY : auto_exchange_map.pb.o

# target to build an object file
auto_exchange_map.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_map.pb.c.o
.PHONY : auto_exchange_map.pb.c.o

auto_exchange_map.pb.i: auto_exchange_map.pb.c.i

.PHONY : auto_exchange_map.pb.i

# target to preprocess a source file
auto_exchange_map.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_map.pb.c.i
.PHONY : auto_exchange_map.pb.c.i

auto_exchange_map.pb.s: auto_exchange_map.pb.c.s

.PHONY : auto_exchange_map.pb.s

# target to generate assembly for a file
auto_exchange_map.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/auto_exchange_map.pb.c.s
.PHONY : auto_exchange_map.pb.c.s

container_interface.pb.o: container_interface.pb.c.o

.PHONY : container_interface.pb.o

# target to build an object file
container_interface.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/container_interface.pb.c.o
.PHONY : container_interface.pb.c.o

container_interface.pb.i: container_interface.pb.c.i

.PHONY : container_interface.pb.i

# target to preprocess a source file
container_interface.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/container_interface.pb.c.i
.PHONY : container_interface.pb.c.i

container_interface.pb.s: container_interface.pb.c.s

.PHONY : container_interface.pb.s

# target to generate assembly for a file
container_interface.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/container_interface.pb.c.s
.PHONY : container_interface.pb.c.s

data_map.pb.o: data_map.pb.c.o

.PHONY : data_map.pb.o

# target to build an object file
data_map.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_map.pb.c.o
.PHONY : data_map.pb.c.o

data_map.pb.i: data_map.pb.c.i

.PHONY : data_map.pb.i

# target to preprocess a source file
data_map.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_map.pb.c.i
.PHONY : data_map.pb.c.i

data_map.pb.s: data_map.pb.c.s

.PHONY : data_map.pb.s

# target to generate assembly for a file
data_map.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_map.pb.c.s
.PHONY : data_map.pb.c.s

data_request.pb.o: data_request.pb.c.o

.PHONY : data_request.pb.o

# target to build an object file
data_request.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_request.pb.c.o
.PHONY : data_request.pb.c.o

data_request.pb.i: data_request.pb.c.i

.PHONY : data_request.pb.i

# target to preprocess a source file
data_request.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_request.pb.c.i
.PHONY : data_request.pb.c.i

data_request.pb.s: data_request.pb.c.s

.PHONY : data_request.pb.s

# target to generate assembly for a file
data_request.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/data_request.pb.c.s
.PHONY : data_request.pb.c.s

dev_hmi.pb.o: dev_hmi.pb.c.o

.PHONY : dev_hmi.pb.o

# target to build an object file
dev_hmi.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/dev_hmi.pb.c.o
.PHONY : dev_hmi.pb.c.o

dev_hmi.pb.i: dev_hmi.pb.c.i

.PHONY : dev_hmi.pb.i

# target to preprocess a source file
dev_hmi.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/dev_hmi.pb.c.i
.PHONY : dev_hmi.pb.c.i

dev_hmi.pb.s: dev_hmi.pb.c.s

.PHONY : dev_hmi.pb.s

# target to generate assembly for a file
dev_hmi.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/dev_hmi.pb.c.s
.PHONY : dev_hmi.pb.c.s

exception.pb.o: exception.pb.c.o

.PHONY : exception.pb.o

# target to build an object file
exception.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/exception.pb.c.o
.PHONY : exception.pb.c.o

exception.pb.i: exception.pb.c.i

.PHONY : exception.pb.i

# target to preprocess a source file
exception.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/exception.pb.c.i
.PHONY : exception.pb.c.i

exception.pb.s: exception.pb.c.s

.PHONY : exception.pb.s

# target to generate assembly for a file
exception.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/exception.pb.c.s
.PHONY : exception.pb.c.s

feeder_interface.pb.o: feeder_interface.pb.c.o

.PHONY : feeder_interface.pb.o

# target to build an object file
feeder_interface.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/feeder_interface.pb.c.o
.PHONY : feeder_interface.pb.c.o

feeder_interface.pb.i: feeder_interface.pb.c.i

.PHONY : feeder_interface.pb.i

# target to preprocess a source file
feeder_interface.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/feeder_interface.pb.c.i
.PHONY : feeder_interface.pb.c.i

feeder_interface.pb.s: feeder_interface.pb.c.s

.PHONY : feeder_interface.pb.s

# target to generate assembly for a file
feeder_interface.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/feeder_interface.pb.c.s
.PHONY : feeder_interface.pb.c.s

scheduler_interface.pb.o: scheduler_interface.pb.c.o

.PHONY : scheduler_interface.pb.o

# target to build an object file
scheduler_interface.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/scheduler_interface.pb.c.o
.PHONY : scheduler_interface.pb.c.o

scheduler_interface.pb.i: scheduler_interface.pb.c.i

.PHONY : scheduler_interface.pb.i

# target to preprocess a source file
scheduler_interface.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/scheduler_interface.pb.c.i
.PHONY : scheduler_interface.pb.c.i

scheduler_interface.pb.s: scheduler_interface.pb.c.s

.PHONY : scheduler_interface.pb.s

# target to generate assembly for a file
scheduler_interface.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/scheduler_interface.pb.c.s
.PHONY : scheduler_interface.pb.c.s

sys_interface.pb.o: sys_interface.pb.c.o

.PHONY : sys_interface.pb.o

# target to build an object file
sys_interface.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/sys_interface.pb.c.o
.PHONY : sys_interface.pb.c.o

sys_interface.pb.i: sys_interface.pb.c.i

.PHONY : sys_interface.pb.i

# target to preprocess a source file
sys_interface.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/sys_interface.pb.c.i
.PHONY : sys_interface.pb.c.i

sys_interface.pb.s: sys_interface.pb.c.s

.PHONY : sys_interface.pb.s

# target to generate assembly for a file
sys_interface.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/sys_interface.pb.c.s
.PHONY : sys_interface.pb.c.s

task.pb.o: task.pb.c.o

.PHONY : task.pb.o

# target to build an object file
task.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/task.pb.c.o
.PHONY : task.pb.c.o

task.pb.i: task.pb.c.i

.PHONY : task.pb.i

# target to preprocess a source file
task.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/task.pb.c.i
.PHONY : task.pb.c.i

task.pb.s: task.pb.c.s

.PHONY : task.pb.s

# target to generate assembly for a file
task.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/task.pb.c.s
.PHONY : task.pb.c.s

train_info.pb.o: train_info.pb.c.o

.PHONY : train_info.pb.o

# target to build an object file
train_info.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_info.pb.c.o
.PHONY : train_info.pb.c.o

train_info.pb.i: train_info.pb.c.i

.PHONY : train_info.pb.i

# target to preprocess a source file
train_info.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_info.pb.c.i
.PHONY : train_info.pb.c.i

train_info.pb.s: train_info.pb.c.s

.PHONY : train_info.pb.s

# target to generate assembly for a file
train_info.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_info.pb.c.s
.PHONY : train_info.pb.c.s

train_interface.pb.o: train_interface.pb.c.o

.PHONY : train_interface.pb.o

# target to build an object file
train_interface.pb.c.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_interface.pb.c.o
.PHONY : train_interface.pb.c.o

train_interface.pb.i: train_interface.pb.c.i

.PHONY : train_interface.pb.i

# target to preprocess a source file
train_interface.pb.c.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_interface.pb.c.i
.PHONY : train_interface.pb.c.i

train_interface.pb.s: train_interface.pb.c.s

.PHONY : train_interface.pb.s

# target to generate assembly for a file
train_interface.pb.c.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f idl_binary_dir/CMakeFiles/idl.dir/build.make idl_binary_dir/CMakeFiles/idl.dir/train_interface.pb.c.s
.PHONY : train_interface.pb.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... idl"
	@echo "... ack.pb.o"
	@echo "... ack.pb.i"
	@echo "... ack.pb.s"
	@echo "... auto_exchange.pb.o"
	@echo "... auto_exchange.pb.i"
	@echo "... auto_exchange.pb.s"
	@echo "... auto_exchange_info.pb.o"
	@echo "... auto_exchange_info.pb.i"
	@echo "... auto_exchange_info.pb.s"
	@echo "... auto_exchange_map.pb.o"
	@echo "... auto_exchange_map.pb.i"
	@echo "... auto_exchange_map.pb.s"
	@echo "... container_interface.pb.o"
	@echo "... container_interface.pb.i"
	@echo "... container_interface.pb.s"
	@echo "... data_map.pb.o"
	@echo "... data_map.pb.i"
	@echo "... data_map.pb.s"
	@echo "... data_request.pb.o"
	@echo "... data_request.pb.i"
	@echo "... data_request.pb.s"
	@echo "... dev_hmi.pb.o"
	@echo "... dev_hmi.pb.i"
	@echo "... dev_hmi.pb.s"
	@echo "... exception.pb.o"
	@echo "... exception.pb.i"
	@echo "... exception.pb.s"
	@echo "... feeder_interface.pb.o"
	@echo "... feeder_interface.pb.i"
	@echo "... feeder_interface.pb.s"
	@echo "... scheduler_interface.pb.o"
	@echo "... scheduler_interface.pb.i"
	@echo "... scheduler_interface.pb.s"
	@echo "... sys_interface.pb.o"
	@echo "... sys_interface.pb.i"
	@echo "... sys_interface.pb.s"
	@echo "... task.pb.o"
	@echo "... task.pb.i"
	@echo "... task.pb.s"
	@echo "... train_info.pb.o"
	@echo "... train_info.pb.i"
	@echo "... train_info.pb.s"
	@echo "... train_interface.pb.o"
	@echo "... train_interface.pb.i"
	@echo "... train_interface.pb.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

