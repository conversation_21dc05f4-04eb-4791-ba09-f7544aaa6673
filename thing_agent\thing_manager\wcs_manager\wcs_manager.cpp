#include "wcs_manager.hpp"

int wcs_manager::init(zmq::context_t &ctx)
{
    func_recv = new zmq::socket_t {ctx, zmq::socket_type::sub};

    func_recv -> set(zmq::sockopt::tcp_keepalive, 1);
    func_recv -> set(zmq::sockopt::tcp_keepalive_idle, 30);
    func_recv -> set(zmq::sockopt::tcp_keepalive_cnt, 5);
    func_recv -> set(zmq::sockopt::tcp_keepalive_intvl, 6);

    func_recv->connect(setting::get_instance()->get_setting().wcs_function_ip);
    func_recv->set(zmq::sockopt::subscribe, "");

    evt_send = new zmq::socket_t {ctx, zmq::socket_type::pub};

    evt_send -> set(zmq::sockopt::tcp_keepalive, 1);
    evt_send -> set(zmq::sockopt::tcp_keepalive_idle, 30);
    evt_send -> set(zmq::sockopt::tcp_keepalive_cnt, 5);
    evt_send -> set(zmq::sockopt::tcp_keepalive_intvl, 6);

    evt_send->bind(setting::get_instance()->get_setting().wcs_event_ip);

    SPDLOG_DEBUG("wcs manager init done");
    return 0;
}

int wcs_manager::run()
{

    return 0;
}

std::unique_ptr<wcs_func_interface> wcs_manager::func_accept()
{
    zmq::message_t message;
    if (func_recv->recv(message, zmq::recv_flags::none))
    {
        auto factory = new func_factory;
        nlohmann::json root = nlohmann::json::parse(message.to_string());
        auto func = factory->create_func(root);
        delete factory;
        SPDLOG_DEBUG("recv wcs function: {}", root.dump());
        if (func)
            return func;
        return nullptr;
    }

    return nullptr;
}

int wcs_manager::evt_report(const std::string &data)
{
    std::lock_guard<std::mutex> lk(event_mtx);
    evt_send->send(zmq::buffer(data), zmq::send_flags::none);
    SPDLOG_DEBUG("repprt wcs event: {}", data);
    return 0;
}

