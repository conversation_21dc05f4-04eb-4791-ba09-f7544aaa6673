/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "auto_exchange.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(dev_axis_work_state, dev_axis_work_state, AUTO)


PB_BIND(dev_motor_work_state, dev_motor_work_state, AUTO)


PB_BIND(dev_hook_work_state, dev_hook_work_state, AUTO)


PB_BIND(auto_exchange_dev_state, auto_exchange_dev_state, 2)


PB_BIND(auto_exchange_agent_state, auto_exchange_agent_state, AUTO)


PB_BIND(auto_exchange_task_move, auto_exchange_task_move, AUTO)


PB_BIND(auto_exchange_task_grab, auto_exchange_task_grab, AUTO)


PB_BIND(auto_exchange_task_grab_move, auto_exchange_task_grab_move, AUTO)


PB_BIND(auto_exchange_task_grab_integration, auto_exchange_task_grab_integration, AUTO)


PB_BIND(auto_exchange_dev_cmd, auto_exchange_dev_cmd, AUTO)


PB_BIND(auto_exchange_mileage_info, auto_exchange_mileage_info, AUTO)


PB_BIND(auto_exchange_task, auto_exchange_task, AUTO)


PB_BIND(auto_exchange_task_state, auto_exchange_task_state, AUTO)


PB_BIND(workstation_ext_state, workstation_ext_state, AUTO)


PB_BIND(auto_exchange_ext_state_single, auto_exchange_ext_state_single, 2)


PB_BIND(auto_exchange_ext_state_multi, auto_exchange_ext_state_multi, 2)


PB_BIND(auto_exchange_ext_task_state_msg, auto_exchange_ext_task_state_msg, AUTO)


PB_BIND(auto_exchange_ext_task_msg, auto_exchange_ext_task_msg, AUTO)


PB_BIND(auto_exchange_ext_manual_task_msg, auto_exchange_ext_manual_task_msg, AUTO)












