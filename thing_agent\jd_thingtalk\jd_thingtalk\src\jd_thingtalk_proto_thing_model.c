/*Copyright (c) 2015-2050, JD All rights reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. */

/* --------------------------------------------------
 * @brief: 协议 物模型上报(thing-model post)相关实现
 *
 * @version: 1.0
 *
 * --------------------------------------------------
 */

#include <stdbool.h>
#include "jd_thingtalk_protocol.h"
#include "jd_thingtalk_proto_internal.h"

#include "jd_thingtalk_string.h"
#include "jd_thingtalk_memory.h"
#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_log.h"

#include "cJSON.h"

/**
 * @brief   物模型(thing-model)消息主题 释放物模型上报结构体 成员变量空间
 *
 * @param[in] in_post:物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_thing_model_post(JDThingTalkProtoThingModelPost_t *in_post)
{
    if (in_post != NULL) {
        if (in_post->deviceId != NULL) {
            jd_thingtalk_pal_free(in_post->deviceId);
            in_post->deviceId = NULL;
        }
        if (in_post->messageId != NULL) {
            jd_thingtalk_pal_free(in_post->messageId);
            in_post->messageId = NULL;
        }
        if (in_post->thing_model.id != NULL) {
            jd_thingtalk_pal_free(in_post->thing_model.id);
            in_post->thing_model.id = NULL;
        }
        if (in_post->thing_model.version != NULL) {
            jd_thingtalk_pal_free(in_post->thing_model.version);
            in_post->thing_model.version = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   物模型(thing-model)消息主题 释放物模型上报响应结构体 成员变量空间
 *
 * @param[in] in_res:物模型上报响应结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E 
 * @see None.
 * @note 只释放其中成员变量的内存空间
 */
int32_t jd_thingtalk_proto_free_thing_model_post_res(JDThingTalkProtoThingModelPostRes_t *in_res)
{
    if (in_res != NULL) {
        if (in_res->deviceId != NULL) {
            jd_thingtalk_pal_free(in_res->deviceId);
            in_res->deviceId = NULL;
        }
        if (in_res->messageId != NULL) {
            jd_thingtalk_pal_free(in_res->messageId);
            in_res->messageId = NULL;
        }
        if (in_res->message != NULL) {
            jd_thingtalk_pal_free(in_res->message);
            in_res->message = NULL;
        }
    }
    return JD_THINGTALK_RET_SUCCESS;
}

/**
 * @brief   物模型(thing-model)消息主题 打包物模型上报结构体
 *
 * @param[in] in_post:物模型上报结构体指针
 * @return 
 *    打包后的json串指针
 * @see None.
 * @note None.
 */
char *jd_thingtalk_proto_pack_thing_model_post(JDThingTalkProtoThingModelPost_t *in_post)
{
    if(NULL == in_post) {
        return NULL;
    }

    cJSON *root;
    char *out  = NULL;
    root = cJSON_CreateObject();
    if(NULL == root){
        goto RET;
    }

    // 添加 deviceId
    if (in_post->deviceId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, in_post->deviceId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID, "");
    }

    // 添加 timestamp
    cJSON_AddNumberToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP, in_post->timestamp);

    // 添加 messageId
    if (in_post->messageId != NULL) {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, in_post->messageId);
    } else {
        cJSON_AddStringToObject(root, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID, "");
    }

    // 添加 thing-model
    cJSON *th_model = cJSON_CreateObject();
    if (th_model == NULL) {
        cJSON_Delete(root);
        goto RET;
    }
    if (in_post->thing_model.id != NULL) {
        cJSON_AddStringToObject(th_model, "id", in_post->thing_model.id);
    } else {
        cJSON_AddStringToObject(th_model, "id", "");
    }    

    if (in_post->thing_model.version != NULL) {
        cJSON_AddStringToObject(th_model, "thing-model-version", in_post->thing_model.version);
    } else {
        cJSON_AddStringToObject(th_model, "thing-model-version", "");
    }

    cJSON_AddItemToObject(root, "thing-model", th_model);

    // 转换成字符串
    out = cJSON_Print(root);

    // 删除 root
    cJSON_Delete(root);

RET:
    return out;
}

/**
 * @brief   物模型(thing-model)消息主题 解析物模型上报响应结构体
 *
 * @param[in] in_json: 输入的json串
 * @param[in] out_res:物模型上报结构体指针
 * @return 
 *    返回值 JDThingTalkRetCode_E
 * @see None.
 * @note None
 */
int32_t jd_thingtalk_proto_parse_thing_model_post_res(char *in_json, JDThingTalkProtoThingModelPostRes_t *out_res)
{
    int ret = JD_THINGTALK_RET_FAILED;
    if (NULL == in_json || NULL == out_res) {
        goto RET;
    }
    cJSON *payload = cJSON_Parse(in_json);
    if (NULL == payload) {
        goto RET;
    }
    
    cJSON *pV = NULL;

    // 解析 deviceId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_DEV_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->deviceId == NULL) {
        out_res->deviceId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->deviceId, pV->valuestring);

    // 解析 timestamp
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_TIMESTAMP);
    if (NULL != pV) {
#ifndef JD_THINGTALK_TIMESTAMP_MS
        out_res->timestamp = (TIMESTMAP_T)pV->valueint;
#else
        out_res->timestamp = (TIMESTMAP_T)pV->valuedouble;
#endif    
    }

    // 解析 messageId
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG_ID);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    if (out_res->messageId == NULL) {
        out_res->messageId = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
    }
    jd_thingtalk_pal_strcpy(out_res->messageId, pV->valuestring);

    // 解析 code
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_CODE);
    if (NULL == pV) {
        cJSON_Delete(payload);
        goto RET;
    }
    out_res->code = pV->valueint;

    // 解析 message (节点可选)
    pV = cJSON_GetObjectItem(payload, JD_THINGTALK_PAYLOAD_NODE_NAME_MSG);
    if (NULL != pV) {
        if (out_res->message == NULL) {
            out_res->message = (char *) jd_thingtalk_pal_malloc((jd_thingtalk_pal_strlen(pV->valuestring) + 2) * sizeof(char));
        }
        jd_thingtalk_pal_strcpy(out_res->message, pV->valuestring);
    }

    // 删除 payload
    cJSON_Delete(payload);

    ret = JD_THINGTALK_RET_SUCCESS;
RET:
    return ret;
}

// end of file
