#ifndef __CFG_HPP__
#define __CFG_HPP__

#pragma pack(push, 1)
typedef struct _carriage_info
{
    uint8_t carriage_id;                    ///<车箱ID号
    uint8_t carriage_motor_run_st_Y;        ///<载货台Y轴电机运行状态
    uint16_t carriage_motor_st_code_Y;        ///<载货台Y轴电机状态码
    uint16_t carriage_motor_error_code_Y;    ///< 载货台Y轴电机错误码
    int16_t carriage_motor_pos_Y;            ///< 载货台Y轴电机位置
    uint32_t carriage_motor_encoder_val_Y;  ///< 载货台Y轴电机编码器值
    uint32_t carriage_zero_encoder_val_Y;   ///< 载货台零点的编码器值
    uint16_t carriage_motor_speed_Y;        ///<载货台Y轴电机速度值
    uint16_t carriage_motor_contorl_quantity_Y;      ///<载货台Y轴电机控制量
    uint8_t carriage_current_cmd_Y;                 ///<载货台Y轴当前命令字, 移动：0x02，较零：0x03
    uint8_t carriage_cmd_result_Y;                 ///<载货台Y轴执行结果, 0任务成功、1执行中、2任务失败
  	uint8_t belt_motor_run_st;          ///< 车厢皮带电机运行状态
    uint16_t belt_motor_st_code;        ///< 皮带电机状态码
    uint16_t belt_motor_error_code;    ///< 皮带电机错误码
	uint32_t belt_motor_encoder_val;  ///< 皮带电机编码器值
    uint8_t belt_motor_current_cmd;   ///<转动：0x04，回零：0x05，上包：0x06，下包：0x07  
    uint8_t belt_motor_cmd_ack;       ///< 皮带电机执行结果:0任务成功、1执行中、2任务失败
	uint8_t carriage_sensor_st;       ///< 载货台传感器状态，高四位皮带零点传感器状态：0：未在零点，1：皮带在零点；低四位下包传感器状态：0：未触发，1：触发
} carriage_info;

/**@struct train_state_net
* @brief 网络端传输过来的车辆状态数据
*/

typedef struct _train_state_net
{
    uint8_t train_run_st;              ///< 车辆运行状态
    int8_t train_locate_st;            ///<车辆定位状态
    uint16_t train_motor_speed;         ///< 行走电机速度
	uint32_t train_motor_pos;           ///< 行走电机位置
    uint8_t train_motor_run_st;         ///< 行走电机运行状态
	uint16_t train_motor_st_code;       ///< 行走电机状态码
    uint16_t train_motor_error_code;    ///< 行走电机错误码
	uint32_t train_motor_encoder_val;   ///< 行走电机编码器值
    uint32_t rotate_motor_encoder_val;   ///< 旋转编码器值
    uint32_t encoder_val;               ///< 编码器值
    int32_t mileage;                    ///<行驶里程, 旋转编码器值计算所得
    int32_t mileage_reference;          ///<行驶里程参考
    uint8_t proximity_sensor_st;       ///< 接近传感器状态
    uint8_t exception_carriage_id;     ///<异常车厢编号
    uint8_t exception_level;             ///<异常等级
    uint32_t exception_code;            ///<异常码
	uint8_t carriage_count;             ///< 车厢数量
    carriage_info carriages_info[30];
    	
} train_state_net;

// 小车运行参数回复结构体
typedef struct _train_run_para
{
    uint16_t timeout_ack;               ///< 超时回复时间
    uint16_t train_motor_speed;         ///< 小车行走速度
    uint16_t walk_motor_acc;
    uint16_t walk_motor_dec;
    uint16_t carriage_motor_speed;
    uint16_t carriage_motor_acc;
    uint16_t carriage_motor_dec;
    uint16_t belt_motor_speed;
    uint16_t belt_zero_speed;
    uint16_t belt_motor_acc;
    uint8_t carriage_num;
    uint16_t unpack_exceed_threshold;   ///< 小车下包超限阈值
    uint8_t unpack_sensor_switch;

}train_run_para;

#pragma pack(pop)




#endif