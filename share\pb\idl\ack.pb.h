/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_ACK_PB_H_INCLUDED
#define PB_ACK_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _ack {
    uint32_t sequence;
} ack;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define ack_init_default                         {0}
#define ack_init_zero                            {0}

/* Field tags (for use in manual encoding/decoding) */
#define ack_sequence_tag                         1

/* Struct field encoding specification for nanopb */
#define ack_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1)
#define ack_CALLBACK NULL
#define ack_DEFAULT NULL

extern const pb_msgdesc_t ack_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define ack_fields &ack_msg

/* Maximum encoded size of messages (where known) */
#define ACK_PB_H_MAX_SIZE                        ack_size
#define ack_size                                 6

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
