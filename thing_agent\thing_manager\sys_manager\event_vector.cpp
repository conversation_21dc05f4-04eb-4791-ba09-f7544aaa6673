#include "event_vector.hpp"

const event_vector::event_pattern event_vector::EVT_PATTERN_ALL = {EVENT_WILDCARD, EVENT_WILDCARD, EVENT_WILDCARD, EVENT_WILDCARD, EVENT_WILDCARD};

event_vector::event_vector(const event_handler *handlers, size_t n) 
{
	for(size_t i=0; i<n; i++)
	{
		add_handler(handlers[i]);
	}
}

event_vector::~event_vector()
{

}

bool event_vector::match(const event_exception &e, const event_pattern &pattern)
{
	if(e.which_evt_except == event_exception_evt_tag)
	{
		return (((pattern.src == EVENT_WILDCARD) || (e.evt_except.evt.src == pattern.src)) &&
				((pattern.dev == EVENT_WILDCARD) || (e.evt_except.evt.dev == pattern.dev)) &&
				((pattern.sub_dev == EVENT_WILDCARD) || (e.evt_except.evt.sub_dev == pattern.sub_dev)) &&
				((pattern.code == EVENT_WILDCARD) || (e.evt_except.evt.code == pattern.code)) &&
				((pattern.sub_code == EVENT_WILDCARD) || (e.evt_except.evt.sub_code == pattern.sub_code)));
	}
	else if(e.which_evt_except == event_exception_except_tag)
	{
		return (((pattern.src == EVENT_WILDCARD) || (e.evt_except.except.src == pattern.src)) &&
				((pattern.dev == EVENT_WILDCARD) || (e.evt_except.except.dev == pattern.dev)) &&
				((pattern.sub_dev == EVENT_WILDCARD) || (e.evt_except.except.sub_dev == pattern.dev)) &&
				((pattern.code == EVENT_WILDCARD) || (e.evt_except.except.code == pattern.code)) &&
				((pattern.sub_code == EVENT_WILDCARD) || (e.evt_except.except.sub_code == pattern.sub_code)));
	}

	return false;
}

int event_vector::process_event(const event_exception &evt_except)
{
	int match_cnt = 0;

	std::lock_guard<std::mutex> lck(list_lock);
	for(const auto h:evt_handler_list)
	{
		if(match(evt_except, h.e))
		{
			h.func(evt_except);	//sys_manager::on_exception(const event_exception &e)
			++match_cnt;
		}
	}

	return match_cnt;
}

int event_vector::add_handler(const event_handler &handler)
{
	std::lock_guard<std::mutex> lck(list_lock);

	auto it = evt_handler_list.cbegin();

	//按照优先级逆序插入，高优先级在前
	for (; it != evt_handler_list.cend(); it++)
	{
		if (it->priority > handler.priority)
		{
			evt_handler_list.insert(it, handler);
			break;
		}
	}

	if (it == evt_handler_list.cend())
	{
		evt_handler_list.push_back(handler);
	}

	return 0;
}
