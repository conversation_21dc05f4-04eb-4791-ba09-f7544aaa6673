#include "task_manager.hpp"

int task_manager::init(zmq::context_t &ctx)
{
    tasks.emplace_back(0);
    tasks.emplace_back(1);
    tasks.emplace_back(2);
    tasks.emplace_back(3);

    task_interface::get_instance()->init(ctx);

    scanner_id = setting::get_instance()->get_setting().scanner_id;

    new std::thread(&task_manager::monitor_task_state_from_scheduler_thread, this);

    return 0;
}
//device_task
int task_manager::issue_task(std::string grid_no, std::string &task_no, int type, float speed)
{
    SPDLOG_DEBUG("grid_no:{}, task_no:{}, task_type:{}", grid_no, task_no, type);

    if (type == NORMAL_SORTING)
    {
        sorting_task_msg task;
        make_task(grid_no, task_no, task, speed);
        set_task_state(task_no, ISSUED);         //下发供包台
        std::unique_lock<std::mutex> lk(task_issuer_mutex);
        task_interface::get_instance()->issue_sorting_task(task);       //阻塞接收ack，收到ack说明供包台已收到
        lk.unlock();
        set_task_state(task_no, IDLE);
    }
    else
    {
        make_simulate_task(grid_no, task_no, type);
        sys_state_interface::get_instance()->issue_simulate_sorting(simulated_task);      //模拟完成属于系统命令，只是物控协议定义在了任务中
    }

    return 0;
}

int task_manager::make_task(const std::string &grid_no, const std::string &task_no, sorting_task_msg &task, float speed)
{
    task.container = atoi(grid_no.c_str());
    if (get_task(task_no))
        task.dev_id = get_task(task_no)->feeder_id;
    else
        task.dev_id = 0;
    for(unsigned int i = 0; i < task_no.length(); ++i)
        task.task_id[i] = task_no[i];
    task.task_id[task_no.length()] = '\0';      //赋值物控下发的任务号，正确性由供包台判断
    task.platform_belt_speed = speed;
    task.containers_count = 0;

    return 0;
}

int task_manager::make_simulate_task(std::string grid_no, std::string task_no, int type)
{
    if (type == DISCLOSED_SORTING)
        simulated_task.cmd = static_cast<uint32_t>(task_cmd_task_cmd_type_HOSPICE_FINISH);
    else if (type == SIMULATIVE_SORTING)
        simulated_task.cmd = static_cast<uint32_t>(task_cmd_task_cmd_type_SIMULATE_FINISH);
    simulated_task.param = atoi(grid_no.c_str());
    for(unsigned int i = 0; i < task_no.length(); ++i)
        simulated_task.task_id[i] = task_no[i];
    simulated_task.task_id[task_no.length()] = '\0';

    return 0;
}

void task_manager::split_codes_group(std::vector<std::string> &group, const char *code)
{
	char code_temp[510];
	strcpy(code_temp, code);
	const char split_sign[] = ",";

	char *res = std::strtok(code_temp, split_sign);
	while (res != nullptr)
	{
		group.emplace_back(std::string(res));
		res = std::strtok(nullptr, split_sign);
	}
}

int task_manager::make_barcode(const sorting_task_msg &task_msg, barcode_task_info &barcode)
{
    const unsigned char* byt = task_msg.task_id;
    char* cbyt = (char*)byt;
    std::string strtaskid(cbyt, cbyt + strlen(cbyt));
    barcode.task_no = strtaskid;

    barcode.feeder_no = std::to_string(task_msg.dev_id);
    barcode.scaner_no = std::to_string(task_msg.dev_id);
    barcode.vol = task_msg.vol;
    barcode.weight = task_msg.weight;
    barcode.barcode_count = task_msg.gd_codes_count;

	split_codes_group(barcode.code_group, task_msg.gd_codes);

    return barcode.code_group.size();
}

int task_manager::get_barcode_to_request(barcode_task_info &barcode)
{
    int size = 0;

    sorting_task_msg barcode_msg;
    if(task_interface::get_instance()->get_barcode(barcode_msg))
    {
        if(barcode_msg.task_valid)
        {
            if (get_task_state(barcode_msg.dev_id) != IDLE)
                SPDLOG_WARN("task state is not IDLE, check wrong sort");

            size = make_barcode(barcode_msg, barcode);
            SPDLOG_DEBUG("recv task goods size :{}",size);
            update_task(barcode_msg.dev_id, barcode.task_no, INIT);           //获取到条码

            return 1;

        }
        
    }

	return 0;
}

std::string task_manager::make_task_state_task_no(const unsigned char *task_id)
{
    const unsigned char* byt_taskid = task_id;
    char* cbyt_taskid = (char*)byt_taskid;

    return std::string(cbyt_taskid);
}

int task_manager::make_task_state_task_type(const sorting_task_state &sorting_state, task_type &type)
{
    if (sorting_state == sorting_task_state_FINISHED || sorting_state == sorting_task_state_MOVING)
        type = NORMAL_SORTING;
    else if (sorting_state == sorting_task_state_FINISHED_MANUALLY)
        type = SIMULATIVE_SORTING;
    else if (sorting_state == sorting_task_state_FINISHED_TOHOSPICE || sorting_state == sorting_task_state_SUSPEND)
        type = DISCLOSED_SORTING;
    else
        SPDLOG_DEBUG("recv wrong task type");

    return 0;
}

int task_manager::make_task_state_task_status(const sorting_task_state &sorting_state, task_status &status)
{
    if ((sorting_state == sorting_task_state_FINISHED) || (sorting_state == sorting_task_state_FINISHED_MANUALLY) || (sorting_state == sorting_task_state_FINISHED_TOHOSPICE))
        status = FINISH;
    else if (sorting_state == sorting_task_state_MOVING || sorting_state == sorting_task_state_SUSPEND)
        status = START;
    else
        SPDLOG_DEBUG("wrong task state:{}", sorting_state);

    return 0;
}

int task_manager::make_task_state_task_report_type(const sorting_task_state &sorting_state, report_server_state &state)
{
    if (sorting_state == sorting_task_state_SUSPEND)
        state = REPORT_SUSPEND;
    else
        state = NORMAL_REPORT;

    if (setting::get_instance()->get_setting().production_mode == 1)
        SPDLOG_DEBUG("report state: {}", state);

    return 0;
}

std::string task_manager::make_task_state_grid_no(uint32_t container_id)
{
    //初始化时从database读格口 存下来，这里再做匹配?
    return std::to_string(container_id);
}

std::string task_manager::make_task_state_sort_no(uint32_t train_id, uint32_t platform_id)
{
    std::stringstream ss;
	ss << std::setfill('0') << std::setw(2) << train_id << std::setw(2) << (platform_id % 100);
    
    return ss.str();
}

std::string task_manager::make_task_state_hospice_reason(const sorting_task_state_msg &sorting_task_state)
{
    if (sorting_task_state.state == sorting_task_state_FINISHED_TOHOSPICE)
    {
        switch (sorting_task_state.exp_info)
        {
        case HOSPICE_REASON_OVERHEIGHT:
            return "超高";
            break;

        case HOSPICE_REASON_OVERLENGTH:
            return "超长";
            break;

        case HOSPICE_REASON_OVERWEIGHT:
            return "超重";
            break;

        case HOSPICE_REASON_NOREAD:
            return "noread";
            break;

        default:
            return "";
            break;
        }
    }

    return "";
}

task_manager::task_exp_reason task_manager::make_task_state_exp_reason(const sorting_task_state_msg &sorting_task_state)
{
    return static_cast<task_exp_reason> (sorting_task_state.suspend_info);
}

int task_manager::make_task_state(const sorting_task_state_msg &sorting_task_state, task_state &task_state_to_report)
{
    task_state_to_report.task_no = make_task_state_task_no(sorting_task_state.task_id);
    make_task_state_task_type(sorting_task_state.state, task_state_to_report.type);
    make_task_state_task_status(sorting_task_state.state, task_state_to_report.status);
    make_task_state_task_report_type(sorting_task_state.state, task_state_to_report.report_state);
    task_state_to_report.grid_no = make_task_state_grid_no(sorting_task_state.container);
    // task_state_to_report.sort_no = std::to_string(sorting_task_state.train_id);
    task_state_to_report.sort_no = make_task_state_sort_no(sorting_task_state.train_id, sorting_task_state.platform_id);
    task_state_to_report.exp_info = make_task_state_hospice_reason(sorting_task_state);
    task_state_to_report.exp_reason = make_task_state_exp_reason(sorting_task_state);

    return 0;
}

int task_manager::monitor_task_state_from_scheduler_thread()
{
    sorting_task_state_msg task_state_msg;
    while (true)
    {
        task_interface::get_instance()->get_sorting_task_state(task_state_msg);

        if (task_state_msg.state == sorting_task_state_MOVING || task_state_msg.state == sorting_task_state_FINISHED ||
            task_state_msg.state == sorting_task_state_FINISHED_MANUALLY || task_state_msg.state == sorting_task_state_FINISHED_TOHOSPICE ||
            task_state_msg.state == sorting_task_state_SUSPEND)
        {
            task_state task_state_to_report;
            make_task_state(task_state_msg, task_state_to_report);

            std::lock_guard<std::mutex> lock(task_state_queue_lock);
            task_state_queue.emplace(task_state_to_report);
        }
        else
            continue;
    }

    return 0;
}

int task_manager::get_task_state_to_report(task_state &task_state)
{
    std::lock_guard<std::mutex> lock(task_state_queue_lock);
    if (!task_state_queue.empty())
    {
        task_state = task_state_queue.front();
        task_state_queue.pop();

        return 1;
    }
    else
        return 0;
}

