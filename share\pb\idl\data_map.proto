syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";

message data_map
{
	enum tunnel_type
	{
		TUNNEL_TYPE_RESERVE = 0; 	//无定义
		TUNNEL_TYPE_STRAIGHT = 1; 	//直线段
		TUNNEL_TYPE_QRC =2;			//弧线段
	};

	message tunnel
	{
		uint32 tunnel_id = 1;
		float tunnel_length = 2;			
		tunnel_type type = 3;
	};

	enum container_type
	{
		ORDINARY = 0;
		SPECIAL_FUNCTION = 1;
		HOSPICE = 2;
		FEEDER = 3;
		EXCESSIVE = 4;
	};

	enum side
	{
		LEFT = 0;
		RIGHT = 1;
	};

	message container_info
	{
		uint32 id = 1;
		container_type type = 2;
		uint32 tunnel = 3;
		float position = 4; 	//格口位置
		float height = 5; 	//格口位置（高度）
		side side = 6;
		uint32 can_addr = 7;
		bool has_satration = 8;
		bool has_rfid = 9;
		bool mobile_rack = 10;
	};

	message containers_info
	{
		repeated container_info container = 1 [(nanopb).max_count = 1000];
	}
	
	message scanner_info
	{
		uint32 scanner_id = 1;
		float  scanner_position = 2;
		float  scanner_center_point = 3;
		float  scanner_width = 4;
	}
	
	
	message feeders_infomation
	{
		uint32 feeder_id = 1;
		uint32 tunnel_id = 2;
		uint32 scaner_cnt = 3;
		float  position = 4;
		float  feeder_width = 5;
		float  center_point = 6;
		float  height = 10;
		repeated scanner_info bind_scanner_if = 7 [(nanopb).max_count=4];
		uint32 bind_hospice_cont = 8;
		uint32 bind_gray_camera = 9;
	}
	
	message gray_camera_info
	{
		uint32 id = 1;
		float dev_pos = 2;			
		float dev_height = 3;
	};
	
	message feeders_info
	{
		repeated feeders_infomation feeder_info = 1 [(nanopb).max_count=6];
	}
	
	enum calib_point_type
	{
		CALIB_POINT_ORDINARY = 0;
		CALIB_POINT_ORIGIN = 1;
	};
	
	message calib_point_info
	{
		uint32 id = 1;
		calib_point_type type = 2;
		uint32 tunnel = 3;
		float position = 4; 
		float global_position =5;
	};
	
	//巷道宽度和高度
	float tunnel_straight = 1;
	float tunnel_height = 2;
	float arc_r = 3;			//换轨弧的长度



	repeated tunnel tunnels = 4 [(nanopb).max_count = 32];
	containers_info containers = 5;
	feeders_info feeders = 6; 
	float total_length = 7;
	uint32 dev_calib_point_cnt = 8;
	
	repeated calib_point_info calib_points = 9 [(nanopb).max_count = 32];
	
	repeated gray_camera_info gray_camera = 10 [(nanopb).max_count = 8];
	
	
};



message train_para
{
	uint32 train_cnt = 1;
	float  train_len = 2;	
	uint32 carriage_cnt = 3;
	uint32 carriage_len = 4;
	uint32 carriage_space = 5;
	uint32 platform_cnt = 6;
	uint32 platform_space = 7;
	uint32 platform_type = 8;
	uint32 platform_heigth_offset = 9;
	int32  train_pos_base_offset = 10;
}


message coordinate_conv_para
{
	int32 x_axis_base_offset = 1;
	int32 y_axis_base_offset = 2;
	int32 z_axis_base_offset = 3;
}