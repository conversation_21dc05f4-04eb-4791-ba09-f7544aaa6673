#include "test_case.hpp"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/async.h>



int init_log(void)
{
	std::string str_log_path = "/home/<USER>/auto_sort_high_efficient/logs/testcase/testcase";

	std::shared_ptr<spdlog::logger> dev_logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>("testcase", str_log_path, 128*1024*1024, 15);
	
	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();

	dev_logger->set_level(spdlog::level::info);
	
	dev_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出
    spdlog::set_default_logger(dev_logger);
	spdlog::flush_every(std::chrono::seconds(3));
	
	spdlog::set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%s: %!: %#] [%l] %v");

	return 0;	
}


int main()
{
    zmq::context_t context(1);
    testcase manager(context);

    init_log();
	SPDLOG_INFO("testcase init log success");

    manager.testcase_init();
	SPDLOG_INFO("testcase init success");

    manager.testcase_run();

	
    
	while (1)
	{
		this_thread::sleep_for(chrono::seconds(1));
	}

}