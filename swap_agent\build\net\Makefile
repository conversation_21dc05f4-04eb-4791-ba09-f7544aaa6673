# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/myfile/project/auto_replace_box/swap_agent/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/net/CMakeFiles/progress.marks
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 net/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/myfile/project/auto_replace_box/swap_agent/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 net/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 net/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 net/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
net/CMakeFiles/lib_net.dir/rule:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f CMakeFiles/Makefile2 net/CMakeFiles/lib_net.dir/rule
.PHONY : net/CMakeFiles/lib_net.dir/rule

# Convenience name for target.
lib_net: net/CMakeFiles/lib_net.dir/rule

.PHONY : lib_net

# fast build rule for target.
lib_net/fast:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/build
.PHONY : lib_net/fast

epoll_poller.o: epoll_poller.cpp.o

.PHONY : epoll_poller.o

# target to build an object file
epoll_poller.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/epoll_poller.cpp.o
.PHONY : epoll_poller.cpp.o

epoll_poller.i: epoll_poller.cpp.i

.PHONY : epoll_poller.i

# target to preprocess a source file
epoll_poller.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/epoll_poller.cpp.i
.PHONY : epoll_poller.cpp.i

epoll_poller.s: epoll_poller.cpp.s

.PHONY : epoll_poller.s

# target to generate assembly for a file
epoll_poller.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/epoll_poller.cpp.s
.PHONY : epoll_poller.cpp.s

tcp_socket.o: tcp_socket.cpp.o

.PHONY : tcp_socket.o

# target to build an object file
tcp_socket.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/tcp_socket.cpp.o
.PHONY : tcp_socket.cpp.o

tcp_socket.i: tcp_socket.cpp.i

.PHONY : tcp_socket.i

# target to preprocess a source file
tcp_socket.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/tcp_socket.cpp.i
.PHONY : tcp_socket.cpp.i

tcp_socket.s: tcp_socket.cpp.s

.PHONY : tcp_socket.s

# target to generate assembly for a file
tcp_socket.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/tcp_socket.cpp.s
.PHONY : tcp_socket.cpp.s

udp_socket.o: udp_socket.cpp.o

.PHONY : udp_socket.o

# target to build an object file
udp_socket.cpp.o:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/udp_socket.cpp.o
.PHONY : udp_socket.cpp.o

udp_socket.i: udp_socket.cpp.i

.PHONY : udp_socket.i

# target to preprocess a source file
udp_socket.cpp.i:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/udp_socket.cpp.i
.PHONY : udp_socket.cpp.i

udp_socket.s: udp_socket.cpp.s

.PHONY : udp_socket.s

# target to generate assembly for a file
udp_socket.cpp.s:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(MAKE) -f net/CMakeFiles/lib_net.dir/build.make net/CMakeFiles/lib_net.dir/udp_socket.cpp.s
.PHONY : udp_socket.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... lib_net"
	@echo "... epoll_poller.o"
	@echo "... epoll_poller.i"
	@echo "... epoll_poller.s"
	@echo "... tcp_socket.o"
	@echo "... tcp_socket.i"
	@echo "... tcp_socket.s"
	@echo "... udp_socket.o"
	@echo "... udp_socket.i"
	@echo "... udp_socket.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/myfile/project/auto_replace_box/swap_agent/build && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

