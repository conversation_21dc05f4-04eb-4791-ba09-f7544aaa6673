/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_TRAIN_INTERFACE_PB_H_INCLUDED
#define PB_TRAIN_INTERFACE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
/* 状态相关 */
typedef enum _train_dev_state {
    train_dev_state_DEV_RESERVE = 0,
    train_dev_state_DEV_INIT = 1,
    train_dev_state_DEV_NORMAL = 2,
    train_dev_state_DEV_ERROR = 3,
    train_dev_state_DEV_FATAL = 4,
    train_dev_state_DEV_UNKNOWN = 5
} train_dev_state;

typedef enum _train_work_state {
    train_work_state_WORK_RESERVE = 0,
    train_work_state_WORK_INIT = 1,
    train_work_state_WORK_CHECK = 2,
    train_work_state_WORK_IDLE = 3,
    train_work_state_WORK_WORK = 4,
    train_work_state_WORK_CALIB = 5,
    train_work_state_WORK_ERROR = 6,
    train_work_state_WORK_FATAL = 7
} train_work_state;

typedef enum _task_type {
    task_type_TASK_NULL = 0,
    task_type_MOVE_POS = 1,
    task_type_MOVE_SPEED = 2,
    task_type_Y_MOVE = 3,
    task_type_CARRIAGE_SHIFT_IN = 4,
    task_type_CARRIAGE_SHIFT_OUT = 5,
    /* CARRIAGE_BELT_SET_ZERO = 6; */
    task_type_MOVE_POS_CALIB = 7,
    task_type_TASK_INTE = 8,
    task_type_TASK_BELT_ROLL_BACK = 9,
    task_type_TASK_BELT_STOP = 10
} task_type;

typedef enum _task_state {
    task_state_IDLE = 0,
    task_state_INIT = 1,
    task_state_START = 2,
    task_state_RUNNING = 3,
    task_state_SUCCEED_OVER = 4,
    task_state_ERROR = 5
} task_state;

typedef enum _shift_dir {
    shift_dir_SHIFT_LEFT = 0,
    shift_dir_SHIFT_RIGHT = 1
} shift_dir;

typedef enum _shift_type {
    shift_type_TAKE_LOAD = 0,
    shift_type_UNLOAD = 1,
    shift_type_DROP_OUT = 2
} shift_type;

typedef enum _dev_cmd_tab {
    dev_cmd_tab_EMERG_STOP = 0, /* 急停 */
    dev_cmd_tab_EMERG_RESET = 1, /* 解除急停 */
    dev_cmd_tab_SLEEP = 2, /* 休眠 */
    dev_cmd_tab_WAKEUP = 3, /* 唤醒 */
    dev_cmd_tab_RESET = 4, /* 复位， */
    dev_cmd_tab_BELT_LEFT_MOVING = 5, /* 皮带左转 */
    dev_cmd_tab_BELT_RIGHT_MOVING = 6, /* 皮带右转 */
    dev_cmd_tab_BELT_STOP = 7, /* 皮带停止转动 */
    dev_cmd_tab_BELT_POS_ZERO = 8, /* 皮带零点复位 */
    dev_cmd_tab_Y_AXIS_POS_CALIB = 9, /* Y轴电机零点校准 */
    dev_cmd_tab_DEV_ENABLE = 10, /* 车组启用 */
    dev_cmd_tab_DEV_DISABLE = 11, /* 车组整体禁用 */
    dev_cmd_tab_SUB_CARRIAGE_ENABLE = 12, /* 车厢启动 */
    dev_cmd_tab_SUB_CARRIAGE_DISABLE = 13, /* 车厢禁用 */
    dev_cmd_tab_SUB_PLATFOEM_ENABLE = 14, /* 子设备启用 */
    dev_cmd_tab_SUB_PLATFORM_DISABLE = 15, /* 子设备禁用 */
    dev_cmd_tab_RESTART = 16 /* 重启， */
} dev_cmd_tab;

typedef enum _carriage_type {
    carriage_type_CARRIAGE_TYPE_RESERVE = 0, /* NULL */
    carriage_type_CARRIAGE_TYPE_HEADSTOCK = 1, /* 车头 */
    carriage_type_CARRIAGE_TYPE_CARRIAGE = 2, /* 车厢 */
    carriage_type_CARRIAGE_TYPE_REAR = 3 /* 车尾 */
} carriage_type;

typedef enum _platform_type {
    platform_type_PLATFORM_TYPE_RESERVE = 0, /* NULL */
    platform_type_PLATFORM_TYPE_BELT = 1, /* 皮带版本 */
    platform_type_PLATFORM_TYPE_DUMP = 2 /* 翻斗版本 */
} platform_type;

/* Struct definitions */
typedef struct _carriage_state {
    uint32_t carriage_id;
    train_dev_state y_axis_encoder_state; /* Y轴电机状态 正常/异常/未知 */
    int32_t y_axis_encoder_value; /* Y轴电机编码器数值 */
    uint32_t y_axis_encoder_err_no; /* Y轴电机故障码 */
    train_dev_state load_platform_state; /* 载货台电机状态 正常/异常/未知 */
    bool load_platform_belt_zero_state; /* 载货台皮带寻零标志 */
    int32_t load_platform_encoder_value; /*  */
    uint32_t load_platform_err_no; /* 载货台故障码 */
    bool carriage_load_state; /* 载货台带载状态 */
    task_type carriage_task; /* 车厢组执行任务类型 */
    task_state carriage_task_state; /* 车厢组执行任务状态 */
} carriage_state;

typedef struct _train_state {
    /* 设备基础信息 */
    uint32_t train_id; /* 车头ID */
    uint32_t carriage_cnt; /* 车厢数量 */
    train_dev_state train_state; /* 车头设备状态 */
    /* component_state train_agent_work_state = 4;			//车头运行状态 */
    uint32_t train_error_no; /* 车头故障码 */
    /* 供电系统状态 */
    bool power_electricity_state; /* 动力电标志 */
    float power_electricity_volt; /* 动力电电压 */
    float power_electricity_current; /* 动力电电流 */
    float control_electricity_volt; /* 控制电电压 */
    float control_electricity_current; /* 控制电电流 */
    /* 车头运动状态 */
    uint32_t motion_positon; /* 车辆坐标 */
    bool motion_positon_valid_flag; /* 车辆位置有效标志 */
    int32_t motion_velocity; /* 车辆速度 */
    uint32_t calib_sensor_value; /* 位置检测开关触发标志 */
    /* 车头运动电机状态 */
    train_dev_state motor_state; /* 行走电机状态 */
    uint32_t motor_encoder_value; /* 行走电机编码器数值 */
    uint32_t motor_error_no; /* 行走电机故障码 */
    bool dev_comm_ack_state; /* 通信ACK标志 */
    train_work_state work_state; /* 车头组工作状态 */
    int32_t current_mileage;
    int32_t encoder_mileage;
    pb_size_t carriage_st_count;
    carriage_state carriage_st[16]; /* 车厢工作状态 */
} train_state;

typedef struct _train_agent_state {
    /* 设备基础信息 */
    uint32_t train_id; /* 车头ID */
    uint32_t carriage_cnt; /* 车厢数量 */
    train_dev_state train_state; /* 车头设备状态 */
    component_state train_agent_work_state; /* 车头运行状态 */
    train_work_state work_state; /* 车头组工作状态 */
    uint32_t train_error_no; /* 车头故障码 */
} train_agent_state;

typedef struct _task_move {
    task_type type;
    uint32_t start;
    uint32_t target;
    uint32_t speed_limit;
} task_move;

typedef struct _task_lifting {
    task_type type;
    uint32_t target; /* Y轴目的位置 */
    uint32_t lift_speed;
    uint32_t lift_acc;
} task_lifting;

typedef struct _task_shift {
    task_type type;
    shift_dir dir;
    uint32_t speed;
    uint32_t target_pos; /* 执行下包时X轴行走位置 */
    uint32_t target_y_pos; /* 执行下包时Y轴位置，用于二次确认 */
} task_shift;

/* 皮带反转及停止的命令，反转命令下 speed 和 target_limit 有意义，停止命令下speed 和target_limit均为0 */
typedef struct _task_belt {
    task_type type;
    shift_dir dir;
    uint32_t target_pos;
    uint32_t speed; /* 反转速度 */
    uint32_t target_limit; /* 反转距离 */
} task_belt;

typedef struct _dev_cmd {
    dev_cmd_tab cmd;
    uint32_t para;
} dev_cmd;

typedef struct _task_integration {
    task_type type;
    uint32_t task_info_move_pos;
    uint32_t task_info_move_speed_limit;
    bool task_lifting_valid;
    uint32_t task_info_y_pos;
    bool has_task_info_shift;
    task_shift task_info_shift;
} task_integration;

typedef struct _mileage_info {
    int32_t current_mileage; /* 行驶里程参考 */
    int32_t encoder_mileage; /* 行驶里程 */
} mileage_info;

typedef struct _train_task {
    uint32_t sequence;
    uint32_t dev_id;
    uint32_t carriage_id; /* carriage_id为零时，全部车厢 */
    pb_size_t which_task;
    union {
        task_move move; /* 小车移动 */
        task_lifting lifting; /* Y轴移动 */
        task_shift shift; /* 皮带移动 */
        dev_cmd_tab cmd;
        task_integration inte_task;
        uint32_t hb_time_sync;
        mileage_info mil_info;
        task_belt roll_back;
    } task;
} train_task;

typedef struct _train_task_state {
    uint32_t dev_id;
    uint32_t carriage_id;
    task_type type;
    task_state state;
} train_task_state;

typedef struct _position_xyz {
    int32_t x;
    int32_t y;
    int32_t z;
} position_xyz;

typedef struct _platform_ext_state {
    uint32_t platform_id;
    int32_t location_z;
    bool online_state;
    bool load_state;
    platform_type type;
    char servo_state_no[16];
    train_dev_state servo_state; /* 行走电机状态 */
    uint32_t servo_speed; /* 行走电速度 */
    bool platform_zero_flag;
} platform_ext_state;

typedef struct _carriage_ext_state {
    uint32_t carriage_id;
    uint32_t pos;
    bool has_pos_3d;
    position_xyz pos_3d;
    carriage_type type;
    train_dev_state work_state;
    bool online_state;
    char firmware_version[16];
    bool power_flag;
    char lifting_state_no[16];
    train_dev_state lifting_state; /* 行走电机状态 */
    uint32_t lifting_speed; /* 行走电速度 */
    pb_size_t platforms_count;
    platform_ext_state platforms[4];
} carriage_ext_state;

typedef struct _train_ext_state_single {
    uint32_t train_id;
    bool online_state;
    uint32_t speed;
    uint32_t fault_state;
    char ip_addr[16];
    char firmware_version[16];
    char motor_state_no[16];
    train_dev_state motor_state; /* 行走电机状态 */
    uint32_t motor_speed; /* 行走电速度 */
    int32_t curr_mileage;
    int32_t encoder_mileage;
    pb_size_t carriages_count;
    carriage_ext_state carriages[16];
} train_ext_state_single;

typedef struct _train_ext_state_multi {
    pb_size_t trains_count;
    train_ext_state_single trains[10];
} train_ext_state_multi;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _train_dev_state_MIN train_dev_state_DEV_RESERVE
#define _train_dev_state_MAX train_dev_state_DEV_UNKNOWN
#define _train_dev_state_ARRAYSIZE ((train_dev_state)(train_dev_state_DEV_UNKNOWN+1))

#define _train_work_state_MIN train_work_state_WORK_RESERVE
#define _train_work_state_MAX train_work_state_WORK_FATAL
#define _train_work_state_ARRAYSIZE ((train_work_state)(train_work_state_WORK_FATAL+1))

#define _task_type_MIN task_type_TASK_NULL
#define _task_type_MAX task_type_TASK_BELT_STOP
#define _task_type_ARRAYSIZE ((task_type)(task_type_TASK_BELT_STOP+1))

#define _task_state_MIN task_state_IDLE
#define _task_state_MAX task_state_ERROR
#define _task_state_ARRAYSIZE ((task_state)(task_state_ERROR+1))

#define _shift_dir_MIN shift_dir_SHIFT_LEFT
#define _shift_dir_MAX shift_dir_SHIFT_RIGHT
#define _shift_dir_ARRAYSIZE ((shift_dir)(shift_dir_SHIFT_RIGHT+1))

#define _shift_type_MIN shift_type_TAKE_LOAD
#define _shift_type_MAX shift_type_DROP_OUT
#define _shift_type_ARRAYSIZE ((shift_type)(shift_type_DROP_OUT+1))

#define _dev_cmd_tab_MIN dev_cmd_tab_EMERG_STOP
#define _dev_cmd_tab_MAX dev_cmd_tab_RESTART
#define _dev_cmd_tab_ARRAYSIZE ((dev_cmd_tab)(dev_cmd_tab_RESTART+1))

#define _carriage_type_MIN carriage_type_CARRIAGE_TYPE_RESERVE
#define _carriage_type_MAX carriage_type_CARRIAGE_TYPE_REAR
#define _carriage_type_ARRAYSIZE ((carriage_type)(carriage_type_CARRIAGE_TYPE_REAR+1))

#define _platform_type_MIN platform_type_PLATFORM_TYPE_RESERVE
#define _platform_type_MAX platform_type_PLATFORM_TYPE_DUMP
#define _platform_type_ARRAYSIZE ((platform_type)(platform_type_PLATFORM_TYPE_DUMP+1))

#define carriage_state_y_axis_encoder_state_ENUMTYPE train_dev_state
#define carriage_state_load_platform_state_ENUMTYPE train_dev_state
#define carriage_state_carriage_task_ENUMTYPE task_type
#define carriage_state_carriage_task_state_ENUMTYPE task_state

#define train_state_train_state_ENUMTYPE train_dev_state
#define train_state_motor_state_ENUMTYPE train_dev_state
#define train_state_work_state_ENUMTYPE train_work_state

#define train_agent_state_train_state_ENUMTYPE train_dev_state
#define train_agent_state_train_agent_work_state_ENUMTYPE component_state
#define train_agent_state_work_state_ENUMTYPE train_work_state

#define task_move_type_ENUMTYPE task_type

#define task_lifting_type_ENUMTYPE task_type

#define task_shift_type_ENUMTYPE task_type
#define task_shift_dir_ENUMTYPE shift_dir

#define task_belt_type_ENUMTYPE task_type
#define task_belt_dir_ENUMTYPE shift_dir

#define dev_cmd_cmd_ENUMTYPE dev_cmd_tab

#define task_integration_type_ENUMTYPE task_type


#define train_task_task_cmd_ENUMTYPE dev_cmd_tab

#define train_task_state_type_ENUMTYPE task_type
#define train_task_state_state_ENUMTYPE task_state


#define platform_ext_state_type_ENUMTYPE platform_type
#define platform_ext_state_servo_state_ENUMTYPE train_dev_state

#define carriage_ext_state_type_ENUMTYPE carriage_type
#define carriage_ext_state_work_state_ENUMTYPE train_dev_state
#define carriage_ext_state_lifting_state_ENUMTYPE train_dev_state

#define train_ext_state_single_motor_state_ENUMTYPE train_dev_state



/* Initializer values for message structs */
#define carriage_state_init_default              {0, _train_dev_state_MIN, 0, 0, _train_dev_state_MIN, 0, 0, 0, 0, _task_type_MIN, _task_state_MIN}
#define train_state_init_default                 {0, 0, _train_dev_state_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, _train_dev_state_MIN, 0, 0, 0, _train_work_state_MIN, 0, 0, 0, {carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default, carriage_state_init_default}}
#define train_agent_state_init_default           {0, 0, _train_dev_state_MIN, _component_state_MIN, _train_work_state_MIN, 0}
#define task_move_init_default                   {_task_type_MIN, 0, 0, 0}
#define task_lifting_init_default                {_task_type_MIN, 0, 0, 0}
#define task_shift_init_default                  {_task_type_MIN, _shift_dir_MIN, 0, 0, 0}
#define task_belt_init_default                   {_task_type_MIN, _shift_dir_MIN, 0, 0, 0}
#define dev_cmd_init_default                     {_dev_cmd_tab_MIN, 0}
#define task_integration_init_default            {_task_type_MIN, 0, 0, 0, 0, false, task_shift_init_default}
#define mileage_info_init_default                {0, 0}
#define train_task_init_default                  {0, 0, 0, 0, {task_move_init_default}}
#define train_task_state_init_default            {0, 0, _task_type_MIN, _task_state_MIN}
#define position_xyz_init_default                {0, 0, 0}
#define platform_ext_state_init_default          {0, 0, 0, 0, _platform_type_MIN, "", _train_dev_state_MIN, 0, 0}
#define carriage_ext_state_init_default          {0, 0, false, position_xyz_init_default, _carriage_type_MIN, _train_dev_state_MIN, 0, "", 0, "", _train_dev_state_MIN, 0, 0, {platform_ext_state_init_default, platform_ext_state_init_default, platform_ext_state_init_default, platform_ext_state_init_default}}
#define train_ext_state_single_init_default      {0, 0, 0, 0, "", "", "", _train_dev_state_MIN, 0, 0, 0, 0, {carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default, carriage_ext_state_init_default}}
#define train_ext_state_multi_init_default       {0, {train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default, train_ext_state_single_init_default}}
#define carriage_state_init_zero                 {0, _train_dev_state_MIN, 0, 0, _train_dev_state_MIN, 0, 0, 0, 0, _task_type_MIN, _task_state_MIN}
#define train_state_init_zero                    {0, 0, _train_dev_state_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, _train_dev_state_MIN, 0, 0, 0, _train_work_state_MIN, 0, 0, 0, {carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero, carriage_state_init_zero}}
#define train_agent_state_init_zero              {0, 0, _train_dev_state_MIN, _component_state_MIN, _train_work_state_MIN, 0}
#define task_move_init_zero                      {_task_type_MIN, 0, 0, 0}
#define task_lifting_init_zero                   {_task_type_MIN, 0, 0, 0}
#define task_shift_init_zero                     {_task_type_MIN, _shift_dir_MIN, 0, 0, 0}
#define task_belt_init_zero                      {_task_type_MIN, _shift_dir_MIN, 0, 0, 0}
#define dev_cmd_init_zero                        {_dev_cmd_tab_MIN, 0}
#define task_integration_init_zero               {_task_type_MIN, 0, 0, 0, 0, false, task_shift_init_zero}
#define mileage_info_init_zero                   {0, 0}
#define train_task_init_zero                     {0, 0, 0, 0, {task_move_init_zero}}
#define train_task_state_init_zero               {0, 0, _task_type_MIN, _task_state_MIN}
#define position_xyz_init_zero                   {0, 0, 0}
#define platform_ext_state_init_zero             {0, 0, 0, 0, _platform_type_MIN, "", _train_dev_state_MIN, 0, 0}
#define carriage_ext_state_init_zero             {0, 0, false, position_xyz_init_zero, _carriage_type_MIN, _train_dev_state_MIN, 0, "", 0, "", _train_dev_state_MIN, 0, 0, {platform_ext_state_init_zero, platform_ext_state_init_zero, platform_ext_state_init_zero, platform_ext_state_init_zero}}
#define train_ext_state_single_init_zero         {0, 0, 0, 0, "", "", "", _train_dev_state_MIN, 0, 0, 0, 0, {carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero, carriage_ext_state_init_zero}}
#define train_ext_state_multi_init_zero          {0, {train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero, train_ext_state_single_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define carriage_state_carriage_id_tag           1
#define carriage_state_y_axis_encoder_state_tag  2
#define carriage_state_y_axis_encoder_value_tag  3
#define carriage_state_y_axis_encoder_err_no_tag 4
#define carriage_state_load_platform_state_tag   5
#define carriage_state_load_platform_belt_zero_state_tag 6
#define carriage_state_load_platform_encoder_value_tag 7
#define carriage_state_load_platform_err_no_tag  8
#define carriage_state_carriage_load_state_tag   9
#define carriage_state_carriage_task_tag         10
#define carriage_state_carriage_task_state_tag   11
#define train_state_train_id_tag                 1
#define train_state_carriage_cnt_tag             2
#define train_state_train_state_tag              3
#define train_state_train_error_no_tag           5
#define train_state_power_electricity_state_tag  6
#define train_state_power_electricity_volt_tag   7
#define train_state_power_electricity_current_tag 8
#define train_state_control_electricity_volt_tag 9
#define train_state_control_electricity_current_tag 10
#define train_state_motion_positon_tag           11
#define train_state_motion_positon_valid_flag_tag 12
#define train_state_motion_velocity_tag          14
#define train_state_calib_sensor_value_tag       15
#define train_state_motor_state_tag              16
#define train_state_motor_encoder_value_tag      17
#define train_state_motor_error_no_tag           18
#define train_state_dev_comm_ack_state_tag       19
#define train_state_work_state_tag               20
#define train_state_current_mileage_tag          21
#define train_state_encoder_mileage_tag          22
#define train_state_carriage_st_tag              23
#define train_agent_state_train_id_tag           1
#define train_agent_state_carriage_cnt_tag       2
#define train_agent_state_train_state_tag        3
#define train_agent_state_train_agent_work_state_tag 4
#define train_agent_state_work_state_tag         5
#define train_agent_state_train_error_no_tag     6
#define task_move_type_tag                       1
#define task_move_start_tag                      2
#define task_move_target_tag                     3
#define task_move_speed_limit_tag                4
#define task_lifting_type_tag                    1
#define task_lifting_target_tag                  2
#define task_lifting_lift_speed_tag              3
#define task_lifting_lift_acc_tag                4
#define task_shift_type_tag                      1
#define task_shift_dir_tag                       2
#define task_shift_speed_tag                     3
#define task_shift_target_pos_tag                4
#define task_shift_target_y_pos_tag              5
#define task_belt_type_tag                       1
#define task_belt_dir_tag                        2
#define task_belt_target_pos_tag                 3
#define task_belt_speed_tag                      4
#define task_belt_target_limit_tag               5
#define dev_cmd_cmd_tag                          1
#define dev_cmd_para_tag                         2
#define task_integration_type_tag                1
#define task_integration_task_info_move_pos_tag  2
#define task_integration_task_info_move_speed_limit_tag 3
#define task_integration_task_lifting_valid_tag  4
#define task_integration_task_info_y_pos_tag     5
#define task_integration_task_info_shift_tag     6
#define mileage_info_current_mileage_tag         1
#define mileage_info_encoder_mileage_tag         2
#define train_task_sequence_tag                  1
#define train_task_dev_id_tag                    2
#define train_task_carriage_id_tag               3
#define train_task_move_tag                      4
#define train_task_lifting_tag                   5
#define train_task_shift_tag                     6
#define train_task_cmd_tag                       7
#define train_task_inte_task_tag                 8
#define train_task_hb_time_sync_tag              9
#define train_task_mil_info_tag                  10
#define train_task_roll_back_tag                 11
#define train_task_state_dev_id_tag              1
#define train_task_state_carriage_id_tag         2
#define train_task_state_type_tag                3
#define train_task_state_state_tag               4
#define position_xyz_x_tag                       1
#define position_xyz_y_tag                       2
#define position_xyz_z_tag                       3
#define platform_ext_state_platform_id_tag       1
#define platform_ext_state_location_z_tag        2
#define platform_ext_state_online_state_tag      3
#define platform_ext_state_load_state_tag        4
#define platform_ext_state_type_tag              5
#define platform_ext_state_servo_state_no_tag    6
#define platform_ext_state_servo_state_tag       7
#define platform_ext_state_servo_speed_tag       8
#define platform_ext_state_platform_zero_flag_tag 9
#define carriage_ext_state_carriage_id_tag       1
#define carriage_ext_state_pos_tag               2
#define carriage_ext_state_pos_3d_tag            3
#define carriage_ext_state_type_tag              4
#define carriage_ext_state_work_state_tag        5
#define carriage_ext_state_online_state_tag      6
#define carriage_ext_state_firmware_version_tag  7
#define carriage_ext_state_power_flag_tag        8
#define carriage_ext_state_lifting_state_no_tag  9
#define carriage_ext_state_lifting_state_tag     10
#define carriage_ext_state_lifting_speed_tag     11
#define carriage_ext_state_platforms_tag         12
#define train_ext_state_single_train_id_tag      1
#define train_ext_state_single_online_state_tag  2
#define train_ext_state_single_speed_tag         3
#define train_ext_state_single_fault_state_tag   4
#define train_ext_state_single_ip_addr_tag       5
#define train_ext_state_single_firmware_version_tag 6
#define train_ext_state_single_motor_state_no_tag 7
#define train_ext_state_single_motor_state_tag   8
#define train_ext_state_single_motor_speed_tag   9
#define train_ext_state_single_curr_mileage_tag  10
#define train_ext_state_single_encoder_mileage_tag 11
#define train_ext_state_single_carriages_tag     12
#define train_ext_state_multi_trains_tag         1

/* Struct field encoding specification for nanopb */
#define carriage_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_id,       1) \
X(a, STATIC,   SINGULAR, UENUM,    y_axis_encoder_state,   2) \
X(a, STATIC,   SINGULAR, INT32,    y_axis_encoder_value,   3) \
X(a, STATIC,   SINGULAR, UINT32,   y_axis_encoder_err_no,   4) \
X(a, STATIC,   SINGULAR, UENUM,    load_platform_state,   5) \
X(a, STATIC,   SINGULAR, BOOL,     load_platform_belt_zero_state,   6) \
X(a, STATIC,   SINGULAR, INT32,    load_platform_encoder_value,   7) \
X(a, STATIC,   SINGULAR, UINT32,   load_platform_err_no,   8) \
X(a, STATIC,   SINGULAR, BOOL,     carriage_load_state,   9) \
X(a, STATIC,   SINGULAR, UENUM,    carriage_task,    10) \
X(a, STATIC,   SINGULAR, UENUM,    carriage_task_state,  11)
#define carriage_state_CALLBACK NULL
#define carriage_state_DEFAULT NULL

#define train_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          1) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_cnt,      2) \
X(a, STATIC,   SINGULAR, UENUM,    train_state,       3) \
X(a, STATIC,   SINGULAR, UINT32,   train_error_no,    5) \
X(a, STATIC,   SINGULAR, BOOL,     power_electricity_state,   6) \
X(a, STATIC,   SINGULAR, FLOAT,    power_electricity_volt,   7) \
X(a, STATIC,   SINGULAR, FLOAT,    power_electricity_current,   8) \
X(a, STATIC,   SINGULAR, FLOAT,    control_electricity_volt,   9) \
X(a, STATIC,   SINGULAR, FLOAT,    control_electricity_current,  10) \
X(a, STATIC,   SINGULAR, UINT32,   motion_positon,   11) \
X(a, STATIC,   SINGULAR, BOOL,     motion_positon_valid_flag,  12) \
X(a, STATIC,   SINGULAR, INT32,    motion_velocity,  14) \
X(a, STATIC,   SINGULAR, UINT32,   calib_sensor_value,  15) \
X(a, STATIC,   SINGULAR, UENUM,    motor_state,      16) \
X(a, STATIC,   SINGULAR, UINT32,   motor_encoder_value,  17) \
X(a, STATIC,   SINGULAR, UINT32,   motor_error_no,   18) \
X(a, STATIC,   SINGULAR, BOOL,     dev_comm_ack_state,  19) \
X(a, STATIC,   SINGULAR, UENUM,    work_state,       20) \
X(a, STATIC,   SINGULAR, INT32,    current_mileage,  21) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,  22) \
X(a, STATIC,   REPEATED, MESSAGE,  carriage_st,      23)
#define train_state_CALLBACK NULL
#define train_state_DEFAULT NULL
#define train_state_carriage_st_MSGTYPE carriage_state

#define train_agent_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          1) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_cnt,      2) \
X(a, STATIC,   SINGULAR, UENUM,    train_state,       3) \
X(a, STATIC,   SINGULAR, UENUM,    train_agent_work_state,   4) \
X(a, STATIC,   SINGULAR, UENUM,    work_state,        5) \
X(a, STATIC,   SINGULAR, UINT32,   train_error_no,    6)
#define train_agent_state_CALLBACK NULL
#define train_agent_state_DEFAULT NULL

#define task_move_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   start,             2) \
X(a, STATIC,   SINGULAR, UINT32,   target,            3) \
X(a, STATIC,   SINGULAR, UINT32,   speed_limit,       4)
#define task_move_CALLBACK NULL
#define task_move_DEFAULT NULL

#define task_lifting_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   target,            2) \
X(a, STATIC,   SINGULAR, UINT32,   lift_speed,        3) \
X(a, STATIC,   SINGULAR, UINT32,   lift_acc,          4)
#define task_lifting_CALLBACK NULL
#define task_lifting_DEFAULT NULL

#define task_shift_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UENUM,    dir,               2) \
X(a, STATIC,   SINGULAR, UINT32,   speed,             3) \
X(a, STATIC,   SINGULAR, UINT32,   target_pos,        4) \
X(a, STATIC,   SINGULAR, UINT32,   target_y_pos,      5)
#define task_shift_CALLBACK NULL
#define task_shift_DEFAULT NULL

#define task_belt_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UENUM,    dir,               2) \
X(a, STATIC,   SINGULAR, UINT32,   target_pos,        3) \
X(a, STATIC,   SINGULAR, UINT32,   speed,             4) \
X(a, STATIC,   SINGULAR, UINT32,   target_limit,      5)
#define task_belt_CALLBACK NULL
#define task_belt_DEFAULT NULL

#define dev_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    cmd,               1) \
X(a, STATIC,   SINGULAR, UINT32,   para,              2)
#define dev_cmd_CALLBACK NULL
#define dev_cmd_DEFAULT NULL

#define task_integration_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   task_info_move_pos,   2) \
X(a, STATIC,   SINGULAR, UINT32,   task_info_move_speed_limit,   3) \
X(a, STATIC,   SINGULAR, BOOL,     task_lifting_valid,   4) \
X(a, STATIC,   SINGULAR, UINT32,   task_info_y_pos,   5) \
X(a, STATIC,   OPTIONAL, MESSAGE,  task_info_shift,   6)
#define task_integration_CALLBACK NULL
#define task_integration_DEFAULT NULL
#define task_integration_task_info_shift_MSGTYPE task_shift

#define mileage_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    current_mileage,   1) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,   2)
#define mileage_info_CALLBACK NULL
#define mileage_info_DEFAULT NULL

#define train_task_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            2) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_id,       3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,move,task.move),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,lifting,task.lifting),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,shift,task.shift),   6) \
X(a, STATIC,   ONEOF,    UENUM,    (task,cmd,task.cmd),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,inte_task,task.inte_task),   8) \
X(a, STATIC,   ONEOF,    UINT32,   (task,hb_time_sync,task.hb_time_sync),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,mil_info,task.mil_info),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,roll_back,task.roll_back),  11)
#define train_task_CALLBACK NULL
#define train_task_DEFAULT NULL
#define train_task_task_move_MSGTYPE task_move
#define train_task_task_lifting_MSGTYPE task_lifting
#define train_task_task_shift_MSGTYPE task_shift
#define train_task_task_inte_task_MSGTYPE task_integration
#define train_task_task_mil_info_MSGTYPE mileage_info
#define train_task_task_roll_back_MSGTYPE task_belt

#define train_task_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_id,       2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3) \
X(a, STATIC,   SINGULAR, UENUM,    state,             4)
#define train_task_state_CALLBACK NULL
#define train_task_state_DEFAULT NULL

#define position_xyz_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    x,                 1) \
X(a, STATIC,   SINGULAR, INT32,    y,                 2) \
X(a, STATIC,   SINGULAR, INT32,    z,                 3)
#define position_xyz_CALLBACK NULL
#define position_xyz_DEFAULT NULL

#define platform_ext_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   platform_id,       1) \
X(a, STATIC,   SINGULAR, INT32,    location_z,        2) \
X(a, STATIC,   SINGULAR, BOOL,     online_state,      3) \
X(a, STATIC,   SINGULAR, BOOL,     load_state,        4) \
X(a, STATIC,   SINGULAR, UENUM,    type,              5) \
X(a, STATIC,   SINGULAR, STRING,   servo_state_no,    6) \
X(a, STATIC,   SINGULAR, UENUM,    servo_state,       7) \
X(a, STATIC,   SINGULAR, UINT32,   servo_speed,       8) \
X(a, STATIC,   SINGULAR, BOOL,     platform_zero_flag,   9)
#define platform_ext_state_CALLBACK NULL
#define platform_ext_state_DEFAULT NULL

#define carriage_ext_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   carriage_id,       1) \
X(a, STATIC,   SINGULAR, UINT32,   pos,               2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pos_3d,            3) \
X(a, STATIC,   SINGULAR, UENUM,    type,              4) \
X(a, STATIC,   SINGULAR, UENUM,    work_state,        5) \
X(a, STATIC,   SINGULAR, BOOL,     online_state,      6) \
X(a, STATIC,   SINGULAR, STRING,   firmware_version,   7) \
X(a, STATIC,   SINGULAR, BOOL,     power_flag,        8) \
X(a, STATIC,   SINGULAR, STRING,   lifting_state_no,   9) \
X(a, STATIC,   SINGULAR, UENUM,    lifting_state,    10) \
X(a, STATIC,   SINGULAR, UINT32,   lifting_speed,    11) \
X(a, STATIC,   REPEATED, MESSAGE,  platforms,        12)
#define carriage_ext_state_CALLBACK NULL
#define carriage_ext_state_DEFAULT NULL
#define carriage_ext_state_pos_3d_MSGTYPE position_xyz
#define carriage_ext_state_platforms_MSGTYPE platform_ext_state

#define train_ext_state_single_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   train_id,          1) \
X(a, STATIC,   SINGULAR, BOOL,     online_state,      2) \
X(a, STATIC,   SINGULAR, UINT32,   speed,             3) \
X(a, STATIC,   SINGULAR, UINT32,   fault_state,       4) \
X(a, STATIC,   SINGULAR, STRING,   ip_addr,           5) \
X(a, STATIC,   SINGULAR, STRING,   firmware_version,   6) \
X(a, STATIC,   SINGULAR, STRING,   motor_state_no,    7) \
X(a, STATIC,   SINGULAR, UENUM,    motor_state,       8) \
X(a, STATIC,   SINGULAR, UINT32,   motor_speed,       9) \
X(a, STATIC,   SINGULAR, INT32,    curr_mileage,     10) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,  11) \
X(a, STATIC,   REPEATED, MESSAGE,  carriages,        12)
#define train_ext_state_single_CALLBACK NULL
#define train_ext_state_single_DEFAULT NULL
#define train_ext_state_single_carriages_MSGTYPE carriage_ext_state

#define train_ext_state_multi_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  trains,            1)
#define train_ext_state_multi_CALLBACK NULL
#define train_ext_state_multi_DEFAULT NULL
#define train_ext_state_multi_trains_MSGTYPE train_ext_state_single

extern const pb_msgdesc_t carriage_state_msg;
extern const pb_msgdesc_t train_state_msg;
extern const pb_msgdesc_t train_agent_state_msg;
extern const pb_msgdesc_t task_move_msg;
extern const pb_msgdesc_t task_lifting_msg;
extern const pb_msgdesc_t task_shift_msg;
extern const pb_msgdesc_t task_belt_msg;
extern const pb_msgdesc_t dev_cmd_msg;
extern const pb_msgdesc_t task_integration_msg;
extern const pb_msgdesc_t mileage_info_msg;
extern const pb_msgdesc_t train_task_msg;
extern const pb_msgdesc_t train_task_state_msg;
extern const pb_msgdesc_t position_xyz_msg;
extern const pb_msgdesc_t platform_ext_state_msg;
extern const pb_msgdesc_t carriage_ext_state_msg;
extern const pb_msgdesc_t train_ext_state_single_msg;
extern const pb_msgdesc_t train_ext_state_multi_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define carriage_state_fields &carriage_state_msg
#define train_state_fields &train_state_msg
#define train_agent_state_fields &train_agent_state_msg
#define task_move_fields &task_move_msg
#define task_lifting_fields &task_lifting_msg
#define task_shift_fields &task_shift_msg
#define task_belt_fields &task_belt_msg
#define dev_cmd_fields &dev_cmd_msg
#define task_integration_fields &task_integration_msg
#define mileage_info_fields &mileage_info_msg
#define train_task_fields &train_task_msg
#define train_task_state_fields &train_task_state_msg
#define position_xyz_fields &position_xyz_msg
#define platform_ext_state_fields &platform_ext_state_msg
#define carriage_ext_state_fields &carriage_ext_state_msg
#define train_ext_state_single_fields &train_ext_state_single_msg
#define train_ext_state_multi_fields &train_ext_state_multi_msg

/* Maximum encoded size of messages (where known) */
#define TRAIN_INTERFACE_PB_H_MAX_SIZE            train_ext_state_multi_size
#define carriage_ext_state_size                  305
#define carriage_state_size                      52
#define dev_cmd_size                             8
#define mileage_info_size                        22
#define platform_ext_state_size                  50
#define position_xyz_size                        33
#define task_belt_size                           22
#define task_integration_size                    46
#define task_lifting_size                        20
#define task_move_size                           20
#define task_shift_size                          22
#define train_agent_state_size                   24
#define train_ext_state_multi_size               50320
#define train_ext_state_single_size              5029
#define train_state_size                         994
#define train_task_size                          66
#define train_task_state_size                    16

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
