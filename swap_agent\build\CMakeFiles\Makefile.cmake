# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/myfile/project/auto_replace_box/share/pb/idl/CMakeLists.txt"
  "/home/<USER>/myfile/project/auto_replace_box/share/pb/nanopb/CMakeLists.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.5.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.5.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.5.1/CMakeSystem.cmake"
  "../fsm_manager/CMakeLists.txt"
  "../net/CMakeLists.txt"
  "../protocol/CMakeLists.txt"
  "../scheduler_msg/CMakeLists.txt"
  "../swap_manage/CMakeLists.txt"
  "../threadpool/CMakeLists.txt"
  "/usr/share/cmake-3.5/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "nanopb_binary_dir/CMakeFiles/CMakeDirectoryInformation.cmake"
  "idl_binary_dir/CMakeFiles/CMakeDirectoryInformation.cmake"
  "threadpool/CMakeFiles/CMakeDirectoryInformation.cmake"
  "net/CMakeFiles/CMakeDirectoryInformation.cmake"
  "protocol/CMakeFiles/CMakeDirectoryInformation.cmake"
  "swap_manage/CMakeFiles/CMakeDirectoryInformation.cmake"
  "scheduler_msg/CMakeFiles/CMakeDirectoryInformation.cmake"
  "fsm_manager/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/autoswap_agent.dir/DependInfo.cmake"
  "nanopb_binary_dir/CMakeFiles/nanopb.dir/DependInfo.cmake"
  "idl_binary_dir/CMakeFiles/idl.dir/DependInfo.cmake"
  "threadpool/CMakeFiles/lib_threadpool.dir/DependInfo.cmake"
  "net/CMakeFiles/lib_net.dir/DependInfo.cmake"
  "protocol/CMakeFiles/lib_protocol.dir/DependInfo.cmake"
  "swap_manage/CMakeFiles/lib_swap_manage.dir/DependInfo.cmake"
  "scheduler_msg/CMakeFiles/lib_msg.dir/DependInfo.cmake"
  "fsm_manager/CMakeFiles/lib_fsm_manager.dir/DependInfo.cmake"
  )
