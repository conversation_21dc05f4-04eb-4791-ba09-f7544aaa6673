﻿



#ifndef __THREADPOOL_CONDITION_HPP__
#define __THREADPOOL_CONDITION_HPP__

#include "thp_mutex.hpp"

#include <memory>

/*!
* @brief 条件变量类 \class
*/
class condition {
public:

	/*!
	 * @brief 构造函数
	*/
	condition();

	/*!
	* @brief 拷贝构造函数，设为delete，阻止拷贝
	*/
	condition(const condition&) = delete;

	/*!
	* @brief 复制操作，设为delete，阻止赋值
	* @return condition& 
	*/
	condition& operator=(const condition&) = delete;

	/*!
	* @brief 析构函数
	*/
	~condition();

	/*!
	* @brief 等候条件变量为真
	* @param [in] mutex_ptr 用于锁住条件变量的互斥量指针
	* @return 成功与否
	*/
	bool wait(thp_mutex* mutex_ptr);

	/*!
	* @brief 定时等待条件变量为真
 	* @param [in] mutex_ptr 用于锁住条件变量的互斥量指针
	* @param [in] seconds 等候时间长度，以秒为单位
	* @return 成功与否
 	*/
	bool timed_wait(thp_mutex* mutex_ptr, long seconds);

	/*!
	* @brief 条件为真，唤醒一个等候条件变量变为真的线程
	* @return 成功与否
	*/
	bool signal();

private:

	/// 内部条件变量id
	pthread_cond_t m_cond;

};



#endif
