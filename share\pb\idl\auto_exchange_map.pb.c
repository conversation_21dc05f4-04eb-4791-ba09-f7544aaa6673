/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.8 */

#include "auto_exchange_map.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(auto_exchange_map, auto_exchange_map, 2)


PB_BIND(auto_exchange_map_pack_station_dev_info, auto_exchange_map_pack_station_dev_info, AUTO)


PB_BIND(auto_exchange_map_pack_station_map, auto_exchange_map_pack_station_map, 2)


PB_BIND(auto_exchange_map_auto_exchange_map_para, auto_exchange_map_auto_exchange_map_para, AUTO)


PB_BIND(auto_exchange_conv_para, auto_exchange_conv_para, AUTO)





