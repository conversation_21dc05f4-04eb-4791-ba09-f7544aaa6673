#include "jd_thingtalk_stdint.h"
#include "jd_thingtalk_time.h"

#ifdef __LINUX_PAL__
#include <stddef.h>
#include <time.h>
#include <sys/time.h>
#endif


/**
 * get time
 *
 * @out param: time
 * @return: UTC Second
 *
 */
uint32_t jd_thingtalk_pal_time(jd_thingtalk_time_t *jl_time)
{
#ifdef __LINUX_PAL__
    time_t timep;
    struct tm *p;
    uint32_t ret = (uint32_t) time(&timep);
    if (jl_time != NULL) {
        p = gmtime(&timep);
        jl_time->year      = p->tm_year;
        jl_time->month     = p->tm_mon;
        jl_time->week      = p->tm_wday;
        jl_time->day       = p->tm_mday;
        jl_time->hour      = p->tm_hour;
        jl_time->minute    = p->tm_min;
        jl_time->second    = p->tm_sec;
        
        jl_time->timestamp = ret;;
    }

    return ret;
#else
    return 0;
#endif
}

/**
* @brief get timestamp(ms)
* @param none
* @return sucess or failed
*/
int32_t jd_thingtalk_pal_time_get_timestamp(jd_thingtalk_time_stamp_t *time_stamp)
{
#ifdef __LINUX_PAL__
    struct timeval now;
    gettimeofday(&now, NULL);
    if(time_stamp != NULL)
    {
        time_stamp->second = (uint32_t) now.tv_sec;
        time_stamp->ms = (uint32_t) (now.tv_usec/1000);
        return 0;
    }
    else {
        return -1;
    }
#else
    return -1;
#endif
}

/**
 * get os ticks
 *
 * @out param: none
 * @return: sys time ticks ms since sys start
*/
uint32_t jd_thingtalk_pal_time_clock_ms(void)
{
#ifdef __LINUX_PAL__
        // 不同平台 clock() 返回的值有区别，请注意
        return (uint32_t) clock();
#else
        return 0;
#endif
}



