#include <string>
#include <iostream>
#include <map>
#include <memory>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

// #include "share/pb/idl/sys_state.pb.h"
#include "../task_manager/task_manager.hpp"
// #include "share/pb/idl/container_cmd.pb.h"
// #include "share/pb/idl/plane_slot.pb.h"
#include "share/nlohmann_json/json.hpp"
#include "../../setting/setting.hpp"

#include "../device_manager/device_manager.hpp"


#define FUNC_CONTAINER_STATE_CONTROL    ("container_state_control")     //wcs的格口状态控制
#define EVT_SYS_STATE                   ("sys_state")
#define EVT_GET_TASK_STATE              ("get_task_state")
#define EVT_CONTAINER_SEAL_STATE        ("container_seal_state")

//function和event类定义


//c++ 11不支持make_unique，手动实现一个
namespace func
{
template<typename _Tp, typename... _Args>
std::unique_ptr<_Tp> make_unique(_Args&&... __args)
{ return std::unique_ptr<_Tp>(new _Tp(std::forward<_Args>(__args)...)); }
}
//对应wcs格口状态枚举
enum wcs_container_state
{
    ENABLED = 1,
    DISABLED = 2,
    FULL = 3,
    BLOCKED,
    BUNDLED     //集包
};

struct wcs_container_state_control
{
    uint32_t container_id;
    wcs_container_state state;
};

class wcs_func_interface
{
public:
    wcs_func_interface() {};
    virtual ~wcs_func_interface() {};
    //std::string func_key;
    //std::map<std::string, std::string> param_pair;
    virtual int issue() = 0;
    void print(const nlohmann::json &root)
    {
        SPDLOG_DEBUG("{}", root.dump());
    }
};

class wcs_func_container_control: public wcs_func_interface
{
public:
    wcs_func_container_control(const nlohmann::json &root)
        : func(new wcs_container_state_control {root["container_id"], root["state"]})
    {
    }
    ~wcs_func_container_control() {delete func;}
    int issue() override
    {
        if (func->state == BUNDLED)
        {
            device_manager::get_instance()->issue_container_contain(func->container_id);
            return 1;
        }
        return 0;
    }
private:
    wcs_container_state_control *func;
};

class func_factory
{
public:
    std::unique_ptr<wcs_func_interface> create_func(const nlohmann::json &root)
    {
        if (!root.count("func_key"))
            return nullptr;

        if (root["func_key"] == FUNC_CONTAINER_STATE_CONTROL)
        {
            return func::make_unique<wcs_func_container_control> (root);
        }
        //else if ()

        return nullptr;
    }
};

//上报的事件类
class wcs_evt_interface
{
public:
    wcs_evt_interface() {}
    virtual ~wcs_evt_interface() {}
//    virtual const std::string &data() = 0;
    virtual const std::string &get_key()
    {
        return evt_key;
    }
    virtual const std::string data()       //todo::发送数据后删除对象，使用引用是否有问题
    {
        return root.dump();
    }
protected:
    std::string evt_key;
    nlohmann::json root;
};

class wcs_evt_sys_state: public wcs_evt_interface
{
public:
    wcs_evt_sys_state(const sys_mode_state &state)
    {
        evt_key = std::string(EVT_SYS_STATE);
        root["event_key"] = evt_key;
        root["state"] = state.state;
        root["id"] = setting::get_instance()->get_setting().device_id;
        root["mode"] = state.mode;
        root["err"] = state.err;
        root["has_dev_st"] = state.has_dev_st;
        root["dev_st"]["emerg_pressed"] = state.dev_st.emerg_pressed;
        root["dev_st"]["safty_door_open"] = state.dev_st.safty_door_open;
    }
private:

};

class wcs_evt_task_state: public wcs_evt_interface
{
public:
    wcs_evt_task_state(const task_manager::task_state &state)
    {
        evt_key = std::string(EVT_GET_TASK_STATE);
        root["event_key"] = evt_key;
        root["sort_id"] = state.sort_no;
        root["task_no"] = state.task_no;
        root["grid_no"] = state.grid_no;
        root["status"] = state.status;
        root["exp_info"] = state.exp_info;
        root["exp_reason"] = state.exp_reason;
        root["type"] = state.type;
        root["report_state"] = state.report_state;
    }
private:

};

class wcs_container_seal_state: public wcs_evt_interface
{
public:
    wcs_container_seal_state(const container_seal_state_single &state)
    {
        evt_key = std::string(EVT_CONTAINER_SEAL_STATE);
        root["event_key"] = evt_key;
        root["container_id"] = state.container_id;
        root["slot_state"] = state_ABSENT;         //空满状态，此表示不解析
        root["seal_state"] = state.seal_state;
    }
    wcs_container_seal_state(const slot_state &state)
    {
        evt_key = std::string(EVT_CONTAINER_SEAL_STATE);
        root["event_key"] = evt_key;
        root["container_id"] = state.id;
        root["slot_state"] = state.st;         //空满
        root["seal_state"] = container_seal_state_UNKNOWN;      //不解析
    }
private:

};

