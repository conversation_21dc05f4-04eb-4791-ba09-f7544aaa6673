#if 0
//PB文件说明

typedef enum _task_type {
    task_type_TASK_NULL = 0,
    task_type_MOVE_POS = 1,
    task_type_MOVE_SPEED = 2,
    task_type_Y_MOVE = 3,
    task_type_CARRIAGE_SHIFT_IN = 4,
    task_type_CARRIAGE_SHIFT_OUT = 5,
    task_type_CARRIAGE_BELT_SET_ZERO = 6,
    task_type_MOVE_POS_CALIB = 7,
    task_type_TASK_INTE = 8
} task_type;


task_move move;
typedef enum _task_type {
    task_type_MOVE_SPEED = 2,   //定位指令, 速度控制
    task_type_MOVE_POS_CALIB = 7,   //校准， 非定位指令
} task_type;


task_lifting lifting; /* Y轴移动 */
typedef enum _task_type {
    task_type_Y_MOVE = 3,
} task_type;


task_shift shift; /* 皮带移动 */
typedef enum _task_type {
    task_type_CARRIAGE_SHIFT_IN = 4,        //上包
    task_type_CARRIAGE_SHIFT_OUT = 5,       //下包
    task_type_CARRIAGE_BELT_SET_ZERO = 6,   //皮带回零
} task_type;


//集成任务：行走+Y轴移动；行走+上下包
typedef struct _task_integration {
    task_type type;
    uint32_t task_info_move_pos;
    uint32_t task_info_move_speed_limit;
    bool task_lifting_valid;
    uint32_t task_info_y_pos;
    bool has_task_info_shift;
    task_shift task_info_shift;
} task_integration;


//车头组工作状态
typedef enum _train_work_state {
    train_work_state_WORK_RESERVE = 0,
    train_work_state_WORK_INIT = 1,
    train_work_state_WORK_CHECK = 2,    //检查
    train_work_state_WORK_IDLE = 3,     //空闲
    train_work_state_WORK_WORK = 4,
    train_work_state_WORK_CALIB = 5,    //校准
    train_work_state_WORK_ERROR = 6,
    train_work_state_WORK_FATAL = 7
} train_work_state;


typedef struct _train_agent_state {
    /* 设备基础信息 */
    uint32_t train_id; /* 车头ID */
    uint32_t carriage_cnt; /* 车厢数量 */
    train_dev_state train_state; /* 车头设备状态，车辆硬件状态 */
    component_state train_agent_work_state; /* 车头运行状态，车代理组件状态 */
    train_work_state work_state; /* 车头组工作状态，车上软件状态 */
    uint32_t train_error_no; /* 车头故障码 */
} train_agent_state;


#endif