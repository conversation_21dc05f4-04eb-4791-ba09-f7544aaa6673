// #include "converter.hpp"
#include "ipc_interface/vehicle_interface.hpp"


void cjs_cvt::to_enum(const char *in, int &value)
{
    if (in == nullptr)
        return;

    if (!strcmp(in, ENUM_0) || !strcmp(in, "0"))
        value = 0;
    else if (!strcmp(in, ENUM_1) || !strcmp(in, "1"))
        value = 1;
    else if (!strcmp(in, ENUM_2) || !strcmp(in, "2"))
        value = 2;
    else if (!strcmp(in, ENUM_3) || !strcmp(in, "3"))
        value = 3;
    else if (!strcmp(in, ENUM_4) || !strcmp(in, "4"))
        value = 4;
    else if (!strcmp(in, ENUM_5) || !strcmp(in, "5"))
        value = 5;
    else if (!strcmp(in, ENUM_6) || !strcmp(in, "6"))
        value = 6;
    else
        SPDLOG_WARN("out of enum range");
}

void cjs_cvt::to_int(const char *in, int &value)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);
    value = root->valueint;
    cJSON_Delete(root);
}

void cjs_cvt::to_string(const char *in, std::string &value)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);
    value = root->valuestring;
    cJSON_Delete(root);
}

void cjs_cvt::to_float(const char *in, float &value)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);
    value = root->valuedouble;
    cJSON_Delete(root);
}

void cjs_cvt::to_container_state(const char *in, std::list<led_info> &states)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    led_info st;
    int size = cJSON_GetArraySize(root);
    for (int i = 0; i < size; i++)
    {
        cJSON *info = cJSON_GetArrayItem(root, i);
        
        cJSON *id = cJSON_GetObjectItem(info, FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_GRID_NO);
        st.id = atoi(id->valuestring);

        cJSON *status = cJSON_GetObjectItem(info, FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_STATUS);

        if (!strcmp(cJSON_Print(status), ENUM_0))
            st.color = 0;
        else if (!strcmp(cJSON_Print(status), ENUM_1))      //物控黄色
            st.color = 3;
        else if (!strcmp(cJSON_Print(status), ENUM_2))      //物控绿色
            st.color = 2;
        else if (!strcmp(cJSON_Print(status), ENUM_3))      //物控红色
            st.color = 1;
        else if (!strcmp(cJSON_Print(status), ENUM_4))      //物控蓝色
            st.color = 4;
        else if (!strcmp(cJSON_Print(status), ENUM_5))      //物控紫色
            st.color = 5;
        else if (!strcmp(cJSON_Print(status), ENUM_6))      //物控白色
            st.color = 7;
        else if (!strcmp(cJSON_Print(status), ENUM_7))      //物控青色
            st.color = 6;
        else
            SPDLOG_DEBUG("out of enum range");

        cJSON *frequency = cJSON_GetObjectItem(info, FUNC_GRID_LAMP_STATUS_NOTIFY_KEY_BLINK_FREQUENCY);
        if (!strcmp(cJSON_Print(frequency), ENUM_0))
            st.flash_freq = 0;
        else if (!strcmp(cJSON_Print(frequency), ENUM_1))
            st.flash_freq = 4;      //表示灯控闪烁频率50hz
        else
            SPDLOG_DEBUG("out of enum range");
        states.emplace_back(st);
    }

    cJSON_Delete(root);
}
void cjs_cvt::to_container_seal_state(const char *in, std::list<container_seal_state_single> &states)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    container_seal_state_single st;
    int size = cJSON_GetArraySize(root);
    for (int i = 0; i < size; i++)
    {
        cJSON *info = cJSON_GetArrayItem(root, i);
        
        cJSON *id = cJSON_GetObjectItem(info, FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_GRID_NO);
        st.container_id = atoi(id->valuestring);

        cJSON *status = cJSON_GetObjectItem(info, FUNC_GRID_LAMP_SEAL_NOTIFY_KEY_STATUS);

        if (!strcmp(cJSON_Print(status), ENUM_0))           //物控空闲
            st.seal_state = container_seal_state_IDLE;
        else if (!strcmp(cJSON_Print(status), ENUM_1))      //物控封箱
            st.seal_state = container_seal_state_SEAL;
        else if (!strcmp(cJSON_Print(status), ENUM_2))      //物控集包
            st.seal_state = container_seal_state_CONTAIN;
        else
            SPDLOG_DEBUG("out of enum range");

        states.emplace_back(st);
    }
    SPDLOG_DEBUG("func_update_container_seal state thingtalk oon_id:{}, status:{}", st.container_id, st.seal_state);
    cJSON_Delete(root);
}
void cjs_cvt::to_position_xyz(const char *in, int32_t &p_x, int32_t &p_y, int32_t &p_z)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    if (root == nullptr)
    {
        SPDLOG_DEBUG("invalid position");
        return;
    }

    cJSON *x = cJSON_GetObjectItem(root, FUNC_CONTROL_DEST_DISPATCH_KEY_X);
    p_x = x->valueint;

    cJSON *y = cJSON_GetObjectItem(root, FUNC_CONTROL_DEST_DISPATCH_KEY_Y);
    p_y = y->valueint;

    cJSON *z = cJSON_GetObjectItem(root, FUNC_CONTROL_DEST_DISPATCH_KEY_Z);
    p_z = z->valueint;

    cJSON_Delete(root);
}

// void cjs_cvt::to_config_set(const char *in, std::list<config_set_cmd> &cmd)
// {
//     if (in == nullptr)
//     return;

//     cJSON *root = cJSON_Parse(in);

//     config_set_cmd temp;
//     int size = cJSON_GetArraySize(root);
//     for (int i = 0; i < size; i++)
//     {
//         cJSON *info = cJSON_GetArrayItem(root, i);
   
//         cJSON *id = cJSON_GetObjectItem(info, FUNC_ONFIG_SET_KEY_NAME);
//        // temp.set_type= atoi(id->valuestring);
//        if(!strcmp(id->valuestring,FUNC_CONFIG_QUERY_KEY_TYPE))
//        {
//             temp.set_type= s_type_excp_HANDLE;
//        }else{
//             temp.set_type= s_type_NULL;
//             SPDLOG_DEBUG("set type out of enum range");
//             //return;
//        }

//         cJSON *value = cJSON_GetObjectItem(info, FUNC_ONFIG_SET_KEY_VALUE);

//         if (!strcmp(cJSON_Print(value), ENUM_0))
//             temp.set_value = 0;
//         else if (!strcmp(cJSON_Print(value), ENUM_1))      //入兜底口
//             temp.set_value = 1;
//         else if (!strcmp(cJSON_Print(value), ENUM_2))      //原地等待
//             temp.set_value = 2;
//         else
//             SPDLOG_DEBUG("out of enum range");

//         cmd.emplace_back(temp);
//     }
//     cJSON_Delete(root);
// }


void cjs_cvt::to_hmi_lamp_state(const char *in, std::string &id, int &state)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    if (root == nullptr)
    {
        SPDLOG_DEBUG("invalid position");
        return;
    }

    cJSON *c_id = cJSON_GetObjectItem(root, FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_PILOT_NO);
    id = c_id->valuestring;

    cJSON *c_st = cJSON_GetObjectItem(root, FUNC_PILOT_LAMP_STATUS_NOTIFY_KEY_STATUS);
    to_int(c_st->valuestring, state);

    cJSON_Delete(root);
}

void cjs_cvt::to_buzzer_info(const char *in, std::string &id, int &state)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    if (root == nullptr)
    {
        SPDLOG_DEBUG("invalid position");
        return;
    }

    cJSON *c_id = cJSON_GetObjectItem(root, FUNC_BUZZER_CONTROL_KEY_BUZZER_NO);
    id = c_id->valuestring;

    cJSON *c_st = cJSON_GetObjectItem(root, FUNC_BUZZER_CONTROL_KEY_BUZZER_STATUS);
    to_int(c_st->valuestring, state);

    cJSON_Delete(root);
}

void cjs_cvt::to_shelf_info(const char *in, std::string &shelf_id, int &action)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    int size = cJSON_GetArraySize(root);

    if (root == nullptr)
    {
        SPDLOG_DEBUG("invalid position");
        return;
    }

    for(int i = 0; i < size; i++)
    {
        cJSON *info = cJSON_GetArrayItem(root, i);
        cJSON *c_id = cJSON_GetObjectItem(info, FUNC_SHELF_LOCK_CONTROL_KEY_SEGMENT_NO);
        shelf_id = c_id->valuestring;

        cJSON *c_st = cJSON_GetObjectItem(info, FUNC_SHELF_LOCK_CONTROL_KEY_ACTION);
        to_int(c_st->valuestring, action);
    }

    cJSON_Delete(root);
}


void cjs_cvt::to_scanner_info(const char *in, std::vector<std::string> &ids, std::vector<int> &modes)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    int size = cJSON_GetArraySize(root);
    for (int i = 0; i < size; i++)
    {
        cJSON *info = cJSON_GetArrayItem(root, i);
        
        cJSON *id = cJSON_GetObjectItem(info, FUNC_AUTO_CODE_SCAN_KEY_CODE_SCAN_NO);
        ids.emplace_back(id->valuestring);

        cJSON *mode = cJSON_GetObjectItem(info, FUNC_AUTO_CODE_SCAN_KEY_MODE);
        int m;
        to_int(mode->valuestring, m);
        modes.emplace_back(m);
    }

    cJSON_Delete(root);
}

void cjs_cvt::to_feeder_belt_info(const char *in, std::vector<std::string> &ids, std::vector<int> &speeds)
{
    if (in == nullptr)
        return;

    cJSON *root = cJSON_Parse(in);

    int size = cJSON_GetArraySize(root);
    for (int i = 0; i < size; i++)
    {
        cJSON *info = cJSON_GetArrayItem(root, i);
        
        cJSON *id = cJSON_GetObjectItem(info, FUNC_CONTROL_DISPATHCH_KEY_BELT_NO);
        ids.emplace_back(id->valuestring);

        cJSON *speed = cJSON_GetObjectItem(info, FUNC_CONTROL_DISPATHCH_KEY_SPEED);
        speeds.emplace_back(speed->valueint);
    }

    cJSON_Delete(root);
}



void cjs_cvt::from_config_query(JDThingTalkProtoFuncCallResFunc_t *response, const uint32_t &set_value)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, FUNC_CONFIG_QUERY_KEY_NAMES, FUNC_CONFIG_QUERY_KEY_TYPE);
    uint32_t value;
    if(set_value == 0)
        value = 1;
    else
        value = 2;
    cJSON_AddNumberToObject(item, FUNC_CONFIG_QUERY_KEY_VALUE, value);

    cJSON_AddItemToArray(state_array, item);

    strcpy(response->out[0]->key, FUNC_RESPONSE_KEY);
    response->out[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}
void cjs_cvt::from_belt_state_get(JDThingTalkProtoFuncCallResFunc_t *response, const feeder_dev_state_total &state)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_BELT_INFO_NO, FUNC_FEEDER_BELT_STATUS_KEY_BELT_NO);
    cJSON_AddNumberToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_SEQUENCE, state.sequence);
    if(state.err_code != 0)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_EXCEPTION);
    else if(state.ready_state == false)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_NO_AVAIL);
    else if(state.ready_state == true)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_AVAILABLE);

    cJSON_AddItemToArray(state_array, item);

    strcpy(response->out[1]->key, FUNC_FEEDER_BELT_STATUS_KEY_BELTS);
    response->out[1]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void cjs_cvt::from_config_set_response(JDThingTalkProtoFuncCallResFunc_t *response,int result)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();

    cJSON_AddStringToObject(item, FUNC_ONFIG_SET_KEY_NAME, FUNC_CONFIG_QUERY_KEY_TYPE);
 
   // cJSON_AddNumberToObject(item, FUNC_ONFIG_SET_KEY_RESULT, FUNC_RESPONSE_SUCCESS);
    if(result == 1)
        cJSON_AddNumberToObject(item, FUNC_ONFIG_SET_KEY_RESULT, FUNC_RESPONSE_SUCCESS);
    else
         cJSON_AddNumberToObject(item, FUNC_ONFIG_SET_KEY_RESULT, FUNC_RESPONSE_FAIL);   
    cJSON_AddItemToArray(state_array, item);

    strcpy(response->out[0]->key, FUNC_CONFIG_QUERY_KEY_CONFIGS);
    response->out[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}
char* evt_cvt::pack_string(char *in_string)
{
    char *out = nullptr;
    cJSON *root = cJSON_CreateString(in_string);
    if (root != nullptr) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }

    return out;
}

char* evt_cvt::pack_string(const char *in_string)
{
    char *out = nullptr;
    cJSON *root = cJSON_CreateString(in_string);
    if (root != nullptr) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }

    return out;
}

char* evt_cvt::pack_enum(int num)
{
    char *out = nullptr;
    cJSON *root = cJSON_CreateNumber(num);
    if (root != nullptr) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }

    return out;
}

char* evt_cvt::pack_int(int num)
{
    char *out = nullptr;
    cJSON *root = cJSON_CreateNumber(num);
    if (root != nullptr) {
        out = cJSON_Print(root);
        cJSON_Delete(root);
    }

    return out;
}

void evt_cvt::from_barcode(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::barcode_task_info &barcode)
{
    strcpy(event->parameters[0]->key, EVT_BARCODE_REPORT_KEY_FEEDER_NO);
    event->parameters[0]->value = pack_string(barcode.feeder_no.c_str());
    strcpy(event->parameters[1]->key, EVT_BARCODE_REPORT_KEY_SCANER_NO);
    event->parameters[1]->value = pack_string(barcode.scaner_no.c_str());
    strcpy(event->parameters[2]->key, EVT_BARCODE_REPORT_KEY_TASK_NO);
    event->parameters[2]->value = pack_string(barcode.task_no.c_str());

//数组创建
    const char *code_group[barcode.code_group.size()];

    for (unsigned int i = 0; i < barcode.code_group.size(); i++)
        code_group[i] = barcode.code_group[i].c_str();

    cJSON *group_str = cJSON_CreateStringArray(code_group, barcode.code_group.size());

    strcpy(event->parameters[3]->key, EVT_BARCODE_REPORT_KEY_BARCODE_GROUP);

    event->parameters[3]->value = cJSON_Print(group_str);       //将cjson格式变量生成char*

    strcpy(event->parameters[4]->key, EVT_BARCODE_REPORT_KEY_LENGTH);
    event->parameters[4]->value = pack_string(std::to_string(barcode.vol.length).c_str());
    strcpy(event->parameters[5]->key, EVT_BARCODE_REPORT_KEY_WIDTH);
    event->parameters[5]->value = pack_string(std::to_string(barcode.vol.width).c_str());
    strcpy(event->parameters[6]->key, EVT_BARCODE_REPORT_KEY_HEIGHT);
    event->parameters[6]->value = pack_string(std::to_string(barcode.vol.height).c_str());
    strcpy(event->parameters[7]->key, EVT_BARCODE_REPORT_KEY_WEIGHT);
    event->parameters[7]->value = pack_string(std::to_string(barcode.weight).c_str());
    strcpy(event->parameters[8]->key, EVT_BARCODE_REPORT_KEY_QTY);
    event->parameters[8]->value = pack_string(std::to_string(barcode.barcode_count).c_str());

    cJSON_Delete(group_str);
}

void evt_cvt::from_normal_task_state(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::task_state &state)
{
    strcpy(event->parameters[0]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_NO);
    event->parameters[0]->value = pack_string(state.task_no.c_str());

    strcpy(event->parameters[1]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_TYPE);
    event->parameters[1]->value = pack_enum(state.type);

    strcpy(event->parameters[2]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_STATUS);
    event->parameters[2]->value = pack_enum(state.status);

    strcpy(event->parameters[3]->key, EVT_TASK_STATUS_REPORT_KEY_GRID_NO);
    event->parameters[3]->value = pack_string(state.grid_no.c_str());

    strcpy(event->parameters[4]->key, EVT_TASK_STATUS_REPORT_KEY_SORT_NO);
    event->parameters[4]->value = pack_string(state.sort_no.c_str());
}

void evt_cvt::from_hospice_task_state(JDThingTalkProtoEvtPostEvt_t *event, const task_manager::task_state &state)
{
    strcpy(event->parameters[0]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_NO);
    event->parameters[0]->value = pack_string(state.task_no.c_str());

    strcpy(event->parameters[1]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_TYPE);
    event->parameters[1]->value = pack_enum(task_manager::DISCLOSED_SORTING);

    strcpy(event->parameters[2]->key, EVT_TASK_STATUS_REPORT_KEY_TASK_STATUS);
    event->parameters[2]->value = pack_enum(task_manager::FINISH);

    strcpy(event->parameters[3]->key, EVT_TASK_STATUS_REPORT_KEY_GRID_NO);
    event->parameters[3]->value = pack_string(state.grid_no.c_str());

    strcpy(event->parameters[4]->key, EVT_TASK_STATUS_REPORT_KEY_SORT_NO);
    event->parameters[4]->value = pack_string(state.sort_no.c_str());

    strcpy(event->parameters[5]->key, EVT_TASK_STATUS_REPORT_KEY_ERROR_REASON);
    event->parameters[5]->value = pack_string(state.exp_info.c_str());
}

void evt_cvt::from_container_group_rfid(JDThingTalkProtoEvtPostEvt_t *event, const box_info_multiple &states)
{
    cJSON *state_array = cJSON_CreateArray();

    for (int i = 0; i < states.boxes_count; i++)
    {
        auto state = states.boxes[i];

        cJSON *item = cJSON_CreateObject();
        std::string group_id = "G" + std::to_string(state.box_id);
        cJSON_AddStringToObject(item, EVT_GRID_GROUP_ACTION_REPORT_GRID_GROUP_NO, group_id.c_str());
        cJSON_AddStringToObject(item, EVT_GRID_GROUP_ACTION_REPORT_SHELF_NO, std::string(state.RFID).c_str());

        if (state.box_st == box_state_BIND)
            cJSON_AddNumberToObject(item, EVT_GRID_GROUP_ACTION_REPORT_STATUS, EVT_GRID_GROUP_ACTION_REPORT_STATUS_BIND);
        else if (state.box_st == box_state_UNBIND)
            cJSON_AddNumberToObject(item, EVT_GRID_GROUP_ACTION_REPORT_STATUS, EVT_GRID_GROUP_ACTION_REPORT_STATUS_UNBIND);

        cJSON_AddItemToArray(state_array, item);
    }
    strcpy(event->parameters[0]->key, EVT_GRID_GROUP_ACTION_REPORT_KEY_GRID_GROUP_RELATION);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_container_rfid(JDThingTalkProtoEvtPostEvt_t *event, const box_info_multiple &states)
{
    cJSON *state_array = cJSON_CreateArray();

    for (int i = 0; i < states.boxes_count; i++)
    {
        auto state = states.boxes[i];

        cJSON *item = cJSON_CreateObject();
        cJSON_AddStringToObject(item, EVT_GRID_CONTAINER_ACTION_REPORT_GRID_NO, std::to_string(state.box_id).c_str());
        cJSON_AddStringToObject(item, EVT_GRID_CONTAINER_ACTION_REPORT_CONTAINER_NO, std::string(state.RFID).c_str());

        if (state.box_st == box_state_BIND)
            cJSON_AddNumberToObject(item, EVT_GRID_CONTAINER_ACTION_REPORT_STATUS, EVT_GRID_CONTAINER_ACTION_REPORT_STATUS_BIND);
        else if (state.box_st == box_state_UNBIND)
            cJSON_AddNumberToObject(item, EVT_GRID_CONTAINER_ACTION_REPORT_STATUS, EVT_GRID_CONTAINER_ACTION_REPORT_STATUS_UNBIND);

        cJSON_AddItemToArray(state_array, item);
    }
    strcpy(event->parameters[0]->key, EVT_GRID_CONTAINER_ACTION_REPORT_KEY_GRID_CONTAINER_RELATION);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_container_satr(JDThingTalkProtoEvtPostEvt_t *event, const slot_state &state)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_GRID_STATUS_REPORT_GRID_NO, std::to_string(state.id).c_str());

    if (state.st == state_FULL)
        cJSON_AddNumberToObject(item, EVT_GRID_STATUS_REPORT_STATUS, EVT_GRID_STATUS_REPORT_STATUS_FULL);
    else if (state.st == state_NORMAL)
        cJSON_AddNumberToObject(item, EVT_GRID_STATUS_REPORT_STATUS, EVT_GRID_STATUS_REPORT_STATUS_EMPTY);

    cJSON_AddItemToArray(state_array, item);

    strcpy(event->parameters[0]->key, EVT_GRID_STATUS_REPORT_KEY_GRID_STATUS_ARRAY);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_shelf_state(JDThingTalkProtoEvtPostEvt_t *event, const shelves_state &shelf_state)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_SHELF_LOCK_STATUS_REPORT_SEGMENT_NO, std::to_string(shelf_state.shelves_group).c_str());

    if (shelf_state.state == shelves_cmd_tab_LOCK)
        cJSON_AddNumberToObject(item, EVT_SHELF_LOCK_STATUS_REPORT_STATUS, EVT_SHELF_LOCK_STATUS_REPORT_STATUS_LOCK);
    else if (shelf_state.state == shelves_cmd_tab_UNLOCK)
        cJSON_AddNumberToObject(item, EVT_SHELF_LOCK_STATUS_REPORT_STATUS, EVT_SHELF_LOCK_STATUS_REPORT_STATUS_UNLOCK);

    cJSON_AddItemToArray(state_array, item);

    strcpy(event->parameters[0]->key, EVT_SHELF_LOCK_REPORT_SHELF_LOCK_STATUS_ARRAY);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_container_raster_state(JDThingTalkProtoEvtPostEvt_t *event, const slot_state &state)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_GRID_GRATING_SENSOR_REPORT_GRID_NO, std::to_string(state.id).c_str());

    cJSON_AddItemToArray(state_array, item);

    strcpy(event->parameters[0]->key, EVT_GRID_GRATING_SENSOR_REPORT_KEY_GRID_SENSOR_ARRAY);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_container_seal_state(JDThingTalkProtoEvtPostEvt_t *event, const container_seal_state_single &state)
{
    cJSON *state_array = cJSON_CreateArray();

    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_GRID_SEAL_ACTION_REPORT_GRID_NO, std::to_string(state.container_id).c_str());

    if (state.seal_state == container_seal_state_IDLE)
        cJSON_AddNumberToObject(item, EVT_GRID_SEAL_ACTION_REPORT_STATUS, EVT_GRID_SEAL_ACTION_REPORT_STATUS_IDLE);
    else if (state.seal_state == container_seal_state_SEAL || state.seal_state == container_seal_state_UNKNOWN)
        cJSON_AddNumberToObject(item, EVT_GRID_SEAL_ACTION_REPORT_STATUS, EVT_GRID_SEAL_ACTION_REPORT_STATUS_SEAL);
    else
        cJSON_AddNumberToObject(item, EVT_GRID_SEAL_ACTION_REPORT_STATUS, EVT_GRID_SEAL_ACTION_REPORT_STATUS_CONTAIN);

    cJSON_AddItemToArray(state_array, item);

    strcpy(event->parameters[0]->key, EVT_GRID_SEAL_ACTION_REPORT_GRID_SEAL_ARRAY);
    event->parameters[0]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

void evt_cvt::from_container_list(JDThingTalkProtoEvtPostEvt_t *event, const std::vector<uint32_t> &con_list)
{
    cJSON *list_array = cJSON_CreateArray();

    for (auto &con: con_list)
    {
        cJSON *item = cJSON_CreateObject();
        cJSON_AddStringToObject(item, EVT_GRID_LIST_NORMAL_REPORT_GRID_NO, std::to_string(con).c_str());
 
        cJSON_AddItemToArray(list_array, item);
    }

    strcpy(event->parameters[0]->key, EVT_GRID_LIST_NORMAL_REPORT_KEY_GRID_NORMAL_ARRAY);
    event->parameters[0]->value = cJSON_Print(list_array);

    cJSON_Delete(list_array);
}

void evt_cvt::from_sys_register(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state)
{
    strcpy(event->parameters[0]->key, EVT_REGISTER_REPORT_KEY_STATUS);
    event->parameters[0]->value = pack_enum(EVT_REGISTER_REPORT_STATUS_REGISTER);

    strcpy(event->parameters[1]->key, EVT_REGISTER_REPORT_KEY_MODE);
    if (state.mode == e_sys_mode_AUTO)
        event->parameters[1]->value = pack_enum(EVT_REGISTER_REPORT_MODE_AUTO);
    else if (state.mode == e_sys_mode_MANNUAL)
        event->parameters[1]->value = pack_enum(EVT_REGISTER_REPORT_MODE_MANUAL);
    else
    {
        SPDLOG_ERROR("get wrong sys mode: {}", state.mode);
        event->parameters[1]->value = pack_enum(EVT_REGISTER_REPORT_MODE_SEMI_AUTO);
    }
}

void evt_cvt::from_sys_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state)
{
    strcpy(event->parameters[0]->key, EVT_STATUS_REPORT_KEY_MODE);
    if (state.mode != e_sys_mode_AUTO)
        event->parameters[0]->value = pack_enum(EVT_STATUS_REPORT_MODE_MANUAL);
    else
        event->parameters[0]->value = pack_enum(EVT_STATUS_REPORT_MODE_AUTO);

    strcpy(event->parameters[1]->key, EVT_STATUS_REPORT_KEY_WORK_STATUS);
    if (state.state == e_wkstate_SYS_AUTO_RUNNING)
        event->parameters[1]->value = pack_enum(EVT_STATUS_REPORT_WORK_STATUS_WORKING);
    else
        event->parameters[1]->value = pack_enum(EVT_STATUS_REPORT_WORK_STATUS_IDLE);

    strcpy(event->parameters[2]->key, EVT_STATUS_REPORT_KEY_FAULT_STATUS);
    if (state.err == e_errstate_NOERROR || state.err == e_errstate_LOCAL_ERROR)
        event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_FAULT_STATUS_NORMAL);
    else
        event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_FAULT_STATUS_ERR);

    strcpy(event->parameters[3]->key, EVT_STATUS_REPORT_KEY_SAFETY_DOOR_STATUS);
    if (state.dev_st.safty_door_open == true)
        event->parameters[3]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_OPENED);
    else
        event->parameters[3]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_CLOSED);

    strcpy(event->parameters[4]->key, EVT_STATUS_REPORT_KEY_RUNNING_STATUS);
    if(state.has_dev_st == true)
    {
        if(state.dev_st.emerg_pressed == true)
            event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_EMERG_STOP);
        else
        {
            if (state.state == e_wkstate_SYS_INIT || state.state == e_wkstate_SYS_CHECK)
                event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_PAUSE);
            else if (state.state == e_wkstate_SYS_AUTO_RUNNING || state.state == e_wkstate_SYS_MANUAL || state.state == e_wkstate_SYS_CALIBRATE) 
                event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_RUNNING);
            else if (state.state == e_wkstate_SYS_STOP || state.state == e_wkstate_SYS_STOPING || state.state == e_wkstate_SYS_PART_ERROR || state.state == e_wkstate_SYS_ERROR_RECOVERY)
                event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_STOP);
        }
    }
    else if(state.has_dev_st == false)
    {
        if (state.state == e_wkstate_SYS_INIT || state.state == e_wkstate_SYS_CHECK)
            event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_PAUSE);
        else if (state.state == e_wkstate_SYS_AUTO_RUNNING || state.state == e_wkstate_SYS_MANUAL || state.state == e_wkstate_SYS_CALIBRATE) 
            event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_RUNNING);
        else if (state.state == e_wkstate_SYS_STOP || state.state == e_wkstate_SYS_STOPING || state.state == e_wkstate_SYS_PART_ERROR || state.state == e_wkstate_SYS_ERROR_RECOVERY)
            event->parameters[4]->value = pack_enum(EVT_STATUS_REPORT_RUNNING_STATUS_STOP);
    }
    else
        SPDLOG_ERROR("error sys state: {}", state.state);
}

bool evt_cvt::is_bit_set(uint32_t value, int bit_position) {
    return ((value & (1 << bit_position)) != 0);
}

void evt_cvt::from_safetygate_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state, int bit_position)
{
    strcpy(event->parameters[0]->key, EVT_SAFETYGATE_STATE_REPORT_KEY_SAFETYGATE_NO);
    strcpy(event->parameters[1]->key, EVT_SAFETYGATE_STATE_REPORT_KEY_BUTTON_STATUS);
    strcpy(event->parameters[2]->key, EVT_SAFETYGATE_STATE_REPORT_KEY_GATE_STATUS);

    switch(bit_position)
    {
        case 0:
            event->parameters[0]->value = pack_int(EVT_SAFETYGATE_STATE_REPORT_SATETYGATE_FRONT);

            if(is_bit_set(state.dev_st.emerg_button_state, 0))
                event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_PRESSED);
             else
                event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_RESTORED);

            if(is_bit_set(state.dev_st.safty_door_state, 0))
                event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_OPENED);
            else
                event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_CLOSED);

            break;

        case 1:
            event->parameters[0]->value = pack_int(EVT_SAFETYGATE_STATE_REPORT_SATETYGATE_TAIL);

            if(is_bit_set(state.dev_st.emerg_button_state, 1))
                event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_PRESSED);
             else
                event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_RESTORED);

            if(is_bit_set(state.dev_st.safty_door_state, 1))
                event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_OPENED);
            else
                event->parameters[2]->value = pack_enum(EVT_STATUS_REPORT_SAFETY_DOOR_STATUS_CLOSED);

            break;

        default:
            break;
    }

}

void evt_cvt::from_electricalcabinet_state(JDThingTalkProtoEvtPostEvt_t *event, const sys_mode_state &state)
{
    strcpy(event->parameters[0]->key, EVT_ELECTRICAL_CABINET_STATE_REPORT_KEY_CABINET_NO);
    event->parameters[0]->value = pack_int(0);

    strcpy(event->parameters[1]->key, EVT_ELECTRICAL_CABINET_STATE_REPORT_KEY_BUTTON_STATUS);
    if(is_bit_set(state.dev_st.emerg_button_state, 2))
        event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_PRESSED);
    else
        event->parameters[1]->value = pack_enum(EVT_KEY_STATUS_REPORT_EMERGENCY_RESTORED);
    
}

std::string evt_cvt::format_conversion(int num)
{
    std::ostringstream oss;
    oss << std::setw(2) << std::setfill('0') << num;
      
    return oss.str();
}

std::string evt_cvt::get_platform_id(int num)
{
    std::ostringstream oss;
    int id = num % 100;

    oss << std::setw(2) << std::setfill('0') << id;
      
    return oss.str();
}

void evt_cvt::from_vehicle_state(JDThingTalkProtoEvtPostEvt_t *event, const std::unordered_map<int, device_manager::vehicle_total_state> &states, const e_sys_mode &mode)
{
    SPDLOG_INFO("from_vehicle_state 123");

    std::string group_id, carriage_id, platform_id;

    cJSON *sort_group_status_array = cJSON_CreateArray();

    for (auto state = states.begin(); state != states.end(); ++state)
    {
        auto &st = state->second;
        cJSON *sort_group_item = cJSON_CreateObject();

        SPDLOG_INFO("train_id:{}, carriage_id:{}, platform_id:{}", state->first, st.running_state.carriage_state[0].carriage_id, st.running_state.carriage_state[0].platform_state[0].platform_id);
        group_id = format_conversion(state->first);
        cJSON_AddStringToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_SORT_GROUP_NO, group_id.c_str());

        cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_SPEED, st.running_state.walk_motor_speed);

        if(st.running_state.basic_info.state)
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_STATUS_IN_SYSTEM);
        else
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_STATUS_OUT_SYSTEM);

        if(st.running_state.fault_state == 1)
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_FAULT_STATUS, EVT_SORT_GROUP_STATUS_REPORT_STATUS_NORMAL);
        else
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_FAULT_STATUS, EVT_SORT_GROUP_STATUS_REPORT_STATUS_FAULT);
        
        cJSON_AddStringToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_LOCAL_IP, st.running_state.basic_info.ip);
        cJSON_AddStringToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_HARDWARE_VERSION, st.running_state.basic_info.hw_ver);

        cJSON_AddStringToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_STATUS_CODE, st.running_state.walk_motor_status_code);

        if (st.running_state.walk_motor_state == train_dev_state_DEV_NORMAL)
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
        else
            cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);

        cJSON_AddNumberToObject(sort_group_item, EVT_SORT_GROUP_STATUS_REPORT_WALK_MOTOR_SPEED, st.running_state.walk_motor_speed);

        SPDLOG_INFO("st.running_state.basic_info.hw_ver:{}", st.running_state.basic_info.hw_ver);

        cJSON *sort_status_array = cJSON_CreateArray();
        cJSON_AddItemToObject(sort_group_item, EVT_SORT_STATUS_REPORT_KEY_SORT_STATUS_ARRAY, sort_status_array);

        for(int i = 0 ; i < st.running_state.carriage_count; i++)
        {
            cJSON *sort_status_item = cJSON_CreateObject();

            carriage_id = format_conversion(st.running_state.carriage_state[i].carriage_id);
            cJSON_AddStringToObject(sort_status_item, EVT_SORT_STATUS_REPORT_SORT_NO, carriage_id.c_str());
            cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_LOCATION_X, st.running_state.carriage_state[i].p_xyz.x);
            cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_LOCATION_Y, st.running_state.carriage_state[i].p_xyz.y);
            cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_LOCATION_Z, 0);

            if(st.running_state.carriage_state[i].car_type == carriage_type_CARRIAGE_TYPE_HEADSTOCK)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_CATEGORY, EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_HEAD);
            else if(st.running_state.carriage_state[i].car_type == carriage_type_CARRIAGE_TYPE_CARRIAGE)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_CATEGORY, EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_CARRIAGE);
            else
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_CATEGORY, EVT_SORT_STATUS_REPORT_CARRIAGE_TYPE_TAIL);

            if (st.running_state.carriage_state[i].carriage_state == train_dev_state_DEV_NORMAL)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_FAULT_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
            else
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_FAULT_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);

            if(st.running_state.carriage_state[i].state == true)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_CARRIAGE_ENABLE);
            else
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_CARRIAGE_DISABLE);
            
            cJSON_AddStringToObject(sort_status_item, EVT_SORT_STATUS_REPORT_SOFTWARE_VERSION, st.running_state.basic_info.sw_ver);

            if(st.running_state.carriage_state[i].car_type == carriage_type_CARRIAGE_TYPE_HEADSTOCK)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_POWERED, EVT_SORT_STATUS_REPORT_CARRIAGE_HAS_TOWERDE);
            else
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_POWERED, EVT_SORT_STATUS_REPORT_CARRIAGE_NO_TOWERED);

            cJSON_AddStringToObject(sort_status_item, EVT_SORT_STATUS_REPORT_HOIST_MOTOR_STATUS_CODE, st.running_state.carriage_state[i].carriage_motor_status_code);

            if (st.running_state.carriage_state[i].carriage_motor_state == train_dev_state_DEV_NORMAL)
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_HOIST_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
            else
                cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_HOIST_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);

            cJSON_AddNumberToObject(sort_status_item, EVT_SORT_STATUS_REPORT_HOIST_MOTOR_SPEED, st.running_state.carriage_state[i].motor_speed);

            cJSON *cargo_platform_status_array = cJSON_CreateArray();
            cJSON_AddItemToObject(sort_status_item, EVT_SORT_STATUS_REPORT_KEY_CARGO_PLATFORM_STATUS_ARRAY, cargo_platform_status_array);

            for(int j = 0; j < st.running_state.carriage_state[i].platform_count; j++)
            {
                cJSON *cargo_platform_status_item = cJSON_CreateObject();

                platform_id = get_platform_id(st.running_state.carriage_state[i].platform_state[j].platform_id);
                cJSON_AddStringToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_PLATFORM_NO, platform_id.c_str());
                cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_LOCATION_Z, st.running_state.carriage_state[i].platform_state[j].z);
                
                if(st.running_state.carriage_state[i].platform_state[j].state == true)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_CARRIAGE_ENABLE);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_STATUS, EVT_SORT_STATUS_REPORT_CARRIAGE_DISABLE);

                if (st.running_state.carriage_state[i].platform_state[j].with_load == true)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_LOAD_STATUS, EVT_SORT_STATUS_REPORT_LOAD_STATUS_WITH_LOAD);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_LOAD_STATUS, EVT_SORT_STATUS_REPORT_LOAD_STATUS_NO_LOAD);

                if (st.running_state.carriage_state[i].platform_state[j].platform_motor_status == train_dev_state_DEV_NORMAL)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_FAULT_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_FAULT_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);

                if (st.running_state.carriage_state[i].platform_state[j].conveyor_type == platform_type_PLATFORM_TYPE_BELT)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE, EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE_BELT);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE, EVT_SORT_STATUS_REPORT_CONVEYOR_TYPE_DUMP);

                cJSON_AddStringToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_STATUS_CODE, st.running_state.carriage_state[i].platform_state[j].platform_motor_status_code);

                if (st.running_state.carriage_state[i].platform_state[j].platform_motor_status == train_dev_state_DEV_NORMAL)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);

                cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_MOTOR_SPEED, st.running_state.carriage_state[i].platform_state[j].platform_motor_speed);

                if (st.running_state.carriage_state[i].platform_state[j].platform_zero_sensor_status == true)
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_ZERO_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_CONVEYOR_SENSOR_STATUS_ON);
                else
                    cJSON_AddNumberToObject(cargo_platform_status_item, EVT_SORT_STATUS_REPORT_CONVEYOR_ZERO_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_CONVEYOR_ZERO_SENSOR_STATUS_OFF);
            
                cJSON_AddItemToArray(cargo_platform_status_array, cargo_platform_status_item);
            }
            
            cJSON_AddItemToArray(sort_status_array, sort_status_item);
        }

        cJSON_AddItemToArray(sort_group_status_array, sort_group_item);
    }

    // // 打印生成的JSON
    // char *json_string = cJSON_Print(sort_group_status_array);
    // printf("----------------------%s\n", json_string);

    strcpy(event->parameters[0]->key, EVT_SORT_STATUS_REPORT_KEY_SORT_GROUP_STATUS_ARRAY);
    event->parameters[0]->value = cJSON_Print(sort_group_status_array);

    cJSON_Delete(sort_group_status_array);
}

void evt_cvt::from_feeder_state(JDThingTalkProtoEvtPostEvt_t *event, const feeder_dev_state_total &feeder_state, const std::map<key_id, key_evt_type> &button_state)
{
    strcpy(event->parameters[0]->key, EVT_FEEDER_STATUS_REPORT_KEY_FEEDER_NO);
    event->parameters[0]->value = pack_int(feeder_state.dev_id);

    strcpy(event->parameters[1]->key, EVT_FEEDER_STATUS_REPORT_KEY_FEEDER_STATUS);
    if (is_feeder_normal(feeder_state) == true)
        event->parameters[1]->value = pack_enum(EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_NORMAL);
    else
        event->parameters[1]->value = pack_enum(EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_ERR);

    cJSON *belts_state_array = cJSON_CreateArray();

    from_feeder_belt_state(*belts_state_array, feeder_state.belt_sensor, feeder_state.belt_motor);

    strcpy(event->parameters[2]->key, EVT_FEEDER_STATUS_REPORT_KEY_BELTS_STATUS);
    event->parameters[2]->value = cJSON_Print(belts_state_array);
    cJSON_Delete(belts_state_array);

    strcpy(event->parameters[3]->key, EVT_FEEDER_STATUS_REPORT_KEY_OCR_STATUS);
    if (feeder_state.auto_scanner.state == feeder_dev_state_DEV_STATE_ERR)
        event->parameters[3]->value = pack_enum(EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_ERR);
    else
        event->parameters[3]->value = pack_enum(EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_NORMAL);

    cJSON *buttons_state_array = cJSON_CreateArray();

    from_feeder_button_state(*buttons_state_array, button_state);

    strcpy(event->parameters[4]->key, EVT_FEEDER_STATUS_REPORT_KEY_BUTTONS_STATUS);
    event->parameters[4]->value = cJSON_Print(buttons_state_array);
    cJSON_Delete(buttons_state_array);
}

void evt_cvt::from_feeder_belt_status(JDThingTalkProtoEvtPostEvt_t *event, const feeder_dev_state_total &feeder_state)
{
    cJSON *state_array = cJSON_CreateArray();
    strcpy(event->parameters[0]->key, EVT_FEEDER_STATUS_REPORT_KEY_FEEDER_NO);
    event->parameters[0]->value = pack_int(feeder_state.dev_id);

    strcpy(event->parameters[1]->key, EVT_FEEDER_BELT_STATUS_REPORT_KEY_BLETS);

    cJSON *item = cJSON_CreateObject();

    cJSON_AddStringToObject(item, EVT_FEEDER_BELT_STATUS_REPORT_KEY_BLET_LINE_NO,EVT_FEEDER_BELT_STATUS_REPORT_KEY_BELT_ONE);

    cJSON_AddNumberToObject(item, EVT_FEEDER_BELT_STATUS_REPORT_KEY_SEQUENCE, feeder_state.sequence);

    if(feeder_state.err_code != 0)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_EXCEPTION);
    else if(feeder_state.ready_state == false)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_NO_AVAIL);
    else if(feeder_state.ready_state == true)
        cJSON_AddStringToObject(item, FUNC_FEEDER_BELT_STATUS_KEY_STATUS, FUNC_FEEDER_BELT_STATUS_KEY_STATUS_AVAILABLE);


    cJSON_AddItemToArray(state_array, item);
    event->parameters[1]->value = cJSON_Print(state_array);

    cJSON_Delete(state_array);
}

bool evt_cvt::is_feeder_normal(const feeder_dev_state_total &state)
{
    for (int i = 0; i < state.belt_motor.motor_count; i++)
        if (state.belt_motor.motor[i].state == feeder_dev_state_DEV_STATE_ERR)
            return false;

    if (state.charger.state == feeder_dev_state_DEV_STATE_ERR)
        return false;

    if (state.auto_scanner.state == feeder_dev_state_DEV_STATE_ERR)
        return false;

    if (state.manual_scanner.state == feeder_dev_state_DEV_STATE_ERR)
        return false;

    return true;
}

void evt_cvt::from_feeder_belt_state(cJSON &array, const feeder_belt_sensor_state_multiple &sensor_state, const feeder_belt_motor_state_multiple &motor_state)
{
    for (int i = 0; i < motor_state.motor_count; i++)
    {
        auto m = motor_state.motor[i];

        if (m.state == feeder_dev_state_DEV_STATE_UNKNOWN)
            continue;

        cJSON *item = cJSON_CreateObject();
        cJSON_AddStringToObject(item, EVT_FEEDER_STATUS_REPORT_BELT_NO, std::to_string(m.dev_id + 1).c_str());

        if (i <= sensor_state.sensor_count && sensor_state.sensor[i].state == feeder_sensor_trigger_state_ON)
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS, EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS_TRIGGER);
        else
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS, EVT_FEEDER_STATUS_REPORT_SENSOR_STATUS_UNTRIGGER);

        if (m.state == feeder_dev_state_DEV_STATE_ERR)
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_MOTOR_STATUS, EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_ERR);
        else
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_MOTOR_STATUS, EVT_FEEDER_STATUS_REPORT_FAULT_STATUS_NORMAL);

        cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_MOTOR_SPEED, m.speed);

        cJSON_AddItemToArray(&array, item);
    }
}

void evt_cvt::from_feeder_button_state(cJSON &array, const std::map<key_id, key_evt_type> &button_state)
{
    for (auto &bt: button_state)
    {
        cJSON *item = cJSON_CreateObject();

        switch (bt.first)
        {
        case key_id_KEY_RESERVE:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_REVERSE);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        case key_id_KEY_START:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_START);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        case key_id_KEY_STOP:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_STOP);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        case key_id_KEY_SLEEP:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_SLEEP);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        case key_id_KEY_RESET:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_RESET);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        case key_id_KEY_EMERG:
            cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE, EVT_FEEDER_STATUS_REPORT_BUTTON_TYPE_EMERG);
            if (bt.second == key_evt_type_EVENT_RELEASE)
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_UP);
            else
                cJSON_AddNumberToObject(item, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION, EVT_FEEDER_STATUS_REPORT_BUTTON_ACTION_DOWN);
            break;
        default:
            SPDLOG_ERROR("wrong key id: {}", bt.first);
            break;
        }

        cJSON_AddItemToArray(&array, item);
    }
}

// void evt_cvt::from_manual_vehicle_state(cJSON &item, const vehicle::vehicle_running_state &st)
// {
//     cJSON_AddStringToObject(&item, EVT_SORT_STATUS_REPORT_WALK_MOTOR_STATUS_CODE, std::to_string(st.walk_motor_state.state_code).c_str());
//     if (st.walk_motor_state.state_code == motor_state_code_ERROR)
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_WALK_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);
//     else
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_WALK_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
//     cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_WALK_MOTOR_SPEED, st.walk_motor_state.speed);

//     cJSON_AddStringToObject(&item, EVT_SORT_STATUS_REPORT_BELT_MOTOR_STATUS_CODE, std::to_string(st.belt_motor_state.state_code).c_str());
//     if (st.belt_motor_state.state_code == motor_state_code_ERROR)
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_BELT_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_ERR);
//     else
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_BELT_MOTOR_STATUS, EVT_SORT_STATUS_REPORT_FAULT_STATUS_NORMAL);
//     cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_BELT_MOTOR_SPEED, st.belt_motor_state.speed);

//     if (st.zero_sensor_st == sensor_trigger_state_ON)
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS_ON);
//     else
//         cJSON_AddNumberToObject(&item, EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_BELT_ZERO_SENSOR_STATUS_OFF);
// }

// void evt_cvt::from_manual_vehicle_sensor_state(cJSON &array, const std::unordered_map<int, device_manager::vehicle_total_state> &states)
// {
//     for (auto state = states.begin(); state != states.end(); ++state)
//     {
//         auto &st = state->second;
//         for (int i = 0; i < st.running_state.load_sensor_st.sensor_count; i++)
//         {
//             auto &sensor = st.running_state.load_sensor_st.sensor[i];
//             cJSON *item = cJSON_CreateObject();
//             cJSON_AddStringToObject(item, EVT_SORT_STATUS_REPORT_FEEDER_REMOVE_SENSOR_NO, std::to_string(sensor.dev_id).c_str());
//             cJSON_AddStringToObject(item, EVT_SORT_STATUS_REPORT_SORT_NO, std::to_string(state->first).c_str());

//             if (sensor.state == sensor_trigger_state_ON)
//                 cJSON_AddNumberToObject(item, EVT_SORT_STATUS_REPORT_FEEDER_REMOVE_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_FEEDEER_REMOVE_SENSOR_STATUS_TRIGGER);
//             else
//                 cJSON_AddNumberToObject(item, EVT_SORT_STATUS_REPORT_FEEDER_REMOVE_SENSOR_STATUS, EVT_SORT_STATUS_REPORT_FEEDEER_REMOVE_SENSOR_STATUS_UNTRIGGER);

//             cJSON_AddItemToArray(&array, item);
//         }
//     }
// }

void evt_cvt::from_vehicle_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_SORTGROUP_ERROR_REPORT_KEY_SORTGROUP_NO);
    event->parameters[0]->value = pack_int(exception.dev);

    strcpy(event->parameters[1]->key, EVT_SORTGROUP_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_vehicle_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    //异常解除只报一个  结构体数组
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_SORTGROUP_ERROR_RELEASE_REPORT_SORTGROUP_NO, std::to_string(exception.dev).c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_SORTGROUP_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_SORTGROUP_ERROR_RELEASE_REPORT_ARRAY);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}


void evt_cvt::from_carriage_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    std::string dev_id;
    strcpy(event->parameters[0]->key, EVT_SORTGROUP_SORT_ERROR_REPORT_KEY_SORTGROUP_NO);
    dev_id = std::to_string(exception.dev) + std::to_string(exception.sub_dev);
    event->parameters[0]->value = pack_string(dev_id.c_str());

    strcpy(event->parameters[1]->key, EVT_SORTGROUP_SORT_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_carriage_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    //异常解除只报一个  结构体数组
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    std::string dev_id;

    dev_id = std::to_string(exception.dev) + std::to_string(exception.sub_dev);
    cJSON_AddStringToObject(item, EVT_SORTGROUP_SORT_ERROR_RELEASE_REPORT_SORTGROUP_NO, dev_id.c_str());

    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_SORTGROUP_SORT_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_PLATFORM_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_PLATFORM);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}



void evt_cvt::from_platform_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    std::string dev_id;
    strcpy(event->parameters[0]->key, EVT_PLATFORM_ERROR_REPORT_KEY_PLATFORM_NO);
    // vehicle_interface::get_instance()->platform_int_to_string(dev_id, exception.dev, exception.sub_dev);
    vehicle_interface::get_instance()->carriage_int_to_string(dev_id, exception.dev, exception.sub_dev);
    event->parameters[0]->value = pack_string(dev_id.c_str());

    strcpy(event->parameters[1]->key, EVT_PLATFORM_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_platform_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    //异常解除只报一个  结构体数组
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    std::string dev_id;

    // vehicle_interface::get_instance()->platform_int_to_string(dev_id, exception.dev, exception.sub_dev);
    vehicle_interface::get_instance()->carriage_int_to_string(dev_id, exception.dev, exception.sub_dev);
    cJSON_AddStringToObject(item, EVT_PLATFORM_ERROR_RELEASE_REPORT_PLATFORM_NO, dev_id.c_str());

    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_PLATFORM_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_PLATFORM_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_PLATFORM);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}

void evt_cvt::from_container_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_GRID_ERROR_REPORT_KEY_GRID_NO);
    event->parameters[0]->value = pack_int(exception.dev);

    strcpy(event->parameters[1]->key, EVT_GRID_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_contianer_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    //异常解除只报一个  结构体数组
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_GRID_ERROR_RELEASE_REPORT_GRID_NO, std::to_string(exception.dev).c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_GRID_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_GRID_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_GRID_ARRAY);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}

void evt_cvt::from_container_group_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_GRID_GROUP_ERROR_REPORT_KEY_GRID_GROUP_NO);
    std::string group_id_str = "G" + std::to_string(exception.dev);
    event->parameters[0]->value = pack_string(group_id_str.c_str());

    strcpy(event->parameters[1]->key, EVT_GRID_GROUP_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_container_group_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    std::string group_id_str = "G" + std::to_string(exception.dev);
    cJSON_AddStringToObject(item, EVT_GRID_GROUP_ERROR_RELEASE_REPORT_GRID_GROUP_NO, group_id_str.c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_GRID_GROUP_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_GRID_GROUP_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_GRID_GROUP_ARRAY);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}


void evt_cvt::from_shelves_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_SEGMENT_ERROR_REPORT_KEY_SEGMENT_NO);
    event->parameters[0]->value = pack_int(exception.dev);

    strcpy(event->parameters[1]->key, EVT_SEGMENT_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_shelves_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_SEGMENT_NO, std::to_string(exception.dev).c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_SEGMENT_ERROR_RELEASE_REPORT_KEY_RELEASE_SEGMENT_ARRAY);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}


void evt_cvt::from_feeder_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_FEEDER_ERROR_REPORT_KEY_FEEDER_NO);
    event->parameters[0]->value = pack_int(exception.dev);

    strcpy(event->parameters[1]->key, EVT_FEEDER_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_feeder_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_FEEDER_ERROR_RELEASE_REPORT_FEEDER_NO, std::to_string(exception.dev).c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_FEEDER_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_FEEDER_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_FEEDER);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}

void evt_cvt::from_switcher_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_SWITCHER_ERROR_REPORT_KEY_SWITCHER_NO);
    event->parameters[0]->value = pack_int(exception.dev);

    strcpy(event->parameters[1]->key, EVT_SWITCHER_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[1]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_switcher_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    cJSON *exception_array = cJSON_CreateArray();
    cJSON *item = cJSON_CreateObject();
    cJSON_AddStringToObject(item, EVT_SWITCHER_ERROR_RELEASE_REPORT_SWITCHER_NO, std::to_string(exception.dev).c_str());
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    cJSON_AddStringToObject(item, EVT_SWITCHER_ERROR_RELEASE_REPORT_ERROR_CODE, (err_code.c_str()));

    cJSON_AddItemToArray(exception_array, item);

    strcpy(event->parameters[0]->key, EVT_SWITCHER_ERROR_RELEASE_REPORT_KEY_ERROR_RELEASE_SWITCHER);
    event->parameters[0]->value = cJSON_Print(exception_array);

    cJSON_Delete(exception_array);
}

void evt_cvt::from_device_exception_occur(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    strcpy(event->parameters[0]->key, EVT_ERROR_REPORT_KEY_ERROR_CODE);
    std::string err_code = std::to_string(exception.code) + "_" + std::to_string(exception.sub_code);
    event->parameters[0]->value = pack_string(err_code.c_str());
}

void evt_cvt::from_device_exception_reset(JDThingTalkProtoEvtPostEvt_t *event, const except_info &exception)
{
    const char *code_group[1];
    code_group[0] = (std::to_string(exception.code) + "_" + std::to_string(exception.sub_code)).c_str();
    cJSON *group_str = cJSON_CreateStringArray(code_group, 1);

    strcpy(event->parameters[0]->key, EVT_ERROR_RELEASE_REPORT_KEY_ERROR_CODE_ARRAY);
    event->parameters[0]->value = cJSON_Print(group_str);

    cJSON_Delete(group_str);
}
